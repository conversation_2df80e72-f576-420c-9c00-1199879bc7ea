<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class GameplanCourse extends Model
{
    protected $guarded = [];

    public function course()
    {
        return $this->belongsTo('App\Course');
    }
    public function student()
    {
        return $this->belongsTo('App\Student', 'user_id');
    }
    public function individual()
    {
        return $this->belongsTo('App\IndividualStudent', 'user_id');
    }
}
