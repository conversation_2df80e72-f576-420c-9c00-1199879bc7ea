@if ($isSubItem)
    <input
        type="checkbox"
        id='chk-{{ $item->anzsco_occupation_id }}-{{ $subItem->anzsco_occupation_id }}'
        name="selected_jobs[]"
        class='custom-chk-btn jobs-chk-btn  sub-job-chk-{{ $item->anzsco_occupation_id }}'
        value="{{ $subItem->anzsco_occupation_id }}"
        data-title="{{ $subItem->anzsco_title }}"
        {{ $isChecked ? 'checked' : '' }}
    />
        <div
            class="job-sug-badge sub-job-badge sub-job-badge-{{ $item->anzsco_occupation_id }}"
            @if (!$isChecked)
                style="display: none;"
            @endif
        >

           <div class="job-sug-badge-header">
            <div>
            {{ $subItem->anzsco_title }}
            </div>
            <div>
            <label
            for='chk-{{ $item->anzsco_occupation_id }}-{{ $subItem->anzsco_occupation_id }}'
            class="{{ $isChecked ? '' : 'sub-job-label sub-job-label-'.$item->anzsco_occupation_id }}"
            >
            {{-- <span class="material-symbols-outlined mdi-add-circle" title="Add">
                add_circle
            </span> --}}
            <span class="mdi-add-circle px-3 py-1 suggestion-add-btn">
               +Add 
              </span>                           
            <span class="material-symbols-outlined mdi-check-circle" title="Added">check_circle</span>
            </label>
           </div>
        </div>
        <div>
            <span class="job-info-btn" data-id="{{ $subItem->anzsco_occupation_id }}" title="info">
                Learn more →
            </span>
    </div>
    </div>
@else
    <input
        type="checkbox"
        id='chk-{{ $item->anzsco_occupation_id }}'
        name="selected_jobs[]"
        class='custom-chk-btn jobs-chk-btn parent-job-chk'
        value="{{ $item->anzsco_occupation_id }}"
        data-title="{{ $item->anzsco_title }}"
        {{ $isChecked ? 'checked' : '' }}
    />
    <div class="job-sug-badge">

        <div class="job-sug-badge-header">
            <div>
                {{ $item->anzsco_title }}
           </div>

           <div>
            <label for='chk-{{ $item->anzsco_occupation_id }}'>
                <span class="mdi-add-circle px-3 py-1 suggestion-add-btn">
                  +Add
                  </span> 
                <span class="material-symbols-outlined mdi-check-circle" title="Added">check_circle</span>
            </label>
           </div>

        </div>

        <span class="job-info-btn" data-id="{{ $item->anzsco_occupation_id }}" title="info">
            Learn more →
        </span>

        {{-- @if ($item->trending)
            <i class="fa-solid fa-fire" aria-hidden="true"></i>
        @endif --}}
    </div>
@endif
