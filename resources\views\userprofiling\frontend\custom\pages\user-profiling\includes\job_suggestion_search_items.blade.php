@if ($result->count())
   
    @foreach ($result as $item)
    <input
        type="checkbox"
        id="chk-searchable-{{ $item->id }}"
        name="selected_jobs[]"
        data-id="{{ $item->id }}"
        data-title="{{ $item->anzsco_title }}"
        class="custom-chk-btn-searchable jobs-chk-btn-searchable"
        value="{{ $item->id }}"
        {{-- @if ($userSelectedOccupations->contains($item->id))
            checked
        @endif --}}
    >
    <label for="chk-searchable-{{ $item->id }}">
        {{ $item->anzsco_title }}
        &nbsp;
        @if ($item->trending)
            <i class="fa-solid fa-fire" aria-hidden="true"></i>
        @endif        
    </label>
    @endforeach

    <button class="btn btn-secondary my-2 my-sm-0 add-job-results w-100" type="button">ADD <i class="fa fa-plus" aria-hidden="true"></i></button>
@else
    <em>No match found...</em>
@endif
