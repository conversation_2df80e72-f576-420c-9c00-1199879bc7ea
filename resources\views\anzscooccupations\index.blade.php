@extends('layouts.admin')

@section('content')
    <div id="anzsco-success-message"></div>
    <div class="row">
        <div class="col-lg-12">
            <div class="card card-default">
                <div class="card-header separator">
                    <div class="card-title">
                        ANZSCO Occupations
                    </div>
                    <div class="card-controls">
                        <button data-target="#addAnzscoModal" data-toggle="modal" id="btnFillSizeToggler"
                            class="btn btn-primary btn-cons">
                            <i class="fa fa-plus"></i> Add New
                        </button>
                    </div>
                </div>
                <div class="card-block">
                    <div class="table-responsive">
                        <table class="table table-hover custom-datatable no-footer" id="anzsco_table">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>ANZSCO Code</th>
                                    <th>Title</th>
                                    <th>Occupation Type</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('modals')
        @include('anzscooccupations.modals.add')
        @include('anzscooccupations.modals.edit')
    @endpush

    @push('scripts')
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/jquery-confirm@3.3.4/dist/jquery-confirm.min.css" />
        <script src="https://cdn.jsdelivr.net/npm/jquery-confirm@3.3.4/dist/jquery-confirm.min.js"></script>
        <script>
            function createTable() {
                $('#anzsco_table').DataTable({
                    bLengthChange: true,
                    lengthMenu: [
                        [10, 25, 50, 100, 100000],
                        [10, 25, 50, 100, "All"]
                    ],
                    bDestroy: true,
                    processing: true,
                    serverSide: true,
                    stateSave: true,
                    order: [
                        [0, "desc"]
                    ],
                    ajax: {
                        url: "{{ route('profiling-posts.anzsco-data.occupations.index') }}",
                        type: "GET",
                        error: function(xhr, status, error) {
                            console.error("AJAX Error:", status, error);
                            console.log(xhr.responseText);
                        }
                    },
                    columns: [{
                            data: 'id',
                            name: 'id'
                        },
                        {
                            data: 'anzsco_code',
                            name: 'anzsco_code',
                            orderable: true
                        },
                        {
                            data: 'anzsco_title',
                            name: 'anzsco_title',
                            orderable: true
                        },
                        {
                            data: 'occupation_type',
                            name: 'occupation_type',
                            orderable: true
                        },
                        {
                            data: 'action',
                            name: 'action',
                            orderable: false
                        }
                    ],
                    language: {
                        emptyTable: "{{ __('No data available in the table') }}",
                        search: "<i class='ti-search'></i>",
                        searchPlaceholder: '{{ __('Quick Search') }}',
                        paginate: {
                            next: "Next",
                            previous: "Previous"
                        }
                    },
                    dom: 'Blfrtip',
                    buttons: [{
                            extend: 'copyHtml5',
                            text: '<i class="fa fa-copy"></i>',
                            titleAttr: '{{ __('Copy') }}',
                            exportOptions: {
                                columns: ':visible:not(:last-child)'
                            }
                        },
                        {
                            extend: 'excelHtml5',
                            text: '<i class="fa fa-file-excel-o"></i>',
                            titleAttr: '{{ __('Excel') }}',
                            exportOptions: {
                                columns: ':visible:not(:last-child)'
                            }
                        },
                        {
                            extend: 'csvHtml5',
                            text: '<i class="fa fa-download"></i>',
                            titleAttr: '{{ __('CSV') }}',
                            exportOptions: {
                                columns: ':visible:not(:last-child)'
                            }
                        },
                        {
                            extend: 'pdfHtml5',
                            text: '<i class="fa fa-file-pdf"></i>',
                            titleAttr: '{{ __('PDF') }}',
                            exportOptions: {
                                columns: ':visible:not(:last-child)'
                            },
                            orientation: 'landscape',
                            pageSize: 'A4',
                            customize: function(doc) {
                                doc.content[1].table.widths = Array(doc.content[1].table.body[0].length + 1)
                                    .join('*').split('');
                            }
                        },
                        {
                            extend: 'print',
                            text: '<i class="fa fa-print"></i>',
                            titleAttr: '{{ __('Print') }}',
                            exportOptions: {
                                columns: ':not(:last-child)'
                            }
                        }
                    ],
                    columnDefs: [{
                            visible: false,
                            targets: 0
                        },
                        {
                            responsivePriority: 1,
                            targets: 1
                        },
                        {
                            responsivePriority: 1,
                            targets: 2
                        },
                        {
                            responsivePriority: 2,
                            targets: -1
                        }
                    ],
                    responsive: true
                });
            }

            createTable();

            // Handle edit modal population
            $(document).on('click', '.edit-anzsco-occupation', function(e) {
                e.preventDefault();
                let data = $(this).data();

                $('#edit_occupation_id').val(data.id);
                $('#edit_anzsco_code').val(data.anzsco_code);
                $('#edit_anzsco_title').val(data.anzsco_title);
                $('#edit_anzsco_description').val(data.anzsco_description);
                $('#edit_sub_profile_code').val(data.sub_profile_code);
                $('#edit_occupation_type').val(data.occupation_type);



                if (data.media) {
                    $('#current_media').html(
                        '<a href="' + data.media + '" target="_blank">View Current Media</a>'
                    );
                } else {
                    $('#current_media').html('');
                }
            });

            // Handle add form submission
            $(document).on('submit', '#addAnzscoForm', function(e) {
                e.preventDefault();
                let form = $(this);
                let formData = new FormData(this);

                // Clear previous error messages
                form.find('.text-danger').text('');

                $.ajax({
                    url: "{{ route('profiling-posts.anzsco-data.occupations.store') }}",
                    type: "POST",
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.success) {
                            $('#addAnzscoModal').modal('hide');
                            createTable();

                            $('#anzsco-success-message').html(`
            <div class="alert alert-success text-center alert-dismissible fade show" role="alert">
                ${response.message}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        `);
                            setTimeout(() => {
                                $('#anzsco-success-message').html('');
                            }, 5000);
                        }
                    },
                    error: function(xhr) {
                        if (xhr.status === 422) {
                            let errors = xhr.responseJSON.errors;
                            $.each(errors, function(field, messages) {
                                form.find(`[name="${field}"]`).siblings('.text-danger').text(
                                    messages[0]);
                            });
                        } else {
                            alert(xhr.responseJSON?.message || "An unexpected error occurred.");
                        }
                    }
                });
            });

            // Handle edit form submission
            $(document).on('submit', '#editAnzscoForm', function(e) {
                e.preventDefault();
                let form = $(this);
                let formData = new FormData(this);
                let id = $('#edit_occupation_id').val();
                let url = "{{ route('profiling-posts.anzsco-data.occupations.update', ':id') }}".replace(':id', id);

                // Clear previous error messages
                form.find('.text-danger').text('');

                $.ajax({
                    url: url,
                    type: "POST",
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.success) {
                            $('#editAnzscoModal').modal('hide');
                            createTable();

                            $('#anzsco-success-message').html(`
            <div class="alert alert-success text-center alert-dismissible fade show" role="alert">
                ${response.message}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        `);
                            setTimeout(() => {
                                $('#anzsco-success-message').html('');
                            }, 5000);
                        }
                    },
                    error: function(xhr) {
                        if (xhr.status === 422) {
                            let errors = xhr.responseJSON.errors;
                            $.each(errors, function(field, messages) {
                                form.find(`[name="${field}"]`).siblings('.text-danger').text(
                                    messages[0]);
                            });
                        } else {
                            alert(xhr.responseJSON?.message || "An unexpected error occurred.");
                        }
                    }
                });
            });

            // Handle delete action
            $(document).on('click', '.delete-anzsco-occupation', function(e) {
                e.preventDefault();
                let url = $(this).attr('href');
                let token = $(this).data('token');

                $.confirm({
                    title: "Alert!",
                    content: "{{ __('Are you sure to delete?') }}",
                    buttons: {
                        confirm: function() {
                            $.ajax({
                                type: "DELETE",
                                url: url,
                                data: {
                                    _token: token
                                },
                                success: function(response) {
                                    if (response.success) {
                                        createTable();

                                        $('#anzsco-success-message').html(`
            <div class="alert alert-success text-center alert-dismissible fade show" role="alert">
                ${response.message}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        `);
                                        setTimeout(() => {
                                            $('#anzsco-success-message').html('');
                                        }, 5000);
                                    } else {
                                        alert(response.message || "An error occurred.");
                                    }
                                },
                                error: function(xhr) {
                                    alert(xhr.responseJSON?.message ||
                                        "An unexpected error occurred.");
                                }
                            });
                        },
                        cancel: function() {}
                    }
                });
            });
        </script>
    @endpush
@endsection
