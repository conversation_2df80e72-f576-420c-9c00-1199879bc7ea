/*! For license information please see 504.js.LICENSE.txt */
(self.webpackChunk=self.webpackChunk||[]).push([[504],{46702:(e,t)=>{var n,o,i;!function(r){if("undefined"!=typeof window){var a,l=0,c=!1,s=!1,d="message".length,u="[iFrameSizer]",m=u.length,f=null,p=window.requestAnimationFrame,g=Object.freeze({max:1,scroll:1,bodyScroll:1,documentElementScroll:1}),h={},v=null,y=Object.freeze({autoResize:!0,bodyBackground:null,bodyMargin:null,bodyMarginV1:8,bodyPadding:null,checkOrigin:!0,inPageLinks:!1,enablePublicMethods:!0,heightCalculationMethod:"bodyOffset",id:"iFrameResizer",interval:32,log:!1,maxHeight:1/0,maxWidth:1/0,minHeight:0,minWidth:0,mouseEvents:!0,resizeFrom:"parent",scrolling:!1,sizeHeight:!0,sizeWidth:!1,warningTimeout:5e3,tolerance:0,widthCalculationMethod:"scroll",onClose:function(){return!0},onClosed:function(){},onInit:function(){},onMessage:function(){_("onMessage function not defined")},onMouseEnter:function(){},onMouseLeave:function(){},onResized:function(){},onScroll:function(){return!0}}),b={};window.jQuery!==r&&((a=window.jQuery).fn?a.fn.iFrameResize||(a.fn.iFrameResize=function(e){return this.filter("iframe").each((function(t,n){P(n,e)})).end()}):B("","Unable to bind to jQuery, it is not fully loaded.")),o=[],(i="function"==typeof(n=U)?n.apply(t,o):n)===r||(e.exports=i),window.iFrameResize=window.iFrameResize||U()}function w(){return window.MutationObserver||window.WebKitMutationObserver||window.MozMutationObserver}function k(e,t,n){e.addEventListener(t,n,!1)}function x(e,t,n){e.removeEventListener(t,n,!1)}function E(e){return u+"["+function(e){var t="Host page: "+e;return window.top!==window.self&&(t=window.parentIFrame&&window.parentIFrame.getId?window.parentIFrame.getId()+": "+e:"Nested host page: "+e),t}(e)+"]"}function N(e){return h[e]?h[e].log:c}function V(e,t){C("log",e,t,N(e))}function B(e,t){C("info",e,t,N(e))}function _(e,t){C("warn",e,t,!0)}function C(e,t,n,o){!0===o&&"object"==typeof window.console&&console[e](E(t),n)}function L(e){function t(){i("Height"),i("Width"),R((function(){z(T),I(P),g("onResized",T)}),T,"init")}function n(e){return"border-box"!==e.boxSizing?0:(e.paddingTop?parseInt(e.paddingTop,10):0)+(e.paddingBottom?parseInt(e.paddingBottom,10):0)}function o(e){return"border-box"!==e.boxSizing?0:(e.borderTopWidth?parseInt(e.borderTopWidth,10):0)+(e.borderBottomWidth?parseInt(e.borderBottomWidth,10):0)}function i(e){var t=Number(h[P]["max"+e]),n=Number(h[P]["min"+e]),o=e.toLowerCase(),i=Number(T[o]);V(P,"Checking "+o+" is in range "+n+"-"+t),i<n&&(i=n,V(P,"Set "+o+" to min value")),i>t&&(i=t,V(P,"Set "+o+" to max value")),T[o]=""+i}function r(e){return L.slice(L.indexOf(":")+d+e)}function a(e,t){var n,o,i;n=function(){var n,o;D("Send Page Info","pageInfo:"+(n=document.body.getBoundingClientRect(),o=T.iframe.getBoundingClientRect(),JSON.stringify({iframeHeight:o.height,iframeWidth:o.width,clientHeight:Math.max(document.documentElement.clientHeight,window.innerHeight||0),clientWidth:Math.max(document.documentElement.clientWidth,window.innerWidth||0),offsetTop:parseInt(o.top-n.top,10),offsetLeft:parseInt(o.left-n.left,10),scrollTop:window.pageYOffset,scrollLeft:window.pageXOffset,documentHeight:document.documentElement.clientHeight,documentWidth:document.documentElement.clientWidth,windowHeight:window.innerHeight,windowWidth:window.innerWidth})),e,t)},o=32,b[i=t]||(b[i]=setTimeout((function(){b[i]=null,n()}),o))}function l(e){var t=e.getBoundingClientRect();return M(P),{x:Math.floor(Number(t.left)+Number(f.x)),y:Math.floor(Number(t.top)+Number(f.y))}}function c(e){var t=e?l(T.iframe):{x:0,y:0},n={x:Number(T.width)+t.x,y:Number(T.height)+t.y};V(P,"Reposition requested from iFrame (offset x:"+t.x+" y:"+t.y+")"),window.top===window.self?(f=n,s(),V(P,"--")):window.parentIFrame?window.parentIFrame["scrollTo"+(e?"Offset":"")](n.x,n.y):_(P,"Unable to scroll to requested position, window.parentIFrame not found")}function s(){!1===g("onScroll",f)?O():I(P)}function p(e){var t={};if(0===Number(T.width)&&0===Number(T.height)){var n=r(9).split(":");t={x:n[1],y:n[0]}}else t={x:T.width,y:T.height};g(e,{iframe:T.iframe,screenX:Number(t.x),screenY:Number(t.y),type:T.type})}function g(e,t){return S(P,e,t)}var v,y,w,E,N,C,L=e.data,T={},P=null;"[iFrameResizerChild]Ready"===L?function(){for(var e in h)D("iFrame requested init",H(e),h[e].iframe,e)}():u===(""+L).slice(0,m)&&L.slice(m).split(":")[0]in h?(w=L.slice(m).split(":"),E=w[1]?parseInt(w[1],10):0,N=h[w[0]]&&h[w[0]].iframe,C=getComputedStyle(N),T={iframe:N,id:w[0],height:E+n(C)+o(C),width:w[2],type:w[3]},P=T.id,h[P]&&(h[P].loaded=!0),(y=T.type in{true:1,false:1,undefined:1})&&V(P,"Ignoring init message from meta parent page"),!y&&function(e){var t=!0;return h[e]||(t=!1,_(T.type+" No settings for "+e+". Message was: "+L)),t}(P)&&(V(P,"Received: "+L),v=!0,null===T.iframe&&(_(P,"IFrame ("+T.id+") not found"),v=!1),v&&function(){var t,n=e.origin,o=h[P]&&h[P].checkOrigin;if(o&&""+n!="null"&&!(o.constructor===Array?function(){var e=0,t=!1;for(V(P,"Checking connection is from allowed list of origins: "+o);e<o.length;e++)if(o[e]===n){t=!0;break}return t}():(t=h[P]&&h[P].remoteHost,V(P,"Checking connection is from: "+t),n===t)))throw new Error("Unexpected message received from: "+n+" for "+T.iframe.id+". Message was: "+e.data+". This error can be disabled by setting the checkOrigin: false option or by providing of array of trusted domains.");return!0}()&&function(){switch(h[P]&&h[P].firstRun&&h[P]&&(h[P].firstRun=!1),T.type){case"close":F(T.iframe);break;case"message":d=r(6),V(P,"onMessage passed: {iframe: "+T.iframe.id+", message: "+d+"}"),g("onMessage",{iframe:T.iframe,message:JSON.parse(d)}),V(P,"--");break;case"mouseenter":p("onMouseEnter");break;case"mouseleave":p("onMouseLeave");break;case"autoResize":h[P].autoResize=JSON.parse(r(9));break;case"scrollTo":c(!1);break;case"scrollToOffset":c(!0);break;case"pageInfo":a(h[P]&&h[P].iframe,P),function(){function e(e,o){function i(){h[n]?a(h[n].iframe,n):t()}["scroll","resize"].forEach((function(t){V(n,e+t+" listener for sendPageInfo"),o(window,t,i)}))}function t(){e("Remove ",x)}var n=P;e("Add ",k),h[n]&&(h[n].stopPageInfo=t)}();break;case"pageInfoStop":h[P]&&h[P].stopPageInfo&&(h[P].stopPageInfo(),delete h[P].stopPageInfo);break;case"inPageLink":n=r(9).split("#")[1]||"",o=decodeURIComponent(n),(i=document.getElementById(o)||document.getElementsByName(o)[0])?(e=l(i),V(P,"Moving to in page link (#"+n+") at x: "+e.x+" y: "+e.y),f={x:e.x,y:e.y},s(),V(P,"--")):window.top===window.self?V(P,"In page link #"+n+" not found"):window.parentIFrame?window.parentIFrame.moveToAnchor(n):V(P,"In page link #"+n+" not found and window.parentIFrame not found");break;case"reset":j(T);break;case"init":t(),g("onInit",T.iframe);break;default:0===Number(T.width)&&0===Number(T.height)?_("Unsupported message received ("+T.type+"), this is likely due to the iframe containing a later version of iframe-resizer than the parent page"):t()}var e,n,o,i,d}())):B(P,"Ignored: "+L)}function S(e,t,n){var o=null,i=null;if(h[e]){if("function"!=typeof(o=h[e][t]))throw new TypeError(t+" on iFrame["+e+"] is not a function");i=o(n)}return i}function T(e){var t=e.id;delete h[t]}function F(e){var t=e.id;if(!1!==S(t,"onClose",t)){V(t,"Removing iFrame: "+t);try{e.parentNode&&e.parentNode.removeChild(e)}catch(e){_(e)}S(t,"onClosed",t),V(t,"--"),T(e)}else V(t,"Close iframe cancelled by onClose event")}function M(e){null===f&&V(e,"Get page position: "+(f={x:window.pageXOffset===r?document.documentElement.scrollLeft:window.pageXOffset,y:window.pageYOffset===r?document.documentElement.scrollTop:window.pageYOffset}).x+","+f.y)}function I(e){null!==f&&(window.scrollTo(f.x,f.y),V(e,"Set page position: "+f.x+","+f.y),O())}function O(){f=null}function j(e){V(e.id,"Size reset requested by "+("init"===e.type?"host page":"iFrame")),M(e.id),R((function(){z(e),D("reset","reset",e.iframe,e.id)}),e,"reset")}function z(e){function t(t){s||"0"!==e[t]||(s=!0,V(o,"Hidden iFrame detected, creating visibility listener"),function(){function e(){function e(e){function t(t){return"0px"===(h[e]&&h[e].iframe.style[t])}function n(e){return null!==e.offsetParent}h[e]&&n(h[e].iframe)&&(t("height")||t("width"))&&D("Visibility change","resize",h[e].iframe,e)}Object.keys(h).forEach((function(t){e(t)}))}function t(t){V("window","Mutation observed: "+t[0].target+" "+t[0].type),A(e,16)}function n(){var e=document.querySelector("body"),n={attributes:!0,attributeOldValue:!1,characterData:!0,characterDataOldValue:!1,childList:!0,subtree:!0};new o(t).observe(e,n)}var o=w();o&&n()}())}function n(n){!function(t){e.id?(e.iframe.style[t]=e[t]+"px",V(e.id,"IFrame ("+o+") "+t+" set to "+e[t]+"px")):V("undefined","messageData id not set")}(n),t(n)}var o=e.iframe.id;h[o]&&(h[o].sizeHeight&&n("height"),h[o].sizeWidth&&n("width"))}function R(e,t,n){n!==t.type&&p&&!window.jasmine?(V(t.id,"Requesting animation frame"),p(e)):e()}function D(e,t,n,o,i){var r,a=!1;o=o||n.id,h[o]&&(n&&"contentWindow"in n&&null!==n.contentWindow?(r=h[o]&&h[o].targetOrigin,V(o,"["+e+"] Sending msg to iframe["+o+"] ("+t+") targetOrigin: "+r),n.contentWindow.postMessage(u+t,r)):_(o,"["+e+"] IFrame("+o+") not found"),i&&h[o]&&h[o].warningTimeout&&(h[o].msgTimeout=setTimeout((function(){!h[o]||h[o].loaded||a||(a=!0,_(o,"IFrame has not responded within "+h[o].warningTimeout/1e3+" seconds. Check iFrameResizer.contentWindow.js has been loaded in iFrame. This message can be ignored if everything is working, or you can set the warningTimeout option to a higher value or zero to suppress this warning."))}),h[o].warningTimeout)))}function H(e){return e+":"+h[e].bodyMarginV1+":"+h[e].sizeWidth+":"+h[e].log+":"+h[e].interval+":"+h[e].enablePublicMethods+":"+h[e].autoResize+":"+h[e].bodyMargin+":"+h[e].heightCalculationMethod+":"+h[e].bodyBackground+":"+h[e].bodyPadding+":"+h[e].tolerance+":"+h[e].inPageLinks+":"+h[e].resizeFrom+":"+h[e].widthCalculationMethod+":"+h[e].mouseEvents}function P(e,t){function n(e){var t=e.split("Callback");if(2===t.length){var n="on"+t[0].charAt(0).toUpperCase()+t[0].slice(1);this[n]=this[e],delete this[e],_(a,"Deprecated: '"+e+"' has been renamed '"+n+"'. The old method will be removed in the next major version.")}}var o,i,a=function(n){if("string"!=typeof n)throw new TypeError("Invaild id for iFrame. Expected String");var o;return""===n&&(e.id=(o=t&&t.id||y.id+l++,null!==document.getElementById(o)&&(o+=l++),n=o),c=(t||{}).log,V(n,"Added missing iframe ID: "+n+" ("+e.src+")")),n}(e.id);a in h&&"iFrameResizer"in e?_(a,"Ignored iFrame, already setup."):(!function(t){var o;t=t||{},h[a]=Object.create(null),h[a].iframe=e,h[a].firstRun=!0,h[a].remoteHost=e.src&&e.src.split("/").slice(0,3).join("/"),function(e){if("object"!=typeof e)throw new TypeError("Options is not an object")}(t),Object.keys(t).forEach(n,t),function(e){for(var t in y)Object.prototype.hasOwnProperty.call(y,t)&&(h[a][t]=Object.prototype.hasOwnProperty.call(e,t)?e[t]:y[t])}(t),h[a]&&(h[a].targetOrigin=!0===h[a].checkOrigin?""===(o=h[a].remoteHost)||null!==o.match(/^(about:blank|javascript:|file:\/\/)/)?"*":o:"*")}(t),function(){switch(V(a,"IFrame scrolling "+(h[a]&&h[a].scrolling?"enabled":"disabled")+" for "+a),e.style.overflow=!1===(h[a]&&h[a].scrolling)?"hidden":"auto",h[a]&&h[a].scrolling){case"omit":break;case!0:e.scrolling="yes";break;case!1:e.scrolling="no";break;default:e.scrolling=h[a]?h[a].scrolling:"no"}}(),function(){function t(t){var n=h[a][t];1/0!==n&&0!==n&&(e.style[t]="number"==typeof n?n+"px":n,V(a,"Set "+t+" = "+e.style[t]))}function n(e){if(h[a]["min"+e]>h[a]["max"+e])throw new Error("Value for min"+e+" can not be greater than max"+e)}n("Height"),n("Width"),t("maxHeight"),t("minHeight"),t("maxWidth"),t("minWidth")}(),"number"!=typeof(h[a]&&h[a].bodyMargin)&&"0"!==(h[a]&&h[a].bodyMargin)||(h[a].bodyMarginV1=h[a].bodyMargin,h[a].bodyMargin=h[a].bodyMargin+"px"),o=H(a),(i=w())&&function(t){e.parentNode&&new t((function(t){t.forEach((function(t){Array.prototype.slice.call(t.removedNodes).forEach((function(t){t===e&&F(e)}))}))})).observe(e.parentNode,{childList:!0})}(i),k(e,"load",(function(){var t,n;D("iFrame.onload",o,e,r,!0),t=h[a]&&h[a].firstRun,n=h[a]&&h[a].heightCalculationMethod in g,!t&&n&&j({iframe:e,height:0,width:0,type:"init"})})),D("init",o,e,r,!0),h[a]&&(h[a].iframe.iFrameResizer={close:F.bind(null,h[a].iframe),removeListeners:T.bind(null,h[a].iframe),resize:D.bind(null,"Window resize","resize",h[a].iframe),moveToAnchor:function(e){D("Move to anchor","moveToAnchor:"+e,h[a].iframe,a)},sendMessage:function(e){D("Send Message","message:"+(e=JSON.stringify(e)),h[a].iframe,a)}}))}function A(e,t){null===v&&(v=setTimeout((function(){v=null,e()}),t))}function W(){"hidden"!==document.visibilityState&&(V("document","Trigger event: Visibility change"),A((function(){G("Tab Visible","resize")}),16))}function G(e,t){Object.keys(h).forEach((function(n){(function(e){return h[e]&&"parent"===h[e].resizeFrom&&h[e].autoResize&&!h[e].firstRun})(n)&&D(e,t,h[n].iframe,n)}))}function Z(){k(window,"message",L),k(window,"resize",(function(){var e;V("window","Trigger event: "+(e="resize")),A((function(){G("Window "+e,"resize")}),16)})),k(document,"visibilitychange",W),k(document,"-webkit-visibilitychange",W)}function U(){function e(e,n){n&&(!function(){if(!n.tagName)throw new TypeError("Object is not a valid DOM element");if("IFRAME"!==n.tagName.toUpperCase())throw new TypeError("Expected <IFRAME> tag, found <"+n.tagName+">")}(),P(n,e),t.push(n))}var t;return function(){var e,t=["moz","webkit","o","ms"];for(e=0;e<t.length&&!p;e+=1)p=window[t[e]+"RequestAnimationFrame"];p?p=p.bind(window):V("setup","RequestAnimationFrame not supported")}(),Z(),function(n,o){switch(t=[],function(e){e&&e.enablePublicMethods&&_("enablePublicMethods option has been removed, public methods are now always available in the iFrame")}(n),typeof o){case"undefined":case"string":Array.prototype.forEach.call(document.querySelectorAll(o||"iframe"),e.bind(r,n));break;case"object":e(n,o);break;default:throw new TypeError("Unexpected data type ("+typeof o+")")}return t}}}()},3368:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});var o=n(1519),i=n.n(o)()((function(e){return e[1]}));i.push([e.id,".mw-900px{max-width:900px}.animated-video>iframe{height:100%!important;width:100%!important}",""]);const r=i},6857:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});var o=n(1519),i=n.n(o)()((function(e){return e[1]}));i.push([e.id,".mw-900px[data-v-42f43f73]{max-width:900px}.w-90[data-v-42f43f73]{width:90%}",""]);const r=i},44044:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});var o=n(1519),i=n.n(o)()((function(e){return e[1]}));i.push([e.id,".app-container{background-color:#fff}.wrap{max-width:75ch;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.btn-white-custom{background:#fff;color:#000}.btn-black-custom:hover,.btn-white-custom{background-color:#fff!important;color:#000!important}.btn-black-custom,.btn-white-custom:hover,.btn.btn-white-custom:hover:not(.btn-active){background-color:#000!important;color:#fff!important}.module-sections{margin-left:-30px;margin-right:-30px;overflow:auto hidden;position:relative}.sticky-top{min-width:calc(100% - 140px);position:fixed}.module-section{border-bottom:1px solid;border-left:1px solid;border-top:1px solid;cursor:pointer;height:100px}.module-sections>.text-center:last-of-type>.module-section{border-right:1px solid}.app-content{padding:0}.banner_detail_box{left:20%;position:absolute;top:50%;transform:translate(-50%,-50%)}.modal-backdrop{opacity:.8!important}.section-content{margin-top:50px;padding-bottom:50px}.section-content iframe{width:100%!important}.section-content iframe.wistia_embed{height:100%!important}.section-content img{max-width:100%}.section-content p iframe,.section-content p img{margin-bottom:-1rem}.pointer{cursor:pointer}.overlay{overflow:overlay}.related{right:5%!important}.banner{background-color:#000;background-image:url(/images/vwe/home-parallax.jpg);background-position:50%;background-repeat:no-repeat;background-size:cover;display:block;min-height:calc(56.25vw - 149px);overflow:hidden;position:relative}.full-view-banner{margin-left:-30px;margin-right:-30px}.banner-video{height:100%}.banner-video>video{left:50%;position:absolute;top:50%;transform:translate(-50%,-50%);width:101%!important}.froala-response,.teacher-feedback{border-radius:10px;height:300px;overflow:auto;padding:20px}.froala-response{background-color:#fff;border:1px solid #bbb}.froala-response iframe{width:100%}.froala-response img{max-width:100%}div#kt_app_content{padding-bottom:0;padding-top:0}@media (max-width:1280px){.banner{height:56.25vw}.banner_detail_box{left:40%}.banner-video>video{height:100%!important;width:calc(65vw + 65vh)!important}}@media (min-width:992px){.sticky-top+.section-content{margin-top:100px}.module-sections{animation-duration:.2s;animation-fill-mode:forwards;animation-name:backtooriginal;z-index:100}.sticky-top{animation-duration:.2s;animation-fill-mode:forwards;animation-name:stick-top}@keyframes stick-top{0%{top:5px}to{top:0}}@keyframes backtooriginal{0%{top:-5px}to{top:0}}}@media (max-width:991px){.full-view-banner,.module-sections{margin-left:-20px;margin-right:-20px}.full-view-banner{margin-top:58.16px}.sticky-top{min-width:100%;top:119px}.module-section{height:100px}}@media (max-width:991px) and (min-width:768px) and (orientation:portrait){.banner{height:86.25vw}.banner-video>video{height:100%!important;width:calc(66vw + 66vh)!important}}@media (max-width:991px) and (orientation:landscape){.banner-video>video{height:auto!important;width:calc(70vw + 70vh)!important}}@media (max-width:767px){.banner{height:calc(100vh - 300px)}.banner_detail_box{left:50%}.sticky-top{margin-top:10px}}@media (max-width:575px){div#kt_app_content{padding-top:30px}.banner_detail_box{width:70vw!important}.full-view-banner{margin-top:0}.banner-video>video{height:100%!important;width:calc(90vw + 90vh)!important}}",""]);const r=i},96268:(e,t,n)=>{"use strict";n.d(t,{Z:()=>y});var o=n(70655),i=n(70821);function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function a(){a=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,o=Object.defineProperty||function(e,t,n){e[t]=n.value},i="function"==typeof Symbol?Symbol:{},l=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function d(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{d({},"")}catch(e){d=function(e,t,n){return e[t]=n}}function u(e,t,n,i){var r=t&&t.prototype instanceof p?t:p,a=Object.create(r.prototype),l=new _(i||[]);return o(a,"_invoke",{value:E(e,n,l)}),a}function m(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=u;var f={};function p(){}function g(){}function h(){}var v={};d(v,l,(function(){return this}));var y=Object.getPrototypeOf,b=y&&y(y(C([])));b&&b!==t&&n.call(b,l)&&(v=b);var w=h.prototype=p.prototype=Object.create(v);function k(e){["next","throw","return"].forEach((function(t){d(e,t,(function(e){return this._invoke(t,e)}))}))}function x(e,t){function i(o,a,l,c){var s=m(e[o],e,a);if("throw"!==s.type){var d=s.arg,u=d.value;return u&&"object"==r(u)&&n.call(u,"__await")?t.resolve(u.__await).then((function(e){i("next",e,l,c)}),(function(e){i("throw",e,l,c)})):t.resolve(u).then((function(e){d.value=e,l(d)}),(function(e){return i("throw",e,l,c)}))}c(s.arg)}var a;o(this,"_invoke",{value:function(e,n){function o(){return new t((function(t,o){i(e,n,t,o)}))}return a=a?a.then(o,o):o()}})}function E(e,t,n){var o="suspendedStart";return function(i,r){if("executing"===o)throw new Error("Generator is already running");if("completed"===o){if("throw"===i)throw r;return L()}for(n.method=i,n.arg=r;;){var a=n.delegate;if(a){var l=N(a,n);if(l){if(l===f)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===o)throw o="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o="executing";var c=m(e,t,n);if("normal"===c.type){if(o=n.done?"completed":"suspendedYield",c.arg===f)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o="completed",n.method="throw",n.arg=c.arg)}}}function N(e,t){var n=t.method,o=e.iterator[n];if(void 0===o)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,N(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),f;var i=m(o,e.iterator,t.arg);if("throw"===i.type)return t.method="throw",t.arg=i.arg,t.delegate=null,f;var r=i.arg;return r?r.done?(t[e.resultName]=r.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,f):r:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,f)}function V(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function B(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function _(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(V,this),this.reset(!0)}function C(e){if(e){var t=e[l];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function t(){for(;++o<e.length;)if(n.call(e,o))return t.value=e[o],t.done=!1,t;return t.value=void 0,t.done=!0,t};return i.next=i}}return{next:L}}function L(){return{value:void 0,done:!0}}return g.prototype=h,o(w,"constructor",{value:h,configurable:!0}),o(h,"constructor",{value:g,configurable:!0}),g.displayName=d(h,s,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===g||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,h):(e.__proto__=h,d(e,s,"GeneratorFunction")),e.prototype=Object.create(w),e},e.awrap=function(e){return{__await:e}},k(x.prototype),d(x.prototype,c,(function(){return this})),e.AsyncIterator=x,e.async=function(t,n,o,i,r){void 0===r&&(r=Promise);var a=new x(u(t,n,o,i),r);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},k(w),d(w,s,"Generator"),d(w,l,(function(){return this})),d(w,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var o in t)n.push(o);return n.reverse(),function e(){for(;n.length;){var o=n.pop();if(o in t)return e.value=o,e.done=!1,e}return e.done=!0,e}},e.values=C,_.prototype={constructor:_,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(B),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function o(n,o){return a.type="throw",a.arg=e,t.next=n,o&&(t.method="next",t.arg=void 0),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var r=this.tryEntries[i],a=r.completion;if("root"===r.tryLoc)return o("end");if(r.tryLoc<=this.prev){var l=n.call(r,"catchLoc"),c=n.call(r,"finallyLoc");if(l&&c){if(this.prev<r.catchLoc)return o(r.catchLoc,!0);if(this.prev<r.finallyLoc)return o(r.finallyLoc)}else if(l){if(this.prev<r.catchLoc)return o(r.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<r.finallyLoc)return o(r.finallyLoc)}}}},abrupt:function(e,t){for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var r=i;break}}r&&("break"===e||"continue"===e)&&r.tryLoc<=t&&t<=r.finallyLoc&&(r=null);var a=r?r.completion:{};return a.type=e,a.arg=t,r?(this.method="next",this.next=r.finallyLoc,f):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),f},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),B(n),f}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var i=o.arg;B(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:C(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),f}},e}var l={class:"modal fade",id:"kt_modal_viewResponse",tabindex:"-1","aria-hidden":"true"},c={class:"modal-dialog modal-dialog-centered modal-xl"},s={class:"modal-content rounded-0 mt-5"},d={class:"modal-header py-3"},u=(0,i.createElementVNode)("h5",{class:"modal-title"},null,-1),m=[(0,i.createElementVNode)("i",{class:"fa-solid fa-expand text-black text-black"},null,-1)],f=["href"],p=[(0,i.createElementVNode)("i",{class:"fa-solid fa-download text-black"},null,-1)],g=(0,i.createElementVNode)("button",{type:"button",class:"btn-close","data-bs-dismiss":"modal"},null,-1),h={class:"modal-body bg-black p-0 text-white text-center"},v=["src"];const y=(0,i.defineComponent)({__name:"ResponseModal",props:{modalSrc:null,downloadUrl:null},setup:function(e){var t=this,n=function(){return(0,o.mG)(t,void 0,void 0,a().mark((function e(){var t;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=document.querySelector("#kt_modal_viewResponse iframe")){e.next=3;break}return e.abrupt("return");case 3:if(e.prev=3,document.fullscreenElement){e.next=9;break}return e.next=7,t.requestFullscreen();case 7:e.next=11;break;case 9:return e.next=11,document.exitFullscreen();case 11:e.next=16;break;case 13:e.prev=13,e.t0=e.catch(3),console.error("Error attempting to toggle fullscreen:",e.t0);case 16:case"end":return e.stop()}}),e,null,[[3,13]])})))};return function(t,o){return(0,i.openBlock)(),(0,i.createElementBlock)("div",l,[(0,i.createElementVNode)("div",c,[(0,i.createElementVNode)("div",s,[(0,i.createElementVNode)("div",d,[u,(0,i.createElementVNode)("div",null,[(0,i.createElementVNode)("span",{class:"mx-4 cursor-pointer",onClick:n},m),e.downloadUrl?((0,i.openBlock)(),(0,i.createElementBlock)("a",{key:0,href:e.downloadUrl,download:"",class:"text-secondary mx-2"},p,8,f)):(0,i.createCommentVNode)("",!0),g])]),(0,i.createElementVNode)("div",h,[(0,i.createElementVNode)("iframe",{class:"w-100",id:"previewFrame",style:{height:"80vh"},src:e.modalSrc,allowfullscreen:""},null,8,v)])])])])}}})},46919:(e,t,n)=>{"use strict";n.d(t,{Z:()=>Ie});var o=n(70821),i=function(e){return(0,o.pushScopeId)("data-v-42f43f73"),e=e(),(0,o.popScopeId)(),e},r={class:"modal fade",id:"kt_modal_share_badge",tabindex:"-1",style:{display:"none"},"aria-hidden":"true"},a={class:"modal-dialog modal-dialog-centered mw-800px"},l={class:"modal-content rounded-0"},c=i((function(){return(0,o.createElementVNode)("div",{class:"modal-header text-white"},[(0,o.createElementVNode)("h5",{class:"modal-title"},"Share Badge"),(0,o.createElementVNode)("button",{type:"button",class:"btn-close","data-bs-dismiss":"modal","aria-label":"Close"})],-1)})),s={class:"modal-body text-start p-6"},d=(0,o.createStaticVNode)('<div class="d-flex align-items-center justify-content-around p-1" data-v-42f43f73><div class="shadow-md mx-auto fs-5" data-v-42f43f73><h2 class="text-xl font-bold fs-3" data-v-42f43f73> Publish your achievements for your network to see. </h2><h6 class="text-black fw-bold mt-5 mb-5" data-v-42f43f73> Add to your LinkedIn Profile </h6><p data-v-42f43f73> Here’s a step-by-step guide to adding badges or certificates to the ‘Licenses &amp; Certifications’ section of your LinkedIn Profile: </p><p data-v-42f43f73> 1. Go to your LinkedIn profile and scroll to your ‘Licenses &amp; certifications’ section. </p><p data-v-42f43f73>2. Click + icon.</p><p data-v-42f43f73> 3. Provide all the relevant information about the badge. You can find this below. </p><p data-v-42f43f73> 4. Don&#39;t forget to also mention the skills you gained from earning the badge. This will give your profile an extra boost and help potential employers understand your expertise. </p></div></div><hr class="mx-auto border-dark opacity-10" data-v-42f43f73>',2),u={class:"container px-1"},m={class:"row mt-5"},f={class:"col-12 col-md-6 fs-5"},p=i((function(){return(0,o.createElementVNode)("h4",{class:"text-start mt-3 mb-6"}," Copy the below fields to your profile ",-1)})),g={class:"p-2 mt-2"},h=i((function(){return(0,o.createElementVNode)("div",null,"Name",-1)})),v={class:"border d-flex justify-content-between p-2 rounded align-items-center"},y={class:"p-2 fw-bold"},b=[i((function(){return(0,o.createElementVNode)("i",{class:"fa-regular fa-copy"},null,-1)}))],w={key:0,class:"text-primary mt-1 fw-semibold"},k={class:"p-2 mt-2"},x=i((function(){return(0,o.createElementVNode)("div",null,"Issuing Organisation",-1)})),E={class:"border d-flex justify-content-between p-2 rounded align-items-center"},N={class:"p-2 fw-bold"},V={key:0},B={key:0},_={key:1},C=[i((function(){return(0,o.createElementVNode)("i",{class:"fa-regular fa-copy"},null,-1)}))],L={key:0,class:"text-primary mt-1 fw-semibold"},S={class:"p-2 mt-2"},T=i((function(){return(0,o.createElementVNode)("div",null,"Issue Date",-1)})),F={class:"border d-flex justify-content-between p-2 rounded align-items-center"},M={class:"p-2 fw-bold"},I=[i((function(){return(0,o.createElementVNode)("i",{class:"fa-regular fa-copy"},null,-1)}))],O={key:0,class:"text-primary mt-1 fw-semibold"},j={key:0,class:"p-2 mt-2"},z=i((function(){return(0,o.createElementVNode)("div",null,"Expiry Date",-1)})),R={class:"border d-flex justify-content-between p-2 rounded align-items-center"},D={class:"p-2 fw-bold"},H=[i((function(){return(0,o.createElementVNode)("i",{class:"fa-regular fa-copy"},null,-1)}))],P={key:0,class:"text-primary mt-1 fw-semibold"},A={class:"p-2 mt-2"},W=i((function(){return(0,o.createElementVNode)("div",null,"Credential ID",-1)})),G={class:"border d-flex justify-content-between p-2 rounded align-items-center"},Z={class:"p-2 fw-bold"},U=[i((function(){return(0,o.createElementVNode)("i",{class:"fa-regular fa-copy"},null,-1)}))],q={key:0,class:"text-primary mt-1 fw-semibold"},Y={class:"col-12 col-md-6 text-center mt-4 mt-md-0"},J=["src"],X=["href"],Q=i((function(){return(0,o.createElementVNode)("i",{class:"fa-solid fa-download"},null,-1)})),K=i((function(){return(0,o.createElementVNode)("div",{class:"modal-footer"},[(0,o.createElementVNode)("button",{type:"button",class:"btn btn-secondary","data-bs-toggle":"modal","data-bs-target":"#kt_modal_badge"}," View Badge ")],-1)}));var $={class:"modal fade",id:"kt_modal_badge",tabindex:"-1",style:{display:"none"},"aria-hidden":"true"},ee={class:"modal-dialog modal-dialog-centered modal-xl"},te={class:"modal-content rounded-0"},ne=(0,o.createElementVNode)("div",{class:"modal-header text-white"},[(0,o.createElementVNode)("h5",{class:"modal-title"},"View Badge"),(0,o.createElementVNode)("button",{type:"button",class:"btn-close","data-bs-dismiss":"modal","aria-label":"Close"})],-1),oe={class:"modal-body text-center px-10"},ie={class:"row gap-4 fs-5"},re={class:"col-7 px-7 py-9 text-start border border-solid rounded"},ae={class:"fw-bold mb-5 mt-5"},le={key:0},ce={class:"mt-7 lh-lg"},se={class:"mb-1"},de=(0,o.createElementVNode)("span",{class:"text-gray-700"},"Module Name: ",-1),ue={class:"mb-1"},me=(0,o.createElementVNode)("span",{class:"text-gray-700"},"Credential ID: ",-1),fe={class:"mb-1"},pe=(0,o.createElementVNode)("span",{class:"text-gray-700"},"Issue Date: ",-1),ge={key:0,class:"mb-1"},he=(0,o.createElementVNode)("span",{class:"text-gray-700"},"Expiry Date: ",-1),ve={class:"mb-1"},ye=(0,o.createElementVNode)("span",{class:"text-gray-700"},"Module Type: ",-1),be={class:"col my-auto"},we={key:0},ke=["innerHTML"],xe=["src"],Ee=(0,o.createElementVNode)("div",{class:"modal-footer border-0"},[(0,o.createElementVNode)("button",{type:"button",class:"btn btn-secondary","data-bs-toggle":"modal","data-bs-target":"#kt_modal_share_badge"}," Share Badge ")],-1);const Ne=(0,o.defineComponent)({props:{selectedBadge:Object},methods:{isVideo:function(e){return e&&e.endsWith(".mp4")}}});var Ve=n(93379),Be=n.n(Ve),_e=n(3368),Ce={insert:"head",singleton:!1};Be()(_e.Z,Ce);_e.Z.locals;var Le=n(83744);const Se=(0,Le.Z)(Ne,[["render",function(e,t,n,i,r,a){var l,c,s,d,u,m,f,p,g,h,v,y,b,w,k,x,E;return(0,o.openBlock)(),(0,o.createElementBlock)("div",$,[(0,o.createElementVNode)("div",ee,[(0,o.createElementVNode)("div",te,[ne,(0,o.createElementVNode)("div",oe,[(0,o.createElementVNode)("div",ie,[(0,o.createElementVNode)("div",re,[(0,o.createElementVNode)("div",null,[(0,o.createElementVNode)("h1",null,(0,o.toDisplayString)(null===(c=null===(l=e.selectedBadge)||void 0===l?void 0:l.badge)||void 0===c?void 0:c.name),1),(0,o.createElementVNode)("p",ae,[(0,o.createTextVNode)(" Verified by "),((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(null===(d=null===(s=e.selectedBadge)||void 0===s?void 0:s.badge)||void 0===d?void 0:d.companies,(function(t,n){var i,r;return(0,o.openBlock)(),(0,o.createElementBlock)("span",{key:t.id},[(0,o.createElementVNode)("u",null,(0,o.toDisplayString)(t.name),1),n!==(null===(r=null===(i=e.selectedBadge)||void 0===i?void 0:i.badge)||void 0===r?void 0:r.companies.length)-1?((0,o.openBlock)(),(0,o.createElementBlock)("span",le," + ")):(0,o.createCommentVNode)("",!0)])})),128))])]),(0,o.createElementVNode)("div",ce,[(0,o.createElementVNode)("p",se,[de,(0,o.createTextVNode)((0,o.toDisplayString)(null===(u=e.selectedBadge)||void 0===u?void 0:u.module_name),1)]),(0,o.createElementVNode)("p",ue,[me,(0,o.createTextVNode)(" "+(0,o.toDisplayString)((null===(m=e.selectedBadge)||void 0===m?void 0:m.credential_id)||"N/A"),1)]),(0,o.createElementVNode)("p",fe,[pe,(0,o.createTextVNode)(" "+(0,o.toDisplayString)(null===(f=e.selectedBadge)||void 0===f?void 0:f.issue_date),1)]),(null===(p=e.selectedBadge)||void 0===p?void 0:p.expiration_date)?((0,o.openBlock)(),(0,o.createElementBlock)("p",ge,[he,(0,o.createTextVNode)(" "+(0,o.toDisplayString)(e.selectedBadge.expiration_date),1)])):(0,o.createCommentVNode)("",!0),(0,o.createElementVNode)("p",ve,[ye,(0,o.createTextVNode)((0,o.toDisplayString)(null===(g=e.selectedBadge)||void 0===g?void 0:g.module_type),1)])])]),(0,o.createElementVNode)("div",be,[e.selectedBadge?((0,o.openBlock)(),(0,o.createElementBlock)("div",we,[(null===(v=null===(h=e.selectedBadge)||void 0===h?void 0:h.badge)||void 0===v?void 0:v.video)?((0,o.openBlock)(),(0,o.createElementBlock)("div",{key:0,class:"animated-video",innerHTML:null===(b=null===(y=e.selectedBadge)||void 0===y?void 0:y.badge)||void 0===b?void 0:b.video},null,8,ke)):((0,o.openBlock)(),(0,o.createElementBlock)("img",{key:1,src:(null===(k=null===(w=e.selectedBadge)||void 0===w?void 0:w.badge)||void 0===k?void 0:k.animated_image_fullpath)||(null===(E=null===(x=e.selectedBadge)||void 0===x?void 0:x.badge)||void 0===E?void 0:E.image_fullpath),alt:"Animated Badge",class:"w-100"},null,8,xe))])):(0,o.createCommentVNode)("",!0)])])]),Ee])])])}]]),Te=(0,o.defineComponent)({components:{ViewBadgeModal:Se},props:{selectedBadge:Object,moduleData:Object,moduleType:String},emits:["shareBadge"],setup:function(e,t){var n=t.emit,i=(0,o.ref)("");return{emitShare:function(){n("shareBadge",e.selectedBadge)},copiedField:i,copyToClipboard:function(e,t){e&&navigator.clipboard.writeText(e).then((function(){i.value=t,setTimeout((function(){i.value=""}),3e3)})).catch((function(e){console.error("Copy failed:",e)}))}}}});var Fe=n(6857),Me={insert:"head",singleton:!1};Be()(Fe.Z,Me);Fe.Z.locals;const Ie=(0,Le.Z)(Te,[["render",function(e,t,n,i,$,ee){var te,ne,oe,ie,re,ae,le,ce,se,de,ue,me,fe,pe,ge,he=(0,o.resolveComponent)("ViewBadgeModal");return(0,o.openBlock)(),(0,o.createElementBlock)(o.Fragment,null,[(0,o.createVNode)(he,{selectedBadge:e.selectedBadge},null,8,["selectedBadge"]),(0,o.createElementVNode)("div",r,[(0,o.createElementVNode)("div",a,[(0,o.createElementVNode)("div",l,[c,(0,o.createElementVNode)("div",s,[d,(0,o.createElementVNode)("div",u,[(0,o.createElementVNode)("div",m,[(0,o.createElementVNode)("div",f,[p,(0,o.createElementVNode)("div",g,[h,(0,o.createElementVNode)("div",v,[(0,o.createElementVNode)("div",y,(0,o.toDisplayString)(null===(ne=null===(te=e.selectedBadge)||void 0===te?void 0:te.badge)||void 0===ne?void 0:ne.name),1),(0,o.createElementVNode)("button",{class:"btn btn-sm btn-outline-primary",onClick:t[0]||(t[0]=function(t){var n;return e.copyToClipboard(null===(n=e.selectedBadge)||void 0===n?void 0:n.name,"name")})},b)]),"name"===e.copiedField?((0,o.openBlock)(),(0,o.createElementBlock)("p",w,"Copied to clipboard!")):(0,o.createCommentVNode)("",!0)]),(0,o.createElementVNode)("div",k,[x,(0,o.createElementVNode)("div",E,[(0,o.createElementVNode)("div",N,[(null===(ie=null===(oe=e.selectedBadge)||void 0===oe?void 0:oe.badge)||void 0===ie?void 0:ie.companies.length)>0?((0,o.openBlock)(),(0,o.createElementBlock)("div",V,[((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(null===(ae=null===(re=e.selectedBadge)||void 0===re?void 0:re.badge)||void 0===ae?void 0:ae.companies,(function(t,n){var i,r;return(0,o.openBlock)(),(0,o.createElementBlock)("span",{key:t.id},[(0,o.createTextVNode)((0,o.toDisplayString)(t.name)+" ",1),n!==(null===(r=null===(i=e.selectedBadge)||void 0===i?void 0:i.badge)||void 0===r?void 0:r.companies.length)-1?((0,o.openBlock)(),(0,o.createElementBlock)("span",B," + ")):(0,o.createCommentVNode)("",!0)])})),128))])):((0,o.openBlock)(),(0,o.createElementBlock)("div",_," N/A "))]),(0,o.createElementVNode)("button",{class:"btn btn-sm btn-outline-primary",onClick:t[1]||(t[1]=function(t){var n;return e.copyToClipboard(null===(n=e.selectedBadge)||void 0===n?void 0:n.name,"name")})},C)]),"name"===e.copiedField?((0,o.openBlock)(),(0,o.createElementBlock)("p",L,"Copied to clipboard!")):(0,o.createCommentVNode)("",!0)]),(0,o.createElementVNode)("div",S,[T,(0,o.createElementVNode)("div",F,[(0,o.createElementVNode)("div",M,(0,o.toDisplayString)(null===(le=e.selectedBadge)||void 0===le?void 0:le.issue_date),1),(0,o.createElementVNode)("button",{class:"btn btn-sm btn-outline-primary",onClick:t[2]||(t[2]=function(t){var n;return e.copyToClipboard(null===(n=e.selectedBadge)||void 0===n?void 0:n.issue_date,"issue_date")})},I)]),"issue_date"===e.copiedField?((0,o.openBlock)(),(0,o.createElementBlock)("p",O,"Copied to clipboard!")):(0,o.createCommentVNode)("",!0)]),(null===(ce=e.selectedBadge)||void 0===ce?void 0:ce.expiration_date)?((0,o.openBlock)(),(0,o.createElementBlock)("div",j,[z,(0,o.createElementVNode)("div",R,[(0,o.createElementVNode)("div",D,(0,o.toDisplayString)(null===(se=e.selectedBadge)||void 0===se?void 0:se.expiration_date),1),(0,o.createElementVNode)("button",{class:"btn btn-sm btn-outline-primary",onClick:t[3]||(t[3]=function(t){var n;return e.copyToClipboard(null===(n=e.selectedBadge)||void 0===n?void 0:n.expiration_date,"expiry_date")})},H)]),"expiry_date"===e.copiedField?((0,o.openBlock)(),(0,o.createElementBlock)("p",P,"Copied to clipboard!")):(0,o.createCommentVNode)("",!0)])):(0,o.createCommentVNode)("",!0),(0,o.createElementVNode)("div",A,[W,(0,o.createElementVNode)("div",G,[(0,o.createElementVNode)("div",Z,(0,o.toDisplayString)((null===(de=e.selectedBadge)||void 0===de?void 0:de.credential_id)||"N/A"),1),(0,o.createElementVNode)("button",{class:"btn btn-sm btn-outline-primary",onClick:t[4]||(t[4]=function(t){var n;return e.copyToClipboard((null===(n=e.selectedBadge)||void 0===n?void 0:n.credential_id)||"N/A","credential_id")})},U)]),"credential_id"===e.copiedField?((0,o.openBlock)(),(0,o.createElementBlock)("p",q,"Copied to clipboard!")):(0,o.createCommentVNode)("",!0)])]),(0,o.createElementVNode)("div",Y,[(0,o.createElementVNode)("div",null,[(0,o.createElementVNode)("img",{src:null===(me=null===(ue=e.selectedBadge)||void 0===ue?void 0:ue.badge)||void 0===me?void 0:me.image_fullpath,class:"img-fluid rounded",style:{"max-width":"100%",height:"auto"}},null,8,J)]),(null===(pe=null===(fe=e.selectedBadge)||void 0===fe?void 0:fe.badge)||void 0===pe?void 0:pe.id)?((0,o.openBlock)(),(0,o.createElementBlock)("a",{key:0,href:"/badges/".concat(null===(ge=e.selectedBadge.badge)||void 0===ge?void 0:ge.id,"/download"),class:"btn btn-sm btn-outline-primary mt-3",download:""},[Q,(0,o.createTextVNode)(" Download Image ")],8,X)):(0,o.createCommentVNode)("",!0)])])])]),K])])])],64)}],["__scopeId","data-v-42f43f73"]])},72504:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>Vt});var o=n(70821),i=["innerHTML"],r=(0,o.createElementVNode)("div",{style:{position:"absolute",width:"100%",height:"100%",opacity:".3",background:"#000"}},null,-1),a={class:"banner_detail_box w-450px"},l={key:0,class:"mt-4 mb-4"},c={class:"row g-3"},s={class:"col-6"},d={class:"d-flex align-items-center mb-10"},u=["src","alt"],m={class:"mb-1 fw-normal text-dark fs-4 text-light"},f=(0,o.createElementVNode)("h1",{class:"fw-normal text-light"},"Skills Training",-1),p=["innerHTML"],g={class:"row text-light align-items-center"},h={key:0,class:"col-md-4 col-lg-3"},v=(0,o.createElementVNode)("i",{class:"fa-regular fa-clock text-white me-2"},null,-1),y=["textContent"],b=["textContent"],w={key:1,class:"col-md-4 col-lg-3"},k=(0,o.createElementVNode)("i",{class:"fa fa-chart-simple text-white me-2"},null,-1),x=["textContent"],E={class:"col-md-5 col-lg-5 mt-lg-0 mt-md-3"},N={key:0,class:"fs-6 text-light px-5 py-2 rounded-pill w-100",style:{"background-color":"#0062ff"}},V={key:1,class:"fs-6 text-dark px-5 py-2 rounded-pill",style:{"background-color":"#e9ff1f"}},B={class:"row mt-5"},_=(0,o.createElementVNode)("i",{class:"fa fa-check text-white"},null,-1),C={key:1,class:"row mt-5"},L=[(0,o.createElementVNode)("div",{class:"col-8 col-sm-6 col-md-12"},[(0,o.createElementVNode)("button",{type:"button",class:"btn btn-black-custom btn-lg rounded-0 w-100 p-md-5","data-bs-toggle":"modal","data-bs-target":"#kt_modal_trailer"}," Watch Trailer ")],-1)],S={key:2,class:"row mt-5"},T={key:0,class:"col-8 col-sm-6 col-md-12"},F={key:1,class:"col-8 col-sm-6 col-md-10"},M=[(0,o.createElementVNode)("button",{class:"btn btn-white-custom btn-lg border-1 rounded-0 w-100 p-md-5",style:{"font-size":"14px !important"},"data-bs-toggle":"modal","data-bs-target":"#kt_modal_viewResponse"}," View Response ",-1)],I={class:"row row-cols-3 mt-5"},O={key:0,class:"col my-auto"},j={class:"row g-3 mt-2"},z={class:"col-12"},R=["src","alt"],D=(0,o.createElementVNode)("div",{class:"overflow-hidden"},[(0,o.createElementVNode)("p",{class:"fw-bold text-light my-auto"}," View Badge ")],-1),H={key:1,class:"col my-auto"},P={class:"row g-3 mt-2"},A={class:"col-12"},W=[(0,o.createElementVNode)("i",{class:"fa-solid fa-file text-light me-2",width:"25"},null,-1),(0,o.createElementVNode)("div",null,[(0,o.createElementVNode)("p",{class:"fw-bold text-light my-auto"}," View Certificate ")],-1)],G={key:2,class:"col my-auto"},Z=[(0,o.createStaticVNode)('<div class="row g-3 mt-2"><div class="col-12"><div class="d-flex align-items-center cursor-pointer w-fit-content" data-bs-toggle="modal" data-bs-target="#kt_modal_feedback"><i class="fa-solid fa-comments text-light me-2" width="25"></i><div><p class="fw-bold text-light my-auto"> View Feedback </p></div></div></div></div>',1)],U={class:"module-section d-flex flex-column justify-content-center align-items-center py-5"},q={class:"svg-icon svg-icon-primary svg-icon-2x"},Y={key:0,xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",width:"24px",height:"24px",viewBox:"0 0 24 24",version:"1.1"},J=[(0,o.createElementVNode)("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},[(0,o.createElementVNode)("mask",{fill:"white"},[(0,o.createElementVNode)("use",{"xlink:href":"#path-1"})]),(0,o.createTextVNode)(),(0,o.createElementVNode)("g"),(0,o.createTextVNode)(),(0,o.createElementVNode)("path",{d:"M15.6274517,4.55882251 L14.4693753,6.2959371 C13.9280401,5.51296885 13.0239252,5 12,5 C10.3431458,5 9,6.34314575 9,8 L9,10 L14,10 L17,10 L18,10 C19.1045695,10 20,10.8954305 20,12 L20,18 C20,19.1045695 19.1045695,20 18,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,12 C4,10.8954305 4.8954305,10 6,10 L7,10 L7,8 C7,5.23857625 9.23857625,3 12,3 C13.4280904,3 14.7163444,3.59871093 15.6274517,4.55882251 Z",fill:"#ffffff"})],-1)],X={key:1,xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",width:"24px",height:"24px",viewBox:"0 0 24 24",version:"1.1"},Q=[(0,o.createElementVNode)("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},[(0,o.createElementVNode)("mask",{fill:"white"},[(0,o.createElementVNode)("use",{"xlink:href":"#path-1"})]),(0,o.createTextVNode)(),(0,o.createElementVNode)("g"),(0,o.createTextVNode)(),(0,o.createElementVNode)("path",{d:"M7,10 L7,8 C7,5.23857625 9.23857625,3 12,3 C14.7614237,3 17,5.23857625 17,8 L17,10 L18,10 C19.1045695,10 20,10.8954305 20,12 L20,18 C20,19.1045695 19.1045695,20 18,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,12 C4,10.8954305 4.8954305,10 6,10 L7,10 Z M12,5 C10.3431458,5 9,6.34314575 9,8 L9,10 L15,10 L15,8 C15,6.34314575 13.6568542,5 12,5 Z",fill:"#000000"})],-1)],K=["innerHTML"],$={class:"m-0 text-white"},ee=["textContent"],te=["textContent"],ne={class:"module-section d-flex flex-column justify-content-center align-items-center py-5"},oe={class:"svg-icon svg-icon-primary svg-icon-2x"},ie={key:0,xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",width:"24px",height:"24px",viewBox:"0 0 24 24",version:"1.1"},re=[(0,o.createElementVNode)("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},[(0,o.createElementVNode)("mask",{fill:"white"},[(0,o.createElementVNode)("use",{"xlink:href":"#path-1"})]),(0,o.createTextVNode)(),(0,o.createElementVNode)("g"),(0,o.createTextVNode)(),(0,o.createElementVNode)("path",{d:"M15.6274517,4.55882251 L14.4693753,6.2959371 C13.9280401,5.51296885 13.0239252,5 12,5 C10.3431458,5 9,6.34314575 9,8 L9,10 L14,10 L17,10 L18,10 C19.1045695,10 20,10.8954305 20,12 L20,18 C20,19.1045695 19.1045695,20 18,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,12 C4,10.8954305 4.8954305,10 6,10 L7,10 L7,8 C7,5.23857625 9.23857625,3 12,3 C13.4280904,3 14.7163444,3.59871093 15.6274517,4.55882251 Z",fill:"#ffffff"})],-1)],ae={key:1,xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",width:"24px",height:"24px",viewBox:"0 0 24 24",version:"1.1"},le=[(0,o.createElementVNode)("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},[(0,o.createElementVNode)("mask",{fill:"white"},[(0,o.createElementVNode)("use",{"xlink:href":"#path-1"})]),(0,o.createTextVNode)(),(0,o.createElementVNode)("g"),(0,o.createTextVNode)(),(0,o.createElementVNode)("path",{d:"M7,10 L7,8 C7,5.23857625 9.23857625,3 12,3 C14.7614237,3 17,5.23857625 17,8 L17,10 L18,10 C19.1045695,10 20,10.8954305 20,12 L20,18 C20,19.1045695 19.1045695,20 18,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,12 C4,10.8954305 4.8954305,10 6,10 L7,10 Z M12,5 C10.3431458,5 9,6.34314575 9,8 L9,10 L15,10 L15,8 C15,6.34314575 13.6568542,5 12,5 Z",fill:"#000000"})],-1)],ce={id:"skillSections",class:"section-content"},se={class:"d-flex justify-content-center"},de={class:"container"},ue={class:"row border rounded p-10 d-flex",style:{"min-height":"70vh"}},me={class:"col-lg-3 col-md-8 overflow-auto",style:{"max-height":"70vh"}},fe={class:"nav nav-tabs nav-pills flex-row border-0 flex-md-column me-5 mb-3 mb-md-0 fs-6"},pe=["onClick"],ge={class:"d-flex flex-column align-items-start"},he={class:"fs-4 fw-bold"},ve={class:"fs-7 text-left text-capitalize"},ye={class:"nav-item w-100 me-0 mb-md-2"},be=[(0,o.createElementVNode)("span",{class:"d-flex flex-column align-items-start"},[(0,o.createElementVNode)("span",{class:"fs-4 fw-bold"},"Final Step"),(0,o.createElementVNode)("span",{class:"fs-7"},"Submission")],-1)],we={class:"col overflow-auto",style:{"max-height":"70vh"}},ke={key:0},xe=["href"],Ee=(0,o.createElementVNode)("i",{class:"fa fa-eye my-auto"},null,-1),Ne={key:0},Ve=(0,o.createElementVNode)("i",{class:"fa-regular fa-clock text-dark me-2"},null,-1),Be=["textContent"],_e=["textContent"],Ce=["innerHTML"],Le=["innerHTML"],Se={key:1},Te=[(0,o.createElementVNode)("span",{class:"text-dark"},[(0,o.createElementVNode)("i",{class:"fa-regular fa-circle-xmark text-dark"}),(0,o.createElementVNode)("span",{class:""}," No was answer required on this section. ")],-1)],Fe={key:0,class:"text-center mt-5"},Me=(0,o.createElementVNode)("h4",null,"Students were asked to upload a document on their final step.",-1),Ie={class:"d-flex justify-content-center gap-10 pt-10"},Oe={key:0},je=(0,o.createElementVNode)("i",{class:"fa fa-eye"},null,-1),ze=["href"],Re=(0,o.createElementVNode)("i",{class:"fa fa-download"},null,-1),De=["href"],He=(0,o.createElementVNode)("i",{class:"fa fa-download"},null,-1),Pe={class:"modal fade",id:"kt_modal_trailer",tabindex:"-1",style:{display:"none"},"aria-hidden":"true"},Ae={class:"modal-dialog modal-dialog-centered mw-900px"},We={class:"modal-content rounded-0"},Ge=["innerHTML"],Ze={class:"modal fade",id:"kt_modal_viewFile",tabindex:"-1","aria-hidden":"true"},Ue={class:"modal-content rounded-0 mt-5"},qe={class:"modal-header py-3"},Ye=(0,o.createElementVNode)("h5",{class:"modal-title"},"Certificate Preview",-1),Je={key:0,class:"fa-solid fa-compress text-black"},Xe={key:1,class:"fa-solid fa-expand text-black"},Qe=["href"],Ke=[(0,o.createElementVNode)("i",{class:"fa-solid fa-download text-black"},null,-1)],$e=(0,o.createElementVNode)("button",{type:"button",class:"btn-close","data-bs-dismiss":"modal"},null,-1),et={class:"modal-body bg-black p-1 text-white text-center"},tt=["src"],nt={key:1},ot={class:"modal fade",id:"kt_modal_feedback",tabindex:"-1",style:{display:"none"},"aria-hidden":"true"},it={class:"modal-dialog modal-dialog-centered mw-600px"},rt={class:"modal-content rounded-0",style:{height:"80vh"}},at=(0,o.createElementVNode)("div",{class:"modal-header text-white"},[(0,o.createElementVNode)("h5",{class:"modal-title"},"Feedback"),(0,o.createElementVNode)("button",{type:"button",class:"btn-close","data-bs-dismiss":"modal","aria-label":"Close"})],-1),lt={class:"modal-body p-4 bg-gray-50 text-left"},ct={class:"p-4 bg-white",style:{height:"90%"}},st=["innerHTML"];var dt=n(70655),ut=n(72961),mt=n(80894),ft=n(22201),pt=n(46702),gt=n.n(pt),ht=n(46919),vt=n(96268);function yt(e){return yt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},yt(e)}function bt(){bt=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,o=Object.defineProperty||function(e,t,n){e[t]=n.value},i="function"==typeof Symbol?Symbol:{},r=i.iterator||"@@iterator",a=i.asyncIterator||"@@asyncIterator",l=i.toStringTag||"@@toStringTag";function c(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,n){return e[t]=n}}function s(e,t,n,i){var r=t&&t.prototype instanceof m?t:m,a=Object.create(r.prototype),l=new V(i||[]);return o(a,"_invoke",{value:k(e,n,l)}),a}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=s;var u={};function m(){}function f(){}function p(){}var g={};c(g,r,(function(){return this}));var h=Object.getPrototypeOf,v=h&&h(h(B([])));v&&v!==t&&n.call(v,r)&&(g=v);var y=p.prototype=m.prototype=Object.create(g);function b(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function w(e,t){function i(o,r,a,l){var c=d(e[o],e,r);if("throw"!==c.type){var s=c.arg,u=s.value;return u&&"object"==yt(u)&&n.call(u,"__await")?t.resolve(u.__await).then((function(e){i("next",e,a,l)}),(function(e){i("throw",e,a,l)})):t.resolve(u).then((function(e){s.value=e,a(s)}),(function(e){return i("throw",e,a,l)}))}l(c.arg)}var r;o(this,"_invoke",{value:function(e,n){function o(){return new t((function(t,o){i(e,n,t,o)}))}return r=r?r.then(o,o):o()}})}function k(e,t,n){var o="suspendedStart";return function(i,r){if("executing"===o)throw new Error("Generator is already running");if("completed"===o){if("throw"===i)throw r;return _()}for(n.method=i,n.arg=r;;){var a=n.delegate;if(a){var l=x(a,n);if(l){if(l===u)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===o)throw o="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o="executing";var c=d(e,t,n);if("normal"===c.type){if(o=n.done?"completed":"suspendedYield",c.arg===u)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o="completed",n.method="throw",n.arg=c.arg)}}}function x(e,t){var n=t.method,o=e.iterator[n];if(void 0===o)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,x(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),u;var i=d(o,e.iterator,t.arg);if("throw"===i.type)return t.method="throw",t.arg=i.arg,t.delegate=null,u;var r=i.arg;return r?r.done?(t[e.resultName]=r.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,u):r:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,u)}function E(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function N(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function V(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(E,this),this.reset(!0)}function B(e){if(e){var t=e[r];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function t(){for(;++o<e.length;)if(n.call(e,o))return t.value=e[o],t.done=!1,t;return t.value=void 0,t.done=!0,t};return i.next=i}}return{next:_}}function _(){return{value:void 0,done:!0}}return f.prototype=p,o(y,"constructor",{value:p,configurable:!0}),o(p,"constructor",{value:f,configurable:!0}),f.displayName=c(p,l,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===f||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,p):(e.__proto__=p,c(e,l,"GeneratorFunction")),e.prototype=Object.create(y),e},e.awrap=function(e){return{__await:e}},b(w.prototype),c(w.prototype,a,(function(){return this})),e.AsyncIterator=w,e.async=function(t,n,o,i,r){void 0===r&&(r=Promise);var a=new w(s(t,n,o,i),r);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},b(y),c(y,l,"Generator"),c(y,r,(function(){return this})),c(y,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var o in t)n.push(o);return n.reverse(),function e(){for(;n.length;){var o=n.pop();if(o in t)return e.value=o,e.done=!1,e}return e.done=!0,e}},e.values=B,V.prototype={constructor:V,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(N),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function o(n,o){return a.type="throw",a.arg=e,t.next=n,o&&(t.method="next",t.arg=void 0),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var r=this.tryEntries[i],a=r.completion;if("root"===r.tryLoc)return o("end");if(r.tryLoc<=this.prev){var l=n.call(r,"catchLoc"),c=n.call(r,"finallyLoc");if(l&&c){if(this.prev<r.catchLoc)return o(r.catchLoc,!0);if(this.prev<r.finallyLoc)return o(r.finallyLoc)}else if(l){if(this.prev<r.catchLoc)return o(r.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<r.finallyLoc)return o(r.finallyLoc)}}}},abrupt:function(e,t){for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var r=i;break}}r&&("break"===e||"continue"===e)&&r.tryLoc<=t&&t<=r.finallyLoc&&(r=null);var a=r?r.completion:{};return a.type=e,a.arg=t,r?(this.method="next",this.next=r.finallyLoc,u):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),u},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),N(n),u}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var i=o.arg;N(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:B(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),u}},e}const wt=(0,o.defineComponent)({name:"skillstraining-detail",components:{BadgeModal:ht.Z,ResponseModal:vt.Z},setup:function(){var e=this,t=(0,mt.oR)(),n=(0,ft.yj)(),i=(0,o.ref)(""),r=(0,o.ref)(""),a=(0,o.ref)(""),l=(0,o.ref)({}),c=(0,o.ref)(null),s=(0,o.ref)(""),d=(0,o.ref)(!1),u=t.getters.currentUser;(0,o.onMounted)((function(){return(0,dt.mG)(e,void 0,void 0,bt().mark((function e(){return bt().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,v();case 2:gt()({heightCalculationMethod:"bodyScroll"},".section-content iframe");case 3:case"end":return e.stop()}}),e)})))})),(0,o.onMounted)((function(){var e=document.getElementById("kt_modal_viewResponse");e?e.addEventListener("show.bs.modal",(function(e){e.relatedTarget&&(i.value=m.value.student_response.view_response_file_path,console.log("checkmodalSrc_value",i.value),r.value=m.value.student_response.id,a.value="skillstraining/responses/".concat(r.value,"/download"))})):console.warn("Modal element not found: #kt_modal_viewResponse")}));var m=(0,o.ref)(),f=(0,o.ref)(),p=n.params.student,g=((0,o.ref)(),(0,o.ref)(!1)),h=0;m.value={id:1,background_imagepath:null,background_video:null,student_response:{activity_responses:{}}},f.value=n.params.id;var v=function(){return(0,dt.mG)(e,void 0,void 0,bt().mark((function e(){var t,n,o,i;return bt().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,ut.Z.get("api/skillstraining/"+f.value+"/"+p);case 3:n=e.sent,o=n.data,m.value=o,m.value.user_response.activity_responses.sort((function(e,t){return e.activity.number-t.activity.number})),i=document.getElementById("banner"),h=i.scrollHeight+120,""===(null===(t=o.student_response)||void 0===t?void 0:t.response_path)&&y(),e.next=15;break;case 12:e.prev=12,e.t0=e.catch(0),console.log(e.t0);case 15:case 16:case"end":return e.stop()}}),e,null,[[0,12]])})))},y=function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10,n=document.getElementById("skillSections");n?(n.scrollIntoView({behavior:"smooth",block:"start"}),console.log("Scrolled to #skillSections")):t>0?setTimeout((function(){return e(t-1)}),300):console.log("#skillSections not found after retries")};return{currentUser:u,skillstraining:m,currentSkillstraining:f,config:{key:"hWA2C-7I2A4C3D5D2D2G3wxeklqcwvffrrhxhoqxpkC7bmnxE2F2G2D1B10B2B3E6F1F2==",height:300,attribution:!1,toolbarButtons:[""],events:{initialized:function(){console.log("initialized")}}},scrolled:g,handleScroll:function(){if((window.innerWidth||document.documentElement.clientWidth||document.body.clientWidth)>991){var e=document.getElementById("kt_app_toolbar");window.scrollY>h?(g.value=!0,e.style.display="none"):(g.value=!1,e.style.display="flex")}},viewResponse:function(){var e=document.querySelector(".banner");window.scrollBy({top:e.scrollHeight,left:0,behavior:"smooth"})},openBadgeModal:function(e){l.value=e},openShareBadgeModal:function(e){l.value=e},selectedBadge:l,isFullscreen:d,loadCertificate:function(){return(0,dt.mG)(e,void 0,void 0,bt().mark((function e(){var t;return bt().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(m.value&&m.value.student_response){e.next=3;break}return console.error("skillstraining.student_response is missing!"),e.abrupt("return");case 3:t=m.value.student_response.id,s.value="/certificate-download-skills/".concat(t,"?preview=true");case 5:case"end":return e.stop()}}),e)})))},toggleFullscreen:function(){d.value=!d.value},certificateUrl:s,selectedActivityId:c,waitForSectionAndScroll:y,downloadUrl:a,modalSrc:i}},props:["id"],created:function(){window.addEventListener("scroll",this.handleScroll)},destroyed:function(){window.removeEventListener("scroll",this.handleScroll)}});var kt=n(93379),xt=n.n(kt),Et=n(44044),Nt={insert:"head",singleton:!1};xt()(Et.Z,Nt);Et.Z.locals;const Vt=(0,n(83744).Z)(wt,[["render",function(e,t,n,dt,ut,mt){var ft,pt,gt=(0,o.resolveComponent)("router-link"),ht=(0,o.resolveComponent)("BadgeModal"),vt=(0,o.resolveComponent)("ResponseModal");return(0,o.openBlock)(),(0,o.createElementBlock)(o.Fragment,null,[(0,o.createElementVNode)("div",{id:"banner",class:"full-view-banner banner",style:(0,o.normalizeStyle)({backgroundImage:"url("+e.skillstraining.background_imagepath+")"})},[e.skillstraining.background_videoid?((0,o.openBlock)(),(0,o.createElementBlock)("div",{key:0,class:"banner-video",innerHTML:e.skillstraining.background_videoid},null,8,i)):(0,o.createCommentVNode)("",!0),r,(0,o.createElementVNode)("div",a,[e.skillstraining.badge&&100!==e.skillstraining.student_completed_percentage?((0,o.openBlock)(),(0,o.createElementBlock)("div",l,[(0,o.createElementVNode)("div",c,[(0,o.createElementVNode)("div",s,[(0,o.createElementVNode)("div",d,[(0,o.createElementVNode)("img",{src:e.skillstraining.badge.image_fullpath,alt:e.skillstraining.badge.name,class:"me-3",width:"25"},null,8,u),(0,o.createElementVNode)("div",null,[(0,o.createElementVNode)("p",m,(0,o.toDisplayString)(e.skillstraining.badge.name),1)])])])])])):(0,o.createCommentVNode)("",!0),f,(0,o.createElementVNode)("h1",{class:"display-4 fw-normal mb-4 text-light",innerHTML:e.skillstraining.title},null,8,p),(0,o.createElementVNode)("div",g,[e.skillstraining.estimated_time&&(e.skillstraining.estimated_time.hours||e.skillstraining.estimated_time.minutes)?((0,o.openBlock)(),(0,o.createElementBlock)("div",h,[v,e.skillstraining.estimated_time&&e.skillstraining.estimated_time.hours?((0,o.openBlock)(),(0,o.createElementBlock)("span",{key:0,textContent:(0,o.toDisplayString)(e.skillstraining.estimated_time.hours+"h ")},null,8,y)):(0,o.createCommentVNode)("",!0),e.skillstraining.estimated_time&&e.skillstraining.estimated_time.minutes?((0,o.openBlock)(),(0,o.createElementBlock)("span",{key:1,textContent:(0,o.toDisplayString)(e.skillstraining.estimated_time.minutes+"m")},null,8,b)):(0,o.createCommentVNode)("",!0)])):(0,o.createCommentVNode)("",!0),e.skillstraining.level?((0,o.openBlock)(),(0,o.createElementBlock)("div",w,[k,(0,o.createElementVNode)("span",{textContent:(0,o.toDisplayString)(e.skillstraining.level)},null,8,x)])):(0,o.createCommentVNode)("",!0),(0,o.createElementVNode)("div",E,[100===e.skillstraining.student_completed_percentage?((0,o.openBlock)(),(0,o.createElementBlock)("span",N," Completed ")):e.skillstraining.student_completed_percentage>0&&e.skillstraining.student_completed_percentage<100?((0,o.openBlock)(),(0,o.createElementBlock)("span",V,(0,o.toDisplayString)(e.skillstraining.student_completed_percentage)+"% Completed ",1)):(0,o.createCommentVNode)("",!0)])]),(0,o.createElementVNode)("div",B,[((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(e.skillstraining.tagged,(function(e){return(0,o.openBlock)(),(0,o.createElementBlock)("div",{class:"col-sm-6 fs-6 text-light p-2",key:e.id},[_,(0,o.createTextVNode)(" "+(0,o.toDisplayString)(e.tag_name),1)])})),128))]),e.skillstraining.foreground_video?((0,o.openBlock)(),(0,o.createElementBlock)("div",C,L)):(0,o.createCommentVNode)("",!0),e.skillstraining.student_response&&"Submitted"==e.skillstraining.student_response.status?((0,o.openBlock)(),(0,o.createElementBlock)("div",S,[""===e.skillstraining.student_response.response_path?((0,o.openBlock)(),(0,o.createElementBlock)("div",T,[(0,o.createElementVNode)("button",{class:"btn btn-white-custom btn-lg border-1 rounded-0 w-100 p-md-5",style:{"font-size":"14px !important"},onClick:t[0]||(t[0]=function(){return e.viewResponse&&e.viewResponse.apply(e,arguments)})}," View Response ")])):((0,o.openBlock)(),(0,o.createElementBlock)("div",F,M))])):(0,o.createCommentVNode)("",!0),(0,o.createElementVNode)("div",I,[e.skillstraining.badge&&100===e.skillstraining.student_completed_percentage?((0,o.openBlock)(),(0,o.createElementBlock)("div",O,[(0,o.createElementVNode)("div",j,[(0,o.createElementVNode)("div",z,[(0,o.createElementVNode)("div",{class:"d-flex align-items-center cursor-pointer","data-bs-toggle":"modal","data-bs-target":"#kt_modal_badge",onClick:t[1]||(t[1]=function(t){return e.openBadgeModal(e.skillstraining.student_response.badge_key)})},[(0,o.createElementVNode)("img",{src:e.skillstraining.badge.image_fullpath,alt:e.skillstraining.badge.name,class:"me-3",width:"25"},null,8,R),D])])])])):(0,o.createCommentVNode)("",!0),"Submitted"==(null===(pt=null===(ft=e.skillstraining)||void 0===ft?void 0:ft.student_response)||void 0===pt?void 0:pt.status)?((0,o.openBlock)(),(0,o.createElementBlock)("div",H,[(0,o.createElementVNode)("div",P,[(0,o.createElementVNode)("div",A,[(0,o.createElementVNode)("div",{class:"d-flex align-items-center cursor-pointer","data-bs-toggle":"modal","data-bs-target":"#kt_modal_viewFile",onClick:t[2]||(t[2]=function(){return e.loadCertificate&&e.loadCertificate.apply(e,arguments)})},W)])])])):(0,o.createCommentVNode)("",!0),e.skillstraining.feedback?((0,o.openBlock)(),(0,o.createElementBlock)("div",G,Z)):(0,o.createCommentVNode)("",!0)])])],4),(0,o.createElementVNode)("div",(0,o.mergeProps)({class:{row:e.skillstraining.student_response.activity_responses.length<6,"sticky-top":e.scrolled}},(0,o.toHandlers)(e.handleScroll,!0),{class:"d-flex bg-black module-sections"}),[((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(e.skillstraining.student_response.activity_responses,(function(t){return(0,o.openBlock)(),(0,o.createElementBlock)("div",{key:t.activity.id,class:(0,o.normalizeClass)(["text-center p-0",[e.skillstraining.student_response.activity_responses.length<6?"col":"col-6 col-sm-4 col-md-2","bg-black"]])},[(0,o.createElementVNode)("div",U,[(0,o.createElementVNode)("span",q,[t?((0,o.openBlock)(),(0,o.createElementBlock)("svg",Y,J)):((0,o.openBlock)(),(0,o.createElementBlock)("svg",X,Q))]),(0,o.createElementVNode)("p",{class:"m-0 px-5 text-white",innerHTML:t.activity.title},null,8,K),(0,o.createElementVNode)("p",$,[t.activity.estimated_time&&t.activity.estimated_time.hours?((0,o.openBlock)(),(0,o.createElementBlock)("span",{key:0,textContent:(0,o.toDisplayString)(t.activity.estimated_time.hours+"h ")},null,8,ee)):(0,o.createCommentVNode)("",!0),t.activity.estimated_time&&t.activity.estimated_time.minutes?((0,o.openBlock)(),(0,o.createElementBlock)("span",{key:1,textContent:(0,o.toDisplayString)(t.activity.estimated_time.minutes+"m")},null,8,te)):(0,o.createCommentVNode)("",!0),(0,o.createTextVNode)("   ")])])],2)})),128)),(0,o.createElementVNode)("div",{class:(0,o.normalizeClass)(["text-center p-0",[e.skillstraining.student_response.activity_responses.length<6?"col":"col-6 col-sm-4 col-md-2 ","bg-black"]])},[(0,o.createElementVNode)("div",ne,[(0,o.createElementVNode)("span",oe,[e.skillstraining.student_response?((0,o.openBlock)(),(0,o.createElementBlock)("svg",ie,re)):((0,o.openBlock)(),(0,o.createElementBlock)("svg",ae,le))]),(0,o.createElementVNode)("p",{class:(0,o.normalizeClass)(["m-0",{"text-white":e.skillstraining.student_response}])}," Final Step ",2),(0,o.createElementVNode)("p",{class:(0,o.normalizeClass)(["m-0",{"text-white":e.skillstraining.student_response}])},"   ",2)])],2)],16),(0,o.createElementVNode)("div",ce,[(0,o.createElementVNode)("div",se,[(0,o.createElementVNode)("div",de,[(0,o.createElementVNode)("div",ue,[(0,o.createElementVNode)("div",me,[(0,o.createElementVNode)("ul",fe,[((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(e.skillstraining.student_response.activity_responses,(function(t){return(0,o.openBlock)(),(0,o.createElementBlock)("li",{key:t.activity.id,class:"nav-item w-100 me-0 mb-md-2"},[(0,o.createElementVNode)("a",{class:(0,o.normalizeClass)(["nav-link w-100 btn btn-flex btn-active-light-secondary btn-active-color-dark",{active:e.selectedActivityId===t.activity.id}]),onClick:function(n){return e.selectedActivityId=t.activity.id}},[(0,o.createElementVNode)("span",ge,[(0,o.createElementVNode)("span",he,"Section "+(0,o.toDisplayString)(t.activity.number),1),(0,o.createElementVNode)("span",ve,(0,o.toDisplayString)(t.activity.title.toLowerCase()),1)])],10,pe)])})),128)),(0,o.createElementVNode)("li",ye,[(0,o.createElementVNode)("a",{class:(0,o.normalizeClass)(["nav-link w-100 btn btn-flex btn-active-light-secondary btn-active-color-dark",{active:null===e.selectedActivityId}]),onClick:t[3]||(t[3]=function(t){return e.selectedActivityId=null})},be,2)])])]),(0,o.createElementVNode)("div",we,[((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(e.skillstraining.student_response.activity_responses,(function(t){return(0,o.openBlock)(),(0,o.createElementBlock)(o.Fragment,{key:t.activity.id},[e.selectedActivityId===t.activity.id?((0,o.openBlock)(),(0,o.createElementBlock)("div",ke,[(0,o.createVNode)(gt,{to:{name:"task-skillstraining-section-detail",params:{id:e.currentSkillstraining,sectionid:t.activity.number}},custom:""},{default:(0,o.withCtx)((function(e){var t=e.href;return[(0,o.createElementVNode)("a",{href:t,target:"_blank",rel:"noopener",class:"d-flex justify-content-end me-3 position-sticky top-0 bg-white p-2 gap-1"},[Ee,(0,o.createTextVNode)(" View Module ")],8,xe)]})),_:2},1032,["to"]),t.activity.estimated_time&&t.activity.estimated_time.hours||t.activity.estimated_time&&t.activity.estimated_time.minutes?((0,o.openBlock)(),(0,o.createElementBlock)("div",Ne,[Ve,t.activity.estimated_time&&t.activity.estimated_time.hours?((0,o.openBlock)(),(0,o.createElementBlock)("span",{key:0,textContent:(0,o.toDisplayString)(t.activity.estimated_time.hours+"h ")},null,8,Be)):(0,o.createCommentVNode)("",!0),t.activity.estimated_time&&t.activity.estimated_time.minutes?((0,o.openBlock)(),(0,o.createElementBlock)("span",{key:1,textContent:(0,o.toDisplayString)(t.activity.estimated_time.minutes+"m")},null,8,_e)):(0,o.createCommentVNode)("",!0)])):(0,o.createCommentVNode)("",!0),(0,o.createElementVNode)("div",{class:"my-5",innerHTML:t.activity.body},null,8,Ce),t.activity.response&&t.response?((0,o.openBlock)(),(0,o.createElementBlock)("div",{key:1,class:"froala-response mb-5",innerHTML:t.response},null,8,Le)):(0,o.createCommentVNode)("",!0)])):(0,o.createCommentVNode)("",!0),!e.selectedActivityId===t.activity.id?((0,o.openBlock)(),(0,o.createElementBlock)("div",Se,Te)):(0,o.createCommentVNode)("",!0)],64)})),128)),null===e.selectedActivityId?((0,o.openBlock)(),(0,o.createElementBlock)("div",Fe,[Me,(0,o.createElementVNode)("div",Ie,["Submitted"==e.skillstraining.student_response.status?((0,o.openBlock)(),(0,o.createElementBlock)("div",Oe,[(0,o.createElementVNode)("button",{class:"btn btn-secondary rounded",style:{"font-size":"14px !important"},"data-bs-toggle":"modal","data-bs-target":"#kt_modal_viewFile",onClick:t[4]||(t[4]=function(){return e.loadCertificate&&e.loadCertificate.apply(e,arguments)})},[je,(0,o.createTextVNode)("View Certificate ")])])):(0,o.createCommentVNode)("",!0),e.skillstraining.response&&e.skillstraining.student_response.response_path?((0,o.openBlock)(),(0,o.createElementBlock)("a",{key:1,href:"/skillstraining/responses/"+e.skillstraining.student_response.id+"/download",class:"btn btn-secondary rounded"},[Re,(0,o.createTextVNode)(" Download Response ")],8,ze)):(0,o.createCommentVNode)("",!0),"Submitted"==e.skillstraining.student_response.status?((0,o.openBlock)(),(0,o.createElementBlock)("a",{key:2,href:"/skillstraining-certificate/"+e.skillstraining.student_response.id,target:"_blank",class:"btn btn-secondary rounded"},[He,(0,o.createTextVNode)(" Download Certificate")],8,De)):(0,o.createCommentVNode)("",!0)])])):(0,o.createCommentVNode)("",!0)])])])])]),(0,o.createElementVNode)("div",Pe,[(0,o.createElementVNode)("div",Ae,[(0,o.createElementVNode)("div",We,[(0,o.createElementVNode)("div",{class:"modal-body bg-black p-1",innerHTML:e.skillstraining.foreground_video},null,8,Ge)])])]),(0,o.createElementVNode)("div",Ze,[(0,o.createElementVNode)("div",{class:(0,o.normalizeClass)(["modal-dialog modal-dialog-centered",e.isFullscreen?"custom-fullscreen-modal":"mw-1200px"])},[(0,o.createElementVNode)("div",Ue,[(0,o.createElementVNode)("div",qe,[Ye,(0,o.createElementVNode)("div",null,[(0,o.createElementVNode)("span",{class:"mx-4 cursor-pointer",onClick:t[5]||(t[5]=function(){return e.toggleFullscreen&&e.toggleFullscreen.apply(e,arguments)})},[e.isFullscreen?((0,o.openBlock)(),(0,o.createElementBlock)("i",Je)):((0,o.openBlock)(),(0,o.createElementBlock)("i",Xe))]),e.skillstraining.student_response?((0,o.openBlock)(),(0,o.createElementBlock)("a",{key:0,href:"/certificate-download-skills/"+e.skillstraining.student_response.id,target:"_blank",class:"text-secondary mx-2"},Ke,8,Qe)):(0,o.createCommentVNode)("",!0),$e])]),(0,o.createElementVNode)("div",et,[e.certificateUrl?((0,o.openBlock)(),(0,o.createElementBlock)("iframe",{key:0,src:e.certificateUrl,class:"w-100",style:(0,o.normalizeStyle)({height:e.isFullscreen?"90vh":"80vh",border:"none"}),allowfullscreen:""},null,12,tt)):((0,o.openBlock)(),(0,o.createElementBlock)("p",nt,"Loading..."))])])],2)]),(0,o.createElementVNode)("div",ot,[(0,o.createElementVNode)("div",it,[(0,o.createElementVNode)("div",rt,[at,(0,o.createElementVNode)("div",lt,[(0,o.createElementVNode)("div",ct,[(0,o.createElementVNode)("p",{innerHTML:e.skillstraining.student_response.feedback,class:"text-gray-700"},null,8,st)])])])])]),(0,o.createVNode)(ht,{selectedBadge:e.selectedBadge,onShareBadge:e.openShareBadgeModal},null,8,["selectedBadge","onShareBadge"]),(0,o.createVNode)(vt,{modalSrc:e.modalSrc,downloadUrl:e.downloadUrl},null,8,["modalSrc","downloadUrl"])],64)}]])}}]);