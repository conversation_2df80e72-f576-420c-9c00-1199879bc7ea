
<?php

// Home
use Diglactic\Breadcrumbs\Breadcrumbs;
use Illuminate\Support\Facades\Auth;
use Diglactic\Breadcrumbs\Generator as BreadcrumbTrail;

Breadcrumbs::for('home', function (BreadcrumbTrail $trail) {
    $trail->push('Home', route('home'));
});

// Extra
Breadcrumbs::for('admintasks', function (BreadcrumbTrail $trail) {
    $trail->push('Tasks');
});

// Extra
Breadcrumbs::for('extra', function (BreadcrumbTrail $trail) {
    $trail->push('Extra');
});

// You
Breadcrumbs::for('you', function (BreadcrumbTrail $trail) {
    $trail->push('You');
});

Breadcrumbs::for('account', function (BreadcrumbTrail $trail) {
    $trail->push('account');
});

Breadcrumbs::for('resources & tasks', function (BreadcrumbTrail $trail) {
    $trail->push('resources & tasks');
});

Breadcrumbs::for('modules', function (BreadcrumbTrail $trail) {
    $trail->parent('resources & tasks');
    $trail->push('Modules');
});

// Explore
Breadcrumbs::for('explore', function (BreadcrumbTrail $trail) {
    $trail->push('Explore');
});

// Explore
Breadcrumbs::for('game-plan', function (BreadcrumbTrail $trail) {
    $trail->push('Gameplan');
});

// Work Experience
Breadcrumbs::for('wew', function (BreadcrumbTrail $trail) {
    $trail->push('Work Experience');
});


// Home > Schools
Breadcrumbs::for('schools', function (BreadcrumbTrail $trail) {
    $trail->push('Schools', route('schools.index'));
});

// Home > Gameplan Questions
Breadcrumbs::for('questions', function (BreadcrumbTrail $trail) {
    $trail->parent('game-plan');
    $trail->push('Questions', route('gameplanQuestions.index'));
});

// Schools > Ordered
Breadcrumbs::for('ordered-schools', function (BreadcrumbTrail $trail) {
    $trail->parent('schools');
    $trail->push('Ordered', '/ordered-schools');
});

// Schools > Visit Requests
Breadcrumbs::for('school-visit-requests', function (BreadcrumbTrail $trail) {
    $trail->parent('schools');
    $trail->push('Visit Requests', '/ordered-schools');
});

// Schools > Visit Templates
Breadcrumbs::for('school-visit-templates', function (BreadcrumbTrail $trail) {
    $trail->parent('schools');
    $trail->push('Visit Templates', '/ordered-schools');
});

Breadcrumbs::for('edit-schools', function (BreadcrumbTrail $trail, $school = "", $isConfirmed = "") {
    if ($isConfirmed) {
        $trail->parent('schools');
    } else {
        $trail->parent('ordered-schools');
    }
    $trail->push('Edit');
    if ($school) {
        $trail->push($school);
    }
});

Breadcrumbs::for('add-schools', function (BreadcrumbTrail $trail, $school = "") {
    $trail->parent('schools');

    $trail->push('Add');
    if ($school) {
        $trail->push($school);
    }
});

Breadcrumbs::for('edit-question', function (BreadcrumbTrail $trail, $question = "") {
    $trail->parent('questions');

    if ($question) {
        $trail->push($question);
    }
});

Breadcrumbs::for('add-question', function (BreadcrumbTrail $trail, $question = "") {
    $trail->parent('questions');

    $trail->push('Add');
    if ($question) {
        $trail->push($question);
    }
});

// Schools > [School name] > Import Students
Breadcrumbs::for('students-import', function (BreadcrumbTrail $trail, $school = "") {
    $trail->parent('schools');
    if ($school) {
        $trail->push($school);
    }
    $trail->push('Import students');
});

// Home > Organisations
Breadcrumbs::for('organisations', function (BreadcrumbTrail $trail) {
    $trail->push('Organisations', route('organisations.index'));
});

Breadcrumbs::for('create-organisation', function (BreadcrumbTrail $trail, $organisation = "", $isConfirmed = "") {
    $trail->push('organisations');
    $trail->push('Create');
});


Breadcrumbs::for('edit-organisation', function (BreadcrumbTrail $trail, $organisation = "") {
    $trail->parent('organisations');
    $trail->push('Edit');
    if ($organisation) {
        $trail->push($organisation);
    }
});

// Home > Roles
Breadcrumbs::for('roles', function (BreadcrumbTrail $trail) {
    $trail->parent('home');
    $trail->push('Roles', route('roles.index'));
});

// Home > Teachers
Breadcrumbs::for('teachers', function (BreadcrumbTrail $trail) {
    if (Auth::user()->isTeacher()) {
        $trail->parent('account');
        $trail->push('Teachers', route('teachers.index'));
    } else {
        $trail->parent('home');
        $trail->push('Teachers', route('teachers.index'));
    }
});

// Home > Markers
Breadcrumbs::for('markers', function (BreadcrumbTrail $trail) {
    $trail->parent('home');
    $trail->push('Markers', route('markers.index'));
});

// Home > Users
Breadcrumbs::for('users', function (BreadcrumbTrail $trail) {
    $trail->parent('home');
    $trail->push('Users', route('users'));
});

Breadcrumbs::for('noticeboard', function (BreadcrumbTrail $trail) {
    if (Auth::user()->isTeacher()) {
        $trail->parent('resources & tasks');
        $trail->push('noticeboard', route('noticeboard.index'));
    } else {
        $trail->parent('home');
        $trail->push('noticeboard', route('noticeboard.index'));
    }
});

// Home > Teachers
Breadcrumbs::for('staff', function (BreadcrumbTrail $trail) {
    if (Auth::user()->isTeacher()) {
        $trail->parent('account');
        $trail->push('Staff', route('staff.index'));
    } else {
        $trail->parent('home');
        $trail->push('Staff', route('staff.index'));
    }
});

// Teacher Position
Breadcrumbs::for('positions', function (BreadcrumbTrail $trail) {
    $trail->parent('teachers');
    $trail->push('Positions', route('positions.index'));
});

// Home > Teacher Resources
Breadcrumbs::for('teacherresources', function (BreadcrumbTrail $trail) {
    if (Auth::user()->isAdmin()) {
        $trail->parent('teachers');
    } elseif (Auth::user()->isTeacher()) {
        $trail->parent('resources & tasks');
    } else {
        $trail->parent('you');
    }
    $trail->push('Teaching Resources', route('teacherresources.index'));
});

// Home > Teacher Resource Types
Breadcrumbs::for('teacherresourcetypes', function (BreadcrumbTrail $trail) {
    $trail->parent('teachers');
    $trail->push('Teaching Resource Types', route('teacherresourcetypes.index'));
});

// Home > Teaching Areas
Breadcrumbs::for('teachingareas', function (BreadcrumbTrail $trail) {
    $trail->parent('teachers');
    $trail->push('Teaching Areas', route('teacherresourcetypes.index'));
});

// Home > Teacher Resource Groups
Breadcrumbs::for('teacherresourcegroups', function (BreadcrumbTrail $trail) {
    $trail->parent('teachers');
    $trail->push('Teaching Resource Groups', route('teacherresourcegroups.index'));
});

// Home > Students
Breadcrumbs::for('students', function (BreadcrumbTrail $trail) {
    if (Auth::user()->isParent()) {
        $trail->push('Children');
    } else {
        if (Auth::user()->isTeacher()) {
            $trail->parent('account');
        } else {
            $trail->parent('home');
        }
        $trail->push('Students', route('students.index'));
    }
});

// Home > Courses
Breadcrumbs::for('courses', function (BreadcrumbTrail $trail) {
    $trail->parent('home');
    $trail->push('Courses', route('courses.index'));
});

// Home > Course Categories
Breadcrumbs::for('coursecategories', function (BreadcrumbTrail $trail) {
    $trail->parent('home');
    $trail->push('Course Categories', route('coursecategories.index'));
});

// Home > Students progress
Breadcrumbs::for('students-progress', function (BreadcrumbTrail $trail) {
    $trail->parent('home');
    $trail->push('Students progress');
});

// Home > Lesson Plan
Breadcrumbs::for('lesson-plan', function (BreadcrumbTrail $trail) {
    $trail->parent('home');
    $trail->push('Lesson Plan');
});

// Home > Dasboard
Breadcrumbs::for('dashboard', function (BreadcrumbTrail $trail) {
    $trail->parent('home');
    $trail->push('Dashboard');
});

// Home > E-Portfolio
Breadcrumbs::for('e-portfolio', function (BreadcrumbTrail $trail) {
    $trail->parent('home');
    $trail->push('E Portfolio', url('e-portfolio'));
});

// Home > Explore Industries >[Industry] > [Template Type] > [Template Name]
Breadcrumbs::for('exploreindustries', function (BreadcrumbTrail $trail, $industry = null, $type = null, $data = null) {
    $trail->parent('explore');
    if (empty($industry) && empty($type)) {
        $trail->push('Industries', route('exploreindustries.index'));
    }
    if (!empty($type) && !empty($industry) && !empty($data)) {
        $trail->push('Industries', route('exploreindustries.index'));
        if ($sub = $industry->parent) {
            $trail->push($sub->name, route('exploreindustries.view', $sub->id));
        }
        $trail->push($industry->name, route('exploreindustries.view', $industry->id));
        // $templateid = $data->template->id;
        // $trail->push($type, route('exploreindustries.lists', [$data->industry->id, $templateid]));
        $trail->push($type);
        $trail->push($data->title);
    } elseif (!empty($type) && !empty($industry)) {
        $trail->push('Industries', route('exploreindustries.index'));
        $trail->push($industry->name, route('exploreindustries.view', $industry->id));
        $trail->push($type);
    } elseif (!empty($industry)) {
        $trail->push('Industries', route('exploreindustries.index'));
        if ($sub = $industry->parent) {
            $trail->push($sub->name, route('exploreindustries.view', $sub->id));
        }
        $trail->push($industry->name, route('exploreindustries.view', $industry->id));
    }
});

// Home > Explore Industries >[Industry] > [Sub category]
Breadcrumbs::for('industry-subcategory', function (BreadcrumbTrail $trail, $industry, $subcategory) {
    $trail->parent('explore');
    $trail->push('Industries', route('exploreindustries.index'));
    $trail->push($industry->name, route('exploreindustries.view', $industry->id));
    $trail->push($subcategory->name);
});

// Home > Explore Categories >[Category] > [Template Type] > [Template Name]
Breadcrumbs::for('explorethingstoknowtemplates', function (BreadcrumbTrail $trail, $category = null, $type = null, $data = null) {
    $trail->parent('explore');
    if (empty($category) && empty($type)) {
        $trail->push('Things To Know', route('explorethingstoknow.index'));
    }
    if (!empty($type) && !empty($category) && !empty($data)) {
        $trail->push('Things To Know', route('explorethingstoknow.index'));
        $trail->push($category->title, route('explorethingstoknowtemplates.view', $category->id));
        $templateid = $data->id;
        $trail->push($type, route('explorethingstoknow.lists', [$data->$category->id, $templateid]));
        $trail->push($data->title);
    } elseif (!empty($type) && !empty($category)) {
        $trail->push('Things To Know', route('explorethingstoknow.index'));
        $trail->push($category->title, route('explorethingstoknowtemplates.view', $category->id));
        $trail->push($type);
    } elseif (!empty($category)) {
        $trail->push('Things To Know', route('explorethingstoknow.index'));
        $trail->push($category->title, route('explorethingstoknowtemplates.view', $category->id));
    }
});

// Home > Subject Selection Guides
Breadcrumbs::for('subject-selection-guides', function (BreadcrumbTrail $trail) {
    $trail->parent('home');
    $trail->push('Subject Selection Guides');
});

Breadcrumbs::for('industrycategories', function (BreadcrumbTrail $trail) {
    $trail->parent('home');
    $trail->push('Industries', route('industrycategories.index'));
});

Breadcrumbs::for('interviewnominees', function (BreadcrumbTrail $trail) {
    $trail->parent('home');
    $trail->push('Interview Nominees', route('interview.nominees'));
});

Breadcrumbs::for('templates', function (BreadcrumbTrail $trail) {
    $trail->parent('industrycategories');
    $trail->push('Templates');
});

Breadcrumbs::for('industryunitStudentEnquiries', function (BreadcrumbTrail $trail) {
    $trail->parent('industrycategories');
    $trail->push('Student Enquiries', route('industryunit.studentEnquiries'));
});

Breadcrumbs::for('industryunitParentEnquiries', function (BreadcrumbTrail $trail) {
    $trail->parent('industrycategories');
    $trail->push('Parent Enquiries', route('industryunit.parentEnquiries'));
});

Breadcrumbs::for('industryunitParentEmails', function (BreadcrumbTrail $trail) {
    $trail->parent('industrycategories');
    $trail->push('Sent to Parents', route('industryunit.parentEmails'));
});


Breadcrumbs::for('industrytemplates', function (BreadcrumbTrail $trail, $industry = null) {
    $trail->parent('industrycategories');
    if ($industry) {
        $trail->push($industry->name, route('industrycategories.show', $industry->id));
    }
});

Breadcrumbs::for('add-template', function (BreadcrumbTrail $trail, $industry = null) {
    $trail->parent('industrycategories');
    if ($industry) {
        $trail->push($industry->name, route('industrycategories.show', $industry->id));
    }
    $trail->push('Add Template');
});

Breadcrumbs::for('edit-template', function (BreadcrumbTrail $trail, /* $industry='', */ $template = null) {
    $trail->parent('industrycategories');
    // if($industry) {
    //     $trail->push($industry->name, route('industrycategories.show', $industry->id));
    // }
    $trail->push('Templates', '/industries/templates');
    if ($template) {
        $trail->push($template);
    }
    $trail->push('Edit');
});

Breadcrumbs::for('thingstoknowcategories', function (BreadcrumbTrail $trail) {
    $trail->parent('home');
    $trail->push('Things to know', route('thingstoknow.categories.index'));
});
Breadcrumbs::for('categorytemplates', function (BreadcrumbTrail $trail) {
    $trail->parent('thingstoknowcategories');
    $trail->push('Templates');
});
Breadcrumbs::for('thingstoknowtemplates', function (BreadcrumbTrail $trail, $category = null) {
    $trail->parent('thingstoknowcategories');
    if ($category) {
        $trail->push($category->title, route('thingstoknow.categories.show', $category->id));
    }
});
Breadcrumbs::for('thingstoknow-add-template', function (BreadcrumbTrail $trail, $category = null) {
    $trail->parent('thingstoknowcategories');
    if ($category) {
        $trail->push($category->title, route('thingstoknow.categories.show', $category->id));
    }
    $trail->push('Add Template');
});
Breadcrumbs::for('thingstoknow-edit-template', function (BreadcrumbTrail $trail, /* $industry='', */ $template = '') {
    $trail->parent('thingstoknowcategories');
    // if($industry) {
    //     $trail->push($industry->name, route('industrycategories.show', $industry->id));
    // }
    $trail->push('Templates', '/thingstoknow/alltemplates');
    if ($template) {
        $trail->push($template);
    }
    $trail->push('Edit');
});


// Resources & Tasks > Work Experience > Responses
Breadcrumbs::for('wew-responses', function (BreadcrumbTrail $trail) {
    if (Auth::user()->isAdmin()) {
        $trail->parent('wew');
        $trail->push('Responses', route('wew.responses'));
    } else {
        $trail->push('Manage');
        $trail->push('Student Responses', route('wew.responses'));
    }
});

// Resources & Tasks > Work Experience Week > Manage
Breadcrumbs::for('wew-manage', function (BreadcrumbTrail $trail) {
    $trail->parent('resources & tasks');
    $trail->push('Work Experience');
    $trail->push('Manage');
});


// Home > Virtual Work Experience > Template
Breadcrumbs::for('workexperience', function (BreadcrumbTrail $trail) {
    $trail->parent('home');
    $trail->push('Virtual Work Experience', route('workexperiencetemplates.index'));
});

// Home > Virtual Work Experience > Add Template
Breadcrumbs::for('workexperience-add', function (BreadcrumbTrail $trail) {
    $trail->parent('workexperience');
    $trail->push('Add Template');
});

// Home > Virtual Work Experience > [template] > Edit
Breadcrumbs::for('workexperience-edit', function (BreadcrumbTrail $trail, $template) {
    $trail->parent('workexperience');
    $trail->push($template);
    $trail->push('Edit');
});

// Work Experience > Virtual Work Experience > [template] > Edit
Breadcrumbs::for('workexperience-responses', function (BreadcrumbTrail $trail) {
    $trail->parent('wew-responses');
    $trail->push('Virtual Work Experience');
});

// Home > Virtual Work Experience > Template
Breadcrumbs::for('exploreworkexperience', function (BreadcrumbTrail $trail) {
    $trail->parent('wew');
    $trail->push('Work Experience', '/exploreworkexperience');
});

// Home > Virtual Work Experience > [template]

Breadcrumbs::for('workexperience-view', function (BreadcrumbTrail $trail, $template = "") {
    $trail->parent('exploreworkexperience');
    $trail->push($template);
});


// Work Experience > Overview
Breadcrumbs::for('wew-overview', function (BreadcrumbTrail $trail) {
    $trail->parent('wew');
    $trail->push('Overview');
});

// Work Experience > Future of Work
Breadcrumbs::for('wew-futureofwork', function (BreadcrumbTrail $trail) {
    $trail->parent('wew');
    $trail->push('Future of Work');
});


// Work Experience > Work, Health & Safety
Breadcrumbs::for('workhealthsafety', function (BreadcrumbTrail $trail) {
    $trail->parent('wew');
    if (Auth::user()->isAdmin()) {
        $trail->push('Work, Health & Safety', route('workhealthsafetytemplates.index'));
    } else {
        $trail->push('Work, Health & Safety', route('wew.workhealthsafety.index'));
    }
});

// Work Experience > Work, Health & Safety > Add Template
Breadcrumbs::for('workhealthsafety-add', function (BreadcrumbTrail $trail) {
    $trail->parent('workhealthsafety');
    $trail->push('Add Template');
});

// Work Experience > Work, Health & Safety > [template] > Edit
Breadcrumbs::for('workhealthsafety-edit', function (BreadcrumbTrail $trail, $template) {
    $trail->parent('workhealthsafety');
    $trail->push($template);
    $trail->push('Edit');
});

// Work Experience > Work, Health & Safety > [template] > Edit
Breadcrumbs::for('workhealthsafety-responses', function (BreadcrumbTrail $trail) {
    $trail->parent('wew-responses');
    $trail->push('Work, Health & Safety');
});

// Work Experience > Work, Health & Safety > [template]
Breadcrumbs::for('workhealthsafety-view', function (BreadcrumbTrail $trail, $template = "") {
    $trail->parent('workhealthsafety');
    $trail->push($template);
});

// Work Experience > Work, Health & Safety > Quiz
Breadcrumbs::for('workhealthsafety-quiz', function (BreadcrumbTrail $trail) {
    $trail->parent('workhealthsafety');
    $trail->push('Quiz');
});


// Work Experience > Skills Training
Breadcrumbs::for('skillstraining', function (BreadcrumbTrail $trail) {
    $trail->parent('wew');
    if (Auth::user()->isAdmin()) {
        $trail->push('Skills Training', route('skillstrainingtemplates.index'));
    } else {
        $trail->push('Skills Training', route('wew.skillstraining.index'));
    }
});

// Work Experience > Skills Training > Add Template
Breadcrumbs::for('skillstraining-add', function (BreadcrumbTrail $trail) {
    $trail->parent('skillstraining');
    $trail->push('Add Template');
});

// Work Experience > Skills Training > [template] > Edit
Breadcrumbs::for('skillstraining-edit', function (BreadcrumbTrail $trail, $template) {
    $trail->parent('skillstraining');
    $trail->push($template);
    $trail->push('Edit');
});

// Work Experience > Skills Training > [template] > Edit
Breadcrumbs::for('skillstraining-responses', function (BreadcrumbTrail $trail) {
    $trail->parent('wew-responses');
    $trail->push('Skills Training');
});

// Work Experience > Skills Training > [template]
Breadcrumbs::for('skillstraining-view', function (BreadcrumbTrail $trail, $template = "") {
    $trail->parent('skillstraining');
    $trail->push($template);
});

// Work Experience > Work Ready
Breadcrumbs::for('workready', function (BreadcrumbTrail $trail) {
    $trail->parent('wew');
    if (Auth::user()->isAdmin()) {
        $trail->push('Work Ready', route('workreadytemplates.index'));
    } else {
        $trail->push('Work Ready', route('wew.workready.index'));
    }
});

// Work Experience > Work Ready > Add Template
Breadcrumbs::for('workready-add', function (BreadcrumbTrail $trail) {
    $trail->parent('workready');
    $trail->push('Add Template');
});

// Work Experience > Work Ready > [template] > Edit
Breadcrumbs::for('workready-edit', function (BreadcrumbTrail $trail, $template) {
    $trail->parent('workready');
    $trail->push($template);
    $trail->push('Edit');
});

// Work Experience > Work Ready > [template] > Edit
Breadcrumbs::for('workready-responses', function (BreadcrumbTrail $trail) {
    $trail->parent('wew-responses');
    $trail->push('Work Ready');
});

// Work Experience > Skills Training > [template]
Breadcrumbs::for('workready-view', function (BreadcrumbTrail $trail, $template = "") {
    $trail->parent('workready');
    $trail->push($template);
});

// Work Experience > Resume’s & Interviews
Breadcrumbs::for('resumesinterviews', function (BreadcrumbTrail $trail) {
    $trail->parent('wew');
    if (Auth::user()->isAdmin()) {
        $trail->push('Resumes & Interviews', route('resumesinterviewstemplates.index'));
    } else {
        $trail->push('Resumes & Interviews', route('wew.resumesinterviews.index'));
    }
});


// Work Experience > Resume’s & Interviews > Add Template
Breadcrumbs::for('resumesinterviews-add', function (BreadcrumbTrail $trail) {
    $trail->parent('resumesinterviews');
    $trail->push('Add Template');
});

// Work Experience > Resume’s & Interviews > [template] > Edit
Breadcrumbs::for('resumesinterviews-edit', function (BreadcrumbTrail $trail, $template) {
    $trail->parent('resumesinterviews');
    $trail->push($template);
    $trail->push('Edit');
});

// Work Experience > Resume’s & Interviews > [template] > Edit
Breadcrumbs::for('resumesinterviews-responses', function (BreadcrumbTrail $trail) {
    $trail->parent('wew-responses');
    $trail->push('Resume’s & Interviews');
});

// Work Experience > Resume’s & Interviews > [template]
Breadcrumbs::for('resumesinterviews-view', function (BreadcrumbTrail $trail, $template = "") {
    $trail->parent('resumesinterviews');
    $trail->push($template);
});

// WEW > Skills Training Categories
Breadcrumbs::for('skillstrainingcategories', function (BreadcrumbTrail $trail) {
    $trail->parent('wew');
    $trail->push('Skills Training Categories');
});

// Tasks > Badges
Breadcrumbs::for('badges', function (BreadcrumbTrail $trail) {
    $trail->parent('admintasks');
    $trail->push('Badges', route('badges.index'));
});

// Tasks > Companies
Breadcrumbs::for('companies', function (BreadcrumbTrail $trail) {
    $trail->parent('home');
    $trail->push('Companies', route('companies.index'));
});

// Companies > Create
Breadcrumbs::for('create-company', function (BreadcrumbTrail $trail) {
    $trail->parent('companies');
    $trail->push('Create');
});

// Companies > Edit
Breadcrumbs::for('edit-company', function (BreadcrumbTrail $trail, $company = "") {
    $trail->parent('companies');
    $trail->push('Edit');
    if ($company) {
        $trail->push($company);
    }
});

// Tasks > Employers
Breadcrumbs::for('employers', function (BreadcrumbTrail $trail) {
    $trail->parent('companies');
    $trail->push('Employers', route('employers.index'));
});

// Employers > Show
Breadcrumbs::for('employer-show', function (BreadcrumbTrail $trail, $employer) {
    $trail->parent('employers');
    $trail->push($employer->name);
});



// Gallery
Breadcrumbs::for('banners', function (BreadcrumbTrail $trail) {
    $trail->parent('wew');
    $trail->push('banners', route('banners.index'));
});


Breadcrumbs::for('notices', function (BreadcrumbTrail $trail) {
    $trail->push('Noticeboard');
});

Breadcrumbs::for('adminnotices', function (BreadcrumbTrail $trail) {
    $trail->parent('notices');
    $trail->push('Admin', route('notices.index'));
});

Breadcrumbs::for('teachernotices', function (BreadcrumbTrail $trail) {
    $trail->parent('notices');
    $trail->push('Teacher', route('teacher.notices'));
});

Breadcrumbs::for('staffnotices', function (BreadcrumbTrail $trail) {
    $trail->parent('notices');
    $trail->push('Staff', route('staff.notices'));
});

//Quizz
Breadcrumbs::for('quiz', function (BreadcrumbTrail $trail) {
    $trail->parent('home');
    $trail->push('Quiz', route('quizzes.index'));
});

// Gallery
Breadcrumbs::for('gallery', function (BreadcrumbTrail $trail) {
    $trail->parent('home');
    $trail->push('Gallery', route('galleries.index'));
});

Breadcrumbs::for('addquiz', function (BreadcrumbTrail $trail) {
    $trail->parent('quiz');
    $trail->push('Add', route('quizzes.create'));
});

Breadcrumbs::for('editquiz', function (BreadcrumbTrail $trail) {
    $trail->parent('quiz');
    $trail->push('Edit');
});

Breadcrumbs::for('question', function (BreadcrumbTrail $trail) {
    $trail->parent('quiz');
    $trail->push('Question', route('questions.index'));
});

Breadcrumbs::for('addquestion', function (BreadcrumbTrail $trail) {
    $trail->parent('question');
    $trail->push('Add', route('questions.create'));
});

Breadcrumbs::for('editquestion', function (BreadcrumbTrail $trail) {
    $trail->parent('question');
    $trail->push('Edit');
});

Breadcrumbs::for('cv', function (BreadcrumbTrail $trail) {
    $trail->parent('explore');
    $trail->push('Resume');
});
Breadcrumbs::for('course-finder', function (BreadcrumbTrail $trail) {
    $trail->parent('explore');
    $trail->push('Course Finder');
});

// Experience Category
Breadcrumbs::for('experiencecategories', function (BreadcrumbTrail $trail) {
    $trail->parent('wew');
    $trail->push('Categories', route('experiencecategories.index'));
});


// CV > CV Types
Breadcrumbs::for('cvtype', function (BreadcrumbTrail $trail) {
    $trail->parent('cv');
    $trail->push('Resume Types', route('cvtypes.index'));
});

// CV > CV Sections
Breadcrumbs::for('cvsection', function (BreadcrumbTrail $trail) {
    $trail->parent('cv');
    $trail->push('Resume Sections', route('cvsections.index'));
});

// CV > CV Templates
Breadcrumbs::for('cvtemplates', function (BreadcrumbTrail $trail) {
    $trail->parent('cv');
    $trail->push('Resume Templates', route('cvsections.index'));
});

// CV > Tips
Breadcrumbs::for('cvtips', function (BreadcrumbTrail $trail) {
    $trail->parent('cv');
    $trail->push('Tips', route('cvtips.index'));
});


Breadcrumbs::for('studentcv', function (BreadcrumbTrail $trail) {
    $trail->parent('explore');
    $trail->push('Resume builder', route('cvs.index'));
});

// CV builder > Setect template
Breadcrumbs::for('cv-selecttemplate', function (BreadcrumbTrail $trail, $id = null) {
    $trail->parent('studentcv');
    if ($id) {
        $trail->push('Select template', route('cvs.selecttemplate', $id));
    } else {
        $trail->push('Select template', route('cvs.selecttemplate'));
    }
});

// CV builder > Setect template > CV builder
Breadcrumbs::for('cvbuilder', function (BreadcrumbTrail $trail, $id) {
    $trail->parent('cv-selecttemplate', $id);
    $trail->push('Resume builder');
});

Breadcrumbs::for('institutes', function (BreadcrumbTrail $trail) {
    $trail->parent('home');
    $trail->push('Institute', route('institutes.index'));
});
Breadcrumbs::for('import-institutes', function (BreadcrumbTrail $trail) {
    $trail->parent('institutes');
    $trail->push('Import');
});
Breadcrumbs::for('universities', function (BreadcrumbTrail $trail) {
    $trail->parent('home');
    $trail->push('University', route('universities.index'));
});
Breadcrumbs::for('colleges', function (BreadcrumbTrail $trail) {
    $trail->parent('home');
    $trail->push('College', route('colleges.index'));
});


Breadcrumbs::for('scholarships', function (BreadcrumbTrail $trail) {
    if (Auth::check() && Auth::user()->isAdmin()) {
        $trail->parent('home');
    } else {
        $trail->parent('explore');
    }
    $trail->push('Scholarships', route('scholarships.index'));
});

Breadcrumbs::for('scholarships-add', function (BreadcrumbTrail $trail) {
    $trail->parent('scholarships');
    $trail->push('Add');
});

Breadcrumbs::for('scholarships-edit', function (BreadcrumbTrail $trail, $scholarship = '') {
    $trail->parent('scholarships');
    $trail->push('Edit');
    if ($scholarship) {
        $trail->push($scholarship);
    }
});

Breadcrumbs::for('scholarshiptips', function (BreadcrumbTrail $trail) {
    $trail->parent('scholarships');
    $trail->push('Tips');
});

Breadcrumbs::for('scholarshipsearch', function (BreadcrumbTrail $trail, $scholarship = '') {
    $trail->parent('scholarships');
    if (!Auth::user()->isAdmin()) {
        $trail->push('Search', route('scholarships.search'));
    }
    if ($scholarship) {
        $trail->push($scholarship);
    }
});

Breadcrumbs::for('scholarshipappliaction', function (BreadcrumbTrail $trail) {
    $trail->parent('scholarships');
    $trail->push('Application Assistant');
});

// Home > Scholarship
Breadcrumbs::for('scholarshipproviders', function (BreadcrumbTrail $trail) {
    $trail->parent('scholarships');
    $trail->push('Providers', route('scholarshipproviders.index'));
});

Breadcrumbs::for('scholarshipnominees', function (BreadcrumbTrail $trail) {
    $trail->parent('scholarships');
    $trail->push('Nominees', route('scholarship.nominees'));
});

Breadcrumbs::for('targetgroups', function (BreadcrumbTrail $trail) {
    $trail->parent('scholarships');
    $trail->push('Target Groups', route('targetgroups.index'));
});

Breadcrumbs::for('fieldofstudy', function (BreadcrumbTrail $trail) {
    $trail->parent('scholarships');
    $trail->push('Field of study', route('fieldofstudies.index'));
});

// Online Lessons
Breadcrumbs::for('lessons', function (BreadcrumbTrail $trail) {
    $trail->push('Online Lessons', route('lessons.index'));
});

// Online Lessons > Add
Breadcrumbs::for('lessons-add', function (BreadcrumbTrail $trail) {
    $trail->parent('lessons');
    $trail->push('Add');
});

// Online Lessons > [Name] > Edit
Breadcrumbs::for('lessons-edit', function (BreadcrumbTrail $trail, $template) {
    $trail->parent('lessons');
    $trail->push($template);
    $trail->push('Edit');
});

// Online Lessons > Lesson Categories
Breadcrumbs::for('lessoncategories', function (BreadcrumbTrail $trail) {
    $trail->parent('lessons');
    $trail->push('Lesson Categories');
});



// You > Your Tasks
Breadcrumbs::for('tasks', function (BreadcrumbTrail $trail) {
    if (Auth::user()->isTeacher() && (session('schoolSection') == 'primary' || Auth::user()->hasPrimarySchoolAccess()) && !Auth::user()->hasSecondarySchoolAccess()) {
        $trail->parent('explore');
        $trail->push('All Tasks', '/tasks');
    } elseif (Auth::user()->isTeacher()) {
        $trail->parent('resources & tasks');
        $trail->push('Student Tasks');
        $trail->push('Manage', '/tasks');
    } else {
        $trail->parent('explore');
        $trail->push('Your Tasks', '/tasks');
    }
});

Breadcrumbs::for('student-tasks', function (BreadcrumbTrail $trail) {
    if (Auth::user()->isTeacher()) {
        $trail->parent('resources & tasks');
        $trail->push('Student Tasks');
        $trail->push('Responses', '/student-tasks');
    } else {
        $trail->parent('you');
        $trail->push('Your Student Tasks', '/student-tasks');
    }
});

Breadcrumbs::for('workexperience-tasks', function (BreadcrumbTrail $trail) {
    if (Auth::user()->isTeacher()) {
        $trail->parent('resources & tasks');
        $trail->push('Virtual Work Experience');
        $trail->push('Responses', '/workexperience-tasks');
    } else {
        $trail->parent('you');
        $trail->push('Your Student Virtual Work Experience Tasks', '/workexperience-tasks');
    }
});

// Online Lessons > Responses
Breadcrumbs::for('lessons-response', function (BreadcrumbTrail $trail) {
    $trail->parent('lessons');
    $trail->push('Responses');
});


// You > Your Tasks > [Name] >

Breadcrumbs::for('tasks-view', function (BreadcrumbTrail $trail, $template = "") {
    $trail->parent('tasks');
    $trail->push($template);
});


// Home > Blog
Breadcrumbs::for('blog', function (BreadcrumbTrail $trail) {
    $trail->parent('home');
    $trail->push('Blog', route('blog'));
});

// Home > Blog > [Category]
Breadcrumbs::for('category', function (BreadcrumbTrail $trail, $category) {
    $trail->parent('blog');
    $trail->push($category->title, route('category', $category->id));
});

// Home > Blog > [Category] > [Post]
Breadcrumbs::for('post', function (BreadcrumbTrail $trail, $post) {
    $trail->parent('category', $post->category);
    $trail->push($post->title, route('post', $post));
});
// Home > Event Categories
Breadcrumbs::for('eventcategories', function (BreadcrumbTrail $trail) {
    $trail->parent('home');
    $trail->push('Event Categories', route('eventcategories.index'));
});
// Home > Events
Breadcrumbs::for('events', function (BreadcrumbTrail $trail) {
    if (Auth::user()->isAdmin()) {
        $trail->parent('home');
        $trail->push('Events', route('events.index'));
    } else {
        $trail->parent('explore');
        $trail->push('Calendar', route('events.index'));
    }
});
// Home > Events > Import
Breadcrumbs::for('events-import', function (BreadcrumbTrail $trail) {
    $trail->parent('events');
    $trail->push('Import');
});
// Home > Subjects
Breadcrumbs::for('subjects', function (BreadcrumbTrail $trail) {
    $trail->parent('home');
    $trail->push('Subjects', route('subjects.index'));
});
// Home > Subjects > Import
Breadcrumbs::for('subjects-import', function (BreadcrumbTrail $trail) {
    $trail->parent('subjects');
    $trail->push('Import');
});

Breadcrumbs::for('subjects-add', function (BreadcrumbTrail $trail) {
    $trail->parent('subjects');
});

Breadcrumbs::for('subjects-edit', function (BreadcrumbTrail $trail, $subject = '') {
    $trail->parent('subjects');
    $trail->push('Edit');
    if ($subject) {
        $trail->push($subject);
    }
});

// Admin > Subject Selection Quiz > Learning Styles
Breadcrumbs::for('learning-styles', function (BreadcrumbTrail $trail) {
    $trail->parent('home');
    $trail->push('subject selection quiz');
    $trail->push('Learning Styles', route('learning-styles.index'));
});

// Admin > Subject Selection Quiz > Subject Type
Breadcrumbs::for('subject-types', function (BreadcrumbTrail $trail) {
    $trail->parent('home');
    $trail->push('subject selection quiz');
    $trail->push('Subject Types', route('subject-types.index'));
});

// Admin > Subject Selection Quiz > Subject Type
Breadcrumbs::for('subject-course-types', function (BreadcrumbTrail $trail) {
    $trail->parent('home');
    $trail->push('subject selection quiz');
    $trail->push('Course Types', route('subject-course-types.index'));
});

// Admin > Subject Selection Quiz > Subject Skills
Breadcrumbs::for('ssq-skills', function (BreadcrumbTrail $trail) {
    $trail->parent('home');
    $trail->push('subject selection quiz');
    $trail->push('Skills', route('ssq-skills.index'));
});

// Admin > Subject Selection Quiz > Subject Interests
Breadcrumbs::for('ssq-interests', function (BreadcrumbTrail $trail) {
    $trail->parent('home');
    $trail->push('subject selection quiz');
    $trail->push('Interests', route('ssq-interests.index'));
});

// Admin > Subject Selection Quiz > Languages
Breadcrumbs::for('ssq-languages', function (BreadcrumbTrail $trail) {
    $trail->parent('home');
    $trail->push('subject selection quiz');
    $trail->push('Languages', route('ssq-languages.index'));
});

// Admin > Subject Selection Quiz > Jobs
Breadcrumbs::for('ssq-jobs', function (BreadcrumbTrail $trail) {
    $trail->parent('home');
    $trail->push('subject selection quiz');
    $trail->push('Jobs', route('ssq-jobs.index'));
});

// Admin > Subject Selection Quiz > Study Habits
Breadcrumbs::for('ssq-study-habits', function (BreadcrumbTrail $trail) {
    $trail->parent('home');
    $trail->push('subject selection quiz');
    $trail->push('Study Habits', route('ssq-study-habits.index'));
});

// Admin > Subject Selection Quiz > Subject Areas
Breadcrumbs::for('subject-areas', function (BreadcrumbTrail $trail) {
    $trail->parent('home');
    $trail->push('subject selection quiz');
    $trail->push('Subject Areas', route('subject-areas.index'));
});

// Profiler
Breadcrumbs::for('profiler', function (BreadcrumbTrail $trail) {
    $trail->push('Profiler');
});

// Profiler > Units > [unit name]
Breadcrumbs::for('profiler-units', function (BreadcrumbTrail $trail, $unit = '') {
    $trail->parent('profiler');
    $trail->push('units', route('units.index'));
    if ($unit) {
        $trail->push($unit);
    }
});

// Profiler > Groups
Breadcrumbs::for('groups', function (BreadcrumbTrail $trail) {
    $trail->parent('profiler');
    $trail->push('Groups', route('groups.index'));
});

// Profiler > Job Categories
Breadcrumbs::for('jobcategories', function (BreadcrumbTrail $trail) {
    $trail->parent('profiler');
    $trail->push('Job Categories', route('jobcategories.index'));
});

// Profiler > Job Categories > Add
Breadcrumbs::for('jobcategories-add', function (BreadcrumbTrail $trail) {
    $trail->parent('jobcategories');
    $trail->push('Add');
});

// Profiler > Job Categories > Edit > [Job Category name]
Breadcrumbs::for('jobcategories-edit', function (BreadcrumbTrail $trail, $jobcategory = '') {
    $trail->parent('jobcategories');
    $trail->push('Edit');
    if ($jobcategory) {
        $trail->push($jobcategory);
    }
});

// Home > Profiler Nominees
Breadcrumbs::for('profilernominees', function (BreadcrumbTrail $trail) {
    $trail->parent('profiler');
    $trail->push('Nominees');
});

// You > Profiling
Breadcrumbs::for('profiling', function (BreadcrumbTrail $trail) {
    $trail->parent('you');
    $trail->push('Profiling', '/studentmodules/profiler');
});

// Home > E-Magazine
Breadcrumbs::for('magazine', function (BreadcrumbTrail $trail) {
    $trail->parent('home');
    $trail->push('E-magazine');
});
// Explore > E-Magazine Editions
Breadcrumbs::for('magazine-edition', function (BreadcrumbTrail $trail) {
    $trail->parent('explore');
    $trail->push('E-magazines', '/e-magazines/editions');
});

// Explore > Magazine Editions > [Edition name]
Breadcrumbs::for('magazine-view', function (BreadcrumbTrail $trail, $edition = '') {
    $trail->parent('magazine-edition');
    if ($edition) {
        $trail->push($edition);
    }
});

// Home > E-Magazine
Breadcrumbs::for('subject-selection', function (BreadcrumbTrail $trail) {
    $trail->parent('home');
    $trail->push('Subject Selection');
});


// Home > Reports
Breadcrumbs::for('reports', function (BreadcrumbTrail $trail) {
    if (Auth::user()->isAdmin()) {
        $trail->parent('home');
        $trail->push('Group Reports', route('reports.index'));
    } elseif (Auth::user()->isTeacher()) {
        $trail->parent('account');
        $trail->push('Reports', route('reports.index'));
    } else {
        $trail->parent('you');
        $trail->push('Your students reports', route('reports.index'));
    }
});

// Home > Parents
Breadcrumbs::for('parents', function (BreadcrumbTrail $trail) {
    $trail->push('Parents', route('parents.index'));
});

// Home > Parents > Parent Categories
Breadcrumbs::for('parentcategories', function (BreadcrumbTrail $trail) {
    $trail->parent('parents');
    $trail->push('Parent Categories');
});

// Home > Parent > Parent Templates
Breadcrumbs::for('parenttemplates', function (BreadcrumbTrail $trail) {
    $trail->parent('parents');
    $trail->push('Parent Templates', '/parenttemplates');
});

// Home > Parent > Children
Breadcrumbs::for('parentchildren', function (BreadcrumbTrail $trail) {
    $trail->parent('you');
    $trail->push('Your Children', '/parent-children');
});

// Home > Parent > Parent Templates > Add
Breadcrumbs::for('parenttemplates-add', function (BreadcrumbTrail $trail) {
    $trail->parent('parenttemplates');
    $trail->push('Add');
});

// Home > Parent > Parent Templates > [Template Name] > Edit
Breadcrumbs::for('parenttemplates-edit', function (BreadcrumbTrail $trail, $template = "") {
    $trail->parent('parenttemplates');
    if ($template) {
        $trail->push($template);
    }
    $trail->push('Edit');
});

// Things To Know
Breadcrumbs::for('thingstoknow', function (BreadcrumbTrail $trail) {
    $trail->push('Things To Know');
});

// Things To Know > Categories
Breadcrumbs::for('thingstoknow-categories', function (BreadcrumbTrail $trail) {
    $trail->parent('thingstoknow');
    $trail->push('Categories', route('thingstoknow.categories.index'));
});

// Things To Know > Templates
Breadcrumbs::for('thingstoknow-templates', function (BreadcrumbTrail $trail) {
    $trail->parent('thingstoknow');
    $trail->push('Templates', route('thingstoknow.templates.index'));
});

// Things To Know > Templates > Add
Breadcrumbs::for('thingstoknow-template-add', function (BreadcrumbTrail $trail) {
    $trail->parent('thingstoknow-templates');
    $trail->push('Add');
});

// Things To Know > Templates > [Templates Name] > Edit
Breadcrumbs::for('thingstoknow-template-edit', function (BreadcrumbTrail $trail, $template = "") {
    $trail->parent('thingstoknow-templates');
    if ($template) {
        $trail->push($template);
    }
    $trail->push('Edit');
});

// Explore > Thing to know
Breadcrumbs::for('explore-thingstoknow', function (BreadcrumbTrail $trail) {
    $trail->parent('explore');
    if (Auth::user()->isParent()) {
        $trail->push('Things To Know', '/thingstoknow');
    } else {
        $trail->push('Things To Know', '/explorethingstoknow');
    }
});

// Explore > Thing to know > [template->name]
Breadcrumbs::for('explore-thingstoknowdetail', function (BreadcrumbTrail $trail, $template = '') {
    $trail->parent('explore-thingstoknow');
    if ($template) {
        $trail->push($template);
    } else {
        $trail->push('Detail');
    }
});


// Home > Industry Templates ADD/EDIT
// Breadcrumbs::for('industrytemplatesadd', function (BreadcrumbTrail $trail) {
//     $trail->parent('home');
//     $trail->push('Industry Categories', route('industrycategories.index'));
//     $trail->push('Industry Templates', route('industrytemplates.index'));
//     $trail->push('Add', route('industrytemplates.create'));
// });

// Home > Invite Parent
Breadcrumbs::for('invite-parent', function (BreadcrumbTrail $trail) {
    $trail->parent('home');
    $trail->push('Invite Parent');
});

Breadcrumbs::for('jobfinder', function (BreadcrumbTrail $trail) {
    $trail->parent('home');
    $trail->push('Job finder', '/jobjinder');
});

Breadcrumbs::for('seopages', function (BreadcrumbTrail $trail) {
    $trail->parent('extra');
    $trail->push('Seo pages', route('seopages.index'));
});

// Home > Media
Breadcrumbs::for('media', function (BreadcrumbTrail $trail) {
    $trail->parent('extra');
    $trail->push('Media', route('medias.index'));
});

// Home > Media
Breadcrumbs::for('videos', function (BreadcrumbTrail $trail) {
    $trail->parent('extra');
    $trail->push('Videos', route('cloudvideos.index'));
});
// Home > Revolution Slider
Breadcrumbs::for('revolution-sliders', function (BreadcrumbTrail $trail) {
    $trail->parent('extra');
    $trail->push('revolution sliders', route('revslider.index'));
});
// Home > Email Templates
Breadcrumbs::for('email-templates', function (BreadcrumbTrail $trail) {
    $trail->parent('extra');
    $trail->push('Email templates', 'emails');
});

// Home > Articles
Breadcrumbs::for('articles', function (BreadcrumbTrail $trail) {
    $trail->parent('extra');
    $trail->push('Articles', route('articles.index'));
});


// Home > Webinars
Breadcrumbs::for('sessions', function (BreadcrumbTrail $trail) {
    $trail->push('sessions', route('sessions.index'));
});



// Home > Edit Profile
Breadcrumbs::for('edit-profile', function (BreadcrumbTrail $trail, $name = '') {

    // if (Auth::user()->isAdmin()) {
    //     $trail->parent('home');
    // } elseif (Auth::user()->isStudent() || (Auth::user()->isTeacher() && session('studentView'))) {
    //     $trail->parent('you');
    //     $trail->push('Your Game plan', route('gameplan.index'));
    // } else {
    //     $trail->parent('students');
    //     if (!empty($name)) {
    //         $trail->push($name);
    //     }
    //     $trail->push('Game plan');
    // }

    if (!$name) {
        if (Auth::user()->isAdmin()) {
            $trail->parent('home');
            $trail->push('Edit Profile', route('profile-edit'));
        } elseif (Auth::user()->isTeacher() && !session('studentView')) {
            $trail->parent('account');
            $trail->push('Your Profile', route('profile-edit'));
        } else {
            $trail->parent('you');
            $trail->push('Your Account', route('profile-edit'));
        }
    } else {
        // if (Auth::user()->isStudent() || (Auth::user()->isTeacher() && session('studentView'))) {
        //     $trail->parent('you');
        //     $trail->push('Your Game plan', route('gameplan.index'));
        // } else {
        $trail->parent('students');
        if (!empty($name)) {
            $trail->push($name);
        }
        $trail->push('Account');
        // }
    }




    // if (Auth::user()->isTeacher()) {
    //     $trail->push('Your profile', route('profile-edit'));
    // } else {
    //     $trail->push('Edit profile', route('profile-edit'));
    // }
});

// you > Gameplan
Breadcrumbs::for('gameplan', function (BreadcrumbTrail $trail, $name = '') {
    if (Auth::user()->isStudent() || (Auth::user()->isTeacher() && session('studentView'))) {
        $trail->parent('you');
        $trail->push('Your Game plan', route('gameplan.index'));
    } else {
        $trail->parent('students');
        if (!empty($name)) {
            $trail->push($name);
        }
        $trail->push('Game plan');
    }
});

Breadcrumbs::for('studentSubjects', function (BreadcrumbTrail $trail, $name = '') {
    if (Auth::user()->isStudent() || (Auth::user()->isTeacher() && session('studentView'))) {
        $trail->parent('you');
        $trail->push('Your Subjects', route('studentSubjects'));
    } else {
        $trail->parent('students');
        if (!empty($name)) {
            $trail->push($name);
        }
        $trail->push('Subjects');
    }
});

Breadcrumbs::for('studentContent', function (BreadcrumbTrail $trail, $name = '') {
    if (Auth::user()->isStudent() || (Auth::user()->isTeacher() && session('studentView'))) {
        $trail->parent('you');
        $trail->push('Your Content', route('studentContent'));
    } else {
        $trail->parent('students');
        if (!empty($name)) {
            $trail->push($name);
        }
        $trail->push('Content');
    }
});

Breadcrumbs::for('studentCourses', function (BreadcrumbTrail $trail, $name = '') {
    if (Auth::user()->isStudent() || (Auth::user()->isTeacher() && session('studentView'))) {
        $trail->parent('you');
        $trail->push('Your Courses', route('studentCourses'));
    } else {
        $trail->parent('students');
        if (!empty($name)) {
            $trail->push($name);
        }
        $trail->push('Courses');
    }
});
Breadcrumbs::for('studentScholarships', function (BreadcrumbTrail $trail, $name = '') {
    if (Auth::user()->isStudent() || (Auth::user()->isTeacher() && session('studentView'))) {
        $trail->parent('you');
        $trail->push('Your Scholarships', route('studentScholarships'));
    } else {
        $trail->parent('students');
        if (!empty($name)) {
            $trail->push($name);
        }
        $trail->push('Scholarships');
    }
});

// You > Profiling > Result
Breadcrumbs::for('profiling-result', function (BreadcrumbTrail $trail, $name = '') {
    if (Auth::user()->isStudent() || (Auth::user()->isTeacher() && session('studentView'))) {
        $trail->parent('profiling');
        $trail->push('Result');
    } else {
        $trail->parent('students');
        if (!empty($name)) {
            $trail->push($name);
        }
        $trail->push('Profiling Result');
    }
});


Breadcrumbs::for('completeTasks', function (BreadcrumbTrail $trail, $name = '') {
    if (Auth::user()->isStudent() || (Auth::user()->isTeacher() && session('studentView'))) {
        $trail->parent('you');
        $trail->push('Your Tasks', route('completedtasks.index'));
    } else {
        $trail->parent('students');
        if (!empty($name)) {
            $trail->push($name);
        }
        $trail->push('Tasks');
    }
});

Breadcrumbs::for('completedSkillstrainings', function (BreadcrumbTrail $trail, $name = '') {
    if (Auth::user()->isStudent() || (Auth::user()->isTeacher() && session('studentView'))) {
        $trail->parent('you');
        $trail->push('Your Skills Training', route('completedskillstrainings.index'));
    } else {
        $trail->parent('students');
        if (!empty($name)) {
            $trail->push($name);
        }
        $trail->push('Skills Training');
    }
});

Breadcrumbs::for('completedWorkexperience', function (BreadcrumbTrail $trail, $name = '') {
    if (Auth::user()->isStudent() || (Auth::user()->isTeacher() && session('studentView'))) {
        $trail->parent('you');
        $trail->push('Your Workexperience', route('completedWorkexperience.index'));
    } else {
        $trail->parent('students');
        if (!empty($name)) {
            $trail->push($name);
        }
        $trail->push('Workexperience');
    }
});

Breadcrumbs::for('your-resumes', function (BreadcrumbTrail $trail, $name = '') {
    if (Auth::user()->isStudent() || (Auth::user()->isTeacher() && session('studentView'))) {
        $trail->parent('you');
        $trail->push('Your Resumes', route('cvs.index'));
    } else {
        $trail->parent('students');
        if (!empty($name)) {
            $trail->push($name);
        }
        $trail->push('Resumes');
    }
});

// Home > Students > [Student Name] > Timeline
Breadcrumbs::for('students-timeline', function (BreadcrumbTrail $trail, $name = "") {
    if (Auth::user()->isStudent() || (Auth::user()->isTeacher() && session('studentView'))) {
        $trail->parent('you');
        $trail->push('Your Timeline');
    } else {
        $trail->parent('students');
        if (!empty($name)) {
            $trail->push($name);
        }
        $trail->push('Timeline');
    }
});

// Home > Students > [Student Name] > Notes
Breadcrumbs::for('students-notes', function (BreadcrumbTrail $trail, $name = "") {
    if (!session('studentView')) {
        $trail->parent('students');
        if (!empty($name)) {
            $trail->push($name);
        }
        $trail->push('Notes');
    } else {
        $trail->parent('you');
        $trail->push('Your Notes');
    }
});

// Home > Students > [Student Name] > Game Plan
Breadcrumbs::for('student-gameplan', function (BreadcrumbTrail $trail, $name = "") {
    $trail->parent('students');
    if (!empty($name)) {
        $trail->push($name);
    }
    $trail->push('Game Plan');
});


// Home > Students > [Student Name] > Profiling
Breadcrumbs::for('student-profiling', function (BreadcrumbTrail $trail, $name = "") {
    $trail->parent('students');
    if (!empty($name)) {
        $trail->push($name);
    }
    $trail->push('Your Profiling');
});

// Home > Students > [Student Name] > Cvs
Breadcrumbs::for('students-cvs', function (BreadcrumbTrail $trail, $name = "") {
    $trail->parent('students');
    if (!empty($name)) {
        $trail->push($name);
    }
    $trail->push('Resumes');
});
