<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Modules\CourseSetting\Entities\Course;

class TechnologyTool extends Model
{
    use HasFactory;

    /**
     * The attributes that aren't mass assignable.
     *
     * @var array
     */
    protected $guarded = ['id'];

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true;

    public function technologyToolCategory()
    {
        return $this->belongsTo(TechnologyToolCategory::class);
    }

    public function technologyToolExamples()
    {
        return $this->hasMany(TechnologyToolExample::class);
    }
    
}
