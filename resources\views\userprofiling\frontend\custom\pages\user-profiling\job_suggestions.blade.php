@extends('userprofiling.frontend.custom.layouts.main')

@section('pageTitle', 'Job Suggestions')

@section('content')
    <div class="profilling-page">
        @include('userprofiling.frontend.custom.pages.user-profiling.includes.top_navbar', [
            'description' =>
                'Based on your engagement, we think you may be interested in the following jobs. Click to add them to your shortlist.',
            'showSearch' => false,
        ])

        <div class="container-fluid pr-0" id="main-section">
            <div class="row mx-0">
                <div class="col-lg-9 col-md-7 mt-md-3 job-clusters-parent">
                    <div class="job-search-results-parent">
                        <form class="d-flex w-100" method="GET" id="jobSuggestionSearchForm"
                            action="{{ route('user.profiling.job.suggestions') }}">
                            <div class="search-wrapper">
                                <input class="form-control job-search-control" type="text" name="search"
                                    autocomplete="one-time-code" value="" placeholder="Search">
                                <span class="search-icon">
                                    <i class="fa fa-search" aria-hidden="true"></i>
                                </span>
                                <button type="button" class="clear-icon btn btn-link p-0 m-0 border-0 clear-job-results">
                                    <i class="fa fa-times" aria-hidden="true"></i>
                                </button>
                            </div>
                            <div class="job-search-results mt-5 w-100"></div>
                        </form>
                    </div>
                    <div class="job-clusters container-fluid mt-3 px-sm-0">
                        <form action="{{ route('user.profiling.store.userSelectedOccupations') }}" method="POST"
                            id="occupationSelectionForm">
                            @csrf
                            {{-- @foreach ($jobs as $key => $item) --}}
                            <div class="job-cluster">
                                {{-- <h3 id="{{ $key }}">
                                        <a href="#{{ $key }}" class="section-link">#{{ $key }}</a>
                                    </h3> --}}
                                <div class="jobs-chk-btn-group">
                                    @foreach ($jobs as $item)
                                        @include(
                                            'userprofiling.frontend.custom.pages.user-profiling.includes.job_suggestion_badge',
                                            [
                                                'item' => $item,
                                                'isChecked' => $userSelectedOccupations->contains(
                                                    $item->anzsco_occupation_id),
                                                'isSubItem' => false,
                                            ]
                                        )
                                        @php
                                            $subJobs = $item->sub_occupations;
                                        @endphp
                                        @if ($subJobs && is_array($subJobs) && count($subJobs) > 0)
                                            @foreach ($subJobs as $subJobItem)
                                                @if (
                                                    !in_array($subJobItem->anzsco_occupation_id, $jobs->pluck('anzsco_occupation_id')->toArray()) &&
                                                        !in_array($subJobItem->anzsco_occupation_id, $jobsNotInSuggestion->pluck('id')->toArray()))
                                                    @php
                                                        $isSubJobsSelected = $userSelectedOccupations->contains(
                                                            $subJobItem->anzsco_occupation_id,
                                                        );
                                                    @endphp
                                                    @include(
                                                        'userprofiling.frontend.custom.pages.user-profiling.includes.job_suggestion_badge',
                                                        [
                                                            'item' => $item,
                                                            'isChecked' => $isSubJobsSelected,
                                                            'isSubItem' => true,
                                                            'subItem' => $subJobItem,
                                                        ]
                                                    )
                                                @endif
                                            @endforeach
                                        @endif
                                    @endforeach


                                    @foreach ($jobsNotInSuggestion as $item)
                                        {{-- {{ dd($item) }} --}}
                                        @include(
                                            'userprofiling.frontend.custom.pages.user-profiling.includes.job_suggestion_badge',
                                            [
                                                'item' => $item,
                                                'isChecked' => $userSelectedOccupations->contains($item->id),
                                                'isSubItem' => false,
                                            ]
                                        )
                                    @endforeach
                                </div>
                            </div>
                            <hr>
                            <div id="more-jobs">

                            </div>
                            {{-- @endforeach --}}
                        </form>

                        {{-- @for ($i = 1; $i <= 4; $i++)
                            <div class="job-cluster">
                                <h3>
                                    CLUSTER {{ $i }}
                                </h3>

                                <div class="jobs-chk-btn-group">
                                    @for ($j = 1; $j <= 6; $j++)
                                            <input
                                                type="checkbox"
                                                id='chk-{{ $i }}{{ $j }}'
                                                name="selected_jobs[]"
                                                class='custom-chk-btn jobs-chk-btn {{ $j==6 ? ' ml-4' : '' }}'
                                                value="{{ $i }}{{ $j }}"

                                                @if (old('selected_jobs'))
                                                    @if (in_array($i . $j, old('selected_jobs')))
                                                        checked
                                                    @endif
                                                @endif
                                            />
                                            <label for='chk-{{ $i }}{{ $j }}'>
                                                Job Title {{ $i }} - {{ $j }}
                                                @php
                                                    $rangeArr = range(1,6);
                                                    shuffle($rangeArr);
                                                @endphp
                                                @if ($j == $rangeArr[0])
                                                    <i class="fa-solid fa-fire" aria-hidden="true"></i>
                                                @endif
                                            </label>

                                            @if (in_array($j, [1, 4]))
                                                <br>
                                            @endif
                                    @endfor
                                </div>

                            </div>
                            <hr>
                        @endfor --}}
                    </div>
                </div>
                <div class="col-lg-3 col-md-5 profile-preview-col">
                    @include('userprofiling.frontend.custom.pages.user-profiling.includes.profile_preview')
                    <button type="submit" form="occupationSelectionForm" class="btn btn-primary next-btn">Next</button>
                </div>
            </div>

        </div>
    </div>

    @include('userprofiling.frontend.custom.pages.user-profiling.includes.job_info_modal')
@endsection

{{-- Push Styles To Head --}}
@push('styles')
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="{{ asset('custom/css/profiling.css') }}?id={{ uniqid() }}">
    <link rel="stylesheet"
        href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@24,400,0,0" />
    <style>
        .my-path-job-title {
            font-weight: 600;
        }

        @media (max-width: 767px) {
            .owl-carousel {
                padding: 0;
            }
        }

        .myPathCarousel .owl-nav>div {
            height: 36px;
            width: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            position: absolute;
            top: 50%;
            left: 10px;
            transform: translateY(-160%);
            color: #202E3B;
        }

        .myPathCarousel .owl-nav>div:last-child {
            left: auto;
            right: 10px;
        }

        .myPathCarousel .owl-nav>div {
            background: rgba(0, 0, 0, .7098039215686275);
            color: #fff;
        }

        .myPathCarousel .owl-nav>div:hover {
            background-color: #FB1159;
            color: #fff !important;
        }

        .my-path-body .nav-pills,
        .nav-pills .nav-link {
            color: #A4A4A4;
            background-color: unset;
        }

        .my-path-body .nav-pills .nav-link.active,
        .nav-pills .show>.nav-link {
            /* color: #1762D9;
                    background-color: unset; */
            border-radius: 10px;
        }

        .material-symbols-outlined {
            vertical-align: middle;
        }

        @media (min-width: 1080px) {
            .modal-skill-match .modal-dialog {
                max-width: 100%;
                width: 85%;
            }
        }

        .modal-skill-match .modal-content {
            /* border-radius: 25px; */
        }

        .modal-skill-match .modal-body {
            position: relative;
            flex: 1 1 auto;
            padding: 2rem;
        }
    </style>
    </style>
@endpush
{{-- Push Styles To Head ./ --}}


{{-- Push Scripts To Foot of Body --}}
@push('scripts')
    <script src="https://vjs.zencdn.net/8.10.0/video.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jQuery-Knob/1.2.13/jquery.knob.min.js"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script src="https://cdn.amcharts.com/lib/5/index.js"></script>
    <script src="https://cdn.amcharts.com/lib/5/xy.js"></script>
    <script src="https://cdn.amcharts.com/lib/5/hierarchy.js"></script>
    <script src="https://cdn.amcharts.com/lib/5/themes/Animated.js"></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script type="text/javascript" src="{{ asset('custom/js/pathCharts.js') }}?id={{ uniqid() }}"></script>
    <script src="https://cdn.amcharts.com/lib/5/percent.js"></script>



    <script>
        var datasets = [];
        let leftDiv = {}
        let rightDiv = {}
        var profilingData = {
            first_name: "{{ auth()->user()->first_name }}",
            last_name: "{{ auth()->user()->last_name }}",
        };

        document.addEventListener("alpine:init", () => {
            profilingData = Alpine.reactive(profilingData)
            leftDiv.profilingData = rightDiv.profilingData = profilingData


        });

        $(document).on('focus', '.job-search-results-parent input', function() {
            $('.job-search-results').show();
            $('.job-search-results-sub').show();
        });
        $(document).on('click', '.clear-job-results', function() {
            $('.job-search-results').hide();
            $('.job-search-control').val('');
            $('.job-search-control-sub').val('');
            $('.job-search-results').hide('');
            $('.job-search-results-sub').hide('');
            searchJobs();
        });
    </script>
    <script>
        var moreJobs = @json($userSelectedOccupations->pluck('id', 'id')->toArray());

        $(function() {
            appendJobSelected();
            showHideSubJobs();

            $(document).on('change', '.custom-chk-btn', function() {
                appendJobSelected();
                showHideSubJobs();
            });

            function showHideSubJobs() {
                $.each($('.jobs-chk-btn'), function(i, v) {
                    let parentJobId = $(v).val();

                    if ($(v).is(':checked')) {
                        $('.sub-job-badge-' + parentJobId).fadeIn();
                    } else {
                        $('.sub-job-badge-' + parentJobId).fadeOut();
                        $('.sub-job-chk-' + parentJobId).prop('checked', false);
                    }
                });
            }

            $(document).on('click', '.add-job-results', function() {
                if ($('.jobs-chk-btn-searchable:checked').length <= 0) {
                    alert('Select jobs before adding.');
                    return false;
                }
                appendSearchedJobSelected();
                $('.job-search-control').val('');
                $('.job-search-control-sub').val('');
                $('.job-search-results').html('');
                $('.job-search-results-sub').html('');
                $('.job-search-results').hide();
                $('.job-search-results-sub').hide();
            });

            $('.job-search-control').on('input', function() {
                searchJobs();
            });

            $('.job-search-control-sub').on('input', function() {
                searchJobs('-sub');
            });

            $('#jobSuggestionSearchForm').on('submit', function(e) {
                e.preventDefault();
                searchJobs();
            });

            $('#jobSuggestionSearchFormSub').on('submit', function(e) {
                e.preventDefault();
                searchJobs('-sub');
            });

            // Show Job Profile Modal
            $(document).on('click', '.job-info-btn', function() {
                let jobId = $(this).data('id');
                $('#modalJobInfo .modal-title').html(`
                <span class="skeleton-box" style="width:55%;"></span>
            `);
                $('#modalJobInfo .job-description').html(`
                <span class="skeleton-box" style="width:80%;"></span>
                <span class="skeleton-box" style="width:90%;"></span>
            `);
                $('#skillsMatchChart').html("");
                $('#stChart').html("");
                $('#ccChart').html("");
                $('#pills-tt-chart').html("");

                datasets = [];
                getJobInfo(jobId);
                $('#modalJobInfo').modal('show');
            });

            $('a[data-toggle="pill"]').on('shown.bs.tab', function(e) {
                let jobId = "";
                let chartType = e.target.dataset.chartType;

                if (chartType == 'stChart') {
                    stChart(jobId);
                }

                if (chartType == 'ccChart') {
                    ccChart(jobId);
                }

                if (chartType == 'skillsMatchChart') {
                    skillsMatchChart(jobId);
                }
            });
        });

        function appendJobSelected() {
            let html = '';

            $('#jobs-selected').html(html);

            $.each($('.jobs-chk-btn:checked'), function(i, v) {
                html += `
                <label class="preview-selected-job-label">
                 ${$(v).data('title')}
                </label>
            `;
            });

            $('#jobs-selected').html(html);
        }

        function appendSearchedJobSelected() {
            let html = '';

            $.each($('.jobs-chk-btn-searchable:checked'), function(i, v) {
                if (!moreJobs[[v.dataset.id]]) {
                    moreJobs[v.dataset.id] = true;

                    html += `
                    <input
                        type="checkbox"
                        id='chk-${v.dataset.id}-more'
                        name="selected_jobs[]"
                        class='custom-chk-btn jobs-chk-btn'
                        value="${v.dataset.id}"
                        data-title="${v.dataset.title}"
                        checked
                    />
                    <div class="job-sug-badge">
                        <div class="job-sug-badge-header">
                           <div style="margin-right:10px">
                             ${$(v).data('title')}
                           </div>
                         <div>
                          <label for='chk-${v.dataset.id}-more'>
                             <span class="mdi-add-circle px-3 py-1 suggestion-add-btn">
                                +Add 
                            </span> 
                          <span class="material-symbols-outlined mdi-check-circle" title="Added">check_circle</span>
                          </label>
                         </div>
                        </div>
                        <span class="job-info-btn" data-id="${v.dataset.id}" title="info">
                          Learn more →
                        </span>
                    </div>
                `;
                }
            });

            $('#more-jobs').append(html);
            setTimeout(() => {
                appendJobSelected();
            }, 150);
        }

        function searchJobs(sub = '') {
            let search = $('.job-search-control' + sub).val();
            $('.job-search-results' + sub).html('');

            $.ajax({
                type: "GET",
                url: "{{ route('user.profiling.jobSuggestionSearch') }}",
                data: {
                    search: search,
                },
                success: function(response) {
                    $('.job-search-results' + sub).html(response);
                }
            });
        }

        let lastSelectedTab = null;

        // Track tab clicks to update lastSelectedTab
        $(document).on('shown.bs.tab', 'a[data-toggle="pill"]', function(e) {
            lastSelectedTab = $(e.target).attr('href').substring(1);
        });

        function getJobInfo(jobId) {
            if (jobId) {
                let url = "{{ route('userJobInfo', ['anzscoOccupation' => ':jobId']) }}";
                url = url.replace(":jobId", jobId);

                $.ajax({
                    type: "GET",
                    url: url,
                    beforeSend: function() {
                        $('.chart-preloader').show();
                    },
                    complete: function() {
                        $('.chart-preloader').hide();
                    },
                    success: function(response) {
                        if (response.job) {
                            let job = response.job;
                            $('#modalJobInfo .modal-title').html(job.anzsco_title);
                            $('#modalJobInfo .job-description').html(job.anzsco_description);

                            $('#pills-tab .nav-link').removeClass('active').attr('aria-selected', 'false');
                            $('#pills-tabContent .tab-pane').removeClass('show active');


                            if (job.media && job.media !== '') {
                                $('#pills-intro-video-tab').removeClass('d-none');
                                $('#pills-intro-video').removeClass('d-none');

                                if (job.media.includes('wistia.com')) {
                                    let videoId = job.media.split('/')[job.media.split('/').length -
                                    1]; 
                                    $('#introVideo').html(`
                                <iframe src="https://fast.wistia.net/embed/iframe/${videoId}?videoFoam=true" 
                                        title="Wistia video" 
                                        allow="autoplay; fullscreen" 
                                        frameborder="0" 
                                        style="width: 100%; height: 100%;">
                                </iframe>
                            `);
                                } else if (job.media.includes('<iframe')) {
                                    $('#introVideo').html(job.media); 
                                } else {
                                    $('#introVideo').html(`
                                <video controls style="width: 100%; height: 100%;">
                                    <source src="${job.media}" type="video/mp4">
                                </video>
                            `);
                                }
                            } else {
                                $('#pills-intro-video-tab').addClass('d-none');
                                $('#pills-intro-video').addClass('d-none');
                                $('#introVideo').html('');
                            }

                            let activeTabId = lastSelectedTab;
                            if (!activeTabId) {
                                activeTabId = (job.media && job.media !== '') ? 'pills-intro-video' :
                                    'pills-skill-match-chart';
                            } else if (activeTabId === 'pills-intro-video' && (!job.media || job.media ===
                                    '')) {
                                activeTabId = 'pills-skill-match-chart';
                            }

                            // Set the active tab
                            $(`#${activeTabId}`).addClass('show active');
                            $(`#${activeTabId}-tab`).addClass('active').attr('aria-selected', 'true');

                        }

                        if (response?.chartData?.skillsMatchChart) {
                            datasets["skillsMatchChart"] = JSON.parse(response.chartData.skillsMatchChart);
                            skillsMatchChart("");
                        }

                        if (response?.chartData?.ccChart) {
                            datasets["ccChart"] = JSON.parse(response.chartData.ccChart);
                            ccChart("");
                        }

                        if (response?.chartData?.stChart) {
                            datasets["stChart"] = processData(JSON.parse(response.chartData.stChart));
                            stChart("");
                        }

                        if (response?.chartData?.ttChart) {
                            $('#pills-tt-chart').html(response.chartData.ttChart)
                        }
                    }
                });
            }
        }

        $(function() {
            $(document).on('click', '.next-btn', function(e) {
            const hasSelectedJobs = $('.jobs-chk-btn[name="selected_jobs[]"]:checked').length > 0;

            if (!hasSelectedJobs) {
                e.preventDefault();

                if ($('#custom-tooltip').length === 0) {
                    const $btn = $(this);
                    const offset = $btn.offset();
                    const tooltip = $('<div id="custom-tooltip">You must select at least one job to move to the next step.</div>').css({
                        position: 'absolute',
                        top: offset.top - 65,
                        left: offset.left + ($btn.outerWidth() / 2) - 110,
                        background: '#f87171',
                        color: '#fff',
                        padding: '8px 12px',
                        borderRadius: '6px',
                        zIndex: 9999,
                        fontSize: '14px',
                        width: '220px',
                        textAlign: 'center',
                        boxShadow: '0 2px 8px rgba(0,0,0,0.2)'
                    });

                    $('body').append(tooltip);
                    setTimeout(() => {
                        tooltip.fadeOut(300, function() { $(this).remove(); });
                    }, 3000);
                }
            }
        });
        });
    </script>
@endpush
{{-- Push Scripts To Foot of Body ./ --}}
