@extends('layouts.admin', ['noGreyBg' => 'no-grey-bg'])

@section('content')
    <section class="my-path-section">

        <div class="path-title-font">

            <div class="path-list bg-white path-content-p pt-2 pb-1">
                <h5 class="path-title m-0">Paths</h5>

                <span class="path-description path-font-size-s d-flex align-items-center">Home <span
                        class="breadcrumb-border-o mx-2"></span> Paths</span>

            </div>

            <div class=" d-flex justify-content-center">

                @if (session('success'))
                    <div id="success-message" class="text-center p-4 rounded my-4 success-alert w-50">
                        {{ session('success') }}
                    </div>
                @endif
            </div>

            <div class="container-fluid bg-light py-5 path-content-p">
                <div class="row">

                    <!-- Left Side -->
                    <div class="col-12 col-md-6 d-flex align-items-center justify-content-start justify-content-md-start">
                        <h5 class="path-title">Your Paths</h5>

                        <div class="mx-4 mx-md-5 ">

                            <a href="#" class="text-muted small text-decoration-none fw-semibold" data-toggle="modal"
                                data-target="#modalPathsIntro">
                                How Paths Work <i class="fa fa-arrow-right"></i>
                            </a>
                        </div>
                    </div>

                    <div class="col-12 col-md-6 d-flex align-items-center justify-content-start justify-content-md-end">
                        {{-- <div class="mr-3">
                        <div class="dropdown">
                            <button class="path-dropdown-button dropdown-toggle" type="button" data-bs-toggle="dropdown"
                                aria-expanded="false"> <span class=""> Filter By Status </span>
                                <i class="fas fa-chevron-down me-2"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#">All</a></li>
                                <li><a class="dropdown-item" href="#">Active</a></li>
                                <li><a class="dropdown-item" href="#">Completed</a></li>
                            </ul>
                        </div>
                    </div> --}}

                        <a href="{{ route('user.profiling.job.suggestions') }}"
                            class="text-decoration-none text-muted small fw-semibold">
                            <i class="fa fa-pencil"></i> Edit Job Selections
                        </a>
                    </div>

                </div>
            </div>

            @foreach ($selectedJobsWithModules as $selectedJob)
                @if ($selectedJob?->total_modules_count > 0)
                    <div class="path-content-p py-3">
                        <div class="m-3">
                            <h6 class="path-titles">{{ $selectedJob->anzsco_title }}</h6>
                        </div>

                        <div class="card full-width path-card-boarder" data-id="{{ $selectedJob->id }}">
                            <div class="path-border-bottom p-1 ">
                                <div class="row justify-content-between align-items-end mx-2 mr-lg-5">
                                    <div class="col-12 col-md-6 row justify-content-start justify-content-md-start px-1">
                                        @if ($selectedJob->core_competency_modules_counts['total'] > 0)
                                            <div class="mr-3">
                                                <a href="javascript:void(0)"
                                                    class="px-3 px-xl-5 small text-decoration-none skills-modules-link tab-active tab-link"
                                                    data-id="{{ $selectedJob->id }}">
                                                    Skills Modules
                                                </a>
                                            </div>
                                        @endif
                                        @if ($selectedJob->primary_vwe_modules_counts['total'] > 0)
                                            <div class="">
                                                <a href="javascript:void(0)"
                                                    class="px-3 px-xl-5 small text-decoration-none vwe-modules-link tab-link"
                                                    data-id="{{ $selectedJob->id }}">
                                                    Virtual Work Experience 
                                                    @if (!$selectedJob->can_continue_vwe)
                                                        <i class=" fa fa-lock"></i>
                                                    @endif
                                                </a>
                                            </div>
                                        @endif

                                    </div>
                                    <div
                                        class="col-12 col-md-6 d-flex justify-content-start justify-content-md-end my-1 p-0 px-md-auto">
                                        {{-- <button type="button" class="custom-pill-btn mr-2 paths-job-info-btn paths-job-info-skills" data-id="{{ $selectedJob->id }}">Skills match</button> --}}
                                        <button type="button" class="custom-pill-btn paths-job-info-btn mt-3 mt-md-auto"
                                            data-id="{{ $selectedJob->id }}">Job Details & Skills Match</button>
                                    </div>
                                </div>
                            </div>
                            <br>
                            <div class="card-body mx-3">

                                @if ($selectedJob->core_competency_modules_counts['total'] > 0)
                                    <div
                                        class="job-modules-suggestions job-modules-suggestions-skills job-modules-suggestions-skills-{{ $selectedJob->id }}">
                                        <!-- Slider START -->
                                        <div
                                            class="responsive-scroll-{{ $selectedJob->id }} cards-section mypaths-slider mypaths-slider-skills mypaths-slider-skills-{{ $selectedJob->id }} d-none">

                                            {{-- {{ dd( $selectedJob) }} --}}
                                            @foreach ($selectedJob->core_competency_modules as $moduleType => $modulesGroup)
                                                @foreach ($modulesGroup as $module)
                                                    @include('paths.includes._path_module_card', [
                                                        'module' => $module,
                                                        'moduleType' => $moduleType,
                                                    ])
                                                @endforeach
                                            @endforeach
                                        </div>
                                        <!-- Slider END -->
                                        <div class="text-right mt-3 px-3 mypaths-slider-nav">
                                            <i
                                                class="fa fa-chevron-circle-left prev-arrow prev-btn-{{ $selectedJob->id }} me-2 fs-4"></i>
                                            <i
                                                class="fa fa-chevron-circle-right next-arrow next-btn-{{ $selectedJob->id }} fs-4"></i>
                                        </div>
                                    </div>
                                @endif

                                @if ($selectedJob->primary_vwe_modules_counts['total'] > 0)
                                    <div
                                        class="job-modules-suggestions job-modules-suggestions-vwe job-modules-suggestions-vwe-{{ $selectedJob->id }} {{ $selectedJob->core_competency_modules_counts['total'] <= 0 ? '' : 'd-none' }}">
                                        <!-- Slider START -->
                                        <div
                                            class="responsive-scroll-{{ $selectedJob->id }} disabled-cards-section mypaths-slider mypaths-slider-vwe mypaths-slider-vwe-{{ $selectedJob->id }} {{ $selectedJob->core_competency_modules_counts['total'] <= 0 ? '' : 'd-none' }}">

                                            @foreach ($selectedJob->primary_vwe_modules as $moduleType => $modulesGroup)
                                                @foreach ($modulesGroup as $module)
                                                    @include('paths.includes._path_module_card', [
                                                        'module' => $module,
                                                        'moduleType' => $moduleType,
                                                        'disabled' => !$selectedJob->can_continue_vwe,
                                                    ])
                                                @endforeach
                                            @endforeach

                                        </div>
                                        <!-- Slider END -->
                                        <div class="text-right mt-3 px-3 mypaths-slider-nav">
                                            <i
                                                class="fa fa-chevron-circle-left prev-arrow prev-btn-{{ $selectedJob->id }} me-2 fs-4"></i>
                                            <i
                                                class="fa fa-chevron-circle-right next-arrow next-btn-{{ $selectedJob->id }} fs-4"></i>
                                        </div>
                                    </div>
                                @endif

                            </div>
                        </div>

                    </div>
                @endif
            @endforeach

        </div>

        @include('paths.includes._paths_info_modal')
        @include('userprofiling.frontend.custom.pages.user-profiling.includes.job_info_modal')
    </section>
@endsection

@include('paths.includes._job_info_modal_assets')

@push('styles')
    <link class="stylesheet" href="{{ asset('css/paths/slick/slick.css') }}" rel="stylesheet" type="text/css" />
    <link class="stylesheet" href="{{ asset('css/paths/slick/slick-theme.css') }}" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="{{ asset('css/path.css') }}">
@endpush

@push('scripts')
    <script src="{{ asset('css/paths/slick/slick.min.js') }}"></script>
    <script src="{{ asset('assets/js/lms/job-info-modal.js') }}"></script>

    <script>
        $(document).ready(function() {

            setTimeout(function() {
                $('#success-message').fadeOut(500, function() {
                    $(this).remove();
                });
            }, 2000);

            let slickOptions = {
                dots: false,
                speed: 300,
                slidesToShow: 4,
                slidesToScroll: 1,
                infinite: false,
                centerMode: false,
                responsive: [{
                        breakpoint: 1500,
                        settings: {
                            slidesToShow: 3,
                            slidesToScroll: 1,
                            infinite: true,
                            dots: false
                        }
                    },
                    {
                        breakpoint: 1124,
                        settings: {
                            slidesToShow: 2,
                            slidesToScroll: 1,
                            infinite: true,
                            dots: false
                        }
                    },
                    {
                        breakpoint: 1024,
                        settings: {
                            slidesToShow: 2,
                            slidesToScroll: 1,
                            infinite: true,
                            dots: false
                        }
                    },
                    {
                        breakpoint: 600,
                        settings: {
                            slidesToShow: 2,
                            slidesToScroll: 2
                        }
                    },
                    {
                        breakpoint: 480,
                        settings: {
                            slidesToShow: 1,
                            slidesToScroll: 1
                        }
                    }
                ]
            };

            function initSlickSliders(selector = '.mypaths-slider') {
                $(selector).not('.slick-initialized').each(function() {
                    const $slider = $(this);
                    const $wrapper = $slider.closest('.job-modules-suggestions');

                    if (!$wrapper.is(':visible')) {
                        return;
                    }

                    const arrowOptions = {
                        prevArrow: $wrapper.find('.prev-arrow'),
                        nextArrow: $wrapper.find('.next-arrow')
                    };

                    const finalOptions = {
                        ...slickOptions,
                        ...arrowOptions
                    };

                    $slider.on('init', function() {
                        $(this).removeClass('d-none');
                    }).slick(finalOptions);
                });
            }

            initSlickSliders();

            $(document).on('click', '.skills-modules-link', function() {
                const id = $(this).data('id');
                showSkillsModules(id);
            });

            $(document).on('click', '.vwe-modules-link', function() {
                const id = $(this).data('id');
                showVWEModules(id);
            });

            const showSkillsModules = (id) => {
                $(`.job-modules-suggestions-skills-${id}`).toggleClass('d-none', false);
                $(`.job-modules-suggestions-vwe-${id}`).toggleClass('d-none', true);
                initSlickSliders($(`.mypaths-slider-skills-${id}`));

            };

            const showVWEModules = (id) => {
                $(`.job-modules-suggestions-skills-${id}`).toggleClass('d-none', true);
                $(`.job-modules-suggestions-vwe-${id}`).toggleClass('d-none', false);
                initSlickSliders($(`.mypaths-slider-vwe-${id}`));
            };

            $(document).on('click', '.tab-link', function() {
                $('.tab-link').removeClass('tab-active');
                $(this).addClass('tab-active');

                // const id = $(this).data('id');
                // if ($(this).hasClass('skills-modules-link')) {
                //     showSkillsModules(id);
                // } else if ($(this).hasClass('vwe-modules-link')) {
                //     showVWEModules(id);
                // }
            });

        });
    </script>
@endpush
