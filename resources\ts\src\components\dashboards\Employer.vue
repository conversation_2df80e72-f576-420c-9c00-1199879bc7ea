<template>
    <div class="card mt-20 mt-lg-5 mb-6">
        <div class="card-body pt-9 pb-0">
            <div class="d-flex flex-wrap flex-sm-nowrap">
                <div class="me-7 mb-4">
                    <div class="symbol symbol-100px symbol-lg-160px symbol-fixed position-relative">
                        <img :src="companyData?.company_logo ? companyData.company_logo: ''" alt="company-logo">
                    </div>
                </div>
                <div class="flex-grow-1">
                    <div class="d-flex justify-content-between align-items-start flex-wrap mb-2">
                        <div class="d-flex flex-column">
                            <div class="d-flex align-items-center mb-2">
                                <a href="#" class="text-gray-900 text-hover-primary fs-2 fw-bold me-1">
                                    {{ companyData?.company_name ? companyData.company_name : 'Loading...' }}
                                </a>
                                <a href="#"><i class="ki-outline ki-verify fs-1 text-primary"></i></a>
                            </div>
                            <div class="d-flex flex-wrap fw-semibold fs-6 mb-4 pe-2">
                                <a href="#"
                                    class="d-flex align-items-center text-gray-500 me-5 mb-2">
                                    <i class="ki-outline ki-profile-circle fs-4 me-1"></i>
                                    {{ companyData?.company_industry_categories ? companyData.company_industry_categories : 'Categories' }}
                                </a>
                                <a href="#"
                                    class="d-flex align-items-center text-gray-500 me-5 mb-2">
                                    <i class="ki-outline ki-geolocation fs-4 me-1"></i>
                                    {{ companyData?.company_states ? companyData.company_states : 'State' }}
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap flex-stack">
                        <div class="d-flex flex-column flex-grow-1 pe-8">
                            <div class="d-flex flex-wrap">
                                <div
                                    class="border border-gray-300 border-dashed rounded min-w-125px py-3 px-4 me-6 mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="fw-bold counted">{{ companyData?.company_created_at ?? '...' }}</div>
                                    </div>
                                    <div class="fw-semibold fs-6 text-gray-500">Active since</div>
                                </div>
                                <div
                                    class="border border-gray-300 border-dashed rounded min-w-125px py-3 px-4 me-6 mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="fw-bold counted" data-kt-countup="true"
                                            :data-kt-countup-value="companyData?.company_modules_counts" data-kt-initialized="1">{{ companyData?.company_modules_counts ?? '0' }}</div>
                                    </div>
                                    <div class="fw-semibold fs-6 text-gray-500"># of Modules</div>
                                </div>
                                <div
                                    class="border border-gray-300 border-dashed rounded min-w-125px py-3 px-4 me-6 mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="fw-bold counted" data-kt-countup="true"
                                            :data-kt-countup-value="companyData?.pipeline_count" data-kt-initialized="1">{{ companyData?.pipeline_count ?? '0' }}</div>
                                    </div>
                                    <div class="fw-semibold fs-6 text-gray-500"># of students enrolled</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="d-flex flex-column flex-xl-row gap-4">
        <div class="col-12 col-xl-7">
            <div class="row row-cols-1 row-cols-md-2 mb-5">
                <div class="col">
                    <div class="card card-flush h-auto mb-5 ">
                        <div class="card-header pt-5">
                            <div class="card-title d-flex flex-column">
                                <h2>Pipeline</h2>
                                <span class="text-gray-500 pt-1 fw-semibold fs-6">Overview</span>
                            </div>
                        </div>
                        <div class="middle p-10 align-items-center ">
                            <p class="fs-3">{{ companyData?.pipeline_count ?? '...' }}</p>
                            <p class="fs-2">Students in your pipeline</p>
                        </div>
                        <div class="d-flex justify-content-center py-4">
                            <a href="/#/pipeline" target="_blank" class="btn btn-light d-flex align-items-center text-black gap-2 px-20">
                                View Pipeline
                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none"
                                    viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round"
                                        d="M17.25 8.25 21 12m0 0-3.75 3.75M21 12H3" />
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="card card-flush h-auto mb-5 ">
                        <div class="card-header pt-5">
                            <div class="card-title d-flex flex-column">
                                <h2>Pipeline</h2>
                                <span class="text-gray-500 pt-1 fw-semibold fs-6">Insights</span>
                            </div>
                        </div>
                        <div class="middle px-10 pt-8 align-items-center ">
                            <p class="fs-2">Here's the top 3 locations where your Pipeline is based.</p>
                        </div>
                        <div class="d-flex justify-content-center py-2 ">
                            <div class="card-botten  pb-4 d-flex align-items-center">
                                <div class="d-flex flex-center me-5 pt-1">
                                    <div id="kt_apex_pie_chart" style="width: 120px; height: 120px;"></div>
                                </div>
                                <div class="d-flex flex-column content-justify-center w-100">
                                    <div v-for="(loc, i) in dashboard.topLocations" :key="loc.state_code"
                                        class="d-flex fs-6 fw-semibold align-items-center gap-3 my-1">
                                        <div class="bullet w-8px h-6px rounded-2"
                                            :style="{ backgroundColor: ['#5389FB', '#E4E6EF', '#FBB853'][i] }"></div>
                                        <div class="text-gray-500 flex-grow-1">
                                            {{ loc.state_name }} ({{ loc.student_count }})
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="card card-flush h-auto mb-5 ">
                        <div class="card-header pt-5">
                            <div class="card-title d-flex flex-column">
                                <h2>Industries</h2>
                                <span class="text-gray-500 pt-1 fw-semibold fs-6">Latest Trends</span>
                            </div>
                        </div>
                        <div class="middle align-items-center p-6">
                            <div id="kt_apex_curve_chart"></div>
                        </div>
                        <div class="d-flex flex-column gap-1 px-10 pb-6">
                            <div v-for="industry in dashboard.industries" :key="industry"
                                class="d-flex align-items-center gap-3 m-2">
                                <div style="width: 20px; height: 20px;"
                                    :style="{ backgroundColor: industryColors[industry] || '#E4E6EF' }"></div>
                                <span>{{ industry }}</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="card card-flush h-auto mb-5 ">
                        <div class="card-header pt-5">
                            <div class="card-title d-flex flex-column">
                                <h2>Modules</h2>
                                <span class="text-gray-500 pt-1 fw-semibold fs-6">Insights</span>
                            </div>
                        </div>
                        <div class="middle px-10 pt-8  align-items-center ">
                            <p class="fs-2">Your most engaged with modules.</p>
                        </div>
                        <div class="d-flex py-4 px-7">
                            <div class="card-botten pt-2 pb-4 d-flex align-items-center">
                                <div class="d-flex flex-center me-5 pt-2">
                                    <div id="kt_apex_chart" style="width: 120px; height: 120px;"></div>
                                </div>
                                <div class="d-flex flex-column content-justify-center">
                                    <div v-for="(type, i) in dashboard.moduleTypes" :key="type"
                                        class="d-flex fs-6 fw-semibold align-items-center gap-3 w-100 my-1">
                                        <div class="bullet w-8px h-6px rounded-2"
                                            :style="{ backgroundColor: ['#F1416C', '#009EF7', '#E4E6EF'][i] }"></div>
                                        <div class="text-gray-500 flex-grow-1">
                                            {{ type }}
                                            <span
                                                v-if="dashboard.moduleTypeCounts && dashboard.moduleTypeCounts[type] !== undefined">
                                                ({{ dashboard.moduleTypeCounts[type] }})
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-xl-5">
            <div class="card mb-5 mb-xxl-8" style="height: 95%;">
                <div class="card-header align-items-center border-0 mt-4">
                    <h3 class="card-title align-items-start flex-column">
                        <span class="fw-bold mb-2 text-dark">Activities</span>
                        <!-- <span class="text-muted fw-semibold fs-7">890,344 Sales</span> -->
                    </h3>
                </div>
                <div class="card-body pt-5 scrollable-timeline">
                    <div class="timeline-label">
                        <template v-for="student in timelineData" :key="student.student_id">
                            <template v-for="activity in student.activities" :key="activity.id">
                                <div class="timeline-item">
                                    <div class="timeline-label fw-bold text-gray-800 fs-6">
                                        {{ formatToAustralianTime(activity.created_at) }}
                                    </div>
                                    <div class="timeline-badge">
                                        <i class="fa fa-genderless text-primary fs-1"></i>
                                    </div>
                                    <div class="timeline-content text-muted ps-3">
                                        <template
                                            v-if="activity.description === 'created' && activity.properties?.attributes?.data?.finishing_school">
                                            <a :href="`/profiles/edit/${student.student_id}`">
                                                <strong style="color: #0062FF;">{{ student.student_name }}</strong>
                                            </a>
                                            has created
                                            <template v-if="activity.gameplan_id">
                                                <strong> <a
                                                        :href="`/gameplans/${student.student_id}/${activity.gameplan_id}`"
                                                        style="color: #0062FF;  cursor: pointer;"
                                                        target="_blank">gameplan</a></strong>
                                            </template>
                                            <template v-else>
                                                gameplan
                                            </template>
                                        </template>
                                        <template v-else-if="isJson(activity.description)">
                                            <span
                                                v-html="prependStudentNameToHtml(activity.description, student.student_name, student.student_id)"></span>
                                        </template>
                                        <template v-else>
                                            <a :href="`/profiles/edit/${student.student_id}`">
                                                <strong style="color: #0062FF;">{{ student.student_name }}</strong>
                                            </a>: {{ activity.description }}
                                        </template>
                                    </div>
                                </div>
                            </template>
                        </template>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script lang="ts">
import { defineComponent, onMounted, ref, nextTick } from 'vue';
import ApexCharts from 'apexcharts';
import axios from 'axios';

export default defineComponent({
    name: 'MainDashboard',
    setup() {
        const companyData = ref<any>(null);
        const dashboard = ref<any>({
            topLocations: [],
            moduleTypes: ['Skills Training', 'Lessons', 'Virtual Work Experience'],
            moduleTypeCounts: {},
            industries: []
        });
        const industryColors: Record<string, string> = {
            Nature: '#F1416C',
            Science: '#009EF7',
            'Environment and Agriculture': '#50CD89',
        };
        const timelineData = ref<any[]>([]);

        // Helper to check if a string is JSON
        function isJson(str: string) {
            try {
                JSON.parse(str);
                return true;
            } catch {
                return false;
            }
        }
        // Helper to parse and extract HTML from description
        function parseHtmlDescription(desc: string) {
            try {
                const obj = JSON.parse(desc);
                return obj.html || desc;
            } catch {
                return desc;
            }
        }


        function prependStudentNameToHtml(
            desc: string,
            studentName: string,
            studentId: string,
            options: { prependName?: boolean } = { prependName: true }
        ) {
            try {
                const obj = JSON.parse(desc);
                let html = obj.html || desc;

                html = html.replace(/<div class=["']event-date["'][^>]*>[\s\S]*?<\/div>/gi, '');

                html = html.replace(/<small class=["']fs-12 hint-text["'][^>]*>[\s\S]*?<\/small>/gi, '');


                html = html.replace(/<a\b[^>]*>[^<]*see\s*response\.?[^<]*<\/a>/gi, '');

                html = html.replace(/<div[^>]*>\s*<\/div>/gi, '');
                html = html.replace(/<p[^>]*>\s*<\/p>/gi, '');

                html = html.replace(/<div[^>]*>[\s\S]*?see\s*response[\s\S]*?<\/div>/gi, '');
                html = html.replace(/<p[^>]*>[\s\S]*?see\s*response[\s\S]*?<\/p>/gi, '');

                html = html.replace(/^<p>/i, '').replace(/<\/p>$/i, '');
                html = html.replace(/^<div[^>]*>/i, '').replace(/<\/div>$/i, '');

                html = html.replace(/see\s*response\.?/gi, '');

                html = html.replace(
                    /<a ([^>]+)>/gi,
                    `<a $1 style="color: #0062FF; font-weight: bold;">`
                );
                if (options.prependName) {
                    return `<a href="/profiles/edit/${studentId}"><strong style="color: #0062FF;">${studentName}</strong></a> ${html}`;
                } else {
                    return html;
                }
            } catch {
                let html = desc.replace(/<div class=["']event-date["'][^>]*>[\s\S]*?<\/div>/gi, '');
                html = html.replace(/<small class=["']fs-12 hint-text["'][^>]*>[\s\S]*?<\/small>/gi, '');
                html = html.replace(/<a\b[^>]*>[^<]*see\s*response\.?[^<]*<\/a>/gi, '');
                html = html.replace(/<div[^>]*>\s*<\/div>/gi, '');
                html = html.replace(/<p[^>]*>\s*<\/p>/gi, '');
                html = html.replace(/<div[^>]*>[\s\S]*?see\s*response[\s\S]*?<\/div>/gi, '');
                html = html.replace(/<p[^>]*>[\s\S]*?see\s*response[\s\S]*?<\/p>/gi, '');
                html = html.replace(/see\s*response\.?/gi, '');
                html = html.replace(
                    /<a ([^>]+)>/gi,
                    `<a $1 style="color: #0062FF; font-weight: bold;">`
                );
                if (options.prependName) {
                    return `<a href="/profiles/edit/${studentId}"><strong style="color: #0062FF;">${studentName}</strong></a> ${html}`;
                } else {
                    return html;
                }
            }
        }


        const fetchDashboardData = async () => {
            try {
                // Fetch consolidated dashboard data (single request for everything)
                const dashboardRes = await axios.get('/employer/get-dashboard-data');
                const dashboardData = dashboardRes.data.data;
                companyData.value = dashboardData;
                console.log('dashboardData', dashboardData);

                // Update industries data
                dashboard.value.industries = dashboardData.company_industry_categories
                    ? dashboardData.company_industry_categories.split(', ').filter(industry => industry.trim() !== '')
                    : [];

                // Update modules data
                dashboard.value.moduleTypeCounts = dashboardData.company_modules;
                dashboard.value.moduleTypes = Object.keys(dashboardData.company_modules)
                    .sort((a, b) => dashboardData.company_modules[b] - dashboardData.company_modules[a]);
                // Update top locations (now included in consolidated response)
                dashboard.value.topLocations = dashboardData.top_locations || [];

            } catch (error) {
                console.error('Error fetching dashboard data:', error);
            }
        };

        const renderCharts = () => {
            // Donut Chart for Modules
            const donutChartElement = document.querySelector("#kt_apex_chart");
            if (donutChartElement) {
                const donutOptions = {
                    chart: {
                        type: 'donut',
                        height: 120,
                    },
                    series: dashboard.value.moduleTypes.map(type => dashboard.value.moduleTypeCounts[type] || 0),
                    labels: dashboard.value.moduleTypes,
                    colors: dashboard.value.moduleTypes.map((type, i) => ['#F1416C', '#009EF7', '#E4E6EF'][i]),
                    dataLabels: { enabled: false },
                    legend: { show: false },
                    stroke: { width: 0 },
                };
                new ApexCharts(donutChartElement, donutOptions).render();
            }
            // Pie Chart for Top Locations
            const pieChartElement = document.querySelector("#kt_apex_pie_chart");
            if (pieChartElement) {
                const pieOptions = {
                    chart: {
                        type: 'pie',
                        height: 120,
                    },
                    series: dashboard.value.topLocations.map(loc => loc.student_count),
                    labels: dashboard.value.topLocations.map(loc => loc.state_name),
                    colors: ['#5389FB', '#E4E6EF', '#FBB853'],
                    dataLabels: { enabled: false },
                    legend: { show: false },
                    stroke: { width: 0 },
                };
                new ApexCharts(pieChartElement, pieOptions).render();
            }
            // Restore previous curve chart style
            const curveChartElement = document.querySelector("#kt_apex_curve_chart");
            if (curveChartElement) {
                const curveOptions = {
                    chart: {
                        type: 'area', // Use 'area' for line with fill
                        height: 130,
                        toolbar: { show: false }
                    },
                    series: [{
                        name: 'Sales',
                        data: [40, 100, 40, 100, 40, 100, 40]
                    }],
                    stroke: {
                        curve: 'smooth',
                        width: 3,
                        colors: ['#009EF7'],
                    },
                    fill: {
                        type: 'solid',
                        colors: ['#009EF7'],
                        opacity: 0.15 // Adjust for shadow/softness
                    },
                    xaxis: {
                        labels: { show: false },
                        axisBorder: { show: false },
                        axisTicks: { show: false }
                    },
                    yaxis: {
                        labels: { show: false },
                        axisBorder: { show: false },
                        axisTicks: { show: false }
                    },
                    colors: ['#009EF7'],
                    dataLabels: { enabled: false },
                    // grid: { strokeDashArray: 4 },
                    grid: { show: false }
                };
                new ApexCharts(curveChartElement, curveOptions).render();
            }
        };

        const fetchPipelineActivities = async () => {
            try {
                const res = await axios.get('/employer/pipeline-activities');
                timelineData.value = res.data.timeline || [];
            } catch (error) {
                console.error('Error fetching pipeline activities:', error);
                timelineData.value = [];
            }
        };

        const formatToAustralianTime = (dateString: string) => {
            return new Date(dateString).toLocaleTimeString('en-AU', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: true,
                timeZone: 'Australia/Sydney'
            });
        }

        onMounted(async () => {
            await fetchDashboardData();
            await nextTick();
            renderCharts();
            await fetchPipelineActivities();
        });

        return {
            companyData,
            dashboard,
            industryColors,
            timelineData,
            isJson,
            parseHtmlDescription,
            prependStudentNameToHtml,
            formatToAustralianTime
        };
    }
});
</script>

<style>
.app-content {
    padding-top: 0px;
    padding-bottom: 0px;
}

.primaryTeacher {
    margin-left: -30px;
    margin-right: -30px;
}

.video-wrapper {
    height: calc(100vh - 117px);
    background-color: #ddd;
    position: relative;
    overflow: hidden;

}

.video-wrapper iframe {
    width: 100vw !important;
    height: 56.26vw;
    /* Given a 16:9 aspect ratio, 9/16*100 = 56.25 */
    min-height: 100vh;
    min-width: 177.77vh;
    /* Given a 16:9 aspect ratio, 16/9*100 = 177.77 */
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}


.content-box {
    margin: 0;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.scrollable-timeline {
    height: 100%;
    max-height: 480px;
    /* adjust as needed to match your previous look */
    overflow-y: auto;
    padding-right: 8px;
    /* optional, for scrollbar space */
}

@media (max-width : 991px) {
    .video-wrapper {
        height: calc(100vh - 128px);
    }
}


@media (max-width : 575px) {
    .video-wrapper {
        height: calc(100vh - 135px);
    }
}

/* Responsive ApexChart container */
.responsive-apexchart {
    width: 100%;
    min-width: 0;
    overflow-x: auto;
}

@media (max-width: 1400px) {
    #kt_card_widget_12_chart {
        height: 100px !important;
        min-height: 100px !important;
    }

    .responsive-apexchart svg {
        height: 100px !important;
    }
}

@media (max-width: 991px) {
    #kt_card_widget_12_chart {
        height: 80px !important;
        min-height: 80px !important;
    }

    .responsive-apexchart svg {
        height: 80px !important;
    }
}

@media (max-width: 575px) {
    #kt_card_widget_12_chart {
        height: 60px !important;
        min-height: 60px !important;
    }

    .responsive-apexchart svg {
        height: 60px !important;
    }
}
</style>