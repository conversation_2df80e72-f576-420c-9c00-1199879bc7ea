@extends('layouts.admin', ['noGreyBg' => 'no-grey-bg'])
@section('pageTitle', 'Students')
@section('breadcrumbs', Breadcrumbs::render('students'))
@section('content')
    <style>
        #students-table th {
            font-family: '<PERSON>', sans-serif !important;
            letter-spacing: 2px;
            font-size: 16px;
            color: #000;
        }

        #students-table .fa.fa-eye {
            color: #000;
        }

        .search-bar {
            border: 2px solid !important;
        }

        .bottom-btns .buttons-csv {
            display: none;
        }

        div.dt-button-background {
            z-index: 0 !important;
        }

        .dropdown-menu>.dropdown-item:hover,
        .dropdown-item:active {
            background-color: #000000 !important;
            color: white !important;
        }
    </style>
    @if (session()->has('message'))
        <div class="alert alert-success text-center">
            <a href="#" class="close" data-dismiss="alert" aria-label="close"> </a> {{ session()->get('message') }}
        </div>
    @endif
    <div class="row">
        <div class="col-lg-12">
            <div class="card card-transparent">
                <div class="card-header">
                    <img src="{{ asset('images/favicon.png') }}" alt="*" class="card-title-X">
                    <div class="card-title custom-card-title">Students</div>
                </div>

            </div>
            <div class="card card-default search-bar">
                <div class="card-header">
                    <div class="card-title oswald fs-16 uppercase ls-2">
                        Search
                    </div>
                </div>
                <div class="card-block pt-0">
                    <div class="row clearfix">
                        <div class="col-md-3">
                            <div class="form-group form-group-default">
                                <label>Student's name</label>
                                <input type="text" class="form-control" name="fullname" />
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group form-group-default form-group-default-select2">
                                <label>Gender</label>
                                <select class="full-width" name="gender" data-init-plugin="select2">
                                    <option value="" selected>Select...</option>
                                    <option value="M">Male</option>
                                    <option value="F">Female</option>
                                    <option value="Other">Other</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group form-group-default form-group-default-select2">
                                <label>Status</label>
                                <select class="full-width" name="status" id="status">
                                    <option value="active" selected="selected">Active</option>
                                    <option value="inactive" @if (old('status') == 'inactive') selected="selected" @endif title="‘Inactive’ means any student who is not currently covered by your subscription.">Inactive</option>
                                    <option value="graduated" @if (old('status') == 'graduated') selected="selected" @endif title="‘Graduated’ means any student who has finished school. These students do not count towards your subscription.">Graduated</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3" id="school_year">
                            <div class="form-group form-group-default form-group-default-select2">
                                <label>Year</label>
                                <select class="full-width" name="year" data-init-plugin="select2">
                                    <option value="" selected>Any</option>
                                    @foreach ($years as $key => $year)
                                        <option value="{{ $year->id }}">{{ $year->title }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3" id="graduate_year">
                            <div class="form-group form-group-default form-group-default-select2">
                                <label>Graduated Year</label>
                                <select class="full-width" name="graduate_year" data-init-plugin="select2">
                                    <option value="" selected>Any</option>
                                    @for ($i = 2018; $i <= date('Y'); $i++)
                                        <option @if (old('graduate_year') == $i) selected @endif>{{ $i }}</option>
                                    @endfor
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row clearfix">
                        <div class="col-md-3" id="graduate_year">
                            <div class="form-group form-group-default form-group-default-select2">
                                <label>Profiling</label>
                                <select class="full-width" name="profiler" data-init-plugin="select2">
                                    <option value="" selected>Any</option>
                                    <option value="completed" @if (old('profiler') == 'completed') selected="selected" @endif>Completed</option>
                                    <option value="incomplete" @if (old('profiler') == 'incomplete') selected="selected" @endif>Incomplete</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3" id="graduate_year">
                            <div class="form-group form-group-default form-group-default-select2">
                                <label>Registration</label>
                                <select class="full-width" name="registration" data-init-plugin="select2">
                                    <option value="" selected>Any</option>
                                    <option value="completed" @if (old('profiler') == 'completed') selected="selected" @endif>Completed</option>
                                    <option value="incomplete" @if (old('profiler') == 'incomplete') selected="selected" @endif>Incomplete</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group form-group-default">
                                <label>Student's email</label>
                                <input type="text" class="form-control" name="email" />
                            </div>
                        </div>
                        <div class="col-md-3">
                            @if ($campuses->count() > 1)
                                <div class="form-group form-group-default form-group-default-select2">
                                    <label>Campus</label>
                                    <select class="full-width" name="campus" data-init-plugin="select2">
                                        <option value="" selected>Any</option>
                                        @foreach ($campuses as $campus)
                                            <option value="{{ $campus->id }}">{{ $campus->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            @elseif($campuses->count())
                                <div class="form-group form-group-default disabled">
                                    <label>Campus</label>
                                    <input type="text" class="form-control" value="{{ $campuses->first()->name }}" disabled />
                                </div>
                            @endif
                        </div>
                        <div class="col-md-12 text-right">
                            <button class="btn btn-primary mt-3" id="btnSearch" type="button">Search</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12">
            <div class="card card-transparent">
                <div class="card-header">
                    <div class="card-controls" id="funcButtons">
                        {{-- <button data-target="#modalStudentAdd" data-toggle="modal" id="btnFillSizeToggler" class="btn btn-primary btn-cons">
                        <i class="fa fa-plus"></i> Add Student
                    </button> --}}
                        <a class="btn btn-primary d-none" id="exportStudents" href="#">Export All <i class="fa fa-download"></i></a>
                    </div>
                </div>
                <div class="card-block pt-0">
                    <form method="POST" action="{{ url('students/bulkAction') }}" id="formTable" onkeydown="return event.key != 'Enter';">
                        @csrf
                        @include('students.modals.bulkAction')
                        <table class="table table-hover custom-datatable no-footer mt-0" id="students-table">
                            <thead>
                                <tr>
                                    <th>
                                        <div class="checkbox check-primary">
                                            <input type="checkbox" id="checkAll">
                                            <label class="fs-16" for="checkAll">Select</label>
                                        </div>
                                    </th>
                                    <th>First Name</th>
                                    <th>Last Name</th>
                                    <th>Email</th>
                                    <th>Year</th>
                                    <th>Graduated year</th>
                                    {{-- <th>Campus</th> --}}
                                    <th>Gender</th>
                                    {{-- <th>Account Created</th> --}}
                                    {{-- <th>Profiling</th> --}}
                                    <th>Registered</th>
                                    <th>Profiling</th>
                                    <th>Profile</th>
                                    {{-- <th class="dontexport">Game Plan</th>
                                <th class="dontexport">Timeline</th>
                                <th class="dontexport">Notes</th>
                                <th class="dontexport">CVs</th> --}}
                                    {{-- <th>Last seen</th> --}}
                                    {{-- <th>Login count</th> --}}
                                    {{-- <th>Avg login duration</th> --}}
                                    {{-- <th>Avg login duration (in sec)</th> --}}
                                    @if (Auth::user()->isTeacher())
                                        <th>Action</th>
                                    @endif
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </form>
                </div>
            </div>
        </div>
    </div>
    @push('modals')
        @if (!Auth::user()->isStaff())
            @include('students.modals.editteacherstudent')
            @include('students.modals.addteacherstudent')
            @include('students.modals.import')
        @endif
    @endpush
    @push('scripts')
        <script>
            var oTable = $('#students-table').DataTable({
                processing: true,
                stateSave: true,
                serverSide: true,
                searching: false,
                //dom: 'Bfrtip',
                dom: "<'row'<'col-md-12'Bl>>" + "<'checkError text-danger'>" + "<'table-responsive'rt><'checkError text-danger mt-2'><'mt-2 bottom-btns'B><'row'<'col-md-12'pi>>",
                "autoWidth": false,
                "order": [
                    [1, "asc"]
                ],
                "lengthMenu": [
                    [10, 25, 50, 100 /* , -1 */ ],
                    [10, 25, 50, 100 /* , "All" */ ] // Not able to handle the request showing All students at once.
                ],
                buttons: [

                    // {
                    //     text: '<i class="fa fa-upload"></i> Bulk import',
                    //     className: 'btn btn-primary btn-cons bulkImport',
                    // },

                    {
                        extend: 'collection',
                        text: 'Add <i class="fa fa-plus">',
                        className: 'btn btn-primary btn-cons',
                        buttons: [{
                                text: 'Add Individual Student',
                                className: 'bulkImport',
                                action: function(e, dt, node, config) {
                                    $('#modalTeacherStudentAdd').modal('show');
                                }
                            },
                            {
                                text: 'Import Group of Students',
                                className: 'bulkImport',
                                action: function(e, dt, node, config) {
                                    $('#bulkImport').modal('show');
                                }
                            }
                        ]
                    },
                    {
                        text: 'Export All <i class="fa fa-download"></i>',
                        className: 'btn btn-primary btn-cons',
                        action: function(e, dt, node, config) {
                            jQuery("#exportStudents")[0].click();
                        }
                    },
                    {
                        text: 'Bulk Action',
                        className: 'btn btn-primary btn-cons bulk-action',
                    }
                ],
                ajax: {
                    url: 'students/getdata',
                    beforeSend: function(request) {
                        request.setRequestHeader("X-CSRF-TOKEN", $('meta[name="csrf-token"]').attr('content'));
                    },
                    "type": "POST",
                    data: function(d) {
                        d.name = $('input[name=fullname]').val();
                        // d.email = $('input[name=email]').val();
                        d.year = $('select[name=year]').val();
                        d.gender = $('select[name=gender]').val();
                        d.campus = $('select[name=campus]').val();
                        d.status = $('select[name=status]').val();
                        d.profiler = $('select[name=profiler]').val();
                        d.registration = $('select[name=registration]').val();
                        d.email = $('input[name=email]').val();
                        d.graduate_year = $('select[name=graduate_year]').val();
                        d.active_inactive = $('select[name=active_inactive]').val();
                        return d;
                    }
                },

                columns: [{
                        data: 'select',
                        name: 'select',
                        orderable: false,
                    },
                    {
                        data: 'firstname',
                        name: 'firstname'
                    },
                    {
                        data: 'lastname',
                        name: 'lastname'
                    },
                    {
                        data: 'email',
                        name: 'email'
                    },
                    {
                        data: 'year_title',
                        name: 'standard_id',
                    },
                    {
                        data: 'graduate_year',
                        name: 'graduate_year',
                    },
                    // {
                    //     data: 'schoolCampus',
                    //     name: 'schoolCampus',
                    //     visible: @json($campuses->count() > 0),
                    //     orderable: false,
                    // },
                    {
                        data: 'gender',
                        name: 'gender'
                    },
                    // {
                    //     data: 'profiling',
                    //     name: 'profiling',
                    //     searchable: false,
                    //     orderable: false,
                    //     className: "text-center",
                    //     visible: function () {
                    //         console.log(jQuery('#status').val());
                    //         return jQuery('#status').val() != 'inactive'
                    //     }
                    // },
                    {
                        data: 'p_accountcreated',
                        name: 'p_accountcreated',
                        searchable: false,
                        orderable: false,
                        className: "text-center",
                    },
                    {
                        data: 'profiling',
                        name: 'profiling',
                        searchable: false,
                        orderable: false,
                        className: "text-center",
                    },
                    {
                        data: 'profile',
                        name: 'profile',
                        searchable: false,
                        orderable: false,
                        className: "text-center",
                    },
                    // {
                    //     data: 'gameplan',
                    //     name: 'gameplan',
                    //     searchable: false,
                    //     orderable: false,
                    //     className: "text-center",
                    // },
                    // {
                    //     data: 'timeline',
                    //     name: 'timeline',
                    //     searchable: false,
                    //     orderable: false,
                    //     className: "text-center",
                    // },
                    // {
                    //     data: 'notes',
                    //     name: 'notes',
                    //     searchable: false,
                    //     orderable: false,
                    //     className: "text-center",
                    // },
                    // {
                    //     data: 'cvs',
                    //     name: 'cvs',
                    //     searchable: false,
                    //     orderable: false,
                    //     className: "text-center",
                    // },
                    // {
                    //     data: 'lastseen',
                    //     name: 'lastseen',
                    //     searchable: false,
                    // },
                    // {
                    //     data: 'logincount',
                    //     name: 'logincount',
                    //     searchable: false,
                    // },
                    // {
                    //     data: 'avgduration',
                    //     name: 'avgduration',
                    //     searchable: false,
                    // },
                    // {
                    //     data: 'avgdurationsec',
                    //     name: 'avgdurationsec',
                    //     searchable: false,
                    //     visible: false,
                    // },
                    {
                        data: 'action',
                        name: 'action',
                        searchable: false,
                        visible: @json(Auth::user()->isTeacher())
                    },
                ],
                "drawCallback": function(settings) {
                    var api = this.api();
                    // console.log( api.column(3).data() );

                    if (jQuery('#status').val() == 'graduated') {
                        api.column(4).visible(false)
                        api.column(5).visible(true)
                        // api.column(12).visible(false)
                    } else {
                        api.column(4).visible(true)
                        api.column(5).visible(false)
                        // api.column(12).visible(true)
                    }

                    // Export Students With Filters Value
                    name = $('input[name=fullname]').val();
                    year = $('select[name=year]').val();
                    gender = $('select[name=gender]').val();
                    @if (Auth::user()->school->campuses()->count() > 1)
                        campus = $('select[name=campus]').val();
                    @else
                        campus = '';
                    @endif
                    status = $('select[name=status]').val();
                    profiler = $('select[name=profiler]').val();
                    registration = $('select[name=registration]').val();
                    email = $('input[name=email]').val();
                    graduate_year = $('select[name=graduate_year]').val();

                    jQuery("#exportStudents").attr("href", "/student/export?fullname=" + name + "&year=" + year + "&gender=" + gender + "&campus=" + campus + "&status=" + status + "&profiler=" + profiler + "&registration=" + registration + "&email=" + email + "&graduate_year=" + graduate_year + "&status=" + status + "");

                    laravel.initialize();
                }
            });
            // new $.fn.dataTable.Buttons(oTable, {
            //     buttons: [{
            //         extend: 'csv',
            //         text: '<i class="fa fa-download"></i> CSV',
            //         className: 'btn btn-primary btn-cons',
            //         exportOptions: {
            //             columns: 'th:not(:last-child)'
            //         }
            //     }],
            // });
            // oTable.buttons().container().appendTo('#funcButtons');

            jQuery(document).on('change', "#checkAll", function() {
                jQuery('input[name="ids[]"]:checkbox').prop('checked', this.checked);
            });


            jQuery('select[name="withselected"]').on('change', function() {
                jQuery("#yearChange").hide();
                jQuery("#active_inactive").hide();
                jQuery("#delete_nominee").hide();
                jQuery("#bulk_graduated_year").hide();
                jQuery(".inactive-message").hide();
                if (this.value == 'Make Active/Inactive') {
                    jQuery("#active_inactive").show();

                } else if (this.value == 'Change year') {
                    jQuery("#yearChange").show();
                    if (jQuery('#bulk_year').val() == 7) {
                        jQuery("#bulk_graduated_year").show();
                    }
                } else if (this.value == 'Reset Profiling') {
                    jQuery("#delete_nominee").show();
                }
            });

            $('input[type=radio][name=remove]').change(function() {
                if(this.value == 1) {
                    console.log('gggg');
                jQuery(".inactive-message").show();
                }else{
                    jQuery(".inactive-message").hide();
                }
            });

            jQuery('#bulk_year').on('change', function() {
                if (this.value == 7) {
                    jQuery("#bulk_graduated_year").show();
                } else {
                    jQuery("#bulk_graduated_year").hide();
                }
            });


            jQuery('#formTable').validate({
                errorPlacement: function(error, element) {
                    if (element.parent('.input-group').length || element.prop('type') === 'checkbox' || element.prop('type') === 'radio') {
                        if (element.attr('name') == 'ids[]') {
                            error.appendTo(jQuery('#checkerror'))
                        } else {
                            error.insertAfter(element.parent());
                        }
                    } else if (element.hasClass('select2-hidden-accessible')) {
                        // error.insertAfter(element.next('span'));
                        element.closest('.form-group').append(error);

                    } else {
                        // else just place the validation message immediatly after the input
                        error.insertAfter(element.parent());
                    }
                },
                focusInvalid: false,
                rules: {
                    'ids[]': 'required',
                    withselected: 'required',
                    standard: {
                        required: function() {
                            return (jQuery('select[name=withselected]').val() == 'Change year');
                        }
                    },
                    bulk_graduated_year: {
                        required: function() {
                            return (jQuery('#bulk_year').val() == 7);
                        }
                    },
                    nominees: {
                        required: function() {
                            return (jQuery('select[name=withselected]').val() == 'Reset Profiling');
                        }
                    },
                },
                messages: {
                    'ids[]': {
                        required: 'Please select atleast one student.'
                    }
                },
                // submitHandler: function(form, event) {
                //     event.preventDefault();
                //     if (!confirm("Do you really want to do this?")) {
                //         return false;
                //     }
                //     form.submit();
                // }
            });

            jQuery("#school_year").show();
            jQuery("#graduate_year").hide();

            // jQuery("#bulk_graduated_year").hide();

            jQuery('#status').on('change', function() {
                if (this.value == 'graduated') {
                    jQuery("#graduate_year").show();
                    jQuery("#school_year").hide();
                } else {
                    jQuery("#school_year").show();
                    jQuery("#graduate_year").hide();
                }
            });

            $("#status").select2({
                escapeMarkup: function(markup) {
                    return markup;
                },
            });

            $('#btnSearch').on('click', function(e) {
                oTable.draw();
                e.preventDefault();
            });

            jQuery(".bulk-action").click(function() {
                if (jQuery('input[name="ids[]"]:checked').length > 0) {
                    jQuery('#bulkAction').modal('show');
                } else {
                    jQuery('.checkError').text('Please select at least one student.');
                }
            });

            jQuery(".bulkImport").click(function() {
                jQuery('#bulkImport').modal('show');
            });

            jQuery(".studentAdd").click(function() {
                jQuery('#modalTeacherStudentAdd').modal('show');
            });
        </script>
    @endpush
@endsection
