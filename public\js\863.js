/*! For license information please see 863.js.LICENSE.txt */
(self.webpackChunk=self.webpackChunk||[]).push([[863],{3368:(e,t,n)=>{"use strict";n.d(t,{Z:()=>i});var o=n(1519),a=n.n(o)()((function(e){return e[1]}));a.push([e.id,".mw-900px{max-width:900px}.animated-video>iframe{height:100%!important;width:100%!important}",""]);const i=a},6857:(e,t,n)=>{"use strict";n.d(t,{Z:()=>i});var o=n(1519),a=n.n(o)()((function(e){return e[1]}));a.push([e.id,".mw-900px[data-v-42f43f73]{max-width:900px}.w-90[data-v-42f43f73]{width:90%}",""]);const i=a},5021:(e,t,n)=>{"use strict";n.d(t,{Z:()=>i});var o=n(1519),a=n.n(o)()((function(e){return e[1]}));a.push([e.id,"#audio{overflow:hidden;transition:height .5s ease}.new-cards li{padding:0 15px;width:220px}.profile-cards{margin:0 -5px}.list-inline{list-style:none;padding-left:0}.new-cards li,.profile-cards li,.template-tiles li{vertical-align:top}.profile-cards li{display:inline-table;margin-top:10px;width:200px}.hover-colored .percentage{overflow:hidden;position:relative}.profile-cards .percentage{background-position:50%;background-repeat:no-repeat;background-size:100%;color:#fff;height:190px;line-height:190px;transition:all .2s ease}.img-fluid{max-width:100%}.hover-colored .percentage>:not(.tile-label){left:50%;position:absolute;top:50%;transform:translate(-50%,-50%);-webkit-transform:translate(-50%,-50%);-moz-transform:translate(-50%,-50%)}.blue-check{line-height:26px}.bg-blue{background-color:#0a0afd!important}.blue-check{height:25px;line-height:26px!important;width:25px}.profile-cards .topic{font-weight:700;line-height:15px;margin:10px 0}.text-master{color:#000!important}.swal2-popup{border-radius:0}.wrap{max-width:75ch;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.banner_detail_box{left:20%;position:absolute;top:50%;transform:translate(-50%,-50%)}.modal-backdrop{opacity:.8!important}#kt_modal_trailer .modal-content{background-color:transparent}.sticky-bottom{z-index:auto}.fa-heart:hover{font-weight:900!important}.btn-black-custom,.btn-white-custom{height:55px;line-height:55px;padding:0!important}.btn-black-custom>i,.btn-white-custom>i{font-size:30px;padding-right:0;vertical-align:-9px}.btn-black-custom>img,.btn-white-custom>img{margin-left:5px;vertical-align:-8px;width:29px}.btn-black-custom:hover,.btn-white-custom{background-color:#fff!important;color:#000!important}.btn-black-custom,.btn-white-custom:hover{background-color:#000!important;color:#fff!important}.btn-black-custom:hover *,.btn-white-custom *{color:#000!important}.btn-black-custom *,.btn-white-custom:hover *{color:#fff!important}.btn-black-custom:hover>.white-icon{display:none}.btn-black-custom:hover>.black-icon{display:inline!important}.pointer{cursor:pointer}.related-overlay{height:0;overflow:overlay;transition:height .3s}.slide-up{height:calc(100vh - 320px)!important}.related{right:5%!important}.related-tile-content>p:first-child{flex:75%}.banner{background-color:#000;background-position:50%;background-repeat:no-repeat;background-size:cover;display:block;min-height:calc(56.25vw - 149px);overflow:hidden;position:relative}.banner-video{height:100%}.banner-video>video{left:50%;position:absolute;top:50%;transform:translate(-50%,-50%);width:101%!important}.bg-dark,.black-strip,.full-view-banner{margin-left:-30px;margin-right:-30px}.bg-dark{background:#000!important}div#kt_app_content{padding-bottom:0;padding-top:0}.mw-1200px{max-width:1200px}@media (max-width:1280px){.banner{height:56.25vw}.banner_detail_box{left:40%}.banner-video>video{height:100%!important;width:calc(65vw + 65vh)!important}}@media (max-width:991px){.black-strip,.full-view-banner{margin-left:-20px;margin-right:-20px}.full-view-banner{margin-top:58.16px}}@media (max-width:991px) and (min-width:768px) and (orientation:portrait){.slide-up{height:calc(100vw - 220px)!important}.banner{height:86.25vw}.banner-video>video{height:100%!important;width:calc(66vw + 66vh)!important}}@media (max-width:991px) and (orientation:landscape){.banner-video>video{height:auto!important;width:calc(70vw + 70vh)!important}}@media (max-width:767px){.banner{height:calc(100vh - 300px)}.banner_detail_box{left:50%}}@media (max-width:575px){div#kt_app_content{padding-top:30px}.banner_detail_box{width:70vw!important}.banner{height:calc(100vh - 242px)}.banner-video>video{height:100%!important;width:calc(90vw + 90vh)!important}.full-view-banner{margin-top:0}}",""]);const i=a},48542:function(e){e.exports=function(){"use strict";var e={awaitingPromise:new WeakMap,promise:new WeakMap,innerParams:new WeakMap,domCache:new WeakMap};const t=e=>{const t={};for(const n in e)t[e[n]]="swal2-"+e[n];return t},n=t(["container","shown","height-auto","iosfix","popup","modal","no-backdrop","no-transition","toast","toast-shown","show","hide","close","title","html-container","actions","confirm","deny","cancel","default-outline","footer","icon","icon-content","image","input","file","range","select","radio","checkbox","label","textarea","inputerror","input-label","validation-message","progress-steps","active-progress-step","progress-step","progress-step-line","loader","loading","styled","top","top-start","top-end","top-left","top-right","center","center-start","center-end","center-left","center-right","bottom","bottom-start","bottom-end","bottom-left","bottom-right","grow-row","grow-column","grow-fullscreen","rtl","timer-progress-bar","timer-progress-bar-container","scrollbar-measure","icon-success","icon-warning","icon-info","icon-question","icon-error"]),o=t(["success","warning","info","question","error"]),a="SweetAlert2:",i=e=>e.charAt(0).toUpperCase()+e.slice(1),r=e=>{console.warn(`${a} ${"object"==typeof e?e.join(" "):e}`)},l=e=>{console.error(`${a} ${e}`)},s=[],c=(e,t)=>{var n;n=`"${e}" is deprecated and will be removed in the next major release. Please use "${t}" instead.`,s.includes(n)||(s.push(n),r(n))},d=e=>"function"==typeof e?e():e,u=e=>e&&"function"==typeof e.toPromise,m=e=>u(e)?e.toPromise():Promise.resolve(e),p=e=>e&&Promise.resolve(e)===e,g=()=>document.body.querySelector(`.${n.container}`),h=e=>{const t=g();return t?t.querySelector(e):null},f=e=>h(`.${e}`),v=()=>f(n.popup),b=()=>f(n.icon),y=()=>f(n.title),w=()=>f(n["html-container"]),k=()=>f(n.image),E=()=>f(n["progress-steps"]),x=()=>f(n["validation-message"]),C=()=>h(`.${n.actions} .${n.confirm}`),B=()=>h(`.${n.actions} .${n.cancel}`),V=()=>h(`.${n.actions} .${n.deny}`),N=()=>h(`.${n.loader}`),A=()=>f(n.actions),_=()=>f(n.footer),T=()=>f(n["timer-progress-bar"]),S=()=>f(n.close),L=()=>{const e=Array.from(v().querySelectorAll('[tabindex]:not([tabindex="-1"]):not([tabindex="0"])')).sort(((e,t)=>{const n=parseInt(e.getAttribute("tabindex")),o=parseInt(t.getAttribute("tabindex"));return n>o?1:n<o?-1:0})),t=Array.from(v().querySelectorAll('\n  a[href],\n  area[href],\n  input:not([disabled]),\n  select:not([disabled]),\n  textarea:not([disabled]),\n  button:not([disabled]),\n  iframe,\n  object,\n  embed,\n  [tabindex="0"],\n  [contenteditable],\n  audio[controls],\n  video[controls],\n  summary\n')).filter((e=>"-1"!==e.getAttribute("tabindex")));return(e=>{const t=[];for(let n=0;n<e.length;n++)-1===t.indexOf(e[n])&&t.push(e[n]);return t})(e.concat(t)).filter((e=>Y(e)))},P=()=>D(document.body,n.shown)&&!D(document.body,n["toast-shown"])&&!D(document.body,n["no-backdrop"]),$=()=>v()&&D(v(),n.toast),M={previousBodyPadding:null},O=(e,t)=>{if(e.textContent="",t){const n=(new DOMParser).parseFromString(t,"text/html");Array.from(n.querySelector("head").childNodes).forEach((t=>{e.appendChild(t)})),Array.from(n.querySelector("body").childNodes).forEach((t=>{t instanceof HTMLVideoElement||t instanceof HTMLAudioElement?e.appendChild(t.cloneNode(!0)):e.appendChild(t)}))}},D=(e,t)=>{if(!t)return!1;const n=t.split(/\s+/);for(let t=0;t<n.length;t++)if(!e.classList.contains(n[t]))return!1;return!0},j=(e,t,a)=>{if(((e,t)=>{Array.from(e.classList).forEach((a=>{Object.values(n).includes(a)||Object.values(o).includes(a)||Object.values(t.showClass).includes(a)||e.classList.remove(a)}))})(e,t),t.customClass&&t.customClass[a]){if("string"!=typeof t.customClass[a]&&!t.customClass[a].forEach)return void r(`Invalid type of customClass.${a}! Expected string or iterable object, got "${typeof t.customClass[a]}"`);q(e,t.customClass[a])}},H=(e,t)=>{if(!t)return null;switch(t){case"select":case"textarea":case"file":return e.querySelector(`.${n.popup} > .${n[t]}`);case"checkbox":return e.querySelector(`.${n.popup} > .${n.checkbox} input`);case"radio":return e.querySelector(`.${n.popup} > .${n.radio} input:checked`)||e.querySelector(`.${n.popup} > .${n.radio} input:first-child`);case"range":return e.querySelector(`.${n.popup} > .${n.range} input`);default:return e.querySelector(`.${n.popup} > .${n.input}`)}},I=e=>{if(e.focus(),"file"!==e.type){const t=e.value;e.value="",e.value=t}},F=(e,t,n)=>{e&&t&&("string"==typeof t&&(t=t.split(/\s+/).filter(Boolean)),t.forEach((t=>{Array.isArray(e)?e.forEach((e=>{n?e.classList.add(t):e.classList.remove(t)})):n?e.classList.add(t):e.classList.remove(t)})))},q=(e,t)=>{F(e,t,!0)},R=(e,t)=>{F(e,t,!1)},U=(e,t)=>{const n=Array.from(e.children);for(let e=0;e<n.length;e++){const o=n[e];if(o instanceof HTMLElement&&D(o,t))return o}},z=(e,t,n)=>{n===`${parseInt(n)}`&&(n=parseInt(n)),n||0===parseInt(n)?e.style[t]="number"==typeof n?`${n}px`:n:e.style.removeProperty(t)},Z=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"flex";e.style.display=t},W=e=>{e.style.display="none"},K=(e,t,n,o)=>{const a=e.querySelector(t);a&&(a.style[n]=o)},G=function(e,t){t?Z(e,arguments.length>2&&void 0!==arguments[2]?arguments[2]:"flex"):W(e)},Y=e=>!(!e||!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)),J=e=>!!(e.scrollHeight>e.clientHeight),X=e=>{const t=window.getComputedStyle(e),n=parseFloat(t.getPropertyValue("animation-duration")||"0"),o=parseFloat(t.getPropertyValue("transition-duration")||"0");return n>0||o>0},Q=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const n=T();Y(n)&&(t&&(n.style.transition="none",n.style.width="100%"),setTimeout((()=>{n.style.transition=`width ${e/1e3}s linear`,n.style.width="0%"}),10))},ee={},te=e=>new Promise((t=>{if(!e)return t();const n=window.scrollX,o=window.scrollY;ee.restoreFocusTimeout=setTimeout((()=>{ee.previousActiveElement instanceof HTMLElement?(ee.previousActiveElement.focus(),ee.previousActiveElement=null):document.body&&document.body.focus(),t()}),100),window.scrollTo(n,o)})),ne=()=>"undefined"==typeof window||"undefined"==typeof document,oe=`\n <div aria-labelledby="${n.title}" aria-describedby="${n["html-container"]}" class="${n.popup}" tabindex="-1">\n   <button type="button" class="${n.close}"></button>\n   <ul class="${n["progress-steps"]}"></ul>\n   <div class="${n.icon}"></div>\n   <img class="${n.image}" />\n   <h2 class="${n.title}" id="${n.title}"></h2>\n   <div class="${n["html-container"]}" id="${n["html-container"]}"></div>\n   <input class="${n.input}" />\n   <input type="file" class="${n.file}" />\n   <div class="${n.range}">\n     <input type="range" />\n     <output></output>\n   </div>\n   <select class="${n.select}"></select>\n   <div class="${n.radio}"></div>\n   <label for="${n.checkbox}" class="${n.checkbox}">\n     <input type="checkbox" />\n     <span class="${n.label}"></span>\n   </label>\n   <textarea class="${n.textarea}"></textarea>\n   <div class="${n["validation-message"]}" id="${n["validation-message"]}"></div>\n   <div class="${n.actions}">\n     <div class="${n.loader}"></div>\n     <button type="button" class="${n.confirm}"></button>\n     <button type="button" class="${n.deny}"></button>\n     <button type="button" class="${n.cancel}"></button>\n   </div>\n   <div class="${n.footer}"></div>\n   <div class="${n["timer-progress-bar-container"]}">\n     <div class="${n["timer-progress-bar"]}"></div>\n   </div>\n </div>\n`.replace(/(^|\n)\s*/g,""),ae=()=>{ee.currentInstance.resetValidationMessage()},ie=e=>{const t=(()=>{const e=g();return!!e&&(e.remove(),R([document.documentElement,document.body],[n["no-backdrop"],n["toast-shown"],n["has-column"]]),!0)})();if(ne())return void l("SweetAlert2 requires document to initialize");const o=document.createElement("div");o.className=n.container,t&&q(o,n["no-transition"]),O(o,oe);const a="string"==typeof(i=e.target)?document.querySelector(i):i;var i;a.appendChild(o),(e=>{const t=v();t.setAttribute("role",e.toast?"alert":"dialog"),t.setAttribute("aria-live",e.toast?"polite":"assertive"),e.toast||t.setAttribute("aria-modal","true")})(e),(e=>{"rtl"===window.getComputedStyle(e).direction&&q(g(),n.rtl)})(a),(()=>{const e=v(),t=U(e,n.input),o=U(e,n.file),a=e.querySelector(`.${n.range} input`),i=e.querySelector(`.${n.range} output`),r=U(e,n.select),l=e.querySelector(`.${n.checkbox} input`),s=U(e,n.textarea);t.oninput=ae,o.onchange=ae,r.onchange=ae,l.onchange=ae,s.oninput=ae,a.oninput=()=>{ae(),i.value=a.value},a.onchange=()=>{ae(),i.value=a.value}})()},re=(e,t)=>{e instanceof HTMLElement?t.appendChild(e):"object"==typeof e?le(e,t):e&&O(t,e)},le=(e,t)=>{e.jquery?se(t,e):O(t,e.toString())},se=(e,t)=>{if(e.textContent="",0 in t)for(let n=0;n in t;n++)e.appendChild(t[n].cloneNode(!0));else e.appendChild(t.cloneNode(!0))},ce=(()=>{if(ne())return!1;const e=document.createElement("div"),t={WebkitAnimation:"webkitAnimationEnd",animation:"animationend"};for(const n in t)if(Object.prototype.hasOwnProperty.call(t,n)&&void 0!==e.style[n])return t[n];return!1})(),de=(e,t)=>{const o=A(),a=N();t.showConfirmButton||t.showDenyButton||t.showCancelButton?Z(o):W(o),j(o,t,"actions"),function(e,t,o){const a=C(),i=V(),r=B();ue(a,"confirm",o),ue(i,"deny",o),ue(r,"cancel",o),function(e,t,o,a){a.buttonsStyling?(q([e,t,o],n.styled),a.confirmButtonColor&&(e.style.backgroundColor=a.confirmButtonColor,q(e,n["default-outline"])),a.denyButtonColor&&(t.style.backgroundColor=a.denyButtonColor,q(t,n["default-outline"])),a.cancelButtonColor&&(o.style.backgroundColor=a.cancelButtonColor,q(o,n["default-outline"]))):R([e,t,o],n.styled)}(a,i,r,o),o.reverseButtons&&(o.toast?(e.insertBefore(r,a),e.insertBefore(i,a)):(e.insertBefore(r,t),e.insertBefore(i,t),e.insertBefore(a,t)))}(o,a,t),O(a,t.loaderHtml),j(a,t,"loader")};function ue(e,t,o){G(e,o[`show${i(t)}Button`],"inline-block"),O(e,o[`${t}ButtonText`]),e.setAttribute("aria-label",o[`${t}ButtonAriaLabel`]),e.className=n[t],j(e,o,`${t}Button`),q(e,o[`${t}ButtonClass`])}const me=(e,t)=>{const o=g();o&&(function(e,t){"string"==typeof t?e.style.background=t:t||q([document.documentElement,document.body],n["no-backdrop"])}(o,t.backdrop),function(e,t){t in n?q(e,n[t]):(r('The "position" parameter is not valid, defaulting to "center"'),q(e,n.center))}(o,t.position),function(e,t){if(t&&"string"==typeof t){const o=`grow-${t}`;o in n&&q(e,n[o])}}(o,t.grow),j(o,t,"container"))},pe=["input","file","range","select","radio","checkbox","textarea"],ge=e=>{if(!ke[e.input])return void l(`Unexpected type of input! Expected "text", "email", "password", "number", "tel", "select", "radio", "checkbox", "textarea", "file" or "url", got "${e.input}"`);const t=ye(e.input),n=ke[e.input](t,e);Z(t),e.inputAutoFocus&&setTimeout((()=>{I(n)}))},he=(e,t)=>{const n=H(v(),e);if(n){(e=>{for(let t=0;t<e.attributes.length;t++){const n=e.attributes[t].name;["type","value","style"].includes(n)||e.removeAttribute(n)}})(n);for(const e in t)n.setAttribute(e,t[e])}},fe=e=>{const t=ye(e.input);"object"==typeof e.customClass&&q(t,e.customClass.input)},ve=(e,t)=>{e.placeholder&&!t.inputPlaceholder||(e.placeholder=t.inputPlaceholder)},be=(e,t,o)=>{if(o.inputLabel){e.id=n.input;const a=document.createElement("label"),i=n["input-label"];a.setAttribute("for",e.id),a.className=i,"object"==typeof o.customClass&&q(a,o.customClass.inputLabel),a.innerText=o.inputLabel,t.insertAdjacentElement("beforebegin",a)}},ye=e=>U(v(),n[e]||n.input),we=(e,t)=>{["string","number"].includes(typeof t)?e.value=`${t}`:p(t)||r(`Unexpected type of inputValue! Expected "string", "number" or "Promise", got "${typeof t}"`)},ke={};ke.text=ke.email=ke.password=ke.number=ke.tel=ke.url=(e,t)=>(we(e,t.inputValue),be(e,e,t),ve(e,t),e.type=t.input,e),ke.file=(e,t)=>(be(e,e,t),ve(e,t),e),ke.range=(e,t)=>{const n=e.querySelector("input"),o=e.querySelector("output");return we(n,t.inputValue),n.type=t.input,we(o,t.inputValue),be(n,e,t),e},ke.select=(e,t)=>{if(e.textContent="",t.inputPlaceholder){const n=document.createElement("option");O(n,t.inputPlaceholder),n.value="",n.disabled=!0,n.selected=!0,e.appendChild(n)}return be(e,e,t),e},ke.radio=e=>(e.textContent="",e),ke.checkbox=(e,t)=>{const o=H(v(),"checkbox");o.value="1",o.id=n.checkbox,o.checked=Boolean(t.inputValue);const a=e.querySelector("span");return O(a,t.inputPlaceholder),o},ke.textarea=(e,t)=>(we(e,t.inputValue),ve(e,t),be(e,e,t),setTimeout((()=>{if("MutationObserver"in window){const t=parseInt(window.getComputedStyle(v()).width);new MutationObserver((()=>{const n=e.offsetWidth+(o=e,parseInt(window.getComputedStyle(o).marginLeft)+parseInt(window.getComputedStyle(o).marginRight));var o;v().style.width=n>t?`${n}px`:null})).observe(e,{attributes:!0,attributeFilter:["style"]})}})),e);const Ee=(t,o)=>{const a=w();j(a,o,"htmlContainer"),o.html?(re(o.html,a),Z(a,"block")):o.text?(a.textContent=o.text,Z(a,"block")):W(a),((t,o)=>{const a=v(),i=e.innerParams.get(t),r=!i||o.input!==i.input;pe.forEach((e=>{const t=U(a,n[e]);he(e,o.inputAttributes),t.className=n[e],r&&W(t)})),o.input&&(r&&ge(o),fe(o))})(t,o)},xe=(e,t)=>{for(const n in o)t.icon!==n&&R(e,o[n]);q(e,o[t.icon]),Ve(e,t),Ce(),j(e,t,"icon")},Ce=()=>{const e=v(),t=window.getComputedStyle(e).getPropertyValue("background-color"),n=e.querySelectorAll("[class^=swal2-success-circular-line], .swal2-success-fix");for(let e=0;e<n.length;e++)n[e].style.backgroundColor=t},Be=(e,t)=>{let n,o=e.innerHTML;t.iconHtml?n=Ne(t.iconHtml):"success"===t.icon?(n='\n  <div class="swal2-success-circular-line-left"></div>\n  <span class="swal2-success-line-tip"></span> <span class="swal2-success-line-long"></span>\n  <div class="swal2-success-ring"></div> <div class="swal2-success-fix"></div>\n  <div class="swal2-success-circular-line-right"></div>\n',o=o.replace(/ style=".*?"/g,"")):n="error"===t.icon?'\n  <span class="swal2-x-mark">\n    <span class="swal2-x-mark-line-left"></span>\n    <span class="swal2-x-mark-line-right"></span>\n  </span>\n':Ne({question:"?",warning:"!",info:"i"}[t.icon]),o.trim()!==n.trim()&&O(e,n)},Ve=(e,t)=>{if(t.iconColor){e.style.color=t.iconColor,e.style.borderColor=t.iconColor;for(const n of[".swal2-success-line-tip",".swal2-success-line-long",".swal2-x-mark-line-left",".swal2-x-mark-line-right"])K(e,n,"backgroundColor",t.iconColor);K(e,".swal2-success-ring","borderColor",t.iconColor)}},Ne=e=>`<div class="${n["icon-content"]}">${e}</div>`,Ae=(e,t)=>{e.className=`${n.popup} ${Y(e)?t.showClass.popup:""}`,t.toast?(q([document.documentElement,document.body],n["toast-shown"]),q(e,n.toast)):q(e,n.modal),j(e,t,"popup"),"string"==typeof t.customClass&&q(e,t.customClass),t.icon&&q(e,n[`icon-${t.icon}`])},_e=e=>{const t=document.createElement("li");return q(t,n["progress-step"]),O(t,e),t},Te=e=>{const t=document.createElement("li");return q(t,n["progress-step-line"]),e.progressStepsDistance&&z(t,"width",e.progressStepsDistance),t},Se=(t,a)=>{((e,t)=>{const n=g(),o=v();t.toast?(z(n,"width",t.width),o.style.width="100%",o.insertBefore(N(),b())):z(o,"width",t.width),z(o,"padding",t.padding),t.color&&(o.style.color=t.color),t.background&&(o.style.background=t.background),W(x()),Ae(o,t)})(0,a),me(0,a),((e,t)=>{const o=E();t.progressSteps&&0!==t.progressSteps.length?(Z(o),o.textContent="",t.currentProgressStep>=t.progressSteps.length&&r("Invalid currentProgressStep parameter, it should be less than progressSteps.length (currentProgressStep like JS arrays starts from 0)"),t.progressSteps.forEach(((e,a)=>{const i=_e(e);if(o.appendChild(i),a===t.currentProgressStep&&q(i,n["active-progress-step"]),a!==t.progressSteps.length-1){const e=Te(t);o.appendChild(e)}}))):W(o)})(0,a),((t,n)=>{const a=e.innerParams.get(t),i=b();if(a&&n.icon===a.icon)return Be(i,n),void xe(i,n);if(n.icon||n.iconHtml){if(n.icon&&-1===Object.keys(o).indexOf(n.icon))return l(`Unknown icon! Expected "success", "error", "warning", "info" or "question", got "${n.icon}"`),void W(i);Z(i),Be(i,n),xe(i,n),q(i,n.showClass.icon)}else W(i)})(t,a),((e,t)=>{const o=k();t.imageUrl?(Z(o,""),o.setAttribute("src",t.imageUrl),o.setAttribute("alt",t.imageAlt),z(o,"width",t.imageWidth),z(o,"height",t.imageHeight),o.className=n.image,j(o,t,"image")):W(o)})(0,a),((e,t)=>{const n=y();G(n,t.title||t.titleText,"block"),t.title&&re(t.title,n),t.titleText&&(n.innerText=t.titleText),j(n,t,"title")})(0,a),((e,t)=>{const n=S();O(n,t.closeButtonHtml),j(n,t,"closeButton"),G(n,t.showCloseButton),n.setAttribute("aria-label",t.closeButtonAriaLabel)})(0,a),Ee(t,a),de(0,a),((e,t)=>{const n=_();G(n,t.footer),t.footer&&re(t.footer,n),j(n,t,"footer")})(0,a),"function"==typeof a.didRender&&a.didRender(v())};function Le(){const t=e.innerParams.get(this);if(!t)return;const o=e.domCache.get(this);W(o.loader),$()?t.icon&&Z(b()):Pe(o),R([o.popup,o.actions],n.loading),o.popup.removeAttribute("aria-busy"),o.popup.removeAttribute("data-loading"),o.confirmButton.disabled=!1,o.denyButton.disabled=!1,o.cancelButton.disabled=!1}const Pe=e=>{const t=e.popup.getElementsByClassName(e.loader.getAttribute("data-button-to-replace"));t.length?Z(t[0],"inline-block"):Y(C())||Y(V())||Y(B())||W(e.actions)},$e=()=>C()&&C().click(),Me=Object.freeze({cancel:"cancel",backdrop:"backdrop",close:"close",esc:"esc",timer:"timer"}),Oe=e=>{e.keydownTarget&&e.keydownHandlerAdded&&(e.keydownTarget.removeEventListener("keydown",e.keydownHandler,{capture:e.keydownListenerCapture}),e.keydownHandlerAdded=!1)},De=(e,t)=>{const n=L();if(n.length)return(e+=t)===n.length?e=0:-1===e&&(e=n.length-1),void n[e].focus();v().focus()},je=["ArrowRight","ArrowDown"],He=["ArrowLeft","ArrowUp"],Ie=(t,n,o)=>{const a=e.innerParams.get(t);a&&(n.isComposing||229===n.keyCode||(a.stopKeydownPropagation&&n.stopPropagation(),"Enter"===n.key?Fe(t,n,a):"Tab"===n.key?qe(n):[...je,...He].includes(n.key)?Re(n.key):"Escape"===n.key&&Ue(n,a,o)))},Fe=(e,t,n)=>{if(d(n.allowEnterKey)&&t.target&&e.getInput()&&t.target instanceof HTMLElement&&t.target.outerHTML===e.getInput().outerHTML){if(["textarea","file"].includes(n.input))return;$e(),t.preventDefault()}},qe=e=>{const t=e.target,n=L();let o=-1;for(let e=0;e<n.length;e++)if(t===n[e]){o=e;break}e.shiftKey?De(o,-1):De(o,1),e.stopPropagation(),e.preventDefault()},Re=e=>{const t=[C(),V(),B()];if(document.activeElement instanceof HTMLElement&&!t.includes(document.activeElement))return;const n=je.includes(e)?"nextElementSibling":"previousElementSibling";let o=document.activeElement;for(let e=0;e<A().children.length;e++){if(o=o[n],!o)return;if(o instanceof HTMLButtonElement&&Y(o))break}o instanceof HTMLButtonElement&&o.focus()},Ue=(e,t,n)=>{d(t.allowEscapeKey)&&(e.preventDefault(),n(Me.esc))};var ze={swalPromiseResolve:new WeakMap,swalPromiseReject:new WeakMap};const Ze=()=>{Array.from(document.body.children).forEach((e=>{e.hasAttribute("data-previous-aria-hidden")?(e.setAttribute("aria-hidden",e.getAttribute("data-previous-aria-hidden")),e.removeAttribute("data-previous-aria-hidden")):e.removeAttribute("aria-hidden")}))},We=()=>{const e=navigator.userAgent,t=!!e.match(/iPad/i)||!!e.match(/iPhone/i),n=!!e.match(/WebKit/i);if(t&&n&&!e.match(/CriOS/i)){const e=44;v().scrollHeight>window.innerHeight-e&&(g().style.paddingBottom=`${e}px`)}},Ke=()=>{const e=g();let t;e.ontouchstart=e=>{t=Ge(e)},e.ontouchmove=e=>{t&&(e.preventDefault(),e.stopPropagation())}},Ge=e=>{const t=e.target,n=g();return!(Ye(e)||Je(e)||t!==n&&(J(n)||!(t instanceof HTMLElement)||"INPUT"===t.tagName||"TEXTAREA"===t.tagName||J(w())&&w().contains(t)))},Ye=e=>e.touches&&e.touches.length&&"stylus"===e.touches[0].touchType,Je=e=>e.touches&&e.touches.length>1,Xe=()=>{if(D(document.body,n.iosfix)){const e=parseInt(document.body.style.top,10);R(document.body,n.iosfix),document.body.style.top="",document.body.scrollTop=-1*e}},Qe=()=>{null===M.previousBodyPadding&&document.body.scrollHeight>window.innerHeight&&(M.previousBodyPadding=parseInt(window.getComputedStyle(document.body).getPropertyValue("padding-right")),document.body.style.paddingRight=`${M.previousBodyPadding+(()=>{const e=document.createElement("div");e.className=n["scrollbar-measure"],document.body.appendChild(e);const t=e.getBoundingClientRect().width-e.clientWidth;return document.body.removeChild(e),t})()}px`)},et=()=>{null!==M.previousBodyPadding&&(document.body.style.paddingRight=`${M.previousBodyPadding}px`,M.previousBodyPadding=null)};function tt(e,t,o,a){$()?st(e,a):(te(o).then((()=>st(e,a))),Oe(ee)),/^((?!chrome|android).)*safari/i.test(navigator.userAgent)?(t.setAttribute("style","display:none !important"),t.removeAttribute("class"),t.innerHTML=""):t.remove(),P()&&(et(),Xe(),Ze()),R([document.documentElement,document.body],[n.shown,n["height-auto"],n["no-backdrop"],n["toast-shown"]])}function nt(e){e=it(e);const t=ze.swalPromiseResolve.get(this),n=ot(this);this.isAwaitingPromise()?e.isDismissed||(at(this),t(e)):n&&t(e)}const ot=t=>{const n=v();if(!n)return!1;const o=e.innerParams.get(t);if(!o||D(n,o.hideClass.popup))return!1;R(n,o.showClass.popup),q(n,o.hideClass.popup);const a=g();return R(a,o.showClass.backdrop),q(a,o.hideClass.backdrop),rt(t,n,o),!0},at=t=>{t.isAwaitingPromise()&&(e.awaitingPromise.delete(t),e.innerParams.get(t)||t._destroy())},it=e=>void 0===e?{isConfirmed:!1,isDenied:!1,isDismissed:!0}:Object.assign({isConfirmed:!1,isDenied:!1,isDismissed:!1},e),rt=(e,t,n)=>{const o=g(),a=ce&&X(t);"function"==typeof n.willClose&&n.willClose(t),a?lt(e,t,o,n.returnFocus,n.didClose):tt(e,o,n.returnFocus,n.didClose)},lt=(e,t,n,o,a)=>{ee.swalCloseEventFinishedCallback=tt.bind(null,e,n,o,a),t.addEventListener(ce,(function(e){e.target===t&&(ee.swalCloseEventFinishedCallback(),delete ee.swalCloseEventFinishedCallback)}))},st=(e,t)=>{setTimeout((()=>{"function"==typeof t&&t.bind(e.params)(),e._destroy()}))};function ct(t,n,o){const a=e.domCache.get(t);n.forEach((e=>{a[e].disabled=o}))}function dt(e,t){if(e)if("radio"===e.type){const n=e.parentNode.parentNode.querySelectorAll("input");for(let e=0;e<n.length;e++)n[e].disabled=t}else e.disabled=t}const ut={title:"",titleText:"",text:"",html:"",footer:"",icon:void 0,iconColor:void 0,iconHtml:void 0,template:void 0,toast:!1,showClass:{popup:"swal2-show",backdrop:"swal2-backdrop-show",icon:"swal2-icon-show"},hideClass:{popup:"swal2-hide",backdrop:"swal2-backdrop-hide",icon:"swal2-icon-hide"},customClass:{},target:"body",color:void 0,backdrop:!0,heightAuto:!0,allowOutsideClick:!0,allowEscapeKey:!0,allowEnterKey:!0,stopKeydownPropagation:!0,keydownListenerCapture:!1,showConfirmButton:!0,showDenyButton:!1,showCancelButton:!1,preConfirm:void 0,preDeny:void 0,confirmButtonText:"OK",confirmButtonAriaLabel:"",confirmButtonColor:void 0,denyButtonText:"No",denyButtonAriaLabel:"",denyButtonColor:void 0,cancelButtonText:"Cancel",cancelButtonAriaLabel:"",cancelButtonColor:void 0,buttonsStyling:!0,reverseButtons:!1,focusConfirm:!0,focusDeny:!1,focusCancel:!1,returnFocus:!0,showCloseButton:!1,closeButtonHtml:"&times;",closeButtonAriaLabel:"Close this dialog",loaderHtml:"",showLoaderOnConfirm:!1,showLoaderOnDeny:!1,imageUrl:void 0,imageWidth:void 0,imageHeight:void 0,imageAlt:"",timer:void 0,timerProgressBar:!1,width:void 0,padding:void 0,background:void 0,input:void 0,inputPlaceholder:"",inputLabel:"",inputValue:"",inputOptions:{},inputAutoFocus:!0,inputAutoTrim:!0,inputAttributes:{},inputValidator:void 0,returnInputValueOnDeny:!1,validationMessage:void 0,grow:!1,position:"center",progressSteps:[],currentProgressStep:void 0,progressStepsDistance:void 0,willOpen:void 0,didOpen:void 0,didRender:void 0,willClose:void 0,didClose:void 0,didDestroy:void 0,scrollbarPadding:!0},mt=["allowEscapeKey","allowOutsideClick","background","buttonsStyling","cancelButtonAriaLabel","cancelButtonColor","cancelButtonText","closeButtonAriaLabel","closeButtonHtml","color","confirmButtonAriaLabel","confirmButtonColor","confirmButtonText","currentProgressStep","customClass","denyButtonAriaLabel","denyButtonColor","denyButtonText","didClose","didDestroy","footer","hideClass","html","icon","iconColor","iconHtml","imageAlt","imageHeight","imageUrl","imageWidth","preConfirm","preDeny","progressSteps","returnFocus","reverseButtons","showCancelButton","showCloseButton","showConfirmButton","showDenyButton","text","title","titleText","willClose"],pt={},gt=["allowOutsideClick","allowEnterKey","backdrop","focusConfirm","focusDeny","focusCancel","returnFocus","heightAuto","keydownListenerCapture"],ht=e=>Object.prototype.hasOwnProperty.call(ut,e),ft=e=>-1!==mt.indexOf(e),vt=e=>pt[e],bt=e=>{ht(e)||r(`Unknown parameter "${e}"`)},yt=e=>{gt.includes(e)&&r(`The parameter "${e}" is incompatible with toasts`)},wt=e=>{vt(e)&&c(e,vt(e))},kt=e=>{const t={};return Object.keys(e).forEach((n=>{ft(n)?t[n]=e[n]:r(`Invalid parameter to update: ${n}`)})),t},Et=e=>{xt(e),delete e.params,delete ee.keydownHandler,delete ee.keydownTarget,delete ee.currentInstance},xt=t=>{t.isAwaitingPromise()?(Ct(e,t),e.awaitingPromise.set(t,!0)):(Ct(ze,t),Ct(e,t))},Ct=(e,t)=>{for(const n in e)e[n].delete(t)};var Bt=Object.freeze({__proto__:null,_destroy:function(){const t=e.domCache.get(this),n=e.innerParams.get(this);n?(t.popup&&ee.swalCloseEventFinishedCallback&&(ee.swalCloseEventFinishedCallback(),delete ee.swalCloseEventFinishedCallback),"function"==typeof n.didDestroy&&n.didDestroy(),Et(this)):xt(this)},close:nt,closeModal:nt,closePopup:nt,closeToast:nt,disableButtons:function(){ct(this,["confirmButton","denyButton","cancelButton"],!0)},disableInput:function(){dt(this.getInput(),!0)},disableLoading:Le,enableButtons:function(){ct(this,["confirmButton","denyButton","cancelButton"],!1)},enableInput:function(){dt(this.getInput(),!1)},getInput:function(t){const n=e.innerParams.get(t||this),o=e.domCache.get(t||this);return o?H(o.popup,n.input):null},handleAwaitingPromise:at,hideLoading:Le,isAwaitingPromise:function(){return!!e.awaitingPromise.get(this)},rejectPromise:function(e){const t=ze.swalPromiseReject.get(this);at(this),t&&t(e)},resetValidationMessage:function(){const t=e.domCache.get(this);t.validationMessage&&W(t.validationMessage);const o=this.getInput();o&&(o.removeAttribute("aria-invalid"),o.removeAttribute("aria-describedby"),R(o,n.inputerror))},showValidationMessage:function(t){const o=e.domCache.get(this),a=e.innerParams.get(this);O(o.validationMessage,t),o.validationMessage.className=n["validation-message"],a.customClass&&a.customClass.validationMessage&&q(o.validationMessage,a.customClass.validationMessage),Z(o.validationMessage);const i=this.getInput();i&&(i.setAttribute("aria-invalid",!0),i.setAttribute("aria-describedby",n["validation-message"]),I(i),q(i,n.inputerror))},update:function(t){const n=v(),o=e.innerParams.get(this);if(!n||D(n,o.hideClass.popup))return void r("You're trying to update the closed or closing popup, that won't work. Use the update() method in preConfirm parameter or show a new popup.");const a=kt(t),i=Object.assign({},o,a);Se(this,i),e.innerParams.set(this,i),Object.defineProperties(this,{params:{value:Object.assign({},this.params,t),writable:!1,enumerable:!0}})}});const Vt=e=>{let t=v();t||new _n,t=v();const n=N();$()?W(b()):Nt(t,e),Z(n),t.setAttribute("data-loading","true"),t.setAttribute("aria-busy","true"),t.focus()},Nt=(e,t)=>{const o=A(),a=N();!t&&Y(C())&&(t=C()),Z(o),t&&(W(t),a.setAttribute("data-button-to-replace",t.className)),a.parentNode.insertBefore(a,t),q([e,o],n.loading)},At=e=>e.checked?1:0,_t=e=>e.checked?e.value:null,Tt=e=>e.files.length?null!==e.getAttribute("multiple")?e.files:e.files[0]:null,St=(e,t)=>{const n=v(),o=e=>{Pt[t.input](n,$t(e),t)};u(t.inputOptions)||p(t.inputOptions)?(Vt(C()),m(t.inputOptions).then((t=>{e.hideLoading(),o(t)}))):"object"==typeof t.inputOptions?o(t.inputOptions):l("Unexpected type of inputOptions! Expected object, Map or Promise, got "+typeof t.inputOptions)},Lt=(e,t)=>{const n=e.getInput();W(n),m(t.inputValue).then((o=>{n.value="number"===t.input?`${parseFloat(o)||0}`:`${o}`,Z(n),n.focus(),e.hideLoading()})).catch((t=>{l(`Error in inputValue promise: ${t}`),n.value="",Z(n),n.focus(),e.hideLoading()}))},Pt={select:(e,t,o)=>{const a=U(e,n.select),i=(e,t,n)=>{const a=document.createElement("option");a.value=n,O(a,t),a.selected=Mt(n,o.inputValue),e.appendChild(a)};t.forEach((e=>{const t=e[0],n=e[1];if(Array.isArray(n)){const e=document.createElement("optgroup");e.label=t,e.disabled=!1,a.appendChild(e),n.forEach((t=>i(e,t[1],t[0])))}else i(a,n,t)})),a.focus()},radio:(e,t,o)=>{const a=U(e,n.radio);t.forEach((e=>{const t=e[0],i=e[1],r=document.createElement("input"),l=document.createElement("label");r.type="radio",r.name=n.radio,r.value=t,Mt(t,o.inputValue)&&(r.checked=!0);const s=document.createElement("span");O(s,i),s.className=n.label,l.appendChild(r),l.appendChild(s),a.appendChild(l)}));const i=a.querySelectorAll("input");i.length&&i[0].focus()}},$t=e=>{const t=[];return"undefined"!=typeof Map&&e instanceof Map?e.forEach(((e,n)=>{let o=e;"object"==typeof o&&(o=$t(o)),t.push([n,o])})):Object.keys(e).forEach((n=>{let o=e[n];"object"==typeof o&&(o=$t(o)),t.push([n,o])})),t},Mt=(e,t)=>t&&t.toString()===e.toString(),Ot=(t,n)=>{const o=e.innerParams.get(t);if(!o.input)return void l(`The "input" parameter is needed to be set when using returnInputValueOn${i(n)}`);const a=((e,t)=>{const n=e.getInput();if(!n)return null;switch(t.input){case"checkbox":return At(n);case"radio":return _t(n);case"file":return Tt(n);default:return t.inputAutoTrim?n.value.trim():n.value}})(t,o);o.inputValidator?Dt(t,a,n):t.getInput().checkValidity()?"deny"===n?jt(t,a):Ft(t,a):(t.enableButtons(),t.showValidationMessage(o.validationMessage))},Dt=(t,n,o)=>{const a=e.innerParams.get(t);t.disableInput(),Promise.resolve().then((()=>m(a.inputValidator(n,a.validationMessage)))).then((e=>{t.enableButtons(),t.enableInput(),e?t.showValidationMessage(e):"deny"===o?jt(t,n):Ft(t,n)}))},jt=(t,n)=>{const o=e.innerParams.get(t||void 0);o.showLoaderOnDeny&&Vt(V()),o.preDeny?(e.awaitingPromise.set(t||void 0,!0),Promise.resolve().then((()=>m(o.preDeny(n,o.validationMessage)))).then((e=>{!1===e?(t.hideLoading(),at(t)):t.close({isDenied:!0,value:void 0===e?n:e})})).catch((e=>It(t||void 0,e)))):t.close({isDenied:!0,value:n})},Ht=(e,t)=>{e.close({isConfirmed:!0,value:t})},It=(e,t)=>{e.rejectPromise(t)},Ft=(t,n)=>{const o=e.innerParams.get(t||void 0);o.showLoaderOnConfirm&&Vt(),o.preConfirm?(t.resetValidationMessage(),e.awaitingPromise.set(t||void 0,!0),Promise.resolve().then((()=>m(o.preConfirm(n,o.validationMessage)))).then((e=>{Y(x())||!1===e?(t.hideLoading(),at(t)):Ht(t,void 0===e?n:e)})).catch((e=>It(t||void 0,e)))):Ht(t,n)},qt=(t,n,o)=>{n.popup.onclick=()=>{const n=e.innerParams.get(t);n&&(Rt(n)||n.timer||n.input)||o(Me.close)}},Rt=e=>e.showConfirmButton||e.showDenyButton||e.showCancelButton||e.showCloseButton;let Ut=!1;const zt=e=>{e.popup.onmousedown=()=>{e.container.onmouseup=function(t){e.container.onmouseup=void 0,t.target===e.container&&(Ut=!0)}}},Zt=e=>{e.container.onmousedown=()=>{e.popup.onmouseup=function(t){e.popup.onmouseup=void 0,(t.target===e.popup||e.popup.contains(t.target))&&(Ut=!0)}}},Wt=(t,n,o)=>{n.container.onclick=a=>{const i=e.innerParams.get(t);Ut?Ut=!1:a.target===n.container&&d(i.allowOutsideClick)&&o(Me.backdrop)}},Kt=e=>e instanceof Element||(e=>"object"==typeof e&&e.jquery)(e),Gt=()=>{if(ee.timeout)return(()=>{const e=T(),t=parseInt(window.getComputedStyle(e).width);e.style.removeProperty("transition"),e.style.width="100%";const n=t/parseInt(window.getComputedStyle(e).width)*100;e.style.width=`${n}%`})(),ee.timeout.stop()},Yt=()=>{if(ee.timeout){const e=ee.timeout.start();return Q(e),e}};let Jt=!1;const Xt={},Qt=e=>{for(let t=e.target;t&&t!==document;t=t.parentNode)for(const e in Xt){const n=t.getAttribute(e);if(n)return void Xt[e].fire({template:n})}};var en=Object.freeze({__proto__:null,argsToParams:e=>{const t={};return"object"!=typeof e[0]||Kt(e[0])?["title","html","icon"].forEach(((n,o)=>{const a=e[o];"string"==typeof a||Kt(a)?t[n]=a:void 0!==a&&l(`Unexpected type of ${n}! Expected "string" or "Element", got ${typeof a}`)})):Object.assign(t,e[0]),t},bindClickHandler:function(){Xt[arguments.length>0&&void 0!==arguments[0]?arguments[0]:"data-swal-template"]=this,Jt||(document.body.addEventListener("click",Qt),Jt=!0)},clickCancel:()=>B()&&B().click(),clickConfirm:$e,clickDeny:()=>V()&&V().click(),enableLoading:Vt,fire:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return new this(...t)},getActions:A,getCancelButton:B,getCloseButton:S,getConfirmButton:C,getContainer:g,getDenyButton:V,getFocusableElements:L,getFooter:_,getHtmlContainer:w,getIcon:b,getIconContent:()=>f(n["icon-content"]),getImage:k,getInputLabel:()=>f(n["input-label"]),getLoader:N,getPopup:v,getProgressSteps:E,getTimerLeft:()=>ee.timeout&&ee.timeout.getTimerLeft(),getTimerProgressBar:T,getTitle:y,getValidationMessage:x,increaseTimer:e=>{if(ee.timeout){const t=ee.timeout.increase(e);return Q(t,!0),t}},isDeprecatedParameter:vt,isLoading:()=>v().hasAttribute("data-loading"),isTimerRunning:()=>ee.timeout&&ee.timeout.isRunning(),isUpdatableParameter:ft,isValidParameter:ht,isVisible:()=>Y(v()),mixin:function(e){return class extends(this){_main(t,n){return super._main(t,Object.assign({},e,n))}}},resumeTimer:Yt,showLoading:Vt,stopTimer:Gt,toggleTimer:()=>{const e=ee.timeout;return e&&(e.running?Gt():Yt())}});class tn{constructor(e,t){this.callback=e,this.remaining=t,this.running=!1,this.start()}start(){return this.running||(this.running=!0,this.started=new Date,this.id=setTimeout(this.callback,this.remaining)),this.remaining}stop(){return this.running&&(this.running=!1,clearTimeout(this.id),this.remaining-=(new Date).getTime()-this.started.getTime()),this.remaining}increase(e){const t=this.running;return t&&this.stop(),this.remaining+=e,t&&this.start(),this.remaining}getTimerLeft(){return this.running&&(this.stop(),this.start()),this.remaining}isRunning(){return this.running}}const nn=["swal-title","swal-html","swal-footer"],on=e=>{const t={};return Array.from(e.querySelectorAll("swal-param")).forEach((e=>{mn(e,["name","value"]);const n=e.getAttribute("name"),o=e.getAttribute("value");t[n]="boolean"==typeof ut[n]?"false"!==o:"object"==typeof ut[n]?JSON.parse(o):o})),t},an=e=>{const t={};return Array.from(e.querySelectorAll("swal-function-param")).forEach((e=>{const n=e.getAttribute("name"),o=e.getAttribute("value");t[n]=new Function(`return ${o}`)()})),t},rn=e=>{const t={};return Array.from(e.querySelectorAll("swal-button")).forEach((e=>{mn(e,["type","color","aria-label"]);const n=e.getAttribute("type");t[`${n}ButtonText`]=e.innerHTML,t[`show${i(n)}Button`]=!0,e.hasAttribute("color")&&(t[`${n}ButtonColor`]=e.getAttribute("color")),e.hasAttribute("aria-label")&&(t[`${n}ButtonAriaLabel`]=e.getAttribute("aria-label"))})),t},ln=e=>{const t={},n=e.querySelector("swal-image");return n&&(mn(n,["src","width","height","alt"]),n.hasAttribute("src")&&(t.imageUrl=n.getAttribute("src")),n.hasAttribute("width")&&(t.imageWidth=n.getAttribute("width")),n.hasAttribute("height")&&(t.imageHeight=n.getAttribute("height")),n.hasAttribute("alt")&&(t.imageAlt=n.getAttribute("alt"))),t},sn=e=>{const t={},n=e.querySelector("swal-icon");return n&&(mn(n,["type","color"]),n.hasAttribute("type")&&(t.icon=n.getAttribute("type")),n.hasAttribute("color")&&(t.iconColor=n.getAttribute("color")),t.iconHtml=n.innerHTML),t},cn=e=>{const t={},n=e.querySelector("swal-input");n&&(mn(n,["type","label","placeholder","value"]),t.input=n.getAttribute("type")||"text",n.hasAttribute("label")&&(t.inputLabel=n.getAttribute("label")),n.hasAttribute("placeholder")&&(t.inputPlaceholder=n.getAttribute("placeholder")),n.hasAttribute("value")&&(t.inputValue=n.getAttribute("value")));const o=Array.from(e.querySelectorAll("swal-input-option"));return o.length&&(t.inputOptions={},o.forEach((e=>{mn(e,["value"]);const n=e.getAttribute("value"),o=e.innerHTML;t.inputOptions[n]=o}))),t},dn=(e,t)=>{const n={};for(const o in t){const a=t[o],i=e.querySelector(a);i&&(mn(i,[]),n[a.replace(/^swal-/,"")]=i.innerHTML.trim())}return n},un=e=>{const t=nn.concat(["swal-param","swal-function-param","swal-button","swal-image","swal-icon","swal-input","swal-input-option"]);Array.from(e.children).forEach((e=>{const n=e.tagName.toLowerCase();t.includes(n)||r(`Unrecognized element <${n}>`)}))},mn=(e,t)=>{Array.from(e.attributes).forEach((n=>{-1===t.indexOf(n.name)&&r([`Unrecognized attribute "${n.name}" on <${e.tagName.toLowerCase()}>.`,t.length?`Allowed attributes are: ${t.join(", ")}`:"To set the value, use HTML within the element."])}))},pn=e=>{const t=g(),o=v();"function"==typeof e.willOpen&&e.willOpen(o);const a=window.getComputedStyle(document.body).overflowY;vn(t,o,e),setTimeout((()=>{hn(t,o)}),10),P()&&(fn(t,e.scrollbarPadding,a),Array.from(document.body.children).forEach((e=>{e===g()||e.contains(g())||(e.hasAttribute("aria-hidden")&&e.setAttribute("data-previous-aria-hidden",e.getAttribute("aria-hidden")),e.setAttribute("aria-hidden","true"))}))),$()||ee.previousActiveElement||(ee.previousActiveElement=document.activeElement),"function"==typeof e.didOpen&&setTimeout((()=>e.didOpen(o))),R(t,n["no-transition"])},gn=e=>{const t=v();if(e.target!==t)return;const n=g();t.removeEventListener(ce,gn),n.style.overflowY="auto"},hn=(e,t)=>{ce&&X(t)?(e.style.overflowY="hidden",t.addEventListener(ce,gn)):e.style.overflowY="auto"},fn=(e,t,o)=>{(()=>{if((/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream||"MacIntel"===navigator.platform&&navigator.maxTouchPoints>1)&&!D(document.body,n.iosfix)){const e=document.body.scrollTop;document.body.style.top=-1*e+"px",q(document.body,n.iosfix),Ke(),We()}})(),t&&"hidden"!==o&&Qe(),setTimeout((()=>{e.scrollTop=0}))},vn=(e,t,o)=>{q(e,o.showClass.backdrop),t.style.setProperty("opacity","0","important"),Z(t,"grid"),setTimeout((()=>{q(t,o.showClass.popup),t.style.removeProperty("opacity")}),10),q([document.documentElement,document.body],n.shown),o.heightAuto&&o.backdrop&&!o.toast&&q([document.documentElement,document.body],n["height-auto"])};var bn={email:(e,t)=>/^[a-zA-Z0-9.+_-]+@[a-zA-Z0-9.-]+\.[a-zA-Z0-9-]{2,24}$/.test(e)?Promise.resolve():Promise.resolve(t||"Invalid email address"),url:(e,t)=>/^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-z]{2,63}\b([-a-zA-Z0-9@:%_+.~#?&/=]*)$/.test(e)?Promise.resolve():Promise.resolve(t||"Invalid URL")};function yn(e){!function(e){e.inputValidator||Object.keys(bn).forEach((t=>{e.input===t&&(e.inputValidator=bn[t])}))}(e),e.showLoaderOnConfirm&&!e.preConfirm&&r("showLoaderOnConfirm is set to true, but preConfirm is not defined.\nshowLoaderOnConfirm should be used together with preConfirm, see usage example:\nhttps://sweetalert2.github.io/#ajax-request"),function(e){(!e.target||"string"==typeof e.target&&!document.querySelector(e.target)||"string"!=typeof e.target&&!e.target.appendChild)&&(r('Target parameter is not valid, defaulting to "body"'),e.target="body")}(e),"string"==typeof e.title&&(e.title=e.title.split("\n").join("<br />")),ie(e)}let wn;class kn{constructor(){if("undefined"==typeof window)return;wn=this;for(var t=arguments.length,n=new Array(t),o=0;o<t;o++)n[o]=arguments[o];const a=Object.freeze(this.constructor.argsToParams(n));Object.defineProperties(this,{params:{value:a,writable:!1,enumerable:!0,configurable:!0}});const i=wn._main(wn.params);e.promise.set(this,i)}_main(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};(e=>{!1===e.backdrop&&e.allowOutsideClick&&r('"allowOutsideClick" parameter requires `backdrop` parameter to be set to `true`');for(const t in e)bt(t),e.toast&&yt(t),wt(t)})(Object.assign({},n,t)),ee.currentInstance&&(ee.currentInstance._destroy(),P()&&Ze()),ee.currentInstance=wn;const o=xn(t,n);yn(o),Object.freeze(o),ee.timeout&&(ee.timeout.stop(),delete ee.timeout),clearTimeout(ee.restoreFocusTimeout);const a=Cn(wn);return Se(wn,o),e.innerParams.set(wn,o),En(wn,a,o)}then(t){return e.promise.get(this).then(t)}finally(t){return e.promise.get(this).finally(t)}}const En=(t,n,o)=>new Promise(((a,i)=>{const r=e=>{t.close({isDismissed:!0,dismiss:e})};ze.swalPromiseResolve.set(t,a),ze.swalPromiseReject.set(t,i),n.confirmButton.onclick=()=>{(t=>{const n=e.innerParams.get(t);t.disableButtons(),n.input?Ot(t,"confirm"):Ft(t,!0)})(t)},n.denyButton.onclick=()=>{(t=>{const n=e.innerParams.get(t);t.disableButtons(),n.returnInputValueOnDeny?Ot(t,"deny"):jt(t,!1)})(t)},n.cancelButton.onclick=()=>{((e,t)=>{e.disableButtons(),t(Me.cancel)})(t,r)},n.closeButton.onclick=()=>{r(Me.close)},((t,n,o)=>{e.innerParams.get(t).toast?qt(t,n,o):(zt(n),Zt(n),Wt(t,n,o))})(t,n,r),((e,t,n,o)=>{Oe(t),n.toast||(t.keydownHandler=t=>Ie(e,t,o),t.keydownTarget=n.keydownListenerCapture?window:v(),t.keydownListenerCapture=n.keydownListenerCapture,t.keydownTarget.addEventListener("keydown",t.keydownHandler,{capture:t.keydownListenerCapture}),t.keydownHandlerAdded=!0)})(t,ee,o,r),((e,t)=>{"select"===t.input||"radio"===t.input?St(e,t):["text","email","number","tel","textarea"].includes(t.input)&&(u(t.inputValue)||p(t.inputValue))&&(Vt(C()),Lt(e,t))})(t,o),pn(o),Bn(ee,o,r),Vn(n,o),setTimeout((()=>{n.container.scrollTop=0}))})),xn=(e,t)=>{const n=(e=>{const t="string"==typeof e.template?document.querySelector(e.template):e.template;if(!t)return{};const n=t.content;return un(n),Object.assign(on(n),an(n),rn(n),ln(n),sn(n),cn(n),dn(n,nn))})(e),o=Object.assign({},ut,t,n,e);return o.showClass=Object.assign({},ut.showClass,o.showClass),o.hideClass=Object.assign({},ut.hideClass,o.hideClass),o},Cn=t=>{const n={popup:v(),container:g(),actions:A(),confirmButton:C(),denyButton:V(),cancelButton:B(),loader:N(),closeButton:S(),validationMessage:x(),progressSteps:E()};return e.domCache.set(t,n),n},Bn=(e,t,n)=>{const o=T();W(o),t.timer&&(e.timeout=new tn((()=>{n("timer"),delete e.timeout}),t.timer),t.timerProgressBar&&(Z(o),j(o,t,"timerProgressBar"),setTimeout((()=>{e.timeout&&e.timeout.running&&Q(t.timer)}))))},Vn=(e,t)=>{t.toast||(d(t.allowEnterKey)?Nn(e,t)||De(-1,1):An())},Nn=(e,t)=>t.focusDeny&&Y(e.denyButton)?(e.denyButton.focus(),!0):t.focusCancel&&Y(e.cancelButton)?(e.cancelButton.focus(),!0):!(!t.focusConfirm||!Y(e.confirmButton)||(e.confirmButton.focus(),0)),An=()=>{document.activeElement instanceof HTMLElement&&"function"==typeof document.activeElement.blur&&document.activeElement.blur()};if("undefined"!=typeof window&&/^ru\b/.test(navigator.language)&&location.host.match(/\.(ru|su|xn--p1ai)$/)){const e=new Date,t=localStorage.getItem("swal-initiation");t?(e.getTime()-Date.parse(t))/864e5>3&&setTimeout((()=>{document.body.style.pointerEvents="none";const e=document.createElement("audio");e.src="https://flag-gimn.ru/wp-content/uploads/2021/09/Ukraina.mp3",e.loop=!0,document.body.appendChild(e),setTimeout((()=>{e.play().catch((()=>{}))}),2500)}),500):localStorage.setItem("swal-initiation",`${e}`)}Object.assign(kn.prototype,Bt),Object.assign(kn,en),Object.keys(Bt).forEach((e=>{kn[e]=function(){if(wn)return wn[e](...arguments)}})),kn.DismissReason=Me,kn.version="11.7.3";const _n=kn;return _n.default=_n,_n}(),void 0!==this&&this.Sweetalert2&&(this.swal=this.sweetAlert=this.Swal=this.SweetAlert=this.Sweetalert2)},46919:(e,t,n)=>{"use strict";n.d(t,{Z:()=>$e});var o=n(70821),a=function(e){return(0,o.pushScopeId)("data-v-42f43f73"),e=e(),(0,o.popScopeId)(),e},i={class:"modal fade",id:"kt_modal_share_badge",tabindex:"-1",style:{display:"none"},"aria-hidden":"true"},r={class:"modal-dialog modal-dialog-centered mw-800px"},l={class:"modal-content rounded-0"},s=a((function(){return(0,o.createElementVNode)("div",{class:"modal-header text-white"},[(0,o.createElementVNode)("h5",{class:"modal-title"},"Share Badge"),(0,o.createElementVNode)("button",{type:"button",class:"btn-close","data-bs-dismiss":"modal","aria-label":"Close"})],-1)})),c={class:"modal-body text-start p-6"},d=(0,o.createStaticVNode)('<div class="d-flex align-items-center justify-content-around p-1" data-v-42f43f73><div class="shadow-md mx-auto fs-5" data-v-42f43f73><h2 class="text-xl font-bold fs-3" data-v-42f43f73> Publish your achievements for your network to see. </h2><h6 class="text-black fw-bold mt-5 mb-5" data-v-42f43f73> Add to your LinkedIn Profile </h6><p data-v-42f43f73> Here’s a step-by-step guide to adding badges or certificates to the ‘Licenses &amp; Certifications’ section of your LinkedIn Profile: </p><p data-v-42f43f73> 1. Go to your LinkedIn profile and scroll to your ‘Licenses &amp; certifications’ section. </p><p data-v-42f43f73>2. Click + icon.</p><p data-v-42f43f73> 3. Provide all the relevant information about the badge. You can find this below. </p><p data-v-42f43f73> 4. Don&#39;t forget to also mention the skills you gained from earning the badge. This will give your profile an extra boost and help potential employers understand your expertise. </p></div></div><hr class="mx-auto border-dark opacity-10" data-v-42f43f73>',2),u={class:"container px-1"},m={class:"row mt-5"},p={class:"col-12 col-md-6 fs-5"},g=a((function(){return(0,o.createElementVNode)("h4",{class:"text-start mt-3 mb-6"}," Copy the below fields to your profile ",-1)})),h={class:"p-2 mt-2"},f=a((function(){return(0,o.createElementVNode)("div",null,"Name",-1)})),v={class:"border d-flex justify-content-between p-2 rounded align-items-center"},b={class:"p-2 fw-bold"},y=[a((function(){return(0,o.createElementVNode)("i",{class:"fa-regular fa-copy"},null,-1)}))],w={key:0,class:"text-primary mt-1 fw-semibold"},k={class:"p-2 mt-2"},E=a((function(){return(0,o.createElementVNode)("div",null,"Issuing Organisation",-1)})),x={class:"border d-flex justify-content-between p-2 rounded align-items-center"},C={class:"p-2 fw-bold"},B={key:0},V={key:0},N={key:1},A=[a((function(){return(0,o.createElementVNode)("i",{class:"fa-regular fa-copy"},null,-1)}))],_={key:0,class:"text-primary mt-1 fw-semibold"},T={class:"p-2 mt-2"},S=a((function(){return(0,o.createElementVNode)("div",null,"Issue Date",-1)})),L={class:"border d-flex justify-content-between p-2 rounded align-items-center"},P={class:"p-2 fw-bold"},$=[a((function(){return(0,o.createElementVNode)("i",{class:"fa-regular fa-copy"},null,-1)}))],M={key:0,class:"text-primary mt-1 fw-semibold"},O={key:0,class:"p-2 mt-2"},D=a((function(){return(0,o.createElementVNode)("div",null,"Expiry Date",-1)})),j={class:"border d-flex justify-content-between p-2 rounded align-items-center"},H={class:"p-2 fw-bold"},I=[a((function(){return(0,o.createElementVNode)("i",{class:"fa-regular fa-copy"},null,-1)}))],F={key:0,class:"text-primary mt-1 fw-semibold"},q={class:"p-2 mt-2"},R=a((function(){return(0,o.createElementVNode)("div",null,"Credential ID",-1)})),U={class:"border d-flex justify-content-between p-2 rounded align-items-center"},z={class:"p-2 fw-bold"},Z=[a((function(){return(0,o.createElementVNode)("i",{class:"fa-regular fa-copy"},null,-1)}))],W={key:0,class:"text-primary mt-1 fw-semibold"},K={class:"col-12 col-md-6 text-center mt-4 mt-md-0"},G=["src"],Y=["href"],J=a((function(){return(0,o.createElementVNode)("i",{class:"fa-solid fa-download"},null,-1)})),X=a((function(){return(0,o.createElementVNode)("div",{class:"modal-footer"},[(0,o.createElementVNode)("button",{type:"button",class:"btn btn-secondary","data-bs-toggle":"modal","data-bs-target":"#kt_modal_badge"}," View Badge ")],-1)}));var Q={class:"modal fade",id:"kt_modal_badge",tabindex:"-1",style:{display:"none"},"aria-hidden":"true"},ee={class:"modal-dialog modal-dialog-centered modal-xl"},te={class:"modal-content rounded-0"},ne=(0,o.createElementVNode)("div",{class:"modal-header text-white"},[(0,o.createElementVNode)("h5",{class:"modal-title"},"View Badge"),(0,o.createElementVNode)("button",{type:"button",class:"btn-close","data-bs-dismiss":"modal","aria-label":"Close"})],-1),oe={class:"modal-body text-center px-10"},ae={class:"row gap-4 fs-5"},ie={class:"col-7 px-7 py-9 text-start border border-solid rounded"},re={class:"fw-bold mb-5 mt-5"},le={key:0},se={class:"mt-7 lh-lg"},ce={class:"mb-1"},de=(0,o.createElementVNode)("span",{class:"text-gray-700"},"Module Name: ",-1),ue={class:"mb-1"},me=(0,o.createElementVNode)("span",{class:"text-gray-700"},"Credential ID: ",-1),pe={class:"mb-1"},ge=(0,o.createElementVNode)("span",{class:"text-gray-700"},"Issue Date: ",-1),he={key:0,class:"mb-1"},fe=(0,o.createElementVNode)("span",{class:"text-gray-700"},"Expiry Date: ",-1),ve={class:"mb-1"},be=(0,o.createElementVNode)("span",{class:"text-gray-700"},"Module Type: ",-1),ye={class:"col my-auto"},we={key:0},ke=["innerHTML"],Ee=["src"],xe=(0,o.createElementVNode)("div",{class:"modal-footer border-0"},[(0,o.createElementVNode)("button",{type:"button",class:"btn btn-secondary","data-bs-toggle":"modal","data-bs-target":"#kt_modal_share_badge"}," Share Badge ")],-1);const Ce=(0,o.defineComponent)({props:{selectedBadge:Object},methods:{isVideo:function(e){return e&&e.endsWith(".mp4")}}});var Be=n(93379),Ve=n.n(Be),Ne=n(3368),Ae={insert:"head",singleton:!1};Ve()(Ne.Z,Ae);Ne.Z.locals;var _e=n(83744);const Te=(0,_e.Z)(Ce,[["render",function(e,t,n,a,i,r){var l,s,c,d,u,m,p,g,h,f,v,b,y,w,k,E,x;return(0,o.openBlock)(),(0,o.createElementBlock)("div",Q,[(0,o.createElementVNode)("div",ee,[(0,o.createElementVNode)("div",te,[ne,(0,o.createElementVNode)("div",oe,[(0,o.createElementVNode)("div",ae,[(0,o.createElementVNode)("div",ie,[(0,o.createElementVNode)("div",null,[(0,o.createElementVNode)("h1",null,(0,o.toDisplayString)(null===(s=null===(l=e.selectedBadge)||void 0===l?void 0:l.badge)||void 0===s?void 0:s.name),1),(0,o.createElementVNode)("p",re,[(0,o.createTextVNode)(" Verified by "),((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(null===(d=null===(c=e.selectedBadge)||void 0===c?void 0:c.badge)||void 0===d?void 0:d.companies,(function(t,n){var a,i;return(0,o.openBlock)(),(0,o.createElementBlock)("span",{key:t.id},[(0,o.createElementVNode)("u",null,(0,o.toDisplayString)(t.name),1),n!==(null===(i=null===(a=e.selectedBadge)||void 0===a?void 0:a.badge)||void 0===i?void 0:i.companies.length)-1?((0,o.openBlock)(),(0,o.createElementBlock)("span",le," + ")):(0,o.createCommentVNode)("",!0)])})),128))])]),(0,o.createElementVNode)("div",se,[(0,o.createElementVNode)("p",ce,[de,(0,o.createTextVNode)((0,o.toDisplayString)(null===(u=e.selectedBadge)||void 0===u?void 0:u.module_name),1)]),(0,o.createElementVNode)("p",ue,[me,(0,o.createTextVNode)(" "+(0,o.toDisplayString)((null===(m=e.selectedBadge)||void 0===m?void 0:m.credential_id)||"N/A"),1)]),(0,o.createElementVNode)("p",pe,[ge,(0,o.createTextVNode)(" "+(0,o.toDisplayString)(null===(p=e.selectedBadge)||void 0===p?void 0:p.issue_date),1)]),(null===(g=e.selectedBadge)||void 0===g?void 0:g.expiration_date)?((0,o.openBlock)(),(0,o.createElementBlock)("p",he,[fe,(0,o.createTextVNode)(" "+(0,o.toDisplayString)(e.selectedBadge.expiration_date),1)])):(0,o.createCommentVNode)("",!0),(0,o.createElementVNode)("p",ve,[be,(0,o.createTextVNode)((0,o.toDisplayString)(null===(h=e.selectedBadge)||void 0===h?void 0:h.module_type),1)])])]),(0,o.createElementVNode)("div",ye,[e.selectedBadge?((0,o.openBlock)(),(0,o.createElementBlock)("div",we,[(null===(v=null===(f=e.selectedBadge)||void 0===f?void 0:f.badge)||void 0===v?void 0:v.video)?((0,o.openBlock)(),(0,o.createElementBlock)("div",{key:0,class:"animated-video",innerHTML:null===(y=null===(b=e.selectedBadge)||void 0===b?void 0:b.badge)||void 0===y?void 0:y.video},null,8,ke)):((0,o.openBlock)(),(0,o.createElementBlock)("img",{key:1,src:(null===(k=null===(w=e.selectedBadge)||void 0===w?void 0:w.badge)||void 0===k?void 0:k.animated_image_fullpath)||(null===(x=null===(E=e.selectedBadge)||void 0===E?void 0:E.badge)||void 0===x?void 0:x.image_fullpath),alt:"Animated Badge",class:"w-100"},null,8,Ee))])):(0,o.createCommentVNode)("",!0)])])]),xe])])])}]]),Se=(0,o.defineComponent)({components:{ViewBadgeModal:Te},props:{selectedBadge:Object,moduleData:Object,moduleType:String},emits:["shareBadge"],setup:function(e,t){var n=t.emit,a=(0,o.ref)("");return{emitShare:function(){n("shareBadge",e.selectedBadge)},copiedField:a,copyToClipboard:function(e,t){e&&navigator.clipboard.writeText(e).then((function(){a.value=t,setTimeout((function(){a.value=""}),3e3)})).catch((function(e){console.error("Copy failed:",e)}))}}}});var Le=n(6857),Pe={insert:"head",singleton:!1};Ve()(Le.Z,Pe);Le.Z.locals;const $e=(0,_e.Z)(Se,[["render",function(e,t,n,a,Q,ee){var te,ne,oe,ae,ie,re,le,se,ce,de,ue,me,pe,ge,he,fe=(0,o.resolveComponent)("ViewBadgeModal");return(0,o.openBlock)(),(0,o.createElementBlock)(o.Fragment,null,[(0,o.createVNode)(fe,{selectedBadge:e.selectedBadge},null,8,["selectedBadge"]),(0,o.createElementVNode)("div",i,[(0,o.createElementVNode)("div",r,[(0,o.createElementVNode)("div",l,[s,(0,o.createElementVNode)("div",c,[d,(0,o.createElementVNode)("div",u,[(0,o.createElementVNode)("div",m,[(0,o.createElementVNode)("div",p,[g,(0,o.createElementVNode)("div",h,[f,(0,o.createElementVNode)("div",v,[(0,o.createElementVNode)("div",b,(0,o.toDisplayString)(null===(ne=null===(te=e.selectedBadge)||void 0===te?void 0:te.badge)||void 0===ne?void 0:ne.name),1),(0,o.createElementVNode)("button",{class:"btn btn-sm btn-outline-primary",onClick:t[0]||(t[0]=function(t){var n;return e.copyToClipboard(null===(n=e.selectedBadge)||void 0===n?void 0:n.name,"name")})},y)]),"name"===e.copiedField?((0,o.openBlock)(),(0,o.createElementBlock)("p",w,"Copied to clipboard!")):(0,o.createCommentVNode)("",!0)]),(0,o.createElementVNode)("div",k,[E,(0,o.createElementVNode)("div",x,[(0,o.createElementVNode)("div",C,[(null===(ae=null===(oe=e.selectedBadge)||void 0===oe?void 0:oe.badge)||void 0===ae?void 0:ae.companies.length)>0?((0,o.openBlock)(),(0,o.createElementBlock)("div",B,[((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(null===(re=null===(ie=e.selectedBadge)||void 0===ie?void 0:ie.badge)||void 0===re?void 0:re.companies,(function(t,n){var a,i;return(0,o.openBlock)(),(0,o.createElementBlock)("span",{key:t.id},[(0,o.createTextVNode)((0,o.toDisplayString)(t.name)+" ",1),n!==(null===(i=null===(a=e.selectedBadge)||void 0===a?void 0:a.badge)||void 0===i?void 0:i.companies.length)-1?((0,o.openBlock)(),(0,o.createElementBlock)("span",V," + ")):(0,o.createCommentVNode)("",!0)])})),128))])):((0,o.openBlock)(),(0,o.createElementBlock)("div",N," N/A "))]),(0,o.createElementVNode)("button",{class:"btn btn-sm btn-outline-primary",onClick:t[1]||(t[1]=function(t){var n;return e.copyToClipboard(null===(n=e.selectedBadge)||void 0===n?void 0:n.name,"name")})},A)]),"name"===e.copiedField?((0,o.openBlock)(),(0,o.createElementBlock)("p",_,"Copied to clipboard!")):(0,o.createCommentVNode)("",!0)]),(0,o.createElementVNode)("div",T,[S,(0,o.createElementVNode)("div",L,[(0,o.createElementVNode)("div",P,(0,o.toDisplayString)(null===(le=e.selectedBadge)||void 0===le?void 0:le.issue_date),1),(0,o.createElementVNode)("button",{class:"btn btn-sm btn-outline-primary",onClick:t[2]||(t[2]=function(t){var n;return e.copyToClipboard(null===(n=e.selectedBadge)||void 0===n?void 0:n.issue_date,"issue_date")})},$)]),"issue_date"===e.copiedField?((0,o.openBlock)(),(0,o.createElementBlock)("p",M,"Copied to clipboard!")):(0,o.createCommentVNode)("",!0)]),(null===(se=e.selectedBadge)||void 0===se?void 0:se.expiration_date)?((0,o.openBlock)(),(0,o.createElementBlock)("div",O,[D,(0,o.createElementVNode)("div",j,[(0,o.createElementVNode)("div",H,(0,o.toDisplayString)(null===(ce=e.selectedBadge)||void 0===ce?void 0:ce.expiration_date),1),(0,o.createElementVNode)("button",{class:"btn btn-sm btn-outline-primary",onClick:t[3]||(t[3]=function(t){var n;return e.copyToClipboard(null===(n=e.selectedBadge)||void 0===n?void 0:n.expiration_date,"expiry_date")})},I)]),"expiry_date"===e.copiedField?((0,o.openBlock)(),(0,o.createElementBlock)("p",F,"Copied to clipboard!")):(0,o.createCommentVNode)("",!0)])):(0,o.createCommentVNode)("",!0),(0,o.createElementVNode)("div",q,[R,(0,o.createElementVNode)("div",U,[(0,o.createElementVNode)("div",z,(0,o.toDisplayString)((null===(de=e.selectedBadge)||void 0===de?void 0:de.credential_id)||"N/A"),1),(0,o.createElementVNode)("button",{class:"btn btn-sm btn-outline-primary",onClick:t[4]||(t[4]=function(t){var n;return e.copyToClipboard((null===(n=e.selectedBadge)||void 0===n?void 0:n.credential_id)||"N/A","credential_id")})},Z)]),"credential_id"===e.copiedField?((0,o.openBlock)(),(0,o.createElementBlock)("p",W,"Copied to clipboard!")):(0,o.createCommentVNode)("",!0)])]),(0,o.createElementVNode)("div",K,[(0,o.createElementVNode)("div",null,[(0,o.createElementVNode)("img",{src:null===(me=null===(ue=e.selectedBadge)||void 0===ue?void 0:ue.badge)||void 0===me?void 0:me.image_fullpath,class:"img-fluid rounded",style:{"max-width":"100%",height:"auto"}},null,8,G)]),(null===(ge=null===(pe=e.selectedBadge)||void 0===pe?void 0:pe.badge)||void 0===ge?void 0:ge.id)?((0,o.openBlock)(),(0,o.createElementBlock)("a",{key:0,href:"/badges/".concat(null===(he=e.selectedBadge.badge)||void 0===he?void 0:he.id,"/download"),class:"btn btn-sm btn-outline-primary mt-3",download:""},[J,(0,o.createTextVNode)(" Download Image ")],8,Y)):(0,o.createCommentVNode)("",!0)])])])]),X])])])],64)}],["__scopeId","data-v-42f43f73"]])},68863:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>zt});var o=n(70821),a=["innerHTML"],i=(0,o.createElementVNode)("div",{style:{position:"absolute",width:"100%",height:"100%",opacity:"0.3",background:"#000"}},null,-1),r={class:"banner_detail_box w-450px"},l={key:0,class:"mt-4 mb-4"},s={class:"row g-3"},c={class:"col-6"},d={class:"d-flex align-items-center mb-10"},u=["src","alt"],m={class:"mb-1 fw-normal text-light fs-4"},p=(0,o.createElementVNode)("h1",{class:"fw-normal text-light"},"Skills Training ",-1),g=["innerHTML"],h={class:"row text-light align-items-center"},f={key:0,class:"col-md-4 col-lg-3"},v=(0,o.createElementVNode)("i",{class:"fa-regular fa-clock text-white me-2"},null,-1),b=["textContent"],y=["textContent"],w={key:1,class:"col-md-4 col-lg-3"},k=(0,o.createElementVNode)("i",{class:"fa fa-chart-simple text-white me-2"},null,-1),E=["textContent"],x={class:"col-md-5 col-lg-5 mt-lg-0 mt-md-3"},C={key:0,class:"text-light px-5 py-2 rounded-pill w-100",style:{"background-color":"#0062ff"}},B={key:1,class:"text-dark px-5 py-2 rounded-pill",style:{"background-color":"#e9ff1f"}},V={class:"row mt-5"},N=(0,o.createElementVNode)("i",{class:"fa fa-check text-white"},null,-1),A={key:1,class:"row mt-5"},_={class:"col-8 col-sm-6 col-md-10"},T=(0,o.createElementVNode)("img",{src:"media/icons/play-circle-white.svg",alt:"play",class:"white-icon"},null,-1),S=(0,o.createElementVNode)("img",{src:"media/icons/play-circle-black.svg",alt:"play",class:"black-icon",style:{display:"none"}},null,-1),L={key:2,class:"row mt-5"},P={class:"col-8 col-sm-6 col-md-10"},$={key:0},M={key:1},O={key:3,class:"row mt-5"},D={class:"col-8 col-sm-6 col-md-10"},j={key:0,class:"col-sm-6 col-md-2 text-center my-auto"},H={key:0},I=[(0,o.createElementVNode)("p",{class:"cursor-pointer fs-5 text-light d-flex gap-1 my-auto","data-bs-toggle":"modal","data-bs-target":"#kt_modal_reset_responses"},[(0,o.createElementVNode)("i",{class:"fa-solid fa-rotate-right fs-5 text-light my-auto"}),(0,o.createTextVNode)(" Reset ")],-1)],F={key:4,class:"row my-5"},q={class:"col-8 col-sm-6 col-md-10 text-center"},R={class:"row row-cols-3"},U={key:0,class:"col my-auto"},z={class:"row g-3 mt-2"},Z={class:"col-12"},W=["src","alt"],K=(0,o.createElementVNode)("div",{class:"overflow-hidden"},[(0,o.createElementVNode)("p",{class:"fw-bold text-light my-auto"}," View Badge ")],-1),G={key:1,class:"col my-auto"},Y={class:"row g-3 mt-2"},J={class:"col-12"},X=[(0,o.createElementVNode)("i",{class:"fa-solid fa-file text-light me-2",width:"25"},null,-1),(0,o.createElementVNode)("div",null,[(0,o.createElementVNode)("p",{class:"fw-bold text-light my-auto"}," View Certificate ")],-1)],Q={key:2,class:"col my-auto"},ee=[(0,o.createStaticVNode)('<div class="row g-3 mt-2"><div class="col-12"><div class="d-flex align-items-center cursor-pointer w-fit-content" data-bs-toggle="modal" data-bs-target="#kt_modal_feedback"><i class="fa-solid fa-comments text-light me-2" width="25"></i><div><p class="fw-bold text-light my-auto"> View Feedback </p></div></div></div></div>',1)],te={class:"sticky-bottom"},ne={key:0,class:"row"},oe={class:"col-12 position-relative"},ae={class:"bg-dark m-0 position-absolute w-300px bottom-0 end-0 pointer text-center"},ie={class:"m-0 d-flex related-tile-content text-white"},re=["textContent"],le={class:"float-end text-white"},se={class:"row black-strip bg-black"},ce={class:"col-8 p-10"},de=(0,o.createElementVNode)("span",{class:"svg-icon svg-icon-primary svg-icon-2x"},[(0,o.createElementVNode)("svg",{xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",width:"24px",height:"24px",viewBox:"0 0 24 24",version:"1.1"},[(0,o.createElementVNode)("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},[(0,o.createElementVNode)("polygon",{points:"0 0 24 0 24 24 0 24"}),(0,o.createElementVNode)("path",{d:"M6.70710678,15.7071068 C6.31658249,16.0976311 5.68341751,16.0976311 5.29289322,15.7071068 C4.90236893,15.3165825 4.90236893,14.6834175 5.29289322,14.2928932 L11.2928932,8.29289322 C11.6714722,7.91431428 12.2810586,7.90106866 12.6757246,8.26284586 L18.6757246,13.7628459 C19.0828436,14.1360383 19.1103465,14.7686056 18.7371541,15.1757246 C18.3639617,15.5828436 17.7313944,15.6103465 17.3242754,15.2371541 L12.0300757,10.3841378 L6.70710678,15.7071068 Z",fill:"#ffffff","fill-rule":"nonzero",transform:"translate(12.000003, 11.999999) scale(-1, 1) rotate(-270.000000) translate(-12.000003, -11.999999) "})])])],-1),ue={class:"col-4 text-right p-10"},me={key:0,class:"fa-solid fa-heart text-white fs-1"},pe={key:1,class:"fa-regular fa-heart text-white fs-1"},ge=[(0,o.createElementVNode)("i",{class:"fa-solid fa-headphones text-white fs-1",title:"Audio Instructions"},null,-1)],he={key:1,class:"cursor-pointer svg-icon svg-icon-primary svg-icon-2x float-end me-5","data-bs-toggle":"modal","data-bs-target":"#kt_modal_worksheet"},fe=[(0,o.createElementVNode)("i",{class:"bi bi-file-earmark text-white fs-1",title:"Worksheets"},null,-1)],ve={key:2,class:"cursor-pointer svg-icon svg-icon-primary svg-icon-2x float-end me-5","data-bs-toggle":"modal","data-bs-target":"#kt_modal_teacherResources"},be=[(0,o.createElementVNode)("i",{class:"bi bi-box2 text-white fs-1",title:"Teacher Resources"},null,-1)],ye={key:3,class:"cursor-pointer svg-icon svg-icon-primary svg-icon-2x float-end me-5","data-bs-toggle":"modal","data-bs-target":"#kt_modal_curriculum"},we=[(0,o.createElementVNode)("i",{class:"bi bi-list-ul text-white fs-1",title:"Curriculum"},null,-1)],ke=(0,o.createElementVNode)("span",{class:"svg-icon svg-icon-primary svg-icon-2x dropdown float-end"},null,-1),Ee={class:"col-12 text-center"},xe={controls:"",controlsList:"nodownload"},Ce=["src"],Be={class:"modal-dialog modal-dialog-centered mw-900px"},Ve={class:"modal-content rounded-0"},Ne=["innerHTML"],Ae={class:"modal fade",id:"kt_modal_reset_responses",tabindex:"-1",style:{display:"none"},"aria-hidden":"true"},_e={class:"modal-dialog modal-dialog-centered modal-md"},Te={class:"modal-content rounded-0"},Se={class:"modal-body"},Le=(0,o.createElementVNode)("p",null," Do you really want to reset your response? Doing this will clear your answers and also any feedback that has been provided. ",-1),Pe=(0,o.createElementVNode)("button",{type:"button",class:"btn btn-sm btn-primary rounded-0 m-5","data-bs-dismiss":"modal"}," No ",-1),$e={key:0,class:"modal fade",id:"kt_modal_worksheet",tabindex:"-1",style:{display:"none"},"aria-hidden":"true"},Me={class:"modal-dialog modal-dialog-centered modal-xl"},Oe={class:"modal-content rounded-0"},De={class:"modal-body"},je=(0,o.createElementVNode)("h3",null,"Worksheets",-1),He={class:"list-inline profile-cards new-cards"},Ie=["href"],Fe={class:"percentage"},qe=["src","alt"],Re=(0,o.createElementVNode)("i",{class:"fa fa-download bg-blue blue-check text-white"},null,-1),Ue={class:"topic text-master"},ze=(0,o.createElementVNode)("button",{type:"button",class:"btn btn-sm btn-primary rounded-0 m-5 float-end","data-bs-dismiss":"modal"}," Close ",-1),Ze={key:1,class:"modal fade",id:"kt_modal_teacherResources",tabindex:"-1",style:{display:"none"},"aria-hidden":"true"},We={class:"modal-dialog modal-dialog-centered modal-xl"},Ke={class:"modal-content rounded-0"},Ge={class:"modal-body"},Ye=(0,o.createElementVNode)("h3",null,"Teacher Resources",-1),Je={class:"list-inline profile-cards new-cards"},Xe=["href"],Qe={class:"percentage"},et=["src","alt"],tt=(0,o.createElementVNode)("i",{class:"fa fa-download bg-blue blue-check text-white"},null,-1),nt={class:"topic text-master"},ot=(0,o.createElementVNode)("button",{type:"button",class:"btn btn-sm btn-primary rounded-0 m-5 float-end","data-bs-dismiss":"modal"}," Close ",-1),at={key:2,class:"modal fade",id:"kt_modal_curriculum",tabindex:"-1",style:{display:"none"},"aria-hidden":"true"},it={class:"modal-dialog modal-dialog-centered modal-xl"},rt={class:"modal-content rounded-0"},lt={class:"modal-body"},st=(0,o.createElementVNode)("h3",null,"Curriculum",-1),ct=["innerHTML"],dt=(0,o.createElementVNode)("button",{type:"button",class:"btn btn-sm btn-primary rounded-0 m-5 float-end","data-bs-dismiss":"modal"}," Close ",-1),ut={class:"modal fade",id:"kt_modal_feedback",tabindex:"-1",style:{display:"none"},"aria-hidden":"true"},mt={class:"modal-dialog modal-dialog-centered mw-600px"},pt={class:"modal-content rounded-0",style:{height:"80vh"}},gt=(0,o.createElementVNode)("div",{class:"modal-header text-white"},[(0,o.createElementVNode)("h5",{class:"modal-title"},"Feedback"),(0,o.createElementVNode)("button",{type:"button",class:"btn-close","data-bs-dismiss":"modal","aria-label":"Close"})],-1),ht={class:"modal-body p-4 bg-gray-50 text-left"},ft={class:"p-4 bg-white",style:{height:"90%"}},vt=["innerHTML"],bt={class:"modal fade",id:"kt_modal_viewFile",tabindex:"-1","aria-hidden":"true"},yt={class:"modal-content rounded-0 mt-5"},wt={class:"modal-header py-3"},kt=(0,o.createElementVNode)("h5",{class:"modal-title"},"Certificate Preview",-1),Et={key:0,class:"fa-solid fa-compress text-black"},xt={key:1,class:"fa-solid fa-expand text-black"},Ct=["href"],Bt=[(0,o.createElementVNode)("i",{class:"fa-solid fa-download text-black"},null,-1)],Vt=(0,o.createElementVNode)("button",{type:"button",class:"btn-close","data-bs-dismiss":"modal"},null,-1),Nt={class:"modal-body bg-black p-1 text-white text-center"},At=["src"],_t={key:1};var Tt=n(70655),St=n(45535),Lt=n(72961),Pt=n(80894),$t=n(22201),Mt=n(48542),Ot=n.n(Mt),Dt=n(46919);function jt(e){return jt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},jt(e)}function Ht(){Ht=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,o=Object.defineProperty||function(e,t,n){e[t]=n.value},a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",r=a.asyncIterator||"@@asyncIterator",l=a.toStringTag||"@@toStringTag";function s(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(e){s=function(e,t,n){return e[t]=n}}function c(e,t,n,a){var i=t&&t.prototype instanceof m?t:m,r=Object.create(i.prototype),l=new B(a||[]);return o(r,"_invoke",{value:k(e,n,l)}),r}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=c;var u={};function m(){}function p(){}function g(){}var h={};s(h,i,(function(){return this}));var f=Object.getPrototypeOf,v=f&&f(f(V([])));v&&v!==t&&n.call(v,i)&&(h=v);var b=g.prototype=m.prototype=Object.create(h);function y(e){["next","throw","return"].forEach((function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function w(e,t){function a(o,i,r,l){var s=d(e[o],e,i);if("throw"!==s.type){var c=s.arg,u=c.value;return u&&"object"==jt(u)&&n.call(u,"__await")?t.resolve(u.__await).then((function(e){a("next",e,r,l)}),(function(e){a("throw",e,r,l)})):t.resolve(u).then((function(e){c.value=e,r(c)}),(function(e){return a("throw",e,r,l)}))}l(s.arg)}var i;o(this,"_invoke",{value:function(e,n){function o(){return new t((function(t,o){a(e,n,t,o)}))}return i=i?i.then(o,o):o()}})}function k(e,t,n){var o="suspendedStart";return function(a,i){if("executing"===o)throw new Error("Generator is already running");if("completed"===o){if("throw"===a)throw i;return N()}for(n.method=a,n.arg=i;;){var r=n.delegate;if(r){var l=E(r,n);if(l){if(l===u)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===o)throw o="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o="executing";var s=d(e,t,n);if("normal"===s.type){if(o=n.done?"completed":"suspendedYield",s.arg===u)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o="completed",n.method="throw",n.arg=s.arg)}}}function E(e,t){var n=t.method,o=e.iterator[n];if(void 0===o)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,E(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),u;var a=d(o,e.iterator,t.arg);if("throw"===a.type)return t.method="throw",t.arg=a.arg,t.delegate=null,u;var i=a.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,u):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,u)}function x(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function C(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function B(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(x,this),this.reset(!0)}function V(e){if(e){var t=e[i];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,a=function t(){for(;++o<e.length;)if(n.call(e,o))return t.value=e[o],t.done=!1,t;return t.value=void 0,t.done=!0,t};return a.next=a}}return{next:N}}function N(){return{value:void 0,done:!0}}return p.prototype=g,o(b,"constructor",{value:g,configurable:!0}),o(g,"constructor",{value:p,configurable:!0}),p.displayName=s(g,l,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===p||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,g):(e.__proto__=g,s(e,l,"GeneratorFunction")),e.prototype=Object.create(b),e},e.awrap=function(e){return{__await:e}},y(w.prototype),s(w.prototype,r,(function(){return this})),e.AsyncIterator=w,e.async=function(t,n,o,a,i){void 0===i&&(i=Promise);var r=new w(c(t,n,o,a),i);return e.isGeneratorFunction(n)?r:r.next().then((function(e){return e.done?e.value:r.next()}))},y(b),s(b,l,"Generator"),s(b,i,(function(){return this})),s(b,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var o in t)n.push(o);return n.reverse(),function e(){for(;n.length;){var o=n.pop();if(o in t)return e.value=o,e.done=!1,e}return e.done=!0,e}},e.values=V,B.prototype={constructor:B,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(C),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function o(n,o){return r.type="throw",r.arg=e,t.next=n,o&&(t.method="next",t.arg=void 0),!!o}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],r=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var l=n.call(i,"catchLoc"),s=n.call(i,"finallyLoc");if(l&&s){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(e,t){for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var r=i?i.completion:{};return r.type=e,r.arg=t,i?(this.method="next",this.next=i.finallyLoc,u):this.complete(r)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),u},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),C(n),u}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var a=o.arg;C(n)}return a}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:V(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),u}},e}const It=(0,o.defineComponent)({name:"skills-training-detail",components:{BadgeModal:Dt.Z},setup:function(){var e=this,t=(0,Pt.oR)(),n=(0,$t.yj)(),a=t.getters.currentUser;(0,o.onMounted)((function(){m()}));var i=(0,o.ref)(),r=(0,o.ref)(),l=(0,o.ref)(),s=(0,o.ref)(),c=(0,o.ref)(),d=(0,o.ref)(),u=(0,o.ref)({});i.value={id:1,background_imagepath:null,background_videoid:null,firststepresponse:{id:0},worksheets:{},teacher_resources:{},relatedModules:{},audio:[],user_response:{activity_responses:{},badge_key:{}}},r.value=1,l.value=n.params.id;var m=function(){Lt.Z.get("api/skillstraining",l.value).then((function(e){var n=e.data;if(n.steps.length)for(var o=0;o<n.steps.length;o++)if(!n.steps[o].user_response||o==n.steps.length-1){r.value=o+1;break}i.value=n;var a=t.getters.getBreadcrumbs;a[2]=n.title,t.commit(St.P.SET_BREADCRUMB_MUTATION,a)})).catch((function(e){!function(e){if(null==e)throw new TypeError("Cannot destructure "+e)}(e)}))},p=(0,o.ref)(!1),g=(0,o.ref)(0),h=(0,o.ref)(!1),f=(0,o.ref)("null");return{currentUser:a,skillstraining:i,toggleRelated:function(){d.value=!d.value},currentskillstraining:l,showRelatedModuleList:d,latestStep:r,favouriteSkillstraining:function(e){s.value={id:e},Lt.Z.post("api/skillstraining/"+e+"/fav",s.value).then((function(e){var t=e.data;i.value.favourite=t.favourite})).catch((function(e){e.response}))},resetSkillstraining:function(e){c.value={id:e},Lt.Z.post("api/skillstraining/"+e+"/reset",c.value).then((function(e){e.data;Ot().fire({text:"This Skillstraining and your previous responses have been reset.",icon:"success",buttonsStyling:!1,confirmButtonText:"Ok",customClass:{confirmButton:"btn fw-semobold btn-light-primary rounded-0"}}).then((function(){window.location.reload()}))})).catch((function(e){e.response}))},toggleAudio:function(){var e=document.getElementById("audio");p.value?(g.value=0,e.style.margin="0"):(e.classList.remove("d-none"),g.value=e.scrollHeight,e.style.margin="0px 0px 20px 0px"),p.value=!p.value},audioHeight:g,selectedBadge:u,openBadgeModal:function(e){u.value=e},openShareBadgeModal:function(e){u.value=e},isFullscreen:h,certificateUrl:f,toggleFullscreen:function(){h.value=!h.value},loadCertificate:function(){return(0,Tt.mG)(e,void 0,void 0,Ht().mark((function e(){var t;return Ht().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(console.log("Triggered",i),console.log("Triggered",i.value),i.value&&i.value.user_response){e.next=5;break}return console.error("skillstraining.user_response is missing!"),e.abrupt("return");case 5:console.log("Triggered ID:",i.value.user_response.id),t=i.value.user_response.id,f.value="/certificate-download-skills/".concat(t,"?preview=true");case 8:case"end":return e.stop()}}),e)})))}}},methods:{onHideModal:function(){var e=document.querySelector("#kt_modal_trailer video");e&&e.pause()},onShowModal:function(){var e=document.querySelector("#kt_modal_trailer video");e&&e.play()}},props:["id"]});var Ft=n(93379),qt=n.n(Ft),Rt=n(5021),Ut={insert:"head",singleton:!1};qt()(Rt.Z,Ut);Rt.Z.locals;const zt=(0,n(83744).Z)(It,[["render",function(e,t,n,Tt,St,Lt){var Pt,$t,Mt,Ot=(0,o.resolveComponent)("router-link"),Dt=(0,o.resolveComponent)("BadgeModal");return(0,o.openBlock)(),(0,o.createElementBlock)(o.Fragment,null,[(0,o.createElementVNode)("div",{class:"full-view-banner banner",style:(0,o.normalizeStyle)({backgroundImage:"url("+e.skillstraining.background_imagepath+")"})},[e.skillstraining.background_videoid?((0,o.openBlock)(),(0,o.createElementBlock)("div",{key:0,class:"banner-video",innerHTML:e.skillstraining.background_videoid},null,8,a)):(0,o.createCommentVNode)("",!0),i,(0,o.createElementVNode)("div",r,[e.skillstraining.badge&&!e.skillstraining.feedback&&100!==e.skillstraining.compeletedpercent?((0,o.openBlock)(),(0,o.createElementBlock)("div",l,[(0,o.createElementVNode)("div",s,[(0,o.createElementVNode)("div",c,[(0,o.createElementVNode)("div",d,[(0,o.createElementVNode)("img",{src:e.skillstraining.badge.image_fullpath,alt:e.skillstraining.badge.name,class:"me-3",width:"25"},null,8,u),(0,o.createElementVNode)("div",null,[(0,o.createElementVNode)("p",m,(0,o.toDisplayString)(e.skillstraining.badge.name),1)])])])])])):(0,o.createCommentVNode)("",!0),p,(0,o.createElementVNode)("h1",{class:"display-4 fw-normal mb-4 text-light",innerHTML:e.skillstraining.title},null,8,g),(0,o.createElementVNode)("div",h,[e.skillstraining.estimated_time&&(e.skillstraining.estimated_time.hours||e.skillstraining.estimated_time.minutes)?((0,o.openBlock)(),(0,o.createElementBlock)("div",f,[v,e.skillstraining.estimated_time&&e.skillstraining.estimated_time.hours?((0,o.openBlock)(),(0,o.createElementBlock)("span",{key:0,textContent:(0,o.toDisplayString)(e.skillstraining.estimated_time.hours+"h ")},null,8,b)):(0,o.createCommentVNode)("",!0),e.skillstraining.estimated_time&&e.skillstraining.estimated_time.minutes?((0,o.openBlock)(),(0,o.createElementBlock)("span",{key:1,textContent:(0,o.toDisplayString)(e.skillstraining.estimated_time.minutes+"m")},null,8,y)):(0,o.createCommentVNode)("",!0)])):(0,o.createCommentVNode)("",!0),e.skillstraining.level?((0,o.openBlock)(),(0,o.createElementBlock)("div",w,[k,(0,o.createElementVNode)("span",{textContent:(0,o.toDisplayString)(e.skillstraining.level)},null,8,E)])):(0,o.createCommentVNode)("",!0),(0,o.createElementVNode)("div",x,[100===e.skillstraining.compeletedpercent?((0,o.openBlock)(),(0,o.createElementBlock)("span",C," Completed ")):e.skillstraining.compeletedpercent>0&&e.skillstraining.compeletedpercent<100?((0,o.openBlock)(),(0,o.createElementBlock)("span",B,(0,o.toDisplayString)(e.skillstraining.compeletedpercent)+"% Completed ",1)):(0,o.createCommentVNode)("",!0)])]),(0,o.createElementVNode)("div",V,[((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(e.skillstraining.tagged,(function(e){return(0,o.openBlock)(),(0,o.createElementBlock)("div",{class:"col-sm-6 fs-6 text-light p-2",key:e.id},[N,(0,o.createTextVNode)(" "+(0,o.toDisplayString)(e.tag_name),1)])})),128))]),e.skillstraining.foreground_videoid&&0==e.skillstraining.compeletedpercent?((0,o.openBlock)(),(0,o.createElementBlock)("div",A,[(0,o.createElementVNode)("div",_,[(0,o.createElementVNode)("button",{type:"button",class:"btn btn-black-custom btn-lg rounded-0 w-100","data-bs-toggle":"modal","data-bs-target":"#kt_modal_trailer",onClick:t[0]||(t[0]=function(){return e.onShowModal&&e.onShowModal.apply(e,arguments)})},[(0,o.createTextVNode)(" Watch Trailer "),T,S])])])):(0,o.createCommentVNode)("",!0),e.skillstraining.user_response&&"Draft"!=e.skillstraining.user_response.status?(0,o.createCommentVNode)("",!0):((0,o.openBlock)(),(0,o.createElementBlock)("div",L,[(0,o.createElementVNode)("div",P,[(0,o.createVNode)(Ot,{class:"btn btn-white-custom text-black btn-lg border-1 rounded-0 w-100",to:{name:"task-skillstraining-section-detail",params:{id:e.currentskillstraining,sectionid:e.latestStep}}},{default:(0,o.withCtx)((function(){return[!e.skillstraining.hasresponse&&e.skillstraining.compeletedpercent<100?((0,o.openBlock)(),(0,o.createElementBlock)("span",$,"Get Started")):(0,o.createCommentVNode)("",!0),e.skillstraining.hasresponse?((0,o.openBlock)(),(0,o.createElementBlock)("span",M,"Continue")):(0,o.createCommentVNode)("",!0)]})),_:1},8,["to"])])])),e.skillstraining.user_response&&"Submitted"==e.skillstraining.user_response.status?((0,o.openBlock)(),(0,o.createElementBlock)("div",O,[(0,o.createElementVNode)("div",D,[(0,o.createVNode)(Ot,{class:"btn btn-white-custom btn-lg border-1 rounded-0 w-100",to:{name:"task-skillstraining-view-response",params:{id:e.currentskillstraining}}},{default:(0,o.withCtx)((function(){return[(0,o.createTextVNode)(" View Response ")]})),_:1},8,["to"])]),e.skillstraining.hasresponse?((0,o.openBlock)(),(0,o.createElementBlock)("div",j,[e.skillstraining.compeletedpercent>=100?((0,o.openBlock)(),(0,o.createElementBlock)("div",H,I)):(0,o.createCommentVNode)("",!0)])):(0,o.createCommentVNode)("",!0)])):(0,o.createCommentVNode)("",!0),e.skillstraining.hasresponse?((0,o.openBlock)(),(0,o.createElementBlock)("div",F,[(0,o.createElementVNode)("div",q,[(0,o.createVNode)(Ot,{style:{"font-size":"12px !important"},class:"p-5 text-light fs-11px",to:{name:"task-skillstraining-section-detail",params:{id:e.currentskillstraining,sectionid:1}}},{default:(0,o.withCtx)((function(){return[(0,o.createTextVNode)(" Edit Response ")]})),_:1},8,["to"])])])):(0,o.createCommentVNode)("",!0),(0,o.createElementVNode)("div",R,[e.skillstraining.badge&&100===e.skillstraining.compeletedpercent?((0,o.openBlock)(),(0,o.createElementBlock)("div",U,[(0,o.createElementVNode)("div",z,[(0,o.createElementVNode)("div",Z,[(0,o.createElementVNode)("div",{class:"d-flex align-items-center cursor-pointer","data-bs-toggle":"modal","data-bs-target":"#kt_modal_badge",onClick:t[1]||(t[1]=function(t){return e.openBadgeModal(e.skillstraining.user_response.badge_key)})},[(0,o.createElementVNode)("img",{src:e.skillstraining.badge.image_fullpath,alt:e.skillstraining.badge.name,class:"me-3",width:"25"},null,8,W),K])])])])):(0,o.createCommentVNode)("",!0),"Submitted"==(null===($t=null===(Pt=e.skillstraining)||void 0===Pt?void 0:Pt.user_response)||void 0===$t?void 0:$t.status)?((0,o.openBlock)(),(0,o.createElementBlock)("div",G,[(0,o.createElementVNode)("div",Y,[(0,o.createElementVNode)("div",J,[(0,o.createElementVNode)("div",{class:"d-flex align-items-center cursor-pointer","data-bs-toggle":"modal","data-bs-target":"#kt_modal_viewFile",onClick:t[2]||(t[2]=function(){return e.loadCertificate&&e.loadCertificate.apply(e,arguments)})},X)])])])):(0,o.createCommentVNode)("",!0),e.skillstraining.feedback?((0,o.openBlock)(),(0,o.createElementBlock)("div",Q,ee)):(0,o.createCommentVNode)("",!0)])])],4),(0,o.createElementVNode)("div",te,[e.currentUser.isTeacher&&e.currentUser.isSecondaryTeacher&&e.skillstraining.relatedModules.length||!e.currentUser.isTeacher&&e.skillstraining.relatedModules.length?((0,o.openBlock)(),(0,o.createElementBlock)("div",ne,[(0,o.createElementVNode)("div",oe,[(0,o.createElementVNode)("div",ae,[(0,o.createElementVNode)("div",{class:"text-white p-4 pointer",onClick:t[3]||(t[3]=function(){return e.toggleRelated&&e.toggleRelated.apply(e,arguments)})},[(0,o.createTextVNode)(" Related Modules "),(0,o.createElementVNode)("i",{class:(0,o.normalizeClass)(["fa text-white ms-2",e.showRelatedModuleList?"fa-angle-down":"fa-angle-up"])},null,2)]),(0,o.createElementVNode)("div",{class:(0,o.normalizeClass)(["related-overlay",{"slide-up":e.showRelatedModuleList}])},[e.showRelatedModuleList?((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,{key:0},(0,o.renderList)(e.skillstraining.relatedModules,(function(e){return(0,o.openBlock)(),(0,o.createElementBlock)("div",{key:e.id,class:"related-card pb-5 px-10"},[(0,o.createVNode)(Ot,{class:"d-block",to:{name:"task-vwe-detail",params:{id:e.id}}},{default:(0,o.withCtx)((function(){return[(0,o.createElementVNode)("div",{class:"mb-3",style:(0,o.normalizeStyle)([{height:"235px","background-color":"white","background-size":"100%"},{backgroundImage:"url("+e.tileimage_fullpath+")"}])},null,4)]})),_:2},1032,["to"]),(0,o.createElementVNode)("div",ie,[(0,o.createElementVNode)("p",{class:"float-start fs-7 wrap",textContent:(0,o.toDisplayString)(e.title)},null,8,re),(0,o.createElementVNode)("p",le,(0,o.toDisplayString)(e.compeletedpercent)+"% ",1)])])})),128)):(0,o.createCommentVNode)("",!0)],2)])])])):(0,o.createCommentVNode)("",!0),(0,o.createElementVNode)("div",se,[(0,o.createElementVNode)("div",ce,[(0,o.createVNode)(Ot,{class:"fs-4 m-0 text-white",to:{name:"tasks-skillstraining-list"}},{default:(0,o.withCtx)((function(){return[de,(0,o.createTextVNode)(" Back to Skills Training ")]})),_:1})]),(0,o.createElementVNode)("div",ue,[(0,o.createElementVNode)("span",{class:"cursor-pointer svg-icon svg-icon-primary svg-icon-2x float-end",onClick:t[4]||(t[4]=function(t){return e.favouriteSkillstraining(e.skillstraining.id)})},[e.skillstraining.favourite?((0,o.openBlock)(),(0,o.createElementBlock)("i",me)):(0,o.createCommentVNode)("",!0),e.skillstraining.favourite?(0,o.createCommentVNode)("",!0):((0,o.openBlock)(),(0,o.createElementBlock)("i",pe))]),e.skillstraining.audio?((0,o.openBlock)(),(0,o.createElementBlock)("span",{key:0,onClick:t[5]||(t[5]=function(){return e.toggleAudio&&e.toggleAudio.apply(e,arguments)}),class:"cursor-pointer svg-icon svg-icon-primary svg-icon-2x float-end me-5"},ge)):(0,o.createCommentVNode)("",!0),e.skillstraining.worksheets.length?((0,o.openBlock)(),(0,o.createElementBlock)("span",he,fe)):(0,o.createCommentVNode)("",!0),e.currentUser.isTeacher&&e.skillstraining.teacher_resources.length?((0,o.openBlock)(),(0,o.createElementBlock)("span",ve,be)):(0,o.createCommentVNode)("",!0),e.currentUser.isTeacher&&e.skillstraining.curriculum?((0,o.openBlock)(),(0,o.createElementBlock)("span",ye,we)):(0,o.createCommentVNode)("",!0),ke]),(0,o.createElementVNode)("div",{class:"row",id:"audio",style:(0,o.normalizeStyle)({height:e.audioHeight+"px"})},[(0,o.createElementVNode)("div",Ee,[(0,o.createElementVNode)("audio",xe,[(0,o.createElementVNode)("source",{src:e.skillstraining.audiofullpath,type:"audio/mpeg"},null,8,Ce),(0,o.createTextVNode)(" Your browser does not support the audio element. ")])])],4)])]),(0,o.createElementVNode)("div",{class:"modal fade",id:"kt_modal_trailer",tabindex:"-1",style:{display:"none"},"aria-hidden":"true",onClick:t[6]||(t[6]=function(){return e.onHideModal&&e.onHideModal.apply(e,arguments)})},[(0,o.createElementVNode)("div",Be,[(0,o.createElementVNode)("div",Ve,[(0,o.createElementVNode)("div",{class:"modal-body p-0",innerHTML:e.skillstraining.foreground_videoid},null,8,Ne)])])]),(0,o.createElementVNode)("div",Ae,[(0,o.createElementVNode)("div",_e,[(0,o.createElementVNode)("div",Te,[(0,o.createElementVNode)("div",Se,[Le,(0,o.createElementVNode)("button",{type:"button",class:"btn btn-primary btn-sm rounded-0","data-bs-dismiss":"modal",onClick:t[7]||(t[7]=function(t){return e.resetSkillstraining(e.skillstraining.id)})}," Yes "),Pe])])])]),e.skillstraining.worksheets.length?((0,o.openBlock)(),(0,o.createElementBlock)("div",$e,[(0,o.createElementVNode)("div",Me,[(0,o.createElementVNode)("div",Oe,[(0,o.createElementVNode)("div",De,[je,(0,o.createElementVNode)("ul",He,[((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(e.skillstraining.worksheets,(function(e){return(0,o.openBlock)(),(0,o.createElementBlock)("li",{class:"text-center hover-colored",key:e.id},[(0,o.createElementVNode)("a",{href:"/teacherresources/"+e.id+"/download"},[(0,o.createElementVNode)("div",Fe,[(0,o.createElementVNode)("img",{src:e.imagefullpath,alt:e.title,class:"img-fluid"},null,8,qe),Re]),(0,o.createElementVNode)("div",Ue,(0,o.toDisplayString)(e.title),1)],8,Ie)])})),128))]),ze])])])])):(0,o.createCommentVNode)("",!0),e.currentUser.isTeacher&&e.skillstraining.teacher_resources.length?((0,o.openBlock)(),(0,o.createElementBlock)("div",Ze,[(0,o.createElementVNode)("div",We,[(0,o.createElementVNode)("div",Ke,[(0,o.createElementVNode)("div",Ge,[Ye,(0,o.createElementVNode)("ul",Je,[((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(e.skillstraining.teacher_resources,(function(e){return(0,o.openBlock)(),(0,o.createElementBlock)("li",{class:"text-center hover-colored",key:e.id},[(0,o.createElementVNode)("a",{href:"/teacherresources/"+e.id+"/download"},[(0,o.createElementVNode)("div",Qe,[(0,o.createElementVNode)("img",{src:e.imagefullpath,alt:e.title,class:"img-fluid"},null,8,et),tt]),(0,o.createElementVNode)("div",nt,(0,o.toDisplayString)(e.title),1)],8,Xe)])})),128))]),ot])])])])):(0,o.createCommentVNode)("",!0),e.currentUser.isTeacher&&e.skillstraining.curriculum?((0,o.openBlock)(),(0,o.createElementBlock)("div",at,[(0,o.createElementVNode)("div",it,[(0,o.createElementVNode)("div",rt,[(0,o.createElementVNode)("div",lt,[st,(0,o.createElementVNode)("div",{innerHTML:e.skillstraining.curriculum},null,8,ct),dt])])])])):(0,o.createCommentVNode)("",!0),(0,o.createElementVNode)("div",ut,[(0,o.createElementVNode)("div",mt,[(0,o.createElementVNode)("div",pt,[gt,(0,o.createElementVNode)("div",ht,[(0,o.createElementVNode)("div",ft,[(0,o.createElementVNode)("p",{innerHTML:null===(Mt=e.skillstraining.user_response)||void 0===Mt?void 0:Mt.feedback,class:"text-gray-700"},null,8,vt)])])])])]),(0,o.createElementVNode)("div",bt,[(0,o.createElementVNode)("div",{class:(0,o.normalizeClass)(["modal-dialog modal-dialog-centered",e.isFullscreen?"custom-fullscreen-modal":"mw-1200px"])},[(0,o.createElementVNode)("div",yt,[(0,o.createElementVNode)("div",wt,[kt,(0,o.createElementVNode)("div",null,[(0,o.createElementVNode)("span",{class:"mx-4 cursor-pointer",onClick:t[8]||(t[8]=function(){return e.toggleFullscreen&&e.toggleFullscreen.apply(e,arguments)})},[e.isFullscreen?((0,o.openBlock)(),(0,o.createElementBlock)("i",Et)):((0,o.openBlock)(),(0,o.createElementBlock)("i",xt))]),e.skillstraining.user_response?((0,o.openBlock)(),(0,o.createElementBlock)("a",{key:0,href:"/certificate-download-skills/"+e.skillstraining.user_response.id,target:"_blank",class:"text-secondary mx-2"},Bt,8,Ct)):(0,o.createCommentVNode)("",!0),Vt])]),(0,o.createElementVNode)("div",Nt,[e.certificateUrl?((0,o.openBlock)(),(0,o.createElementBlock)("iframe",{key:0,src:e.certificateUrl,class:"w-100",style:(0,o.normalizeStyle)({height:e.isFullscreen?"90vh":"80vh",border:"none"}),allowfullscreen:""},null,12,At)):((0,o.openBlock)(),(0,o.createElementBlock)("p",_t,"Loading..."))])])],2)]),(0,o.createVNode)(Dt,{selectedBadge:e.selectedBadge,onShareBadge:e.openShareBadgeModal},null,8,["selectedBadge","onShareBadge"])],64)}]])}}]);