<template>
    <!-- View Badge -->
    <ViewBadgeModal :selectedBadge="selectedBadge" @openShareModal="openShareBadgeModal"/>

    <!-- Share Badge -->
    <div class="modal fade" id="kt_modal_share_badge" tabindex="-1" style="display: none" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered mw-800px">
            <div class="modal-content rounded-0">
                <div class="modal-header text-white">
                    <h5 class="modal-title">Share Badge</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-start p-6">
                    <div class="d-flex align-items-center justify-content-around p-1">
                        <div class="shadow-md mx-auto fs-5">
                            <h2 class="text-xl font-bold fs-3">
                                Publish your achievements for your network to
                                see.
                            </h2>
                            <h6 class="text-black fw-bold mt-5 mb-5">
                                Add to your LinkedIn Profile
                            </h6>

                            <p>
                                Here’s a step-by-step guide to adding badges or
                                certificates to the ‘Licenses & Certifications’
                                section of your LinkedIn Profile:
                            </p>
                            <p>
                                1. Go to your LinkedIn profile and scroll to
                                your ‘Licenses & certifications’ section.
                            </p>
                            <p>2. Click + icon.</p>
                            <p>
                                3. Provide all the relevant information about
                                the badge. You can find this below.
                            </p>
                            <p>
                                4. Don't forget to also mention the skills you
                                gained from earning the badge. This will give
                                your profile an extra boost and help potential
                                employers understand your expertise.
                            </p>
                        </div>
                    </div>
                    <hr class="mx-auto border-dark opacity-10">
                    <div class="container px-1">
                        <div class="row mt-5">
                            <!-- Left Section (Fields) -->
                            <div class="col-12 col-md-6 fs-5">
                                <h4 class="text-start mt-3 mb-6">
                                    Copy the below fields to your profile
                                </h4>

                                <div class="p-2 mt-2">
                                    <div>Name</div>
                                    <div class="border d-flex justify-content-between p-2 rounded align-items-center">
                                        <div class="p-2 fw-bold">
                                            {{ selectedBadge?.badge?.name }}
                                        </div>
                                        <button class="btn btn-sm btn-outline-primary" @click="copyToClipboard(selectedBadge?.name, 'name')">
                                            <i class="fa-regular fa-copy"></i>
                                        </button>
                                    </div>
                                    <p v-if="copiedField === 'name'" class="text-primary mt-1 fw-semibold">Copied to clipboard!</p>
                                </div>

                                <div class="p-2 mt-2">
                                    <div>Issuing Organisation</div>
                                    <div class="border d-flex justify-content-between p-2 rounded align-items-center">
                                        <div class="p-2 fw-bold">
                                            <div v-if="selectedBadge?.badge?.companies.length > 0">
                                                <span v-for=" (company, index) in selectedBadge?.badge?.companies" :key="company.id">
                                                    {{ company.name }}
                                                    <span v-if="index !== selectedBadge?.badge?.companies.length - 1"> + </span>
                                                </span>
                                            </div>
                                            <div v-else>
                                                 N/A
                                            </div>

                                        </div>
                                        <button class="btn btn-sm btn-outline-primary" @click="copyToClipboard(selectedBadge?.name, 'name')">
                                            <i class="fa-regular fa-copy"></i>
                                        </button>
                                    </div>
                                    <p v-if="copiedField === 'name'" class="text-primary mt-1 fw-semibold">Copied to clipboard!</p>
                                </div>

                                <div class="p-2 mt-2">
                                    <div>Issue Date</div>
                                    <div class="border d-flex justify-content-between p-2 rounded align-items-center">
                                        <div class="p-2 fw-bold">
                                            {{
                                                // selectedBadge?.badge?.formatted_issue_date
                                                selectedBadge?.issue_date
                                            }}
                                        </div>
                                        <button class="btn btn-sm btn-outline-primary" @click="copyToClipboard(selectedBadge?.issue_date, 'issue_date')">
                                            <i class="fa-regular fa-copy"></i>
                                        </button>
                                    </div>
                                    <p v-if="copiedField === 'issue_date'" class="text-primary mt-1 fw-semibold">Copied to clipboard!</p>
                                </div>

                                <div v-if="selectedBadge?.expiration_date" class="p-2 mt-2">
                                    <div>Expiry Date</div>
                                    <div class="border d-flex justify-content-between p-2 rounded align-items-center">
                                        <div class="p-2 fw-bold">
                                            {{
                                                selectedBadge?.expiration_date
                                            }}
                                        </div>
                                        <button class="btn btn-sm btn-outline-primary" @click="copyToClipboard(selectedBadge?.expiration_date, 'expiry_date')">
                                            <i class="fa-regular fa-copy"></i>
                                        </button>
                                    </div>
                                    <p v-if="copiedField === 'expiry_date'" class="text-primary mt-1 fw-semibold">Copied to clipboard!</p>
                                </div>

                                <div class="p-2 mt-2">
                                    <div>Credential ID</div>
                                    <div class="border d-flex justify-content-between p-2 rounded align-items-center">
                                        <div class="p-2 fw-bold">
                                            {{ selectedBadge?.credential_id || "N/A" }}
                                        </div>
                                        <button class="btn btn-sm btn-outline-primary"  @click="copyToClipboard(selectedBadge?.credential_id || 'N/A', 'credential_id')">
                                            <i class="fa-regular fa-copy"></i>
                                        </button>
                                    </div>
                                    <p v-if="copiedField === 'credential_id'" class="text-primary mt-1 fw-semibold">Copied to clipboard!</p>
                                </div>

                                <!-- <div class="p-2 mt-2">
                                    <div>Course</div>
                                    <div
                                        class="border d-flex justify-content-between p-2 rounded align-items-center"
                                    >
                                        <div class="p-2 fw-bold">Lessons</div>
                                        <button
                                            class="btn btn-sm btn-outline-primary"
                                            @click="copyToClipboard('Lessons')"
                                        >
                                            <i class="fa-regular fa-copy"></i>
                                        </button>
                                    </div>
                                </div> -->
                            </div>

                            <!-- Right Section (Image & Download) -->
                            <div class="col-12 col-md-6 text-center mt-4 mt-md-0">
                                <div>
                                    <img :src="selectedBadge?.badge?.image_fullpath" class="img-fluid rounded" style="max-width: 100%; height: auto" />
                                </div>

                                <a v-if="selectedBadge?.badge?.id" :href="`/badges/${selectedBadge.badge?.id}/download`" class="btn btn-sm btn-outline-primary mt-3" download>
                                    <i class="fa-solid fa-download"></i>
                                    Download Image
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" @click="openViewBadgeModal">
                        View Badge
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent,ref } from "vue";
import ViewBadgeModal from "@/components/badges/viewBadgeModal.vue";
import * as bootstrap from 'bootstrap';

export default defineComponent({
    components: {
        ViewBadgeModal,
    },
    props: {
        selectedBadge: Object,
        moduleData: Object,
        moduleType: String,
    },
    emits: ["shareBadge"],
    setup(props, { emit }) {
        const emitShare = () => {
            emit("shareBadge", props.selectedBadge);
        };

        const copiedField = ref("");

        const copyToClipboard = (text: string, field: string) => {
            if (!text) return;

            navigator.clipboard
                .writeText(text)
                .then(() => {
                    copiedField.value = field;
                    setTimeout(() => {
                        copiedField.value = "";
                    }, 3000);
                })
                .catch((err) => {
                    console.error("Copy failed:", err);
                });
        };

        const openShareBadgeModal = () => {
            // Use Bootstrap's Modal API to show the modal
            const modalElement = document.getElementById('kt_modal_share_badge');
            if (modalElement) {
                // Check if there's an existing modal instance
                let bsModal = bootstrap.Modal.getInstance(modalElement);
                if (!bsModal) {
                    // If no instance exists, create a new one
                    bsModal = new bootstrap.Modal(modalElement, {
                        backdrop: 'static' // Prevents closing when clicking outside
                    });
                }
                bsModal.show();
            }
        };

        const openViewBadgeModal = () => {
            // First, hide the current modal
            const currentModal = document.getElementById('kt_modal_share_badge');
            if (currentModal) {
                const bsCurrentModal = bootstrap.Modal.getInstance(currentModal);
                if (bsCurrentModal) {
                    bsCurrentModal.hide();
                }
            }

            // Then show the badge modal
            const modalElement = document.getElementById('kt_modal_badge');
            if (modalElement) {
                // Check if there's an existing modal instance
                let bsModal = bootstrap.Modal.getInstance(modalElement);
                if (!bsModal) {
                    // If no instance exists, create a new one
                    bsModal = new bootstrap.Modal(modalElement, {
                        backdrop: 'static' // Prevents closing when clicking outside
                    });
                }
                bsModal.show();
            }
        };

        return {
            emitShare,
            copiedField,
            copyToClipboard,
            openShareBadgeModal,
            openViewBadgeModal,
        };
    },
});
</script>

<style scoped>
.mw-900px {
    max-width: 900px;
}

.w-90 {
    width: 90%;
}
</style>