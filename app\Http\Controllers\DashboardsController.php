<?php

namespace App\Http\Controllers;

use DB;
use Auth;
use Cookie;
use App\Plan;
use App\User;
use App\Admin;
use App\Staff;
use App\Lesson;
use App\Notice;
use App\Gallery;
use App\Student;
use App\Teacher;
use App\Gameplan;
use App\Standard;
use Carbon\Carbon;
use App\ChildParent;
use App\Industryunit;
use App\IndustryCategory;
use App\WorkreadyTemplate;
use App\DashboardChecklist;
use Illuminate\Support\Str;
use App\StudentModuleResult;
use Illuminate\Http\Request;
use App\SkillstrainingTemplate;
use App\WorkexperienceTemplate;
use League\ColorExtractor\Color;
use App\WorkhealthsafetyTemplate;
use App\ResumesinterviewsTemplate;
use League\ColorExtractor\Palette;
use App\Notifications\InboxMessage;
use Illuminate\Support\Facades\Log;
use App\Http\Resources\UserResource;
use App\Library\CustomClasses\Indeed;

use Illuminate\Support\Facades\Cache;
use Overtrue\LaravelFavorite\Favorite;
use Illuminate\Support\Facades\Storage;
use App\Jobs\UpdateMailchimpAudienceJob;
use App\Http\Requests\ContactFormRequest;
use League\ColorExtractor\ColorExtractor;

class DashboardsController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function index()
    {
        if (Auth::user()->isAdmin() || Auth::user()->isMarker()) {
            return view('home');
        }
        Log::info("User #" . Auth::user()->id . " in ");
        if (!session()->has("teacherView") && !session()->has("studentView")) {
            // ddd(session()->get("teacherView"));
            // die("jkj");
            session(['teacherView' => 'true']);
        }
        if (Auth::user()->isTeacher() || Auth::user()->isStaff()) {
            if (!session('schoolSection') && Auth::user()->hasSecondarySchoolAccess() && Auth::user()->hasPrimarySchoolAccess()) {
                return  redirect(route('landing') . '#/dashboard');
                // return view('dashboards.school-section');
            }

            if (session('schoolSection') == 'primary' || (Auth::user()->hasPrimarySchoolAccess() && !Auth::user()->hasSecondarySchoolAccess())) {
                session()->forget('studentView');
                if (!session('schoolSection')) {
                    session(['schoolSection' => 'primary']);
                }
                return view('primaryschools.teachers.home');
            } else {
                session(['schoolSection' => 'secondary']);
            }

            $popup = Cookie::get('teacherLoggedInPopup');
            if (!$popup) {
                Cookie::queue('teacherLoggedInPopup', true);
            }
        }

        if (Auth::user()->isCompassTeacher()) {
            return view('primaryschools.teachers.home');
        }

        if (Auth::user()->isTeacher() && !session('studentView')) {

            $ids = Auth::user()->school->students()
                ->when(Auth::user()->campuses()->exists(), function ($query) {
                    return $query->whereHas('campuses', function ($q) {
                        $q->whereIn('campus_id', Auth::user()->campuses()->pluck('campus_id'));
                    });
                })->pluck('id');

            $industries = IndustryCategory::pluck('name')->mapWithKeys(function ($name, $key) {
                return [$name => 0];
            });


            $data1 = StudentModuleResult::with('industryOne:id,name')->select('industry1_id', DB::raw('count(*) as total'))->whereIn('user_id', $ids)->groupBY('industry1_id')->get()->pluck('total', 'industryOne.name')->toArray();

            $data2 = StudentModuleResult::with('industryTwo:id,name')->select('industry2_id', DB::raw('count(*) as total'))->whereIn('user_id', $ids)->groupBY('industry2_id')->get()->pluck('total', 'industryTwo.name')->toArray();

            $data3 = StudentModuleResult::with('industryThree:id,name')->select('industry3_id', DB::raw('count(*) as total'))->whereIn('user_id', $ids)->groupBY('industry3_id')->get()->pluck('total', 'industryThree.name')->toArray();

            $recommended = collect(array_merge_recursive(array_merge_recursive($data1, $data2), $data3))->map(function ($item, $key) {
                if (is_array($item)) {
                    return array_sum($item);
                }
                return $item;
            });
            $industries = $recommended->union($industries)->sortKeys();

            $hasFullAccess = Auth::user()->hasFullAccess();
            $hasManagerAccess = Auth::user()->hasManagerAccess();
            if ($hasManagerAccess) {
                // $notices = Notice::ofType('dashboard')->limit(3)->orderBy('created_at', 'desc')->get();
                $notices = Notice::with("author:id,name")->ofType('dashboard')->limit(3)->orderBy('created_at', 'desc')->get();
            } else {
                // $notices = Notice::ofType('dashboard')->limit(30)->orderBy('created_at', 'desc')->get();
                $notices = Notice::with("author:id,name")->ofType('dashboard')->limit(30)->orderBy('created_at', 'desc')->get();
                if (!$notices->count()) {
                    $notices = Notice::with("author:id,name")->ofType('general')->limit(30)->orderBy('created_at', 'desc')->get();
                    // $notices = Notice::ofType('general')->limit(30)->orderBy('created_at', 'desc')->get();
                }
            }
            $hasPremiumAccess = Teacher::find(Auth::id())->hasPremiumAccess();

            // return view('dashboards.teacher', compact('industries', 'notices', 'hasFullAccess', 'hasManagerAccess', 'hasPremiumAccess'));
            return  redirect(route('landing') . '#/dashboard');
        } elseif (Auth::user()->isStaff() && !session('studentView')) {
            $ids = Auth::user()->organisation->students()
                ->when(Auth::user()->orgcampuses()->exists(), function ($query) {
                    return $query->whereHas('orgcampuses', function ($q) {
                        $q->whereIn('campus_id', Auth::user()->orgcampuses()->pluck('campus_id'));
                    });
                })->pluck('id');

            $industries = IndustryCategory::pluck('name')->mapWithKeys(function ($name, $key) {
                return [$name => 0];
            });


            $data1 = StudentModuleResult::with('industryOne:id,name')->select('industry1_id', DB::raw('count(*) as total'))->whereIn('user_id', $ids)->groupBY('industry1_id')->get()->pluck('total', 'industryOne.name')->toArray();

            $data2 = StudentModuleResult::with('industryTwo:id,name')->select('industry2_id', DB::raw('count(*) as total'))->whereIn('user_id', $ids)->groupBY('industry2_id')->get()->pluck('total', 'industryTwo.name')->toArray();

            $data3 = StudentModuleResult::with('industryThree:id,name')->select('industry3_id', DB::raw('count(*) as total'))->whereIn('user_id', $ids)->groupBY('industry3_id')->get()->pluck('total', 'industryThree.name')->toArray();

            $recommended = collect(array_merge_recursive(array_merge_recursive($data1, $data2), $data3))->map(function ($item, $key) {
                if (is_array($item)) {
                    return array_sum($item);
                }
                return $item;
            });
            $industries = $recommended->union($industries)->sortKeys();

            $hasFullAccess = Auth::user()->hasFullAccess();
            $hasManagerAccess = Auth::user()->hasManagerAccess();
            if ($hasManagerAccess) {
                $notices = Notice::with("author:id,name")->ofType('dashboard')->limit(3)->orderBy('created_at', 'desc')->get();
            } else {
                $notices = Notice::with("author:id,name")->ofType('dashboard')->limit(30)->orderBy('created_at', 'desc')->get();
                if (!$notices->count()) {
                    $notices = Notice::with("author:id,name")->ofType('general')->limit(30)->orderBy('created_at', 'desc')->get();
                }
            }
            $hasPremiumAccess = Staff::find(Auth::id())->hasPremiumAccess();
            return view('dashboards.staff', compact('notices', 'industries', 'hasFullAccess', 'hasManagerAccess', 'hasPremiumAccess'));
        }

        $user = Auth::user();
        $firstlogin = $user->isFirstLogin();

        if ($firstlogin) {
            $user->firstlogin = Carbon::now();
            $user->save();
        }
        if ($user->isStudent() || $user->isParent() || $user->isTeacher()) {
            return  redirect(route('landing') . '#/dashboard');
        }

        $templates = collect();
        $templates->checklist = Cache::rememberForever('dashboard_industryunitchecklist', function () {
            return Industryunit::published()->where('type', 'checklist')->with('industries')->orderBy('id', 'desc')->limit(4)->get();
        });
        $templates->scrapbook = Cache::rememberForever('dashboard_industryunitscrapbook', function () {
            return Industryunit::published()->where('type', 'scrapbook')->with('industries')->orderBy('id', 'desc')->limit(4)->get();
        });
        // ddd($data->scrapbook );
        $templates->onthephonewith = Cache::rememberForever('dashboard_industryunitonthephonewith', function () {
            return Industryunit::published()->where('type', 'phoneafriend')->with('industries')->orderBy('id', 'desc')->limit(4)->get();
        });

        $templates->careertimeline = Cache::rememberForever('dashboard_industryunitcareertimeline', function () {
            return Industryunit::published()->where('type', 'careertimeline')->with('industries')->orderBy('id', 'desc')->limit(4)->get();
        });
        $templates->video = Cache::rememberForever('dashboard_industryunitvideo', function () {
            return Industryunit::published()->where('type', 'video')->with('industries')->orderBy('id', 'desc')->limit(4)->get();
        });

        return view('dashboards.index', compact('templates', 'firstlogin'));
    }
    public function getTeacherPopularIndustries()
    {
        if (Auth::user()->isTeacher()) {

            $ids = Auth::user()->school->students()
                ->when(Auth::user()->campuses()->exists(), function ($query) {
                    return $query->whereHas('campuses', function ($q) {
                        $q->whereIn('campus_id', Auth::user()->campuses()->pluck('campus_id'));
                    });
                })->pluck('id');

            $industries = IndustryCategory::pluck('name')->mapWithKeys(function ($name, $key) {
                return [$name => 0];
            });
            $students = Student::select('id', 'name')->whereIn('id', $ids)->with(['plans' => function ($q) {
                $q->select('id', 'user_id', 'interestedin')->with('industries:id,name')->latest();
            }])->get();
            $industries = [];
            foreach ($students as $key => $student) {
                foreach ($student->plans as $plan) {
                    foreach ($plan->industries as $industry) {
                        if (isset($industries[$industry->name])) {
                            $industries[$industry->name]++;
                        } else {
                            $industries[$industry->name] = 1;
                        }
                    }
                }
            }
            // $data1 = StudentModuleResult::with('industryOne:id,name')->select('industry1_id', DB::raw('count(*) as total'))->whereIn('user_id', $ids)->groupBY('industry1_id')->get()->pluck('total', 'industryOne.name')->toArray();

            // $data2 = StudentModuleResult::with('industryTwo:id,name')->select('industry2_id', DB::raw('count(*) as total'))->whereIn('user_id', $ids)->groupBY('industry2_id')->get()->pluck('total', 'industryTwo.name')->toArray();

            // $data3 = StudentModuleResult::with('industryThree:id,name')->select('industry3_id', DB::raw('count(*) as total'))->whereIn('user_id', $ids)->groupBY('industry3_id')->get()->pluck('total', 'industryThree.name')->toArray();

            // $recommended = collect(array_merge_recursive(array_merge_recursive($data1, $data2), $data3))->map(function ($item, $key) {
            //     if (is_array($item)) {
            //         return array_sum($item);
            //     }
            //     return $item;
            // });
            // $industries = $recommended->union($industries)->sortKeys();
            $serieslabels = $seriesdata = [];
            foreach ($industries as $key => $v) {
                if ($v > 0) {
                    $serieslabels[] = $key;
                    $seriesdata[] = $v;
                }
            }
            return response()->json(['seriesdata' => $seriesdata, 'serieslabels' => $serieslabels]);
        }
    }
    public function viewChange($type)
    {
        session()->forget('studentView');
        if ($type == 'student') {
            session(['studentView' => 'true']);
            session()->forget('teacherView');
        } else {
            session(['teacherView' => 'true']);
        }

        return redirect('/#/dashboard');
    }

    public function schoolSection($school)
    {
        session(['schoolSection' => $school]);
        return redirect('home');
    }

    public function contactUs(ContactFormRequest $message, Admin $admin)
    {
        //send notification to the admin
        $admin->notify(new InboxMessage($message));

        // redirect the user back
        $redirectUrl = url()->previous();
        if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
            $redirectUrl = session()->get('previousUrl');
        }
        return redirect($redirectUrl)->with('message', 'Thanks for the message! We will get back to you soon!');
    }

    public function imageGallery()
    {
        $screenType = request()->get('type');
        $imageGallery = [];
        $user = Auth::user();
        $type = '';
        if ($user->isParent()) {
            $type = 'Parent';
        } elseif ($user->isStudent() ||  session()->has("studentView")) {
            $type = 'Student';
            if (@$user->school->detail->institute_type->value == 'tertiary') {
                $type = 'Tertiary Student';
            }
        } elseif ($user->isTeacher() && !session()->has("studentView")) {
            $type = 'Teacher';
        }

        if ($type) {
            $gallery = Gallery::where('type', $type)->latest()->first();

            if ($gallery) {
                foreach ($gallery->data as $key => $tile) {
                    if ($screenType == 'mobile' && isset($tile['mobileimage'])) {
                        $image = $tile['mobileimage'];
                        $video = '';
                        $bg = $tile['mobile_bg_color'];
                        $text = $tile['mobile_text_color'];
                    } elseif (isset($tile['video']) && $tile['video']) {
                        $image = '';
                        $video = $tile['video'];
                        $bg = ($tile['desktop_bg_color'] ?? '#000');
                        $text = ($tile['desktop_text_color'] ?? '#fff');
                    } else {
                        $video = '';
                        $image = $tile['image'];
                        $bg = ($tile['desktop_bg_color'] ?? '#000');
                        $text = ($tile['desktop_text_color'] ?? '#fff');
                    }
                    // try {
                    //     $palette = Palette::fromFilename(Storage::url($tile['image']));
                    //     $extractor = new ColorExtractor($palette);
                    //     $colors = $extractor->extract(1);
                    //     $bgcolor = Color::fromIntToHex($colors[0]);
                    // } catch (\Throwable $th) {
                    //     $bgcolor="";
                    // }

                    $imageGallery[] = [
                        'id' => $key,
                        'title' => $tile['title'],
                        'url' => ($image) ? Storage::url($image) : '',
                        'video' =>  $video,
                        'link' => $tile['link'],
                        'order' => $tile['order'],
                        'bgcolor' => $bg,
                        'textcolor' => $text
                    ];
                }

                foreach ($imageGallery as $key => $row) {
                    $count[$key] = $row['order'];
                }
                array_multisort($count, SORT_ASC, $imageGallery);

                // $imageGallery = collect($imgs)->sortBy('order')->toArray();
            } else {
                $imageGallery = [
                    [
                        'id' => 1,
                        'title' => "Explore a Virtual Tour inside Qantas Hangar 3 here!",
                        'url' => "images/dashboard/gallery/VR_TCD.png",
                        'link' => route('exploreindustries.show', ['industry' => 14, 'unit' => 1102])
                    ],
                    [
                        'id' => 2,
                        'title' => "TCD spent the day at the new Allianz stadium in Sydney, watch the interview here.",
                        'url' => "images/dashboard/gallery/SportsPresenter.jpg",
                        'link' => route('exploreindustries.show', ['industry' => 2, 'unit' => 1153])
                    ],
                    [
                        'id' => 3,
                        'title' => "TCD is hosting a National Virtual Work Experience Competition in partnership with Network Seven! ",
                        'url' => "images/dashboard/gallery/VirtualWorkExperience.jpg",
                        'link' =>  "https://www.thecareersdepartment.com/competition"
                    ],
                    [
                        'id' => 4,
                        'title' => "Preview our new graduate tour with a Software Engineer at Rio Tinto!",
                        'url' => "images/dashboard/gallery/RioTinto.png",
                        'link' =>  route('exploreindustries.show', ['industry' => 30, 'unit' => 1158])
                    ],
                    [
                        'id' => 5,
                        'title' => "Prepare your child for a future of tech! View the new UX Design task.",
                        'url' => "images/dashboard/gallery/UXDesign.gif",
                        'link' => route('exploreworkexperience.show', 19)
                    ]
                ];
            }
        }
        // dd($imageGallery);
        return $imageGallery;
    }

    public function recomendations()
    {
        $recomendations = [];
        $user = Auth::user();

        if ($user->isParent()) {
            if (Auth::user()->profile->premium_access == 1) {
                $taskLimit = 2;
                $tasks = Lesson::published()->select('id', 'tileimage', 'title')->withCount('responses')->whereHas('responses', function ($query) {
                    $query->where(function ($qr) {
                        $qr->whereStatus('Submitted')->orWhereNotNull('response_path');
                    });
                })->orderByDesc('responses_count')->limit($taskLimit)->get();

                foreach ($tasks as $task) {
                    $recomendations[] = [
                        'id' => $task->id,
                        'banner' => Storage::url($task->tileimage),
                        'title' => strlen($task->title) > 65 ? substr($task->title, 0, 65) . "..." : $task->title,
                        'url' =>  route('tasks.show', $task->id),
                        'type' => 'LESSON'
                    ];
                }
                $wveLimit = 4 - count($recomendations);

                $templates = WorkexperienceTemplate::published()->select('id', 'tileimage', 'title')->withCount('responses')->whereHas('responses', function ($query) {
                    $query->where(function ($qr) {
                        $qr->whereNotNull('response_path');
                    });
                })->orderByDesc('responses_count')->limit($wveLimit)->get();

                foreach ($templates as $template) {
                    $recomendations[] = [
                        'id' => $template->id,
                        'banner' => Storage::url($template->tileimage),
                        'title' => strlen($template->title) > 65 ? substr($template->title, 0, 65) . "..." : $template->title,
                        'url' =>  route('exploreworkexperience.show', $template->id),
                        'type' => 'WORK EXPERIENCE'
                    ];
                }
                $stLimit = 5 - count($recomendations);

                $templates = SkillstrainingTemplate::forStudents()->select('id', 'tileimage', 'title')->withCount('responses')->whereHas('responses', function ($query) {
                    $query->where(function ($qr) {
                        $qr->whereStatus('Submitted')->orWhereNotNull('response_path');
                    });
                })->orderByDesc('responses_count')->with('responses')->limit($stLimit)->get();

                foreach ($templates as $template) {
                    $recomendations[] = [
                        'id' => $template->id,
                        'banner' => Storage::url($template->tileimage),
                        'title' => strlen($template->title) > 65 ? substr($template->title, 0, 65) . "..." : $template->title,
                        'url' =>  route('wew.skillstraining.show', $template->id),
                        'type' => 'SKILLS TRAINING'
                    ];
                }
            } else {
                $recomendations = [
                    [
                        'id' => 70,
                        'banner' => 'images/dashboard/recommends/freeParent/VjAzMbAUi6HQ16o0NMSdopZVIYtB8XENIxAYNS2j.jpg',
                        'title' => strlen('Agricultural Technican Work Experience') > 65 ? substr('Agricultural Technican Work Experience', 0, 65) . "..." : 'Agricultural Technican Work Experience',
                        'url' => route('exploreworkexperience.show', 70),
                        'type' => 'WORK EXPERIENCE'
                    ],
                    [
                        'id' => 71,
                        'banner' => 'images/dashboard/recommends/freeParent/Nyng8cWh7gWF6bRMUeqtQa1vYAuKFLvM7uJAeQ8B-2.png',
                        'title' => strlen('Ecosystem Reconstruction Advisor Work Experience') > 65 ? substr('Ecosystem Reconstruction Advisor Work Experience', 0, 65) . "..." : 'Ecosystem Reconstruction Advisor Work Experience',
                        'url' => route('exploreworkexperience.show', 71),
                        'type' => 'WORK EXPERIENCE'
                    ],
                    [
                        'id' => 52,
                        'banner' => 'images/dashboard/recommends/freeParent/ILjMERVDSMuM8C7K4Y2VJvYgxBb1h8IMNyxh60CN.jpg',
                        'title' => strlen('Understanding Data Skills Module') > 65 ? substr('Understanding Data Skills Module', 0, 65) . "..." : 'Understanding Data Skills Module',
                        'url' => route('wew.skillstraining.show', 52),
                        'type' => 'SKILLS TRAINING'
                    ],
                    [
                        'id' => 53,
                        'banner' => 'images/dashboard/recommends/freeParent/car.png',
                        'title' => strlen('Automotive Mechanics Work Experience') > 65 ? substr('Automotive Mechanics Work Experience', 0, 65) . "..." : 'Automotive Mechanics Work Experience',
                        'url' => route('exploreworkexperience.show', 53),
                        'type' => 'WORK EXPERIENCE'
                    ],
                    [
                        'id' => 44,
                        'banner' => 'images/dashboard/recommends/freeParent/cyber2.png',
                        'title' => strlen('Cyber Security Work Experience') > 65 ? substr('Cyber Security Work Experience', 0, 65) . "..." : 'Cyber Security Work Experience',
                        'url' => route('exploreworkexperience.show', 44),
                        'type' => 'WORK EXPERIENCE'
                    ],
                    [
                        'id' => 1102,
                        'banner' => 'images/dashboard/recommends/freeParent/IglPr4SJhV4arAM7xeGYto54u0cg88kCGruXLS5R.png',
                        'title' => strlen('Qantas Hangar 360 Tour') > 65 ? substr('Qantas Hangar 360 Tour', 0, 65) . "..." : 'Qantas Hangar 360 Tour',
                        'url' => route('exploreindustries.show', ['industry' => 14, 'unit' => 1102]),
                        'type' => 'INDUSTRY'
                    ],
                    // [
                    //     'id' => 2,
                    //     'banner' => 'images/dashboard/recommends/freeParent/IglPr4SJhV4arAM7xeGYto54u0cg88kCGruXLS5R.png',
                    //     'title' => "Work Health and Safety Module",
                    //     'url' => route('wew.workhealthsafety.show', 2),
                    //     'type' => ''
                    // ]
                ];
            }
        } else {
            $units = Industryunit::select('id', 'title', 'banner', 'type')->with('industries', 'template:slug,name', 'tagged')->orderBy('id', 'DESC')->get();

            $c = 0;
            foreach ($units as $unit) {
                $industry =  $unit->industries;
                if ($unit->banner && $industry->count() > 0) {
                    $recomendations[] = [
                        'id' => $unit->id,
                        'banner' => Storage::url($unit->banner),
                        'title' => strlen($unit->title) > 65 ? substr($unit->title, 0, 65) . "..." : $unit->title,
                        'subtitle' => '',
                        'url' => route('exploreindustries.show', ['industry' => $industry->first()->id, 'unit' => $unit->id]),
                        'type' => strtoupper($unit->template->name),
                    ];
                    $c++;
                }

                if ($c == 5) {
                    break;
                }
            }
        }

        // elseif ($user->isStudent() || session()->get('studentView')) {
        // $student = Student::find(Auth::user()->id);
        // $templates = $student->templateProgress()->with('template')->limit(6)->get();
        // foreach ($templates as $template) {
        //     $recomendations[] = [
        //         'id' => $template->template->id,
        //         'banner' => Storage::url($template->template->banner),
        //         'title' => $template->template->title,
        //         'url' => '',
        //         'type' => $template->template->type
        //     ];
        //     // route('exploreindustries.show',industryid,unitid)
        // }
        //     $recomendations = [
        //         [
        //             'id' => 66,
        //             'banner' => 'images/dashboard/recommends/student/Tiles_0000s_0000s_0016_FLORIST.png',
        //             'title' => "Florist Work Experience",
        //             'url' => route('exploreworkexperience.show', 66),
        //             'type' => 'WORK EXPERIENCE'
        //         ],
        //         [
        //             'id' => 11,
        //             'banner' => 'images/dashboard/recommends/student/Virtual-Work-Experience.png',
        //             'title' => "Graphic Design Work Experience",
        //             'url' => route('exploreworkexperience.show', 11),
        //             'type' => 'WORK EXPERIENCE'
        //         ],
        //         [
        //             'id' => 610,
        //             'banner' => 'images/dashboard/recommends/student/io1QZIANZ9t3mn2QlDigtkkvd2Y6aFHNuRLWVk4Z.jpg',
        //             'title' => "Fashion Design Graduate Tour",
        //             'url' => route('exploreindustries.show', ['industry' => 11, 'unit' => 610]),
        //             'type' => 'INDUSTRY'
        //         ],
        //         [
        //             'id' => 1153,
        //             'banner' => 'images/dashboard/recommends/student/axO1FUydtiGkrrTbiwXOAeAqfAN0oVE0uXwqoPWD.jpg',
        //             'title' => "Weather Presenter Graduate Tour",
        //             'url' => route('exploreindustries.show', ['industry' => 2, 'unit' => 1153]),
        //             'type' => 'INDUSTRY'
        //         ],
        //         [
        //             'id' => 1052,
        //             'banner' => 'images/dashboard/recommends/student/btipmZiQJbU0lWlcY5YjXX4q7jycMBOsMa7MW2pd.jpg',
        //             'title' => "Gallery Media Coordinator",
        //             'url' => route('exploreindustries.show', ['industry' => 2, 'unit' => 1052]),
        //             'type' => 'INDUSTRY'
        //         ],
        //         [
        //             'id' => 1035,
        //             'banner' => 'images/dashboard/recommends/student/1HwknMrRsJnX07oleDwv7ApN7U0UPhX1TkORJCCH.gif',
        //             'title' => "Makeup Artist Graduate Tour",
        //             'url' => route('exploreindustries.show', ['industry' => 18, 'unit' => 1035]),
        //             'type' => 'INDUSTRY'
        //         ]
        //     ];
        // }
        return $recomendations;
    }
    public function activeTasks()
    {
        $tasklist = [];

        if (Auth::user()->isParent()) {
            if (Auth::user()->profile->premium_access == 1) {
                // $user = Auth::user()->childInvitees()->where('processed', "1");
                $user_id = session()->get('child_id');

                if ($user_id) {
                    // $child_ids = $user->pluck('child_id');
                    $taskLimit = 2;

                    $tasks = Lesson::published()->select('id', 'tileimage', 'title')->whereHas('responses', function ($query) use ($user_id) {
                        $query->where('student_id', $user_id)->where(function ($qr) {
                            $qr->whereStatus('Submitted')->orWhereNotNull('response_path');
                        });
                    })->limit($taskLimit)->latest()->get();
                    foreach ($tasks as $task) {
                        $tasklist[] = [
                            'id' => $task->id,
                            'banner' => Storage::url($task->tileimage),
                            'title' => strlen($task->title) > 65 ? substr($task->title, 0, 65) . "..." : $task->title,
                            'url' =>  route('tasks.show', $task->id),
                            'type' => 'LESSON'
                        ];
                    }
                    $wveLimit = 4 - count($tasklist);

                    $id = Auth::id();
                    $templates = WorkexperienceTemplate::published()->select('id', 'tileimage', 'title')->whereHas('responses', function ($query) use ($user_id) {
                        $query->where('student_id', $user_id);
                    })->with('userResponse')->limit($wveLimit)->latest()->get();
                    foreach ($templates as $template) {
                        $tasklist[] = [
                            'id' => $template->id,
                            'banner' => Storage::url($template->tileimage),
                            'title' => strlen($template->title) > 65 ? substr($template->title, 0, 65) . "..." : $template->title,
                            'url' =>  route('exploreworkexperience.show', $template->id),
                            'type' => 'WORK EXPERIENCE'
                        ];
                    }
                    $stLimit = 6 - count($tasklist);

                    $templates = SkillstrainingTemplate::forStudents()->select('id', 'tileimage', 'title')->with('userResponse')->whereHas('responses', function ($query) use ($user_id) {
                        $query->where('student_id', $user_id);
                    })->whereHas("years", function ($query) {
                        $query->where('standard_id', Auth::user()->profile->standard_id);
                    })->limit($stLimit)->latest()->get();
                    foreach ($templates as $template) {
                        $tasklist[] = [
                            'id' => $template->id,
                            'banner' => Storage::url($template->tileimage),
                            'title' => strlen($template->title) > 65 ? substr($template->title, 0, 65) . "..." : $template->title,
                            'url' =>  route('wew.skillstraining.show', $template->id),
                            'type' => 'SKILLS TRAINING'
                        ];
                    }
                }
            } else {
                $tasklist = [
                    [
                        'id' => 1,
                        'banner' => 'images/dashboard/completedTasks/parent/Tiles_0000s_0010_property.png',
                        'title' => 'Sustainability + Commercial Property',
                        'url' => route('exploreworkexperience.show', 69),
                        'type' => 'Virtual Work Experience'
                    ],
                    [
                        'id' => 2,
                        'banner' => 'images/dashboard/completedTasks/parent/Tiles_0000s_0000s_0013_park.png',
                        'title' => 'Park Ranger',
                        'url' => route('exploreworkexperience.show', 60),
                        'type' => 'Virtual Work Experience'
                    ],
                    [
                        'id' => 3,
                        'banner' => 'images/dashboard/completedTasks/parent/Tiles_0000s_0010_property.png',
                        'title' => 'Property Development',
                        'url' => route('exploreworkexperience.show', 61),
                        'type' => 'Virtual Work Experience'
                    ],
                    [
                        'id' => 4,
                        'banner' => 'images/dashboard/completedTasks/parent/9wkDAVY5RcWIlWVjcjZqmp77uQt4OArlXsrNVSdJ.jpg',
                        'title' => 'Learn: Coding',
                        'url' => route('wew.skillstraining.show', 16),
                        'type' => 'Skills Training'
                    ],
                    [
                        'id' => 5,
                        'banner' => 'images/dashboard/completedTasks/parent/WSWFbBhZQx2lbqdOYFXExWn4ADlMImgFpZ6Z5OtN.jpg',
                        'title' => 'UX Design',
                        'url' => route('exploreworkexperience.show', 45),
                        'type' => 'Virtual Work Experience'
                    ],
                    [
                        'id' => 6,
                        'banner' => 'images/dashboard/completedTasks/parent/9wkDAVY5RcWIlWVjcjZqmp77uQt4OArlXsrNVSdJ.jpg',
                        'title' => 'Learn: Digital Design',
                        'url' => route('wew.skillstraining.show', 32),
                        'type' => 'Skills Training'
                    ],
                    [
                        'id' => 7,
                        'banner' => 'images/dashboard/completedTasks/parent/9wkDAVY5RcWIlWVjcjZqmp77uQt4OArlXsrNVSdJ.jpg',
                        'title' => 'Learn: Google Anayltics ',
                        'url' => route('wew.skillstraining.show', 1),
                        'type' => 'Skills Training'
                    ],
                ];
            }
        } else {
            $student = Auth::user();

            $taskLimit = 2;
            $tasks = Lesson::published()->select('id', 'tileimage', 'title')->whereHas('responses', function ($query) use ($student) {
                $query->whereStudentId($student->id)->where(function ($qr) {
                    $qr->whereStatus('Submitted')->orWhereNotNull('response_path');
                });
            })->limit($taskLimit)->latest()->get();
            foreach ($tasks as $task) {
                $tasklist[] = [
                    'id' => $task->id,
                    'banner' => Storage::url($task->tileimage),
                    'title' => strlen($task->title) > 65 ? substr($task->title, 0, 65) . "..." : $task->title,
                    'url' =>  route('tasks.show', $task->id),
                    'type' => 'LESSON'
                ];
            }
            $wveLimit = 4 - count($tasklist);

            $id = Auth::id();
            $templates = WorkexperienceTemplate::published()->select('id', 'tileimage', 'title')->whereHas('responses', function ($query) use ($student) {
                $query->whereStudentId($student->id);
            })->with('userResponse')->limit($wveLimit)->latest()->get();
            foreach ($templates as $template) {
                $tasklist[] = [
                    'id' => $template->id,
                    'banner' => Storage::url($template->tileimage),
                    'title' => strlen($template->title) > 65 ? substr($template->title, 0, 65) . "..." : $template->title,
                    'url' =>  route('exploreworkexperience.show', $template->id),
                    'type' => 'WORK EXPERIENCE'
                ];
            }
            $stLimit = 6 - count($tasklist);

            $templates = SkillstrainingTemplate::forStudents()->select('id', 'tileimage', 'title')->with('userResponse')->whereHas('responses', function ($query) use ($student) {
                $query->whereStudentId($student->id);
            })->whereHas("years", function ($query) {
                $query->where('standard_id', Auth::user()->profile->standard_id);
            })->limit($stLimit)->latest()->get();
            foreach ($templates as $template) {
                $tasklist[] = [
                    'id' => $template->id,
                    'banner' => Storage::url($template->tileimage),
                    'title' => strlen($template->title) > 65 ? substr($template->title, 0, 65) . "..." : $template->title,
                    'url' =>  route('wew.skillstraining.show', $template->id),
                    'type' => 'SKILLS TRAINING'
                ];
            }
        }

        return $tasklist;
    }
    public function noticeboard()
    {
        $notices = collect();
        if (Auth::user()->isParent()) {
            $amdinnotices =  Notice::wherePublish(true)->with("author")->whereParents(true)->get();

            $schoolIds = Auth::user()->children()->whereNotNull('school_id')->pluck('school_id');
            $ids = [];
            if ($schoolIds->isNotEmpty()) {
                $ids = Teacher::whereIn('school_id', $schoolIds)->pluck('id');
            }
            $schoolnotices = Notice::whereIn('user_id', $ids)->wherePublish(true)->whereParents(true)->with("author")->get();


            $orgIds = Auth::user()->children()->whereNotNull('school_id')->pluck('school_id');
            $ids = [];
            if ($orgIds->isNotEmpty()) {
                $ids = Teacher::whereIn('organisation_id', $schoolIds)->pluck('id');
            }
            $orgnotices = Notice::whereIn('user_id', $ids)->wherePublish(true)->whereParents(true)->with("author")->get();

            $notices =  $amdinnotices->merge($schoolnotices)->merge($orgnotices)->unique();
            // $notices = Notice::ofType('school')->get();
        } else {
            if (!Auth::user()->school_id && Auth::user()->organisation_id) {
                $notices = Notice::ofType('organisation')->with("author")->orderBy('created_at', 'desc')->get();
            } else {
                $notices = Notice::ofType('school')->with("author")->orderBy('created_at', 'desc')->get();
            }
        }

        return $notices->take(7);
    }
    public function industries()
    {
        $maxindustriestoshow = 5;
        $industries = [];
        if (Auth::user()->isStudent() || session()->get('studentView')) {
            $gameplanIndustries = Gameplan::where('user_id', Auth::id())->latest()->first()->industries()->with('units')->limit(5)->orderBy('gameplan_industries.priority')->get()->map(function ($query) {
                $query->setRelation('units', $query->units->take(6));
                return $query;
            });

            if ($gameplanIndustries) {
                $addedindustries = [];
                foreach ($gameplanIndustries as $industry) {
                    if (!in_array($industry->id, $addedindustries)) {
                        $addedindustries[] = $industry->id;
                        $units = $industry->units;
                        if ($units) {
                            $iu = [];
                            $c = 0;
                            foreach ($units as $unit) {
                                $iu[] = [
                                    'banner' => $unit->banner ? Storage::url($unit->banner) : '',
                                    'title' => $unit->title,
                                    'subtitle' => '',
                                    'url' => route('exploreindustries.show', ['industry' => $industry->id, 'unit' => $unit->id])
                                ];
                            }
                            $industries[] = [
                                'id' => $industry->id,
                                'title' => $industry->name,
                                'banner' => $industry->tileimage ? Storage::url($industry->tileimage) : '',
                                'url' => '',
                                'content' => $iu
                            ];
                        }
                    }
                }
            }
            // $industries = [
            //     [
            //         'id' => 1,
            //         'title' => 'Design',
            //         'banner' => 'images/dashboard/industries/design.png',
            //         'url' => '',
            //         'content' => [
            //             [
            //                 'banner' => 'images/dashboard/industries/dAgEYcJFLiDMxfa3XMlhCiSNFDTIjipxgXtya3XZ.gif',
            //                 'title' => 'Fashion Designer',
            //                 'subtitle' => '',
            //                 'url'=>route('exploreindustries.show', ['industry' => 11, 'unit' => 610])
            //             ],
            //             [
            //                 'banner' => 'images/dashboard/industries/1utfHbRbzJj2IxsmKO9AJCoHQc9ifBm7u8DIKdYS.jpg',
            //                 'title' => '3D Animator',
            //                 'subtitle' => '',
            //                 'url'=>route('exploreindustries.show', ['industry' => 11, 'unit' => 657])
            //             ],
            //             [
            //                 'banner' => 'images/dashboard/industries/dAgEYcJFLiDMxfa3XMlhCiSNFDTIjipxgXtya3XZ.gif',
            //                 'title' => 'Graphic Designer',
            //                 'subtitle' => '',
            //                 'url'=>route('exploreindustries.show', ['industry' => 11, 'unit' => 440])
            //             ],
            //             [
            //                 'banner' => 'images/dashboard/industries/dAgEYcJFLiDMxfa3XMlhCiSNFDTIjipxgXtya3XZ.gif',
            //                 'title' => 'Bachelor of Design',
            //                 'subtitle' => '',
            //                 'url'=>route('exploreindustries.show', ['industry' => 11, 'unit' => 585])
            //             ]
            //         ]
            //     ],
            //     [
            //         'id' => 2,
            //         'title' => 'Education',
            //         'banner' => 'images/dashboard/industries/education.png',
            //         'url' => '',
            //         'content' => [
            //             [
            //                 'banner' => 'images/dashboard/industries/u8QUK5tizt3s7UVq8i5QVzIKb7YChnbINq2FDLE4.jpg',
            //                 'title' => 'Psychologist Consultation',
            //                 'subtitle' => '',
            //                 'url'=>route('exploreindustries.show', ['industry' => 26, 'unit' => 1125])
            //             ],
            //             [
            //                 'banner' => 'images/dashboard/industries/dAgEYcJFLiDMxfa3XMlhCiSNFDTIjipxgXtya3XZ.gif',
            //                 'title' => 'Psychologist',
            //                 'subtitle' => '',
            //                 'url'=>route('exploreindustries.show', ['industry' => 26, 'unit' => 1048])
            //             ],
            //             [
            //                 'banner' => 'images/dashboard/industries/gsHpyJ3t1T45drHUKWIvtMPegVIMAepMwJeggRM3.gif',
            //                 'title' => 'Early Childhood Educator ',
            //                 'subtitle' => '',
            //                 'url'=>route('exploreindustries.show', ['industry' => 26, 'unit' => 1040])
            //             ],
            //             [
            //                 'banner' => 'images/dashboard/industries/dAgEYcJFLiDMxfa3XMlhCiSNFDTIjipxgXtya3XZ.gif',
            //                 'title' => 'Primary School Teacher',
            //                 'subtitle' => '',
            //                 'url'=>route('exploreindustries.show', ['industry' => 26, 'unit' => 1095])
            //             ],
            //         ]
            //     ],
            //     [
            //         'id' => 3,
            //         'title' => 'Advertising and Communications',
            //         'banner' => 'images/dashboard/industries/advertising.png',
            //         'url' => '',
            //         'content' => [
            //             [
            //                 'banner' => 'images/dashboard/industries/axO1FUydtiGkrrTbiwXOAeAqfAN0oVE0uXwqoPWD.jpg',
            //                 'title' => 'Weather Presenter',
            //                 'subtitle' => '',
            //                 'url'=>route('exploreindustries.show', ['industry' => 2, 'unit' => 1153])
            //             ],
            //             [
            //                 'banner' => 'images/dashboard/industries/OSn9sMKIBeYBvnmMe4VHe4lLJyNuRFUE8jjZzaNX-2.png',
            //                 'title' => 'Sports broadcasting',
            //                 'subtitle' => '',
            //                 'url'=>route('exploreindustries.show', ['industry' => 2, 'unit' => 578])
            //             ],
            //             [
            //                 'banner' => 'images/dashboard/industries/3TNzrMrF6xaS8ANsdKTPhLbSn7IVV3Z2mOxK9HgE.png',
            //                 'title' => 'Radio Host',
            //                 'subtitle' => '',
            //                 'url'=>route('exploreindustries.show', ['industry' => 2, 'unit' => 583])
            //             ],
            //             [
            //                 'banner' => 'images/dashboard/industries/OcFiCr0kP6VFbKSFkVkbGJMU6adA3w4btTyGXV5w.png',
            //                 'title' => 'Studying Journalism ',
            //                 'subtitle' => '',
            //                 'url'=>route('exploreindustries.show', ['industry' => 2, 'unit' => 538])
            //             ],
            //             [
            //                 'banner' => 'images/dashboard/industries/kmsqUJmQDxMSB4PaQr4aVjntxR22keKgU4fefDaZ.png',
            //                 'title' => 'Radio Producer',
            //                 'subtitle' => '',
            //                 'url'=>route('exploreindustries.show', ['industry' => 34, 'unit' => 1156])
            //             ],
            //             [
            //                 'banner' => 'images/dashboard/industries/oxsSLHoR2yIuQLj7DfmhldSdQyd5QvLw2kLRsAU1.png',
            //                 'title' => 'Public Relations Checklist',
            //                 'subtitle' => '',
            //                 'url'=>route('exploreindustries.show', ['industry' => 2, 'unit' => 568])
            //             ],
            //         ]
            //     ]

            // ];
        } elseif (Auth::user()->isParent()) {
            if (Auth::user()->profile->premium_access == 1) {
                // $user = Auth::user()->childInvitees()->where('processed', "1");
                $user_id = session()->get('child_id');
                if ($user_id) {
                    // $child_ids = $user->pluck('child_id');
                    $currentPlan = Gameplan::where('user_id', $user_id)->with(['industries'])
                        ->with(array('industries' => function ($query) {
                            $query
                                ->with(array('units' => function ($query) {
                                    $query->orderBy('industryunit_id', 'DESC');
                                }))
                                ->latest();
                        }))
                        ->latest()->first();
                    // $currentPlan = Plan::whereIn('user_id', $child_ids)->with(['industries'])->latest()->first();
                    if ($currentPlan && $currentPlan->industries) {
                        $addedindustries = [];
                        foreach ($currentPlan->industries as $industry) {
                            if (!in_array($industry->id, $addedindustries) && !empty($industry->tileimage)) {
                                $addedindustries[] = $industry->id;
                                $units = $industry->units;
                                if ($units) {
                                    $iu = [];
                                    $c = 0;
                                    foreach ($units as $unit) {
                                        if ($unit->banner) {
                                            $iu[] = [
                                                'banner' => Storage::url($unit->banner),
                                                'title' => $unit->title,
                                                'subtitle' => '',
                                                'url' => route('exploreindustries.show', ['industry' => $industry->id, 'unit' => $unit->id])
                                            ];
                                            $c++;
                                        }

                                        if ($c == 6) {
                                            break;
                                        }
                                    }
                                    $industries[] = [
                                        'id' => $industry->id,
                                        'title' => $industry->name,
                                        'banner' => Storage::url($industry->tileimage),
                                        'url' => '',
                                        'content' => $iu
                                    ];
                                }
                            } else {
                                // dd($industry);
                            }
                            if (count($addedindustries) == $maxindustriestoshow) {
                                break;
                            }
                        }
                    }
                }
            } else {
                $industries = [
                    [
                        'id' => 1,
                        'title' => 'Agriculture and Environement',
                        'banner' => 'images/dashboard/industries/agriculture.png',
                        'url' => '',
                        'content' => [
                            [
                                'banner' => 'images/dashboard/industries/sPmpw83uviRNg5tvh5sl9i3oxJQsJOhmxsaIQeWA.png',
                                'title' => 'Ecosystem Reconstruction Advisor',
                                'subtitle' => '',
                                'url' => route('exploreindustries.show', ['industry' => 3, 'unit' => 1121])
                            ],
                            [
                                'banner' => 'images/dashboard/industries/yK4jR1bfKIgu1g2D64DKdrwphBfkHXFVAqQ2IJ8k.png',
                                'title' => 'Field Technician',
                                'subtitle' => '',
                                'url' => route('exploreindustries.show', ['industry' => 3, 'unit' => 1111])
                            ],
                            [
                                'banner' => 'images/dashboard/industries/oQPCDl2Hkxdca20Pkku04K9trld5xT48yXPptiEb.png',
                                'title' => 'Agricultural Technican ',
                                'subtitle' => '',
                                'url' => route('exploreindustries.show', ['industry' => 3, 'unit' => 1101])
                            ],
                            [
                                'banner' => 'images/dashboard/industries/PobVo41dK4AAEXBUTB0Tz5YceQLF3xG5bOol7UID.png',
                                'title' => 'Aquaculture Technician ',
                                'subtitle' => '',
                                'url' => route('exploreindustries.show', ['industry' => 3, 'unit' => 1126])
                            ],
                            [
                                'banner' => 'images/dashboard/industries/DUCyInZ5YT1n0yeMmR70BjUyT8zrNIeMic8QoxdS.jpg',
                                'title' => 'Agriculture Course',
                                'subtitle' => '',
                                'url' => route('exploreindustries.show', ['industry' => 3, 'unit' => 579])
                            ],
                            [
                                'banner' => 'images/dashboard/industries/4qGpfXTy2v0fxYjxD0X9aA0sqasZOsk0WmQbDFt9.jpg',
                                'title' => 'Assistant Wine Maker ',
                                'subtitle' => '',
                                'url' => route('exploreindustries.show', ['industry' => 3, 'unit' => 1064])
                            ]
                        ]
                    ],
                    [
                        'id' => 2,
                        'title' => 'Trades and Mining',
                        'banner' => 'images/dashboard/industries/trades mining.png',
                        'url' => '',
                        'content' => [
                            [
                                'banner' => 'images/dashboard/industries/S1rHnYAzRHPUQ8XVvLfzJM4ZmWdhlMHhyaF0OAHE.gif',
                                'title' => 'Plumber',
                                'subtitle' => '',
                                'url' => route('exploreindustries.show', ['industry' => 30, 'unit' => 1036])
                            ],
                            [
                                'banner' => 'images/dashboard/industries/sPmpw83uviRNg5tvh5sl9i3oxJQsJOhmxsaIQeWA.png',
                                'title' => 'Aircraft Engineer',
                                'subtitle' => '',
                                'url' => route('exploreindustries.show', ['industry' => 30, 'unit' => 1082])
                            ],
                            [
                                'banner' => 'images/dashboard/industries/gsHpyJ3t1T45drHUKWIvtMPegVIMAepMwJeggRM3.gif',
                                'title' => 'Ecosytem Reconstruction Advisor  ',
                                'subtitle' => '',
                                'url' => route('exploreindustries.show', ['industry' => 30, 'unit' => 1037])
                            ],
                            [
                                'banner' => 'images/dashboard/industries/SvZNeJ2e3u51koCkQUZDQzcli7NAFLgeqvtRmDQy-2.gif',
                                'title' => 'Electrical Apprentice',
                                'subtitle' => '',
                                'url' => route('exploreindustries.show', ['industry' => 30, 'unit' => 1073])
                            ],
                            [
                                'banner' => 'images/dashboard/industries/GcRipx4bkSQGEJSoLey44cMGII185wXvxhxxTJUd.jpg',
                                'title' => 'Electrican ',
                                'subtitle' => '',
                                'url' => route('exploreindustries.show', ['industry' => 30, 'unit' => 1037])
                            ],
                            [
                                'banner' => 'images/dashboard/industries/azinQQAlaRov9pI0guw1sLI7tDExTHoJ8tyNV4E3.png',
                                'title' => 'Software Engineer',
                                'subtitle' => '',
                                'url' => route('exploreindustries.show', ['industry' => 30, 'unit' => 1158])
                            ],
                        ]
                    ],
                    [
                        'id' => 3,
                        'title' => 'Toursim and Hospitality ',
                        'banner' => 'images/dashboard/industries/transport tourism hospo.png',
                        'url' => '',
                        'content' => [
                            [
                                'banner' => 'images/dashboard/industries/v7q98iDcWhvLtLzoa8EAOEdX9nqTIdMXpSvVleeO.png',
                                'title' => 'Guest Service Agent',
                                'subtitle' => '',
                                'url' => route('exploreindustries.show', ['industry' => 34, 'unit' => 1155])
                            ],
                            [
                                'banner' => 'images/dashboard/industries/SvZNeJ2e3u51koCkQUZDQzcli7NAFLgeqvtRmDQy-2.gif',
                                'title' => 'Chef',
                                'subtitle' => '',
                                'url' => route('exploreindustries.show', ['industry' => 34, 'unit' => 1087])
                            ],
                            [
                                'banner' => 'images/dashboard/industries/YXSC7FPSu0VEkEQoP7hSgniZY3QuGBaAeQx6zu7c.jpg',
                                'title' => 'Skipper',
                                'subtitle' => '',
                                'url' => route('exploreindustries.show', ['industry' => 34, 'unit' => 1045])
                            ],
                            [
                                'banner' => 'images/dashboard/industries/mOXQ5Fobq7Bdfcx6CLNA5jvVEDlVCoBKC4creo8c.png',
                                'title' => 'Rottnest Island Ranger ',
                                'subtitle' => '',
                                'url' => route('exploreindustries.show', ['industry' => 34, 'unit' => 1112])
                            ],
                            [
                                'banner' => 'images/dashboard/industries/OLhkpTT7HWPsqEjTxoJSLHVIgy9Tut5PoVFMdmMa.png',
                                'title' => 'Event Coordinator ',
                                'subtitle' => '',
                                'url' => route('exploreindustries.show', ['industry' => 34, 'unit' => 1156])
                            ],
                            [
                                'banner' => 'images/dashboard/industries/vacII7JTms9B1Fi4LCQhs9k6BcwXvuKj8bmCQ5Hb.jpg',
                                'title' => 'Deckhand',
                                'subtitle' => '',
                                'url' => route('exploreindustries.show', ['industry' => 34, 'unit' => 1156])
                            ],
                        ]
                    ]

                ];
            }
        }
        return $industries;
    }
    public function jobfinder(Request $request)
    {
        $jobs = [];
        if (Auth::user()->isParent()) {
            $industry = request()->get('industry');
            if (Auth::user()->profile->premium_access == 1) {
                $user_id = session()->get('child_id');
            } else {
                $user_id = Auth::user()->id;
            }

            if ($user_id) {
                $user = User::find($user_id);
                if ($user->postcode) {
                    $location = $user->postcode;
                } else {
                    $location = $user->state->name;
                };

                $l = $location;
                $userip = $request->ip();
                $useragent = $request->header('User-Agent');
                if ($industry) {
                    $q = $industry;
                } else {
                    $q = 'construction';
                }

                $client = new Indeed(config('services.indeed.id'));

                $params = array(
                    "v" => "2",     // API Version
                    'q' => $q,
                    'l' => $l,
                    "co" => "au",      // Country Code default US
                    "userip" => $userip, // user's IP Address
                    "useragent" => $useragent,    // user agent
                    'limit' => 3,
                    'sort' => 'relevance',
                    'radius' => 50,
                    'jt' => 'fulltime',
                    'start' => $request->start,
                );
                $fulltimejobs = $client->search($params);
                $params['jt'] = 'internship';
                $internshipjobs = $client->search($params);
                $params['jt'] = 'parttime';
                $parttimejobs = $client->search($params);
                $jobs = [
                    'internship' => $internshipjobs['results'],
                    'parttime' => $parttimejobs['results'],
                    'fulltime' => $fulltimejobs['results']
                ];
            }
        } else {
        }
        return $jobs;
    }

    public function childSave(Request $request, $id)
    {
        session()->forget('child_id');
        $request->session()->put('child_id', $id);
        return  redirect(route('landing') . '#/dashboard?for=' . $id);
    }

    public function searchResults($value)
    {
        $user = Auth::user();
        $searchedListData = [];

        if ($user->isTeacher() && !session()->get('studentView')) {
            // For students
            $campus = false;
            $campuses = $user->campuses()->pluck('campus_id');

            if ($campuses->count()) {
                if (request('campus')) {
                    $campus = array(request('campus'));
                } else {
                    $campus = $campuses;
                }
            }

            $students = Student::select('id', 'name', 'school_id')->where('school_id', $user->school_id)
                ->where('name', 'like', '%' . $value . '%')
                ->when($campus, function ($query) use ($campus) {
                    $query->where(function ($query) use ($campus) {
                        if (request('campus')) {
                            return $query->whereHas('campuses', function ($q) use ($campus) {
                                $q->whereIn('campus_id', $campus);
                            });
                        } else {
                            return $query->whereHas('campuses', function ($q) use ($campus) {
                                $q->whereIn('campus_id', $campus);
                            })->orDoesntHave('campuses');
                        }
                    });
                })
                ->when(session('schoolSection') == 'primary', function ($query) {
                    return $query->whereHas("profile", function ($query) {
                        $years = Standard::primarySchool()->pluck('id');
                        $query->whereIn('standard_id', $years);
                    });
                })
                ->when(session('schoolSection') == 'secondary', function ($query) {
                    return $query->whereHas("profile", function ($query) {
                        $years = Standard::secondarySchool()->pluck('id');
                        $query->whereIn('standard_id', $years)->orWhere(function ($query) {
                            $query->where('standard_id', null)
                                ->where('accountcreated', 0);
                        });
                    });
                })
                ->orWhere(function ($query) use ($value, $user) {
                    $query->where('school_id', $user->school_id)->where('email', 'like', '%' . $value . '%');
                })->take(10)->get();

            if ($students->count() > 0) {
                foreach ($students as $student) {
                    $searchedListData['students'][] = [
                        'id' => $student->id,
                        'name' => $student->name,
                        'standard' => $student->profile->standard_id,
                    ];
                }
            } else {
                $searchedListData['students'] = '';
            }
            // For students

            // For teachers
            $teachers = Teacher::select('id', 'name')->where("users.id", "<>", Auth::id())->where('school_id', $user->school_id)
                ->where('name', 'like', '%' . $value . '%')
                ->when($campus, function ($query) use ($campus) {
                    $query->where(function ($query) use ($campus) {
                        if (request('campus')) {
                            return $query->whereHas('campuses', function ($q) use ($campus) {
                                $q->whereIn('campus_id', $campus);
                            });
                        } else {
                            return $query->whereHas('campuses', function ($q) use ($campus) {
                                $q->whereIn('campus_id', $campus);
                            })->orDoesntHave('campuses');
                        }
                    });
                })
                ->when(session('schoolSection') == 'primary', function ($query) {
                    $query->whereHas("profile", function ($query) {
                        $query->where('primary_section', true);
                    });
                })
                ->when(session('schoolSection') == 'secondary', function ($query) {
                    $query->whereHas("profile", function ($query) {
                        $query->where('secondary_section', true);
                    });
                })
                ->orWhere(function ($query) use ($value, $user) {
                    $query->where('school_id', $user->school_id)->where('email', 'like', '%' . $value . '%');
                })->take(10)->get();

            if ($teachers->count() > 0) {
                foreach ($teachers as $teacher) {
                    $searchedListData['teachers'][] = [
                        'id' => $teacher->id,
                        'name' => $teacher->name,
                        'access' => strtoupper($teacher->profile->access_level),
                    ];
                }
            } else {
                $searchedListData['teachers'] = '';
            }
            // For teachers
        }
        // else{
        //     $searchedListData['students']= '';
        //     $searchedListData['teachers']= '';
        // }

        if ($user->isParent()) {
            $children = $user->childInvitees()->where('processed', '1')->with('child')->whereHas('child', function ($q) use ($value) {
                $q->where('name', 'like', '%' . $value . '%');
            })->get();

            if ($children->count() > 0) {
                foreach ($children as $child) {
                    $searchedListData['students'][] = [
                        'id' => $child->child_id,
                        'name' => $child->child->name,
                        'standard' => $child->child->profile->standard_id,
                    ];
                }
            } else {
                $searchedListData['students'] = '';
            }
        }

        if ($user->isStudent() || $user->isParent()) {
            $tools =  collect([
                [
                    'title' => 'Job Finder',
                    'url' => '/jobfinder'
                ],
                [
                    'title' => 'Scholarship Finder',
                    'url' => '/scholarships'
                ],
                [
                    'title' => 'Resume Builder',
                    'url' => '/cvs'
                ],
                [
                    'title' => 'Course Finder',
                    'url' => '/coursefinder'
                ],
                [
                    'title' => 'ePortfolio',
                    'url' => $user->isStudent() ? '/eportfolio' : '#'
                ]
            ]);

            $filter =  $tools->filter(function ($item) use ($value) {
                return false !== stristr($item['title'], $value);
            })->all();

            $tools = collect($filter);

            if ($tools->count() > 0) {
                foreach ($tools as $tool) {
                    $searchedListData['tools'][] = [
                        'title' => $tool['title'],
                        'url' => $tool['url'],
                    ];
                }
            } else {
                $searchedListData['tools'] = '';
            }
        }

        // For Industries
        $industries = Industryunit::published()->select('id', 'banner', 'title', 'publish', 'type')->where('title', 'like', '%' . $value . '%')->with('industries:id,name')->take(10)->get();
        if ($user->hasIndustriesAccess() && $industries->count() > 0) {
            foreach ($industries as $template) {
                $industry = $template->industries;
                if ($template->banner && $industry->count() > 0) {
                    $searchedListData['industries'][] = [
                        'id' => $template->id,
                        'tile_img' => $template->tileimage_fullpath,
                        'title' =>  strlen($template->title) > 35 ? substr($template->title, 0, 35) . "..." : $template->title,
                        'url' =>  route('exploreindustries.show', ['industry' => $industry->first()->id, 'unit' => $template->id]),
                        'type' => (strlen($industry->first()->name) > 20 ? substr($industry->first()->name, 0, 20) . "..." : $industry->first()->name) . ' | ' . strtoupper($template->template->name),
                    ];
                }
            }
        } else {
            $searchedListData['industries'] = '';
        }
        // For Industries

        $vwes = WorkexperienceTemplate::published()->select('id', 'title', 'tileimage', 'publish')->where('title', 'like', '%' . $value . '%')->take(10)->get();
        if ($user->hasVweAccess() && $vwes->count() > 0) {
            foreach ($vwes as $template) {
                if ($template->tileimage) {
                    $searchedListData['vwes'][] = [
                        'id' => $template->id,
                        'tile_img' => $template->tileimage_fullpath,
                        'title' =>  strlen($template->title) > 35 ? substr($template->title, 0, 35) . "..." : $template->title,
                        'url' =>  "/#/tasks/vwe/{$template->id}",
                        'type' => 'VIRTUAL WORK EXPERIENCE',
                    ];
                }
            }
        } else {
            $searchedListData['vwes'] = '';
        }

        $lessons = Lesson::published()->select('id', 'title', 'tileimage', 'publish')
            ->when(($user->isStudent() || session()->has("studentView")), function ($query) {
                return $query->whereHas('years', function ($q) {
                    $q->where('standard_id', Auth::user()->profile->standard_id);
                });
            })
            ->where('title', 'like', '%' . $value . '%')->take(10)->get();
        if ($user->hasLessonsAccess() && $lessons->count() > 0) {
            foreach ($lessons as $template) {
                if ($template->tileimage) {
                    $searchedListData['lessons'][] = [
                        'id' => $template->id,
                        'tile_img' => $template->tileimage_fullpath,
                        'title' => strlen($template->title) > 35 ? substr($template->title, 0, 35) . "..." : $template->title,
                        'url' =>  "/#/tasks/lessons/{$template->id}",
                        'type' => 'LESSON',
                    ];
                }
            }
        } else {
            $searchedListData['lessons'] = '';
        }

        $skills = SkillstrainingTemplate::forStudents()->select('id', 'title', 'tileimage', 'publish')->where('title', 'like', '%' . $value . '%')->take(10)->get();
        if ($user->hasSkillsTrainingAccess() && $skills->count() > 0) {
            foreach ($skills as $template) {
                if ($template->tileimage) {
                    $searchedListData['skills_trainings'][] = [
                        'id' => $template->id,
                        'tile_img' => $template->tileimage_fullpath,
                        'title' => strlen($template->title) > 35 ? substr($template->title, 0, 35) . "..." : $template->title,
                        'url' =>  "/#/tasks/skillstraining/{$template->id}",
                        'type' => 'SKILLS TRAINING'
                    ];
                }
            }
        } else {
            $searchedListData['skills_trainings'] = '';
        }

        return $searchedListData;
    }

    public function saveParentIndustries(Request $request)
    {
        try {
            $id = Auth::id();
            $parent = ChildParent::find($id);
            $parent->industries()->sync($request->industries);
            UpdateMailchimpAudienceJob::dispatch(auth()->user());
            return $parent->industries()->pluck('name', 'industry_categories.id');
        } catch (\Throwable $th) {
            return 'error';
        }
    }


    public function getParentIndustries()
    {
        try {
            $id = Auth::id();
            // Retrieve the ChildParent model for the given user ID
            $childParent = ChildParent::find($id);

            if (!$childParent) {
                return response()->json(['error' => 'User not found'], 404);
            }

            // Get the selected industries associated with the user
            $selectedIndustries = $childParent->industries->pluck('id')->toArray();
            //  dd($selectedIndustries);
            return response()->json(['selectedIndustries' => $selectedIndustries]);
        } catch (\Throwable $th) {
            return response()->json(['error' => 'An error occurred'], 500);
        }
    }

    public function saveParentDashboardChecklist(Request $request)
    {
        $data = $request->all();
        $user = ChildParent::find(Auth::id());
        if (Auth::user()->getFavoriteItems(Industryunit::class)->count() >= 5) {
            $data['explore_industries'] = 1;
        }
        $data['child'] = Auth::user()->childInvitees()->exists();
        $user->saveParentDashboardChecklist($data);
        return $user->dashboardChecklist;
    }

    public function getParentDashboardChecklist()
    {
        $user = ChildParent::find(Auth::id());
        $checklist = $user->dashboardChecklist;
        if ($checklist && !$checklist->child && Auth::user()->childInvitees()->exists()) {
            $data = [];
            $data['child'] = true;
            $user->saveParentDashboardChecklist($data);
        }
        return $user->dashboardChecklist()->first();
    }
}
