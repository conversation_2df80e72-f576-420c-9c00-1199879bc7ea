<?php

namespace App\Http\Controllers;

use App\Mail\vweFeedbackNotification;

use App\Audio;
use App\Badge;
use App\Country;
use App\IndustryCategory;
use App\Language;
use App\User;
use App\Step;
use App\WorkexperienceTemplate;
use App\WorkexperienceResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use \Conner\Tagging\Model\Tag;
use Auth;
use App\Standard;
use App\School;
use App\Organisation;
use Mail;
use App\Mail\VweSubmissionMessage;
use App\WorkexperienceSubject;
use App\Exports\WeResponsesExport;
use App\GeneralCapability;
use App\Jobs\NotifyUserOfCompletedWeResponsesExport;
use App\Jobs\ProcessSCORMPackageJob;
use App\Services\ANZSCO;
use App\SkillstrainingTemplate;
use App\TeacherResource;
use App\VweStepResponse;
use Illuminate\Database\Eloquent\Collection;
use App\WorkingStyle;
use Carbon\Carbon;
use Spatie\Activitylog\Models\Activity;
use App\Services\UserAccessService;

class WorkexperienceTemplateController extends Controller
{
    protected ANZSCO $anzsco;

    public function __construct(ANZSCO $anzsco)
    {
        $this->anzsco = $anzsco;
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $templates = WorkexperienceTemplate::select('id', 'title', 'publish')->paginate(20);

        return view('workexperience.index', compact('templates'));
    }


    public function responses(Request $request)
    {
        $schools = School::confirmed()->select('id', 'name')->get();
        $orgs = Organisation::confirmed()->select('id', 'name')->get();

        if ($request->institute_type) {
            $selected_institute = $request->institute_type;
        } else {
            $selected_institute = 'School';
        }

        $responses = WorkexperienceResponse::where('status', 'Submitted')->with('student:id,name,school_id,organisation_id,state_id', 'student.profile:firstname,lastname,school,user_id,standard_id,graduate_year', 'student.profile.class:id,title', 'student.school:id,name', 'student.organisation:id,name', 'template:id,title')
            ->when($request->name, function ($query) use ($request) {
                return $query->whereHas('student', function ($q) use ($request) {
                    $q->where('name', 'like', '%' . $request->name . '%');
                });
            })
            ->when($request->template, function ($query) use ($request) {
                return $query->whereHas('template', function ($q) use ($request) {
                    $q->whereId($request->template);
                });
            })
            ->when(Auth::user()->isAdmin() && $selected_institute, function ($query) use ($request, $selected_institute) {
                if ($selected_institute == 'School' && $request->school) {
                    return $query->whereHas('student', function ($q) use ($request) {
                        $q->where('school_id', $request->school);
                    });
                } elseif ($selected_institute == 'Organisation' && $request->organisation) {
                    return $query->whereHas('student', function ($q) use ($request) {
                        $q->where('organisation_id', $request->organisation);
                    });
                } elseif ($selected_institute == 'School') {
                    return $query->whereHas('student', function ($q) {
                        $q->whereNotNull('school_id');
                    });
                } elseif ($selected_institute == 'Organisation') {
                    return $query->whereHas('student', function ($q) {
                        $q->whereNotNull('organisation_id');
                    });
                }
            })
            // ->when($request->school, function ($query) use ($request) {
            //     return $query->whereHas('student', function ($q) use ($request) {
            //         $q->where('school_id', $request->school);
            //     });
            // })
            ->when($request->standard, function ($query) use ($request) {
                return $query->whereHas('student', function ($q) use ($request) {
                    $q->whereHas('profile', function ($q) use ($request) {
                        $q->where('standard_id', $request->standard);
                    });
                });
            })
            ->when($request->state, function ($query) use ($request) {
                return $query->whereHas('student', function ($q) use ($request) {
                    $q->whereStateId($request->state);
                });
            })
            ->when($request->feedback, function ($query) use ($request) {
                if (Auth::user()->isStaff() || (Auth::user()->isAdmin() && $request->institute_type == 'Organisation')) {
                    if ($request->feedback == "no") {
                        return $query->whereNull('org_feedback');
                    } else if ($request->feedback == "yes") {
                        return $query->whereNotNull('org_feedback');
                    } else if ($request->feedback == "pending") {
                        return $query->whereNotNull('org_feedback')->where('approve', '');
                    }
                } else {
                    if ($request->feedback == "no") {
                        return $query->whereNull('feedback');
                    } else if ($request->feedback == "yes") {
                        return $query->whereNotNull('feedback');
                    } else if ($request->feedback == "pending") {
                        return $query->whereNotNull('feedback')->where('approve', '');
                    }
                }
            })
            ->orderBy('submitted_at', 'desc')->paginate(20);

        $standards = Standard::all();
        $states = Country::find(1)->states()->orderBy('name')->get();
        $schools = School::confirmed()->select('id', 'name')->get();
        $orgs = Organisation::confirmed()->select('id', 'name')->get();
        $templates = WorkexperienceTemplate::select("id", "title")->get();

        $request->flash();

        return view('workexperience.responses', compact('responses', 'templates', 'standards', 'states', 'schools', 'orgs', 'selected_institute'));
    }

    // public function taskresponses(Request $request)
    // {
    //     if (!Auth::user()->hasAccessToVirtualworkexperience()) {
    //         abort(403, 'You do not have permission to perform this action.');
    //     }
    //     $tasks = WorkexperienceTemplate::published()
    //         ->when($request->template, function ($query) use ($request) {
    //             return $query->whereId($request->template);
    //         })->orderBy('id')->get();

    //     $campus = false;
    //     $campuses = Auth::user()->campuses()->get();
    //     if ($campuses->count()) {
    //         if ($request->campus) {
    //             $campus = array($request->campus);
    //         } else {
    //             $campus = $campuses->pluck('id');
    //         }
    //     }

    //     $templates = WorkexperienceTemplate::published()->pluck('title', 'id');

    //     $students = Student::with('workexperienceResponses')->where('school_id', Auth::user()->school_id)
    //         ->when(!($request->name || $request->template || $request->year || $request->campus), function ($query) {
    //             return $query->has("workexperienceResponses");
    //         })
    //         ->when($request->name, function ($query) use ($request) {
    //             return $query->where('name', 'like', $request->name . '%');
    //         })
    //         ->when($campus, function ($query) use ($campus) {
    //             return $query->whereHas('campuses', function ($q) use ($campus) {
    //                 $q->whereIn('campus_id', $campus);
    //             });
    //         })
    //         ->when($request->template, function ($query) use ($request) {
    //             return $query->with(['workexperienceResponses' => function ($q) use ($request) {
    //                 $q->whereTemplateId($request->template);
    //             }]);
    //         })
    //         ->when($request->year, function ($query) use ($request) {
    //             $query->whereHas('profile', function ($q) use ($request) {
    //                 $q->where('standard_id', $request->year);
    //             });
    //         })->pluck('name', 'id');

    //     $data = [];

    //     foreach ($students as $id => $name) {
    //         $aa = ['name' => $name];
    //         $aa['tasks'] = [];
    //         foreach ($tasks as $task) {
    //             $aa['tasks'][$task->id] = $task->responses()->whereStudentId($id)->first();
    //         }
    //         $data[] = $aa;
    //     }
    //     $years = Standard::onlySchool()->get();
    //     $request->flash();

    //     return view('workexperience.teachers.responses', compact('tasks', 'data', 'years', 'templates', 'campuses'));
    // }

    public function taskresponses(Request $request)
    {
        $responses = WorkexperienceResponse::submitted()->with('student:id,name,school_id', 'student.profile:firstname,lastname,user_id,standard_id,graduate_year', 'student.profile.class:id,title', 'student.school:id,name', 'template:id,title', 'student.campuses')->whereHas('student', function ($q) use ($request) {
            if (Auth::user()->isStaff()) {
                $q->whereHas('profile', function ($q) {
                    $q->whereIn('standard_id', Standard::pluck('id'));
                })->where('organisation_id', Auth::user()->organisation_id);
            } else {
                $q->whereHas('profile', function ($q) {
                    $q->whereIn('standard_id', Standard::pluck('id'));
                })->where('school_id', Auth::user()->school_id);
            }
        })
            ->when($request->name, function ($query) use ($request) {
                return $query->whereHas('student', function ($q) use ($request) {
                    $q->where('name', 'like', '%' . $request->name . '%');
                });
            })
            ->when($request->template, function ($query) use ($request) {
                return $query->whereHas('template', function ($q) use ($request) {
                    $q->whereId($request->template);
                });
            })
            ->when($request->standard, function ($query) use ($request) {
                return $query->whereHas('student', function ($q) use ($request) {
                    $q->whereHas('profile', function ($q) use ($request) {
                        $q->where('standard_id', $request->standard);
                    });
                });
            })
            ->when($request->campus, function ($query) use ($request) {
                return $query->whereHas('student', function ($q) use ($request) {
                    $q->whereHas('campuses', function ($q) use ($request) {
                        $q->where('campus_id', $request->campus);
                    });
                });
            })
            ->when($request->feedback, function ($query) use ($request) {
                if ($request->feedback == "no") {
                    return $query->whereNull('feedback');
                } else if ($request->feedback == "yes") {
                    return $query->whereNotNull('feedback');
                } else if ($request->feedback == "pending") {
                    return $query->whereNotNull('feedback')->where('approve', '');
                }
            })
            ->when($request->approve, function ($query) use ($request) {
                if ($request->approve == "no") {
                    return $query->where('approve', 0);
                } else if ($request->approve == "yes") {
                    return $query->where('approve', 1);
                }
            })
            ->latest()->paginate(20);
        $request->flash();
        $standards = Standard::get();
        // $schools = School::confirmed()->select('id', 'name')->get();
        $templates = WorkexperienceTemplate::select("id", "title")->get();
        if (Auth::user()->isStaff()) {
            $campuses = Auth::user()->organisation->campuses()->get();
        } else {
            $campuses = Auth::user()->school->campuses()->get();
        }
        return view('workexperience.teachers.responses', compact('responses', 'templates', 'standards', 'campuses'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $industries = IndustryCategory::pluck('name', 'id');
        $subjects = WorkexperienceSubject::pluck('name', 'id');
        $countries = Country::with('states')->get();
        $skillsTrainings = SkillstrainingTemplate::pluck('title', 'id');
        $workingStyles = WorkingStyle::pluck('name', 'id');
        $resources = TeacherResource::orderBy('title')->pluck('title', 'id');
        $badges = Badge::pluck('name', 'id');
        return view('workexperience.add', compact('industries', 'subjects', 'countries', 'skillsTrainings', 'workingStyles', 'resources', 'badges'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $tileimage = $request->tileimage->store('attachments/workexperience', ['visibility' => 'public']);
        if ($request->background_imagepath) {
            $background_imagepath = $request->background_imagepath->store('attachments/workexperience', ['visibility' => 'public']);
        }
        if ($request->foreground_imagepath) {
            $foreground_imagepath = $request->foreground_imagepath->store('attachments/workexperience', ['visibility' => 'public']);
        }
        if ($request->certificate_image) {
            $certificate_image = $request->certificate_image->store('attachments/workexperience', ['visibility' => 'public']);
        }

        $workexperiencetemplate = new WorkexperienceTemplate;
        $workexperiencetemplate->title = $request->title;
        $workexperiencetemplate->short_description = $request->short_description;
        $workexperiencetemplate->tileimage = $tileimage;
        $workexperiencetemplate->background_videoid = $request->background_videoid;
        if ($request->background_imagepath) {
            $workexperiencetemplate->background_imagepath = $background_imagepath;
        }
        $workexperiencetemplate->foreground_videoid = $request->foreground_videoid;
        if ($request->foreground_imagepath) {
            $workexperiencetemplate->foreground_imagepath = $foreground_imagepath;
        }
        if ($request->certificate_image) {
            $workexperiencetemplate->certificate_image = $certificate_image;
        }
        $workexperiencetemplate->body = $request->body;
        $workexperiencetemplate->curriculum = $request->curriculum;
        $workexperiencetemplate->publish = $request->publish;
        $workexperiencetemplate->is_premium = $request->is_premium ? true : false;
        $workexperiencetemplate->estimated_time = $request->estimated_time;
        $workexperiencetemplate->level = $request->level;
        $workexperiencetemplate->response = $request->response ? true : false;
        $workexperiencetemplate->module_type_id = $request->module_type_id;
        $workexperiencetemplate->badge_id = $request->badge;


        $numbers = $request->numbers;
        $titles = $request->steptitles;
        $times = $request->steptimes;
        $images = $request->stepbg_images;
        $videos = $request->stepbg_videos;
        $bodies = $request->stepbodies;
        $responses = $request->stepresponses;
        $stepsIsScorm = $request->step_is_scorm;
        $stepsScormFile = $request->step_scorm_file;
        $stepIsScormScoring = $request->step_is_scorm_scoring ?? [];
        $scormScoringStepKey = array_key_first($stepIsScormScoring);

        $steps = [];
        if (!empty($numbers)) {
            foreach ($numbers as $key => $number) {
                $bg_image = null;
                if ($number) {
                    if (@$images[$key]) {
                        $bg_image = $images[$key]->store('attachments/workexperience', ['visibility' => 'public']);
                    }
                    $steps[$key] = new Step([
                        'number' => $number,
                        'estimated_time' => @$times[$key],
                        'bg_image' => $bg_image,
                        'bg_video' => @$videos[$key],
                        'title' => $titles[$key],
                        'body' => $bodies[$key],
                        'response' => @$responses[$key] ? true : false,
                        'is_scorm' => isset($stepsIsScorm[$key]),
                        'is_scorm_scoring' => $scormScoringStepKey == $key,
                    ]);
                }
            }
            $workexperiencetemplate->save();
            $workexperiencetemplate->steps()->saveMany($steps);

            foreach ($steps as $key => $step) {
                if ($step->is_scorm && $step->id && isset($stepsScormFile[$key])) {
                    ProcessSCORMPackageJob::dispatch(
                        decrypt($stepsScormFile[$key]),
                        Step::class,
                        $step->id
                    );
                }
            }
        } else {
            $workexperiencetemplate->save();
        }

        if ($request->audio_path) {
            $audio_path = $request->audio_path->store('attachments/audios', ['visibility' => 'public']);

            $language = Language::whereName('English')->whereVersion('AU')->value('id');

            $audio = new Audio;
            $audio->path = $audio_path;
            $audio->language_id = $language;
        }

        if ($request->audio_path) {
            $workexperiencetemplate->audio()->save($audio);
        }

        $workexperiencetemplate->states()->sync($request->states);
        $workexperiencetemplate->teacherResources()->attach($request->resources);
        $workexperiencetemplate->worksheets()->attach($request->worksheets);
        $workexperiencetemplate->industries()->attach($request->industries);
        $workexperiencetemplate->skillstrainingTemplates()->attach($request->skillstrainings);
        $workexperiencetemplate->workingStyles()->attach($request->working_styles);

        $subjects = [];

        if ($request->subjects) {
            foreach ($request->subjects as $subject) {
                if (is_numeric($subject)) {
                    $subjects[] = $subject;
                } else {
                    $new_subject = WorkexperienceSubject::firstOrCreate(
                        ['name' => $subject],
                    );

                    $subjects[] = $new_subject->id;
                }
            }
            $workexperiencetemplate->subjects()->attach($subjects);
        }

        if ($request->skills) {
            $workexperiencetemplate->tag($request->skills);
            foreach ($request->skills as $tagName) {
                $tag = Tag::where('name', $tagName)->first();
                if ($tag) {
                    $tag->setGroup("Skills");
                }
            }
        }

        if ($request->general_capabilities) {
            $ids = [];
            foreach ($request->general_capabilities as $general_capability) {
                $gc = GeneralCapability::firstOrNew([
                    'title' => $general_capability
                ]);
                $gc->save();
                $ids[] = $gc->id;
            }
            $workexperiencetemplate->generalCapabilities()->attach($ids);
        }

        $this->anzsco->saveData($workexperiencetemplate, $request->all());

        return redirect('/workexperiencetemplates')->with('message', 'Template has been created successfully.');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\WorkexperienceTemplate  $workexperiencetemplate
     * @return \Illuminate\Http\Response
     */
    public function show(WorkexperienceTemplate $workexperiencetemplate)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\WorkexperienceTemplate  $workexperiencetemplate
     * @return \Illuminate\Http\Response
     */
    public function edit(WorkexperienceTemplate $workexperiencetemplate)
    {
        $industries = IndustryCategory::pluck('name', 'id');
        $subjects = WorkexperienceSubject::pluck('name', 'id');
        $countries = Country::with('states')->get();
        $skillsTrainings = SkillstrainingTemplate::pluck('title', 'id');
        $workingStyles = WorkingStyle::pluck('name', 'id');
        $resources = TeacherResource::orderBy('title')->pluck('title', 'id');
        $badges = Badge::pluck('name', 'id');
        return view('workexperience.edit', compact('workexperiencetemplate', 'industries', 'subjects', 'countries', 'skillsTrainings', 'workingStyles', 'resources', 'badges'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\WorkexperienceTemplate  $workexperiencetemplate
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, WorkexperienceTemplate $workexperiencetemplate)
    {
        if ($request->tileimage) {
            if ($workexperiencetemplate->tileimage && Storage::exists($workexperiencetemplate->tileimage)) {
                Storage::delete($workexperiencetemplate->tileimage);
            }
            $tileimage = $request->tileimage->store('attachments/workexperience', ['visibility' => 'public']);
        } else {
            $tileimage = $workexperiencetemplate->tileimage;
        }
        if ($request->background_imagepath) {
            if ($workexperiencetemplate->background_imagepath && Storage::exists($workexperiencetemplate->background_imagepath)) {
                Storage::delete($workexperiencetemplate->background_imagepath);
            }
            $background_imagepath = $request->background_imagepath->store('attachments/workexperience', ['visibility' => 'public']);
        } else {
            $background_imagepath = $workexperiencetemplate->background_imagepath;
        }
        if ($request->foreground_imagepath) {
            if ($workexperiencetemplate->foreground_imagepath && Storage::exists($workexperiencetemplate->foreground_imagepath)) {
                Storage::delete($workexperiencetemplate->foreground_imagepath);
            }
            $foreground_imagepath = $request->foreground_imagepath->store('attachments/workexperience', ['visibility' => 'public']);
        } else {
            if (!$request->foreground_imagepath_old && $workexperiencetemplate->foreground_imagepath && Storage::exists($workexperiencetemplate->foreground_imagepath)) {
                Storage::delete($workexperiencetemplate->foreground_imagepath);
            }
            $foreground_imagepath = $request->foreground_imagepath_old;
        }

        if ($request->certificate_image) {
            if ($workexperiencetemplate->certificate_image && Storage::exists($workexperiencetemplate->certificate_image)) {
                Storage::delete($workexperiencetemplate->certificate_image);
            }
            $certificate_image = $request->certificate_image->store('attachments/workexperience', ['visibility' => 'public']);
        } else {
            if (!$request->certificate_image_old && $workexperiencetemplate->certificate_image && Storage::exists($workexperiencetemplate->certificate_image)) {
                Storage::delete($workexperiencetemplate->certificate_image);
            }
            $certificate_image = $request->certificate_image_old;
        }

        $workexperiencetemplate->title = $request->title;
        $workexperiencetemplate->short_description = $request->short_description;
        $workexperiencetemplate->tileimage = $tileimage;
        $workexperiencetemplate->body = $request->body;
        $workexperiencetemplate->curriculum = $request->curriculum;
        $workexperiencetemplate->background_videoid = $request->background_videoid;
        if ($request->background_imagepath) {
            $workexperiencetemplate->background_imagepath = $background_imagepath;
        }
        $workexperiencetemplate->foreground_videoid = $request->foreground_videoid;
        $workexperiencetemplate->foreground_imagepath = $foreground_imagepath;
        $workexperiencetemplate->certificate_image = $certificate_image;
        $workexperiencetemplate->publish = $request->publish;
        $workexperiencetemplate->is_premium = $request->is_premium ? true : false;
        $workexperiencetemplate->estimated_time = $request->estimated_time;
        $workexperiencetemplate->level = $request->level;
        $workexperiencetemplate->response = $request->response ? true : false;
        $workexperiencetemplate->module_type_id = $request->module_type_id;
        $workexperiencetemplate->badge_id = $request->badge;

        if ($request->audio_path) {
            if ($workexperiencetemplate->audio && Storage::exists($workexperiencetemplate->audio->path)) {
                Storage::delete($workexperiencetemplate->audio->path);
            }
            $audio_path = $request->audio_path->store('attachments/audios', ['visibility' => 'public']);
        } else {
            if (!$request->audio_path_old && $workexperiencetemplate->audio && Storage::exists($workexperiencetemplate->audio->path)) {
                Storage::delete($workexperiencetemplate->audio->path);
                $workexperiencetemplate->audio->delete();
            }
            $audio_path = $request->audio_path_old;
        }

        if ($audio_path) {
            $language = Language::whereName('English')->whereVersion('AU')->value('id');

            $audio = $workexperiencetemplate->audio ?? new Audio;
            $audio->path = $audio_path;
            $audio->language_id = $language;
        }

        $workexperiencetemplate->save();
        if ($audio_path) {
            $workexperiencetemplate->audio()->save($audio);
        }


        $stepids = $request->stepids;
        $numbers = $request->numbers;
        $times = $request->steptimes;
        $images = $request->stepbg_images;
        $oldimages = $request->stepbg_images_old;
        $videos = $request->stepbg_videos;
        $titles = $request->steptitles;
        $bodies = $request->stepbodies;
        $responses = $request->stepresponses;
        $stepsIsScorm = $request->step_is_scorm;
        $stepsScormFile = $request->step_scorm_file;
        $stepIsScormScoring = $request->step_is_scorm_scoring ?? [];
        $scormScoringStepKey = array_key_first($stepIsScormScoring);

        $diff = $workexperiencetemplate->steps()->pluck('id')->diff($stepids);

        Step::find($diff)->each->delete();
        $workexperiencetemplate->steps()->update([
            'is_scorm_scoring' => false
        ]);

        if (!empty($numbers)) {
            foreach ($numbers as $key => $number) {
                if ($number) {
                    $bg_image = null;
                    if (isset($stepids[$key])) {
                        $step = Step::find($stepids[$key]);

                        if (@$images[$key]) {
                            if ($step->bg_image && Storage::exists($step->bg_image)) {
                                Storage::delete($step->bg_image);
                            }
                            $bg_image = $images[$key]->store('attachments/workexperience', ['visibility' => 'public']);
                        } elseif (!@$images[$key] && !@$oldimages[$key]) {
                            if ($step->bg_image && Storage::exists($step->bg_image)) {
                                Storage::delete($step->bg_image);
                            }
                        } else {
                            $bg_image = $step->bg_image;
                        }
                        $step->number = $number;
                        $step->title = $titles[$key];
                        $step->estimated_time = $times[$key];
                        $step->bg_video = $videos[$key];
                        $step->bg_image = $bg_image;
                        $step->body = $bodies[$key];
                        $step->response = @$responses[$key] ? true : false;
                        $step->is_scorm = isset($stepsIsScorm[$key]);
                        $step->is_scorm_scoring = $scormScoringStepKey == $key;
                        $step->save();

                        if ($step->is_scorm && isset($stepsScormFile[$key])) {
                            ProcessSCORMPackageJob::dispatch(
                                decrypt($stepsScormFile[$key]),
                                Step::class,
                                $step->id
                            );
                        }
                    } else {
                        if (@$images[$key]) {
                            $bg_image = $images[$key]->store('attachments/workexperience', ['visibility' => 'public']);
                        }
                        $step = new Step();
                        $step->number = $number;
                        $step->title = $titles[$key];
                        $step->estimated_time = $times[$key];
                        $step->bg_video = $videos[$key];
                        $step->bg_image = $bg_image;
                        $step->body = $bodies[$key];
                        $step->response = @$responses[$key] ? true : false;
                        $step->is_scorm = isset($stepsIsScorm[$key]);
                        $step->is_scorm_scoring = $scormScoringStepKey == $key;
                        $workexperiencetemplate->steps()->save($step);

                        if ($step->is_scorm && isset($stepsScormFile[$key])) {
                            ProcessSCORMPackageJob::dispatch(
                                decrypt($stepsScormFile[$key]),
                                Step::class,
                                $step->id
                            );
                        }
                    }
                }
            }
        }

        $workexperiencetemplate->states()->sync($request->states);
        $workexperiencetemplate->teacherResources()->sync($request->resources);
        $workexperiencetemplate->worksheets()->sync($request->worksheets);
        $workexperiencetemplate->industries()->sync($request->industries);
        $workexperiencetemplate->skillstrainingTemplates()->sync($request->skillstrainings);
        $workexperiencetemplate->workingStyles()->sync($request->working_styles);

        $subjects = [];

        if ($request->subjects) {
            foreach ($request->subjects as $subject) {
                if (is_numeric($subject)) {
                    $subjects[] = $subject;
                } else {
                    $new_subject = WorkexperienceSubject::firstOrCreate(
                        ['name' => $subject],
                    );

                    $subjects[] = $new_subject->id;
                }
            }
        }
        $workexperiencetemplate->subjects()->sync($subjects);

        if ($request->skills) {
            $workexperiencetemplate->retag($request->skills);

            foreach ($request->skills as $tagName) {
                $tag = Tag::where('name', $tagName)->first();
                if ($tag) {
                    $tag->setGroup("Skills");
                }
            }
        } else {
            $workexperiencetemplate->untag();
        }

        if ($request->general_capabilities) {
            $ids = [];
            foreach ($request->general_capabilities as $general_capability) {
                $gc = GeneralCapability::firstOrNew([
                    'title' => $general_capability
                ]);
                $gc->save();
                $ids[] = $gc->id;
            }
            $workexperiencetemplate->generalCapabilities()->sync($ids);
        } else {
            $workexperiencetemplate->generalCapabilities()->detach();
        }

        $this->anzsco->saveData($workexperiencetemplate, $request->all());

        return redirect('/workexperiencetemplates')->with('message', 'Template has been updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\WorkexperienceTemplate  $workexperiencetemplate
     * @return \Illuminate\Http\Response
     */
    public function destroy(WorkexperienceTemplate $workexperiencetemplate)
    {
        $workexperiencetemplate->delete();
        $redirectUrl = url()->previous();
        if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
            $redirectUrl = session()->get('previousUrl');
        }
        return redirect($redirectUrl)->with('message', 'Template has been deleted successfully.');
    }

    public function destroyresponse($id)
    {
        $workexperience = WorkexperienceResponse::find($id);
        if (!UserAccessService::currentUserCanAccess($workexperience->student_id)) {
            abort(403, 'You do not have permission to perform this action.');
        }
        $id = $workexperience->template_id;
        $workexperience->delete();

        if (Auth::user()->isAdmin()) {
            return redirect('/wew/responses/workexp')->with('message', 'Response has been deleted successfully!');
        } elseif (Auth::user()->isTeacher() || Auth::user()->isStaff()) {
            return redirect('/workexperience-tasks')->with('message', 'Response has been deleted successfully!');
        }
        return redirect()->route("exploreworkexperience.show", $id)->with('message', 'Response has been deleted successfully!');
    }

    public function getFeedback($id)
    {
        $data = WorkexperienceResponse::whereId($id);
        if (Auth::user()->isAdmin() || Auth::user()->isMarker()) {
            return $data->pluck('feedback', 'approve');
        }
        return $data->value('feedback');
    }

    public function storeFeedback(Request $request, $id)
    {
        if ($request->approve == 1 || $request->feedback) {

            $approve = ($request->approve == 1 ? true : false);
            $response = WorkexperienceResponse::find($id);
            if (!$response->user_id) {
                $response->user_id = Auth::id();
            }
            $response->feedback = $request->feedback;
            if (Auth::user()->isAdmin()) {
                $response->approve =  $approve;
            }
            $response->save();
            if ($response->approve) {
                Mail::send(new vweFeedbackNotification($response, $response->student()->first()));
            }
        }

        return redirect('wew/responses/workexp/?name=' . request('oldname') . '&template=' . request('oldtemplate') . '&standard=' . request('oldstandard') . '&feedback=' . request('oldfeedback') . '&institute_type=' . request('oldinstitute_type') . '&school=' . request('oldschool') . '&organisation=' . request('oldorganisation') . '')->with('message', 'Feedback has been added successfully!');

        // return redirect('/workexperiencetemplates/responses')->with('message', 'Feedback has been added successfully!');
    }

    public function getMessage($id)
    {
        $data = WorkexperienceResponse::select('template_id', 'student_id', 'message')->with('student:id,name', 'template:id,title')->where('id', $id)->first();
        return $data;
    }

    public function storeMessage(Request $request, $id)
    {
        $response = WorkexperienceResponse::find($id);
        $response->message = $request->message;
        $response->save();

        Mail::to($response->student->email)->send(new VweSubmissionMessage($response));

        return redirect('wew/responses/workexp/?name=' . request('oldname') . '&template=' . request('oldtemplate') . '&standard=' . request('oldstandard') . '&feedback=' . request('oldfeedback') . '&institute_type=' . request('oldinstitute_type') . '&school=' . request('oldschool') . '&organisation=' . request('oldorganisation') . '')->with('message', 'Message has been added successfully!');

        // return redirect('/workexperiencetemplates/responses')->with('message', 'Feedback has been added successfully!');
    }

    public function downloadResponses($id)
    {
        $response = WorkexperienceResponse::find($id);
        return Storage::cloud()->download($response->response_path, $response->user_and_template);
    }

    public function approve($id)
    {
        $response = WorkexperienceResponse::find($id);
        $response->approve = !$response->approve;
        $response->save();
        if (request()->wantsJson()) {
            return response()->json('success');
        }
        $redirectUrl = url()->previous();
        if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
            $redirectUrl = session()->get('previousUrl');
        }
        return redirect($redirectUrl)/* ->with('message', 'Your application has been saved!') */;
    }

    public function export(Request $request)
    {
        $filename = 'exports/we-responses-' . time() . '.xlsx';
        $user = Auth::user();
        (new WeResponsesExport($request->all(), $user))->queue($filename)->chain([
            new NotifyUserOfCompletedWeResponsesExport(request()->user(), $filename),
        ]);

        $urlAddon = Auth::user()->isTeacher() ? 'erience' : '';

        return redirect('wew/responses/workexp' . $urlAddon)->with('message', "Export is in process, you will get an email with download link once it is ready.");
    }

    public function addStepResponseForCompletedTasks()
    {
        WorkexperienceResponse::select('id', 'template_id', 'created_at', 'updated_at')->with('template:id', 'template.steps:id,template_id')->chunk(50, function (Collection $responses) {
            foreach ($responses as $response) {
                $stepResponses = [];
                foreach ($response->template->steps as $step) {
                    $stepResponses[] = new VweStepResponse([
                        'step_id' => $step->id,
                        'created_at' => $response->created_at,
                        'updated_at' => $response->updated_at,
                    ]);
                }
                $response->stepResponses()->saveMany($stepResponses);
            }
        });

        $redirectUrl = url()->previous();
        if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
            $redirectUrl = session()->get('previousUrl');
        }
        return redirect($redirectUrl)->with('messages', 'Vwe step responses saved successfully!');
    }

    public function singleResponse()
    {
        $id = request('id');
        $activityLogId = request('activity_log_id');

        $data = WorkexperienceResponse::whereId($id)->with('template:id,title', 'student:id,name')->first();
        $log = Activity::find($activityLogId);
        if (Auth::user()->isStudent() || ((Auth::user()->isTeacher() || Auth::user()->isStaff()) && session('studentView'))) {
            $html = "<p>You completed the <a href='/#/tasks/vwe/" . ($data->template->id ?? "") ."'>" . $data->template->title . "</a> virtual work experience.</p><div class='event-date'> <small class='fs-12 hint-text'>" . Carbon::parse($log->created_at)->format('M d, Y g:i a') . "</small> </div>";
        } else {
            $html = "<p>" . $data->student?->name . " completed the <a href='/#/tasks/vwe/" . ($data->template->id ?? "") ."'>" . $data->template->title . "</a>  virtual work experience.</p><div class='event-date'> <small class='fs-12 hint-text'>" . Carbon::parse($log->created_at)->format('M d, Y g:i a') . "</small> </div>";
        }
        return response()->json([
            'success' => true,
            'html' => $html
        ]);
    }
}
