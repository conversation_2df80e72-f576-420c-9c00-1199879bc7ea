<!DOCTYPE html>
<html lang="{{ config('app.locale') }}">

<head>
    <meta charset="utf-8" />
    <meta name="description" content="A 1-on-1 careers program to deliver success to students, parents and schools.">
    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">
    @if (!isset($DesktopView))
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    @endif
    <title>
        @hasSection('pageTitle')
            @yield('pageTitle') - {{ config('app.name') }}
        @else
            {{ config('app.name') }}
        @endif
    </title>
    {{-- <link rel="shortcut icon" sizes="60x60" href="{{asset('images/TCD-favicon-X.jpg')}}"> --}}
    <link rel="shortcut icon" sizes="60x60" href="{{ asset('images/TCD-favicon-X.jpg') }}">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ asset('images/TCD-favicon-X.jpg') }}">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-touch-fullscreen" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">

    <meta content="" name="author" />
    @if (!Auth::user()->isAdmin())
        <!-- Google Tag Manager -->
        <script>
            (function(w, d, s, l, i) {
                w[l] = w[l] || [];
                w[l].push({
                    'gtm.start': new Date().getTime(),
                    event: 'gtm.js'
                });
                var f = d.getElementsByTagName(s)[0],
                    j = d.createElement(s),
                    dl = l != 'dataLayer' ? '&l=' + l : '';
                j.async = true;
                j.src =
                    'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
                f.parentNode.insertBefore(j, f);
            })(window, document, 'script', 'dataLayer', 'GTM-P756BP9');
        </script>
        <!-- End Google Tag Manager -->
    @endif
    <link href="https://fonts.googleapis.com/css?family=Montserrat" rel="stylesheet">
    <link class="main-stylesheet" href="{{ asset('css/admin.css') }}" rel="stylesheet" type="text/css" />`
    <link href="{{ mix('css/custom.css') }}" rel="stylesheet">
    <link class="main-stylesheet" href="{{ asset('css/custom-18jan23.css') }}" rel="stylesheet" type="text/css" />

    @stack('stylesheets')
    <script>
        window.App = {!! json_encode(['csrfToken' => csrf_token(), 'user' => Auth::user()->only(['id', 'school_id', 'name']), 'signedIn' => Auth::check()]) !!};
    </script>
    <script src="{{ asset('js/app.js') }}{{ config('app.assets_cache_busting') ? '?id=' . uniqid() : '' }}" type="text/javascript"></script>

    <!-- Meta Pixel Code -->
    <script>
        ! function(f, b, e, v, n, t, s)

        {
            if (f.fbq) return;
            n = f.fbq = function() {
                n.callMethod ?

                    n.callMethod.apply(n, arguments) : n.queue.push(arguments)
            };

            if (!f._fbq) f._fbq = n;
            n.push = n;
            n.loaded = !0;
            n.version = '2.0';

            n.queue = [];
            t = b.createElement(e);
            t.async = !0;

            t.src = v;
            s = b.getElementsByTagName(e)[0];

            s.parentNode.insertBefore(t, s)
        }(window, document, 'script',

            'https://connect.facebook.net/en_US/fbevents.js');


        fbq('init', '1917553959016280');

        fbq('track', 'PageView');
    </script>

    <noscript>
        <img height="1" width="1" src="https://www.facebook.com/tr?id=1917553959016280&ev=PageView&noscript=1" />
    </noscript>
    <!-- End Meta Pixel Code -->
    @yield('head')
    <style>
        body.fixed-header .header {
            z-index: 1000;
        }

        .cke_notification.cke_notification_warning {
            display: none;
        }
    </style>
    @stack('styles')
</head>

<body class="fixed-header menu-pin bg-white {{ isset($noGreyBg) ? $noGreyBg : '' }}">
    @if (!Auth::user()->isAdmin())
        <!-- Google Tag Manager (noscript) -->
        <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-P756BP9" height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
        <!-- End Google Tag Manager (noscript) -->
    @endif
    {{-- You this form to logout instead of a link with GET request to prevent cross-site request forgery. --}}
    <form id="logout-form" action="{{ route('logout') }}" method="POST" style="display: none;"> {{ csrf_field() }} </form>
    <form id="sessionexpire-form" action="{{ route('session-expire') }}" method="POST" style="display: none;"> {{ csrf_field() }} </form>
    <div id="app">
        @include ('layouts.adminnav')
        <!--   START PAGE-CONTAINER   -->
        <div class="page-container">
            <!--   START PAGE HEADER WRAPPER   -->
            <!--   START HEADER   -->
            <div class="header @if (!Auth::user()->isAdmin()) hidden-lg-up @endif">
                <!--   START MOBILE SIDEBAR TOGGLE   -->
                <a href="#" class="btn-link toggle-sidebar hidden-lg-up pg pg-menu" data-toggle="sidebar"> </a>
                <!--   END MOBILE SIDEBAR TOGGLE   -->
                <div class="">
                    <div class="inline padding-10 brand">
                        <img src="{{ url('../css/img/logo-register.png ') }}" alt="logo" class="p-10 img-fluid " data-src="{{ url('../css/img/logo-register.png ') }}" data-src-retina="{{ url('../css/img/logo-register.png ') }}">
                    </div>
                    <!--   START NOTIFICATION LIST   -->
                    <!-- <ul class="hidden-md-down notification-list no-margin hidden-sm-down b-grey b-l b-r no-style p-l-30 p-r-20">
                    <li class="inline p-r-10">
                        <div class="dropdown">
                            <a href="javascript:;" id="notification-center" class="header-icon pg pg-world" data-toggle="dropdown">
                                <span class="bubble"></span>
                            </a>
                        </div>
                    </li>
                    <li class="inline p-r-10">
                        <a href="#" class="header-icon pg pg-link"></a>
                    </li>
                    <li class="inline p-r-10">
                        <a href="#" class="header-icon pg pg-thumbs"></a>
                    </li>
                </ul> -->
                </div>
                <div class="d-flex align-items-center">
                    <!--   START User Info  -->
                    @if (Auth::user()->isAdmin())
                        <div class="pull-left p-r-10 fs-14 font-heading hidden-md-down">
                            <span class="semi-bold">{{ Auth::user()->name }}</span>
                        </div>
                        <div class="dropdown pull-right">
                            <button class="profile-dropdown-toggle" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <span class="inline thumbnail-wrapper d32 circular">
                                    <img src="{{ Auth::user()->avatar_path }}" data-src="{{ Auth::user()->avatar_path }}" alt="" width="32" height="32">
                                </span>
                            </button>
                            <div class="dropdown-menu dropdown-menu-right profile-dropdown" role="menu">
                                <a href="{{ route('profile-edit') }}" class="dropdown-item">
                                    <i class="fa fa-edit"></i>Edit Profile
                                </a>
                                <a href="{{ route('logout') }}" onclick="event.preventDefault(); document.getElementById('logout-form').submit();" class="clearfix bg-master-lighter dropdown-item">
                                    <span class="pull-left">Logout</span>
                                    <span class="pull-right">
                                        <i class="pg-power"></i>
                                    </span>
                                </a>
                            </div>
                        </div>
                    @elseif(Auth::user()->isTeacher() || Auth::user()->isStaff())
                        <div class="dropdown pull-right">
                            <button class="profile-dropdown-toggle" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <span role="button" class="inline thumbnail-wrapper d32 circular user-initials">
                                    {{ Auth::user()->initials }}
                                </span>
                            </button>
                            <div class="dropdown-menu dropdown-menu-right profile-dropdown" role="menu">
                                <a href="{{ route('profile-edit') }}" class="dropdown-item">
                                    <i class="fa fa-edit"></i>Edit Profile
                                </a>
                                <a href="https://help.thecareersdepartment.com/en/" class="dropdown-item" target="_blank">
                                    <i class="fa fa-question"></i>Support
                                </a>
                                <a href="{{ route('logout') }}" onclick="event.preventDefault(); document.getElementById('logout-form').submit();" class="clearfix bg-master-lighter dropdown-item">
                                    <span class="pull-left">Logout</span>
                                    <span class="pull-right">
                                        <i class="pg-power"></i>
                                    </span>
                                </a>
                            </div>
                        </div>
                    @else
                        <div class="dropdown pull-right">
                            <button class="p-0 profile-dropdown-toggle" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <span class="inline thumbnail-wrapper d32 circular user-initials">
                                    {{ Auth::user()->initials }}
                                </span>
                            </button>
                            <div class="dropdown-menu dropdown-menu-right profile-dropdown" role="menu">
                                <a href="{{ route('profile-edit') }}" class="dropdown-item">
                                    <i class="fa fa-edit"></i>Edit Profile
                                </a>
                                <a href="{{ route('logout') }}" onclick="event.preventDefault(); document.getElementById('logout-form').submit();" class="clearfix bg-master-lighter dropdown-item">
                                    <span class="pull-left">Logout</span>
                                    <span class="pull-right">
                                        <i class="pg-power"></i>
                                    </span>
                                </a>
                            </div>
                        </div>
                        <!--   END User Info  -->
                    @endif
                </div>
            </div>
            <!--   END HEADER   -->
            <!--   END PAGE HEADER WRAPPER   -->
            <!--   START PAGE CONTENT WRAPPER   -->
            <div class="page-content-wrapper">
                <!--   START PAGE CONTENT   -->
                <div class="content">
                    <!--   START JUMBOTRON   -->
                    <div class="jumbotron" data-pages="parallax" style="overflow: visible;z-index: 100">
                        <div class=" container-fluid sm-p-l-0 sm-p-r-0">
                            {{-- @if (!isset($hideBreadcrumb)) --}}
                            @if (!Auth::user()->isAdmin())
                                @if (Auth::user()->isTeacher() || Auth::user()->isStaff())
                                    <div class="inner row pt-2 column-reverse-md-down {{ isset($hideBreadcrumb) ? 'hidden-md-down' : '' }}">
                                        @if (isset($breadcrumb_change))
                                            <div class="col-lg-8 d-inline-flex p-t-5">
                                                <span role="button" onclick="breadcrumbAction(this)" class="toggle-breadcrumb" style="transform: rotate(45deg);">&times;</span>
                                                <div class="pl-3 breadcrumb-section" style="display: none;">
                                                    @yield('breadcrumbs')
                                                </div>
                                            </div>
                                        @endif
                                        <div class="@if (isset($breadcrumb_change)) col-lg-4 @else col-lg-12 @endif">
                                            <div class="dropdown pull-right hidden-md-down">
                                                <button role="button" class="profile-dropdown-toggle" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                    <span class="inline thumbnail-wrapper d32 circular user-initials">
                                                        {{ Auth::user()->initials }}
                                                    </span>
                                                </button>
                                                <div class="dropdown-menu dropdown-menu-right profile-dropdown" role="menu">
                                                    <a href="{{ route('profile-edit') }}" class="dropdown-item">
                                                        <i class="fa fa-edit"></i>Edit Profile
                                                    </a>
                                                    <a href="https://help.thecareersdepartment.com/en/" class="dropdown-item" target="_blank">
                                                        <i class="fa fa-question"></i>Support
                                                    </a>
                                                    <a href="{{ route('logout') }}" onclick="event.preventDefault(); document.getElementById('logout-form').submit();" class="clearfix bg-master-lighter dropdown-item">
                                                        <span class="pull-left">Logout</span>
                                                        <span class="pull-right">
                                                            <i class="pg-power"></i>
                                                        </span>
                                                    </a>
                                                </div>
                                            </div>
                                            <div class="py-2 pull-right p-r-10 fs-14">
                                                <span class="select-view-tab ">
                                                    {{-- @if ((Auth::user()->isTeacher() || Auth::user()->isStaff()) && session('schoolSection') != 'primary') --}}
                                                    @if ((Auth::user()->isTeacher() || Auth::user()->isStaff()) && Auth::user()->hasSecondarySchoolAccess())
                                                        <a href="{{ url('viewtype/student') }}" @if (session('studentView') == 'true') class="active" @endif>Student view</a>
                                                        <a href="{{ url('viewtype/teacher') }}" @if (!session()->has('studentView')) class="active" @endif>
                                                            {{ Auth::user()->isTeacher() ? 'Teacher' : 'Staff' }} {{--  --}}view
                                                        </a>
                                                    @endif
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    @if (!isset($breadcrumb_change))
                                        <div class="row">
                                            <div class="col-12">@yield('breadcrumbs')</div>
                                        </div>
                                    @endif
                                @else
                                    <div class="pt-2 pb-1 inner row hidden-md-down">
                                        @if (isset($breadcrumb_change))
                                            <div class="col-lg-10 d-inline-flex p-t-5">
                                                <span role="button" onclick="breadcrumbAction(this)" class="toggle-breadcrumb" style="transform: rotate(45deg);">&times;</span>
                                                <div class="pl-3 breadcrumb-section" style="display: none;">
                                                    @yield('breadcrumbs')
                                                </div>
                                            </div>
                                        @endif
                                        <div class="@if (isset($breadcrumb_change)) col-lg-2 @else col-lg-12 @endif">
                                            <div class="dropdown pull-right">
                                                <button role="button" class="profile-dropdown-toggle" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                    <span class="inline thumbnail-wrapper d32 circular user-initials">
                                                        {{ Auth::user()->initials }}
                                                    </span>
                                                </button>
                                                <div class="dropdown-menu dropdown-menu-right profile-dropdown" role="menu">
                                                    <a href="{{ route('profile-edit') }}" class="dropdown-item">
                                                        <i class="fa fa-edit"></i>Edit Profile
                                                    </a>
                                                    <a href="{{ route('logout') }}" onclick="event.preventDefault(); document.getElementById('logout-form').submit();" class="clearfix bg-master-lighter dropdown-item">
                                                        <span class="pull-left">Logout</span>
                                                        <span class="pull-right">
                                                            <i class="pg-power"></i>
                                                        </span>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    @if (!isset($breadcrumb_change))
                                        <div class="row">
                                            <div class="col-12">@yield('breadcrumbs')</div>
                                        </div>
                                    @endif
                                @endif
                            @else
                                <div class="row">
                                    <div class="col-12">@yield('breadcrumbs')</div>
                                </div>
                            @endif
                            {{-- @endif --}}
                        </div>
                    </div>
                    <!--   END JUMBOTRON   -->
                    <!--   START CONTAINER FLUID   -->
                    <!-- <div class="container"> -->
                    <div class="container-fluid path-custom-container">
                        @yield('content')
                    </div>
                    <!-- </div> -->
                    <!--   END CONTAINER FLUID   -->
                </div>
                <!--   END PAGE CONTENT   -->
                <!--   START FOOTER   -->
                <div class="container-fluid footer">
                    <div class="copyright sm-text-center">
                        <p class="small no-margin pull-left sm-pull-reset">
                            <span class="hint-text">Copyright © </span>
                            <span class="font-montserrat"></span>.
                            <span class="hint-text mr-sm-3">All rights reserved.</span>
                            <span class="d-block d-sm-inline-block">
                                <a target="_blank" href="/terms">Terms of use</a>
                                <span class="mx-2">|</span>
                                <a target="_blank" href="/privacy">Privacy Policy</a>
                            </span>
                        </p>
                        <div class="clearfix"></div>
                    </div>
                </div>
                <!--   END FOOTER   -->
            </div>
            <!--   END PAGE CONTENT WRAPPER   -->
        </div>
        <!--   END PAGE CONTAINER   -->
        <!--   START OVERLAY   -->
    </div>
    <!--   App  Ends   -->
    {{-- @if (Auth::user()->isTeacher() || Auth::user()->isStaff())
        <div style="position: fixed; bottom: 25px; right: 25px;">
            <a href="https://help.thecareersdepartment.com/en/" target="_blank" class="d-block">
                <img src="{{ asset('images/support.png') }}" alt="logo" style="width: 70px;">
            </a>
        </div>
    @endif --}}
    @stack('modals')
    {{-- @if (config('app.env') != 'local') --}}
    @include('partials.howitworks')
    {{-- @endif --}}
    @if (!Auth::user()->isAdmin())
        @if (Auth::user()->profile->year_popup)
            @include('partials.yearrollover')
        @elseif(isset($planPopup) && $planPopup && !(isset($firstlogin) && $firstlogin))
            <!-- {{-- @if (isset($firstlogin) && !$firstlogin) --}} -->
            @include('partials.planpopup', ['gameplan' => $gameplan, 'questions' => $questions])

            {{-- @include('partials.planpopup', ['gameplan' => $gameplan ,'finishingSchoolText' => $finishingSchoolText , 'questions' => $questions]) --}}
        @elseif(isset($teacherLoggedInPopup) && $teacherLoggedInPopup && session('schoolSection') != 'primary')
            {{-- @include('partials.teacherLoggedInPopup') --}}
        @endif
        @if (Auth::user()->isStudent() || ((Auth::user()->isTeacher() || Auth::user()->isStaff()) && session('studentView')))
            @include('partials.subjectselectionpopup')
        @endif
    @endif
    <!-- {{-- <script src="{{ asset('js/app.js') }}"></script> --}}-->
    @include('partials.scripts.lms-globals')
    <script src="{{ asset('js/admin.js') }}" type="text/javascript"></script>
    <script src="{{ asset('js/auth.js') }}" type="text/javascript"></script>


    @stack('scriptslib')
    <!--   END VENDOR JS   -->

    @stack('scripts')
    {{-- @if (config('app.env') != 'local' && Auth::user()->showChatWidget()) <script type="text/javascript"> (function(w, d) { w.HelpCrunch = function() { w.HelpCrunch.q.push(arguments) }; w.HelpCrunch.q = []; function r() { var s = document.createElement('script'); s.async = 1; s.type = 'text/javascript'; s.src = 'https://widget.helpcrunch.com/'; (d.body || d.head).appendChild(s); } if (w.attachEvent) { w.attachEvent('onload', r) } else { w.addEventListener('load', r, false) } })(window, document) </script> <script type="text/javascript"> HelpCrunch('init', 'thecareersdepartment', { applicationId: 4332, applicationSecret: 'pgr0uLByub1ykrNeVsjvkdP0hpaDERyr23DtoWOtu50IJw+rI6IDLkrptK3VqB43xE30IBVMX4f7JahXF/+Urg==', user: { email: '{{ Auth::user()->email }}', name: '{{ Auth::user()->name }}', user_id: '{{ Auth::id() }}', } }); HelpCrunch('showChatWidget'); </script> @endif --}}
    <!-- Global site tag (gtag.js) - Google Analytics -->
    {{-- <script async src="https://www.googletagmanager.com/gtag/js?id=***********-4"></script> --}}
    <script>
        function breadcrumbAction(element) {
            if (jQuery(element).next().is(":visible")) {
                jQuery(element).css('transform', "rotate(45deg)")
            } else {
                jQuery(element).css('transform', "")
            }
            jQuery(".breadcrumb-section").slideToggle();
        }

        // window.dataLayer = window.dataLayer || [];

        // function gtag() {
        //     dataLayer.push(arguments);
        // }
        // gtag('js', new Date());
        // gtag('config', '***********-4');

        // jQuery(function() {
        //     var time = {{ config('session.lifetime') }} * 60000;
        //     setTimeout(function() {
        //         location.reload();
        //     }, time);
        //     jQuery(document).idleTimer(7200000); // 1hr
        //     // jQuery(document).idleTimer(10000); //  15sec
        //     jQuery(document).on("idle.idleTimer", function(event, elem, obj) {
        //         jQuery.ajax({
        //             url: "/checkLoginStatus",
        //             success: function(response) {
        //                 if (response == 'refresh') {
        //                     window.location.reload()
        //                 } else {
        //                     jQuery('#sessionexpire-form').submit();
        //                 }
        //             }
        //         });
        //     });
        // });
    </script>
    @if (Auth::check() && !Auth::user()->isAdmin())
        <script>
            jQuery(document).ready(function() {

                function saveSession() {
                    jQuery.ajax({
                        url: "/updateUserSession",
                        data: {
                            return_url: window.location.href
                        }
                    });
                }
                saveSession();

                setInterval(function() {
                    saveSession();
                }, 60000);
            });
        </script>
    @endif
    <script>
        jQuery('.sidebar-collapse').click(function() {
            jQuery(".content").animate({
                paddingLeft: '50px'
            }, 300);
            jQuery(".myNav").animate({
                width: '0'
            }, 500);
        });
        jQuery('.sidebar-expand').click(function() {
            jQuery(".myNav").animate({
                width: '250px'
            }, 500);
            setTimeout(
                function() {
                    jQuery(".content").animate({
                        paddingLeft: '250px'
                    }, 300);
                }, 200);
        });
    </script>
    @include('partials.scripts.intercom')
</body>

</html>
