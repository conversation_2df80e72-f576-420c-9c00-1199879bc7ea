/*! For license information please see 438.js.LICENSE.txt */
(self.webpackChunk=self.webpackChunk||[]).push([[438],{46702:(e,t)=>{var n,r,i;!function(o){if("undefined"!=typeof window){var s,a=0,c=!1,l=!1,u="message".length,d="[iFrameSizer]",m=d.length,h=null,p=window.requestAnimationFrame,f=Object.freeze({max:1,scroll:1,bodyScroll:1,documentElementScroll:1}),_={},g=null,y=Object.freeze({autoResize:!0,bodyBackground:null,bodyMargin:null,bodyMarginV1:8,bodyPadding:null,checkOrigin:!0,inPageLinks:!1,enablePublicMethods:!0,heightCalculationMethod:"bodyOffset",id:"iFrameResizer",interval:32,log:!1,maxHeight:1/0,maxWidth:1/0,minHeight:0,minWidth:0,mouseEvents:!0,resizeFrom:"parent",scrolling:!1,sizeHeight:!0,sizeWidth:!1,warningTimeout:5e3,tolerance:0,widthCalculationMethod:"scroll",onClose:function(){return!0},onClosed:function(){},onInit:function(){},onMessage:function(){I("onMessage function not defined")},onMouseEnter:function(){},onMouseLeave:function(){},onResized:function(){},onScroll:function(){return!0}}),b={};window.jQuery!==o&&((s=window.jQuery).fn?s.fn.iFrameResize||(s.fn.iFrameResize=function(e){return this.filter("iframe").each((function(t,n){F(n,e)})).end()}):x("","Unable to bind to jQuery, it is not fully loaded.")),r=[],(i="function"==typeof(n=Y)?n.apply(t,r):n)===o||(e.exports=i),window.iFrameResize=window.iFrameResize||Y()}function v(){return window.MutationObserver||window.WebKitMutationObserver||window.MozMutationObserver}function w(e,t,n){e.addEventListener(t,n,!1)}function E(e,t,n){e.removeEventListener(t,n,!1)}function C(e){return d+"["+function(e){var t="Host page: "+e;return window.top!==window.self&&(t=window.parentIFrame&&window.parentIFrame.getId?window.parentIFrame.getId()+": "+e:"Nested host page: "+e),t}(e)+"]"}function S(e){return _[e]?_[e].log:c}function M(e,t){O("log",e,t,S(e))}function x(e,t){O("info",e,t,S(e))}function I(e,t){O("warn",e,t,!0)}function O(e,t,n,r){!0===r&&"object"==typeof window.console&&console[e](C(t),n)}function L(e){function t(){i("Height"),i("Width"),D((function(){V(N),R(F),f("onResized",N)}),N,"init")}function n(e){return"border-box"!==e.boxSizing?0:(e.paddingTop?parseInt(e.paddingTop,10):0)+(e.paddingBottom?parseInt(e.paddingBottom,10):0)}function r(e){return"border-box"!==e.boxSizing?0:(e.borderTopWidth?parseInt(e.borderTopWidth,10):0)+(e.borderBottomWidth?parseInt(e.borderBottomWidth,10):0)}function i(e){var t=Number(_[F]["max"+e]),n=Number(_[F]["min"+e]),r=e.toLowerCase(),i=Number(N[r]);M(F,"Checking "+r+" is in range "+n+"-"+t),i<n&&(i=n,M(F,"Set "+r+" to min value")),i>t&&(i=t,M(F,"Set "+r+" to max value")),N[r]=""+i}function o(e){return L.slice(L.indexOf(":")+u+e)}function s(e,t){var n,r,i;n=function(){var n,r;z("Send Page Info","pageInfo:"+(n=document.body.getBoundingClientRect(),r=N.iframe.getBoundingClientRect(),JSON.stringify({iframeHeight:r.height,iframeWidth:r.width,clientHeight:Math.max(document.documentElement.clientHeight,window.innerHeight||0),clientWidth:Math.max(document.documentElement.clientWidth,window.innerWidth||0),offsetTop:parseInt(r.top-n.top,10),offsetLeft:parseInt(r.left-n.left,10),scrollTop:window.pageYOffset,scrollLeft:window.pageXOffset,documentHeight:document.documentElement.clientHeight,documentWidth:document.documentElement.clientWidth,windowHeight:window.innerHeight,windowWidth:window.innerWidth})),e,t)},r=32,b[i=t]||(b[i]=setTimeout((function(){b[i]=null,n()}),r))}function a(e){var t=e.getBoundingClientRect();return k(F),{x:Math.floor(Number(t.left)+Number(h.x)),y:Math.floor(Number(t.top)+Number(h.y))}}function c(e){var t=e?a(N.iframe):{x:0,y:0},n={x:Number(N.width)+t.x,y:Number(N.height)+t.y};M(F,"Reposition requested from iFrame (offset x:"+t.x+" y:"+t.y+")"),window.top===window.self?(h=n,l(),M(F,"--")):window.parentIFrame?window.parentIFrame["scrollTo"+(e?"Offset":"")](n.x,n.y):I(F,"Unable to scroll to requested position, window.parentIFrame not found")}function l(){!1===f("onScroll",h)?j():R(F)}function p(e){var t={};if(0===Number(N.width)&&0===Number(N.height)){var n=o(9).split(":");t={x:n[1],y:n[0]}}else t={x:N.width,y:N.height};f(e,{iframe:N.iframe,screenX:Number(t.x),screenY:Number(t.y),type:N.type})}function f(e,t){return T(F,e,t)}var g,y,v,C,S,O,L=e.data,N={},F=null;"[iFrameResizerChild]Ready"===L?function(){for(var e in _)z("iFrame requested init",B(e),_[e].iframe,e)}():d===(""+L).slice(0,m)&&L.slice(m).split(":")[0]in _?(v=L.slice(m).split(":"),C=v[1]?parseInt(v[1],10):0,S=_[v[0]]&&_[v[0]].iframe,O=getComputedStyle(S),N={iframe:S,id:v[0],height:C+n(O)+r(O),width:v[2],type:v[3]},F=N.id,_[F]&&(_[F].loaded=!0),(y=N.type in{true:1,false:1,undefined:1})&&M(F,"Ignoring init message from meta parent page"),!y&&function(e){var t=!0;return _[e]||(t=!1,I(N.type+" No settings for "+e+". Message was: "+L)),t}(F)&&(M(F,"Received: "+L),g=!0,null===N.iframe&&(I(F,"IFrame ("+N.id+") not found"),g=!1),g&&function(){var t,n=e.origin,r=_[F]&&_[F].checkOrigin;if(r&&""+n!="null"&&!(r.constructor===Array?function(){var e=0,t=!1;for(M(F,"Checking connection is from allowed list of origins: "+r);e<r.length;e++)if(r[e]===n){t=!0;break}return t}():(t=_[F]&&_[F].remoteHost,M(F,"Checking connection is from: "+t),n===t)))throw new Error("Unexpected message received from: "+n+" for "+N.iframe.id+". Message was: "+e.data+". This error can be disabled by setting the checkOrigin: false option or by providing of array of trusted domains.");return!0}()&&function(){switch(_[F]&&_[F].firstRun&&_[F]&&(_[F].firstRun=!1),N.type){case"close":A(N.iframe);break;case"message":e=o(6),M(F,"onMessage passed: {iframe: "+N.iframe.id+", message: "+e+"}"),f("onMessage",{iframe:N.iframe,message:JSON.parse(e)}),M(F,"--");break;case"mouseenter":p("onMouseEnter");break;case"mouseleave":p("onMouseLeave");break;case"autoResize":_[F].autoResize=JSON.parse(o(9));break;case"scrollTo":c(!1);break;case"scrollToOffset":c(!0);break;case"pageInfo":s(_[F]&&_[F].iframe,F),function(){function e(e,r){function i(){_[n]?s(_[n].iframe,n):t()}["scroll","resize"].forEach((function(t){M(n,e+t+" listener for sendPageInfo"),r(window,t,i)}))}function t(){e("Remove ",E)}var n=F;e("Add ",w),_[n]&&(_[n].stopPageInfo=t)}();break;case"pageInfoStop":_[F]&&_[F].stopPageInfo&&(_[F].stopPageInfo(),delete _[F].stopPageInfo);break;case"inPageLink":!function(e){var t,n=e.split("#")[1]||"",r=decodeURIComponent(n),i=document.getElementById(r)||document.getElementsByName(r)[0];i?(t=a(i),M(F,"Moving to in page link (#"+n+") at x: "+t.x+" y: "+t.y),h={x:t.x,y:t.y},l(),M(F,"--")):window.top===window.self?M(F,"In page link #"+n+" not found"):window.parentIFrame?window.parentIFrame.moveToAnchor(n):M(F,"In page link #"+n+" not found and window.parentIFrame not found")}(o(9));break;case"reset":P(N);break;case"init":t(),f("onInit",N.iframe);break;default:0===Number(N.width)&&0===Number(N.height)?I("Unsupported message received ("+N.type+"), this is likely due to the iframe containing a later version of iframe-resizer than the parent page"):t()}var e}())):x(F,"Ignored: "+L)}function T(e,t,n){var r=null,i=null;if(_[e]){if("function"!=typeof(r=_[e][t]))throw new TypeError(t+" on iFrame["+e+"] is not a function");i=r(n)}return i}function N(e){var t=e.id;delete _[t]}function A(e){var t=e.id;if(!1!==T(t,"onClose",t)){M(t,"Removing iFrame: "+t);try{e.parentNode&&e.parentNode.removeChild(e)}catch(e){I(e)}T(t,"onClosed",t),M(t,"--"),N(e)}else M(t,"Close iframe cancelled by onClose event")}function k(e){null===h&&M(e,"Get page position: "+(h={x:window.pageXOffset===o?document.documentElement.scrollLeft:window.pageXOffset,y:window.pageYOffset===o?document.documentElement.scrollTop:window.pageYOffset}).x+","+h.y)}function R(e){null!==h&&(window.scrollTo(h.x,h.y),M(e,"Set page position: "+h.x+","+h.y),j())}function j(){h=null}function P(e){M(e.id,"Size reset requested by "+("init"===e.type?"host page":"iFrame")),k(e.id),D((function(){V(e),z("reset","reset",e.iframe,e.id)}),e,"reset")}function V(e){function t(t){l||"0"!==e[t]||(l=!0,M(r,"Hidden iFrame detected, creating visibility listener"),function(){function e(){function e(e){function t(t){return"0px"===(_[e]&&_[e].iframe.style[t])}function n(e){return null!==e.offsetParent}_[e]&&n(_[e].iframe)&&(t("height")||t("width"))&&z("Visibility change","resize",_[e].iframe,e)}Object.keys(_).forEach((function(t){e(t)}))}function t(t){M("window","Mutation observed: "+t[0].target+" "+t[0].type),$(e,16)}function n(){var e=document.querySelector("body"),n={attributes:!0,attributeOldValue:!1,characterData:!0,characterDataOldValue:!1,childList:!0,subtree:!0};new r(t).observe(e,n)}var r=v();r&&n()}())}function n(n){!function(t){e.id?(e.iframe.style[t]=e[t]+"px",M(e.id,"IFrame ("+r+") "+t+" set to "+e[t]+"px")):M("undefined","messageData id not set")}(n),t(n)}var r=e.iframe.id;_[r]&&(_[r].sizeHeight&&n("height"),_[r].sizeWidth&&n("width"))}function D(e,t,n){n!==t.type&&p&&!window.jasmine?(M(t.id,"Requesting animation frame"),p(e)):e()}function z(e,t,n,r,i){var o,s=!1;r=r||n.id,_[r]&&(n&&"contentWindow"in n&&null!==n.contentWindow?(o=_[r]&&_[r].targetOrigin,M(r,"["+e+"] Sending msg to iframe["+r+"] ("+t+") targetOrigin: "+o),n.contentWindow.postMessage(d+t,o)):I(r,"["+e+"] IFrame("+r+") not found"),i&&_[r]&&_[r].warningTimeout&&(_[r].msgTimeout=setTimeout((function(){!_[r]||_[r].loaded||s||(s=!0,I(r,"IFrame has not responded within "+_[r].warningTimeout/1e3+" seconds. Check iFrameResizer.contentWindow.js has been loaded in iFrame. This message can be ignored if everything is working, or you can set the warningTimeout option to a higher value or zero to suppress this warning."))}),_[r].warningTimeout)))}function B(e){return e+":"+_[e].bodyMarginV1+":"+_[e].sizeWidth+":"+_[e].log+":"+_[e].interval+":"+_[e].enablePublicMethods+":"+_[e].autoResize+":"+_[e].bodyMargin+":"+_[e].heightCalculationMethod+":"+_[e].bodyBackground+":"+_[e].bodyPadding+":"+_[e].tolerance+":"+_[e].inPageLinks+":"+_[e].resizeFrom+":"+_[e].widthCalculationMethod+":"+_[e].mouseEvents}function F(e,t){function n(e){var t=e.split("Callback");if(2===t.length){var n="on"+t[0].charAt(0).toUpperCase()+t[0].slice(1);this[n]=this[e],delete this[e],I(r,"Deprecated: '"+e+"' has been renamed '"+n+"'. The old method will be removed in the next major version.")}}var r=function(n){if("string"!=typeof n)throw new TypeError("Invaild id for iFrame. Expected String");var r;return""===n&&(e.id=(r=t&&t.id||y.id+a++,null!==document.getElementById(r)&&(r+=a++),n=r),c=(t||{}).log,M(n,"Added missing iframe ID: "+n+" ("+e.src+")")),n}(e.id);r in _&&"iFrameResizer"in e?I(r,"Ignored iFrame, already setup."):(!function(t){var i;t=t||{},_[r]=Object.create(null),_[r].iframe=e,_[r].firstRun=!0,_[r].remoteHost=e.src&&e.src.split("/").slice(0,3).join("/"),function(e){if("object"!=typeof e)throw new TypeError("Options is not an object")}(t),Object.keys(t).forEach(n,t),function(e){for(var t in y)Object.prototype.hasOwnProperty.call(y,t)&&(_[r][t]=Object.prototype.hasOwnProperty.call(e,t)?e[t]:y[t])}(t),_[r]&&(_[r].targetOrigin=!0===_[r].checkOrigin?""===(i=_[r].remoteHost)||null!==i.match(/^(about:blank|javascript:|file:\/\/)/)?"*":i:"*")}(t),function(){switch(M(r,"IFrame scrolling "+(_[r]&&_[r].scrolling?"enabled":"disabled")+" for "+r),e.style.overflow=!1===(_[r]&&_[r].scrolling)?"hidden":"auto",_[r]&&_[r].scrolling){case"omit":break;case!0:e.scrolling="yes";break;case!1:e.scrolling="no";break;default:e.scrolling=_[r]?_[r].scrolling:"no"}}(),function(){function t(t){var n=_[r][t];1/0!==n&&0!==n&&(e.style[t]="number"==typeof n?n+"px":n,M(r,"Set "+t+" = "+e.style[t]))}function n(e){if(_[r]["min"+e]>_[r]["max"+e])throw new Error("Value for min"+e+" can not be greater than max"+e)}n("Height"),n("Width"),t("maxHeight"),t("minHeight"),t("maxWidth"),t("minWidth")}(),"number"!=typeof(_[r]&&_[r].bodyMargin)&&"0"!==(_[r]&&_[r].bodyMargin)||(_[r].bodyMarginV1=_[r].bodyMargin,_[r].bodyMargin=_[r].bodyMargin+"px"),function(t){var n=v();n&&function(t){e.parentNode&&new t((function(t){t.forEach((function(t){Array.prototype.slice.call(t.removedNodes).forEach((function(t){t===e&&A(e)}))}))})).observe(e.parentNode,{childList:!0})}(n),w(e,"load",(function(){var n,i;z("iFrame.onload",t,e,o,!0),n=_[r]&&_[r].firstRun,i=_[r]&&_[r].heightCalculationMethod in f,!n&&i&&P({iframe:e,height:0,width:0,type:"init"})})),z("init",t,e,o,!0)}(B(r)),_[r]&&(_[r].iframe.iFrameResizer={close:A.bind(null,_[r].iframe),removeListeners:N.bind(null,_[r].iframe),resize:z.bind(null,"Window resize","resize",_[r].iframe),moveToAnchor:function(e){z("Move to anchor","moveToAnchor:"+e,_[r].iframe,r)},sendMessage:function(e){z("Send Message","message:"+(e=JSON.stringify(e)),_[r].iframe,r)}}))}function $(e,t){null===g&&(g=setTimeout((function(){g=null,e()}),t))}function H(){"hidden"!==document.visibilityState&&(M("document","Trigger event: Visibility change"),$((function(){U("Tab Visible","resize")}),16))}function U(e,t){Object.keys(_).forEach((function(n){(function(e){return _[e]&&"parent"===_[e].resizeFrom&&_[e].autoResize&&!_[e].firstRun})(n)&&z(e,t,_[n].iframe,n)}))}function G(){w(window,"message",L),w(window,"resize",(function(){var e;M("window","Trigger event: "+(e="resize")),$((function(){U("Window "+e,"resize")}),16)})),w(document,"visibilitychange",H),w(document,"-webkit-visibilitychange",H)}function Y(){function e(e,n){n&&(!function(){if(!n.tagName)throw new TypeError("Object is not a valid DOM element");if("IFRAME"!==n.tagName.toUpperCase())throw new TypeError("Expected <IFRAME> tag, found <"+n.tagName+">")}(),F(n,e),t.push(n))}var t;return function(){var e,t=["moz","webkit","o","ms"];for(e=0;e<t.length&&!p;e+=1)p=window[t[e]+"RequestAnimationFrame"];p?p=p.bind(window):M("setup","RequestAnimationFrame not supported")}(),G(),function(n,r){switch(t=[],function(e){e&&e.enablePublicMethods&&I("enablePublicMethods option has been removed, public methods are now always available in the iFrame")}(n),typeof r){case"undefined":case"string":Array.prototype.forEach.call(document.querySelectorAll(r||"iframe"),e.bind(o,n));break;case"object":e(n,r);break;default:throw new TypeError("Unexpected data type ("+typeof r+")")}return t}}}()},86896:(e,t,n)=>{"use strict";n.d(t,{Z:()=>o});var r=n(1519),i=n.n(r)()((function(e){return e[1]}));i.push([e.id,".scorm-content[data-v-977ea95c]{position:relative;width:100%}.scorm-iframe[data-v-977ea95c]{height:calc(100vh - 110px);width:100%}.scorm-completed-section[data-v-977ea95c]{align-items:center;display:flex;justify-content:center;min-height:70vh}.scorm-completed-section-body[data-v-977ea95c]{border-radius:.475rem;max-width:580px}.scorm-completed-section-body-main[data-v-977ea95c]{padding:3rem;text-align:center}.scorm-completed-section-text[data-v-977ea95c]{margin-top:2rem}.scorm-completed-section-btn-group[data-v-977ea95c]{margin-top:3rem}.btn-grey[data-v-977ea95c]{background:#f8f6f6cf;color:#606060;font-weight:600;padding:1rem 3rem!important}.scorm-completed-section-header[data-v-977ea95c]{align-items:center;background:hsla(0,0%,85%,0);border-bottom:1px solid #f3f3f3;cursor:pointer;display:flex;justify-content:flex-end;padding:1rem}.scorm-completed-section-header .fa[data-v-977ea95c]{font-size:15px}",""]);const o=i},38732:(e,t,n)=>{"use strict";n.d(t,{Z:()=>o});var r=n(1519),i=n.n(r)()((function(e){return e[1]}));i.push([e.id,".custom-info-popup{background:#fbcecd;background:linear-gradient(180deg,#fbcecd,#fff 50%,#fff)}.custom-start-over-btn{background:#f8f6f6cf;color:#606060;font-weight:600}.custom-swal-info-icon{background:hsla(8,79%,78%,.34);box-shadow:0 0 0 10px hsla(8,79%,78%,.34)}.btn-global-grey{background:#f8f6f6cf!important;color:#606060!important;font-weight:600!important}",""]);const o=i},86228:(e,t,n)=>{"use strict";n.d(t,{Z:()=>o});var r=n(1519),i=n.n(r)()((function(e){return e[1]}));i.push([e.id,".fr-box{z-index:0}.wrap{max-width:55ch;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.btn-black{background:#000;border:1px solid!important;color:#fff}.btn-black:hover{background:#fff;color:#000}.section-content{margin:0}.section-content iframe{width:100%!important}.section-content iframe.wistia_embed{height:100%!important}.section-content img{max-width:100%}.section-content p iframe,.section-content p img{margin-bottom:-1rem}.section-content iframe,.section-content img{margin-left:-31px;margin-right:-30px;max-width:calc(100% + 61px)!important;width:calc(100% + 61px)!important}.btn-white{border:1px solid #000!important}.btn-white:hover,.btn.btn-white:hover:not(.btn-active){background-color:#000!important;color:#fff!important}::-webkit-scrollbar-thumb,::-webkit-scrollbar-thumb:hover{background:#000!important}.module-sections{margin-left:-30px;margin-right:-30px;overflow:auto hidden;position:relative}.sticky-top{min-width:calc(100% - 140px);position:fixed}.app-content{padding:0}.full-page{margin-left:-20px;margin-right:-20px}.banner{background-color:#000;background-position:50%;background-repeat:no-repeat;background-size:cover;display:block;min-height:calc(56.25vw - 149px);overflow:hidden;position:relative}.footer-buttons,.full-view-banner{margin-left:-30px;margin-right:-30px}.banner-video{height:100%}.banner-video>video{left:50%;position:absolute;top:50%;transform:translate(-50%,-50%);width:101%!important}.page-content{padding:0 15px;position:absolute;top:40%;width:100%}.module-section{border-bottom:1px solid;border-left:1px solid;border-top:1px solid;cursor:pointer;height:100px}.module-sections>.text-center:last-of-type>.module-section{border-right:1px solid}@media (max-width:1280px){.banner{height:56.25vw}.banner-video>video{height:100%!important;width:calc(65vw + 65vh)!important}}@media (min-width:992px){.sticky-top+.section-content{margin-top:100px}.module-sections{animation-duration:.2s;animation-fill-mode:forwards;animation-name:backtooriginal;z-index:100}.sticky-top{animation-duration:.2s;animation-fill-mode:forwards;animation-name:stick-top}@keyframes stick-top{0%{top:5px}to{top:0}}@keyframes backtooriginal{0%{top:-5px}to{top:0}}}@media (max-width:991px){.footer-buttons,.full-view-banner,.module-sections{margin-left:-20px;margin-right:-20px}.sticky-top{min-width:100%;top:119px}.full-view-banner{margin-top:58.16px}.module-section{height:100px}}@media (max-width:991px) and (min-width:768px) and (orientation:portrait){.banner{height:86.25vw}.banner-video>video{height:100%!important;width:calc(66vw + 66vh)!important}}@media (max-width:991px) and (orientation:landscape){.banner-video>video{height:auto!important;width:calc(70vw + 70vh)!important}}@media (max-width:767px){.banner{height:calc(100vh - 300px)}.full-view-banner{margin-top:0}.banner-video>video{height:100%!important;width:calc(90vw + 90vh)!important}.sticky-top{margin-top:10px}}",""]);const o=i},48542:function(e){e.exports=function(){"use strict";var e={awaitingPromise:new WeakMap,promise:new WeakMap,innerParams:new WeakMap,domCache:new WeakMap};const t=e=>{const t={};for(const n in e)t[e[n]]="swal2-"+e[n];return t},n=t(["container","shown","height-auto","iosfix","popup","modal","no-backdrop","no-transition","toast","toast-shown","show","hide","close","title","html-container","actions","confirm","deny","cancel","default-outline","footer","icon","icon-content","image","input","file","range","select","radio","checkbox","label","textarea","inputerror","input-label","validation-message","progress-steps","active-progress-step","progress-step","progress-step-line","loader","loading","styled","top","top-start","top-end","top-left","top-right","center","center-start","center-end","center-left","center-right","bottom","bottom-start","bottom-end","bottom-left","bottom-right","grow-row","grow-column","grow-fullscreen","rtl","timer-progress-bar","timer-progress-bar-container","scrollbar-measure","icon-success","icon-warning","icon-info","icon-question","icon-error"]),r=t(["success","warning","info","question","error"]),i="SweetAlert2:",o=e=>e.charAt(0).toUpperCase()+e.slice(1),s=e=>{console.warn(`${i} ${"object"==typeof e?e.join(" "):e}`)},a=e=>{console.error(`${i} ${e}`)},c=[],l=(e,t)=>{var n;n=`"${e}" is deprecated and will be removed in the next major release. Please use "${t}" instead.`,c.includes(n)||(c.push(n),s(n))},u=e=>"function"==typeof e?e():e,d=e=>e&&"function"==typeof e.toPromise,m=e=>d(e)?e.toPromise():Promise.resolve(e),h=e=>e&&Promise.resolve(e)===e,p=()=>document.body.querySelector(`.${n.container}`),f=e=>{const t=p();return t?t.querySelector(e):null},_=e=>f(`.${e}`),g=()=>_(n.popup),y=()=>_(n.icon),b=()=>_(n.title),v=()=>_(n["html-container"]),w=()=>_(n.image),E=()=>_(n["progress-steps"]),C=()=>_(n["validation-message"]),S=()=>f(`.${n.actions} .${n.confirm}`),M=()=>f(`.${n.actions} .${n.cancel}`),x=()=>f(`.${n.actions} .${n.deny}`),I=()=>f(`.${n.loader}`),O=()=>_(n.actions),L=()=>_(n.footer),T=()=>_(n["timer-progress-bar"]),N=()=>_(n.close),A=()=>{const e=Array.from(g().querySelectorAll('[tabindex]:not([tabindex="-1"]):not([tabindex="0"])')).sort(((e,t)=>{const n=parseInt(e.getAttribute("tabindex")),r=parseInt(t.getAttribute("tabindex"));return n>r?1:n<r?-1:0})),t=Array.from(g().querySelectorAll('\n  a[href],\n  area[href],\n  input:not([disabled]),\n  select:not([disabled]),\n  textarea:not([disabled]),\n  button:not([disabled]),\n  iframe,\n  object,\n  embed,\n  [tabindex="0"],\n  [contenteditable],\n  audio[controls],\n  video[controls],\n  summary\n')).filter((e=>"-1"!==e.getAttribute("tabindex")));return(e=>{const t=[];for(let n=0;n<e.length;n++)-1===t.indexOf(e[n])&&t.push(e[n]);return t})(e.concat(t)).filter((e=>J(e)))},k=()=>V(document.body,n.shown)&&!V(document.body,n["toast-shown"])&&!V(document.body,n["no-backdrop"]),R=()=>g()&&V(g(),n.toast),j={previousBodyPadding:null},P=(e,t)=>{if(e.textContent="",t){const n=(new DOMParser).parseFromString(t,"text/html");Array.from(n.querySelector("head").childNodes).forEach((t=>{e.appendChild(t)})),Array.from(n.querySelector("body").childNodes).forEach((t=>{t instanceof HTMLVideoElement||t instanceof HTMLAudioElement?e.appendChild(t.cloneNode(!0)):e.appendChild(t)}))}},V=(e,t)=>{if(!t)return!1;const n=t.split(/\s+/);for(let t=0;t<n.length;t++)if(!e.classList.contains(n[t]))return!1;return!0},D=(e,t,i)=>{if(((e,t)=>{Array.from(e.classList).forEach((i=>{Object.values(n).includes(i)||Object.values(r).includes(i)||Object.values(t.showClass).includes(i)||e.classList.remove(i)}))})(e,t),t.customClass&&t.customClass[i]){if("string"!=typeof t.customClass[i]&&!t.customClass[i].forEach)return void s(`Invalid type of customClass.${i}! Expected string or iterable object, got "${typeof t.customClass[i]}"`);$(e,t.customClass[i])}},z=(e,t)=>{if(!t)return null;switch(t){case"select":case"textarea":case"file":return e.querySelector(`.${n.popup} > .${n[t]}`);case"checkbox":return e.querySelector(`.${n.popup} > .${n.checkbox} input`);case"radio":return e.querySelector(`.${n.popup} > .${n.radio} input:checked`)||e.querySelector(`.${n.popup} > .${n.radio} input:first-child`);case"range":return e.querySelector(`.${n.popup} > .${n.range} input`);default:return e.querySelector(`.${n.popup} > .${n.input}`)}},B=e=>{if(e.focus(),"file"!==e.type){const t=e.value;e.value="",e.value=t}},F=(e,t,n)=>{e&&t&&("string"==typeof t&&(t=t.split(/\s+/).filter(Boolean)),t.forEach((t=>{Array.isArray(e)?e.forEach((e=>{n?e.classList.add(t):e.classList.remove(t)})):n?e.classList.add(t):e.classList.remove(t)})))},$=(e,t)=>{F(e,t,!0)},H=(e,t)=>{F(e,t,!1)},U=(e,t)=>{const n=Array.from(e.children);for(let e=0;e<n.length;e++){const r=n[e];if(r instanceof HTMLElement&&V(r,t))return r}},G=(e,t,n)=>{n===`${parseInt(n)}`&&(n=parseInt(n)),n||0===parseInt(n)?e.style[t]="number"==typeof n?`${n}px`:n:e.style.removeProperty(t)},Y=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"flex";e.style.display=t},q=e=>{e.style.display="none"},W=(e,t,n,r)=>{const i=e.querySelector(t);i&&(i.style[n]=r)},Z=function(e,t){t?Y(e,arguments.length>2&&void 0!==arguments[2]?arguments[2]:"flex"):q(e)},J=e=>!(!e||!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)),K=e=>!!(e.scrollHeight>e.clientHeight),X=e=>{const t=window.getComputedStyle(e),n=parseFloat(t.getPropertyValue("animation-duration")||"0"),r=parseFloat(t.getPropertyValue("transition-duration")||"0");return n>0||r>0},Q=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const n=T();J(n)&&(t&&(n.style.transition="none",n.style.width="100%"),setTimeout((()=>{n.style.transition=`width ${e/1e3}s linear`,n.style.width="0%"}),10))},ee={},te=e=>new Promise((t=>{if(!e)return t();const n=window.scrollX,r=window.scrollY;ee.restoreFocusTimeout=setTimeout((()=>{ee.previousActiveElement instanceof HTMLElement?(ee.previousActiveElement.focus(),ee.previousActiveElement=null):document.body&&document.body.focus(),t()}),100),window.scrollTo(n,r)})),ne=()=>"undefined"==typeof window||"undefined"==typeof document,re=`\n <div aria-labelledby="${n.title}" aria-describedby="${n["html-container"]}" class="${n.popup}" tabindex="-1">\n   <button type="button" class="${n.close}"></button>\n   <ul class="${n["progress-steps"]}"></ul>\n   <div class="${n.icon}"></div>\n   <img class="${n.image}" />\n   <h2 class="${n.title}" id="${n.title}"></h2>\n   <div class="${n["html-container"]}" id="${n["html-container"]}"></div>\n   <input class="${n.input}" />\n   <input type="file" class="${n.file}" />\n   <div class="${n.range}">\n     <input type="range" />\n     <output></output>\n   </div>\n   <select class="${n.select}"></select>\n   <div class="${n.radio}"></div>\n   <label for="${n.checkbox}" class="${n.checkbox}">\n     <input type="checkbox" />\n     <span class="${n.label}"></span>\n   </label>\n   <textarea class="${n.textarea}"></textarea>\n   <div class="${n["validation-message"]}" id="${n["validation-message"]}"></div>\n   <div class="${n.actions}">\n     <div class="${n.loader}"></div>\n     <button type="button" class="${n.confirm}"></button>\n     <button type="button" class="${n.deny}"></button>\n     <button type="button" class="${n.cancel}"></button>\n   </div>\n   <div class="${n.footer}"></div>\n   <div class="${n["timer-progress-bar-container"]}">\n     <div class="${n["timer-progress-bar"]}"></div>\n   </div>\n </div>\n`.replace(/(^|\n)\s*/g,""),ie=()=>{ee.currentInstance.resetValidationMessage()},oe=e=>{const t=(()=>{const e=p();return!!e&&(e.remove(),H([document.documentElement,document.body],[n["no-backdrop"],n["toast-shown"],n["has-column"]]),!0)})();if(ne())return void a("SweetAlert2 requires document to initialize");const r=document.createElement("div");r.className=n.container,t&&$(r,n["no-transition"]),P(r,re);const i="string"==typeof(o=e.target)?document.querySelector(o):o;var o;i.appendChild(r),(e=>{const t=g();t.setAttribute("role",e.toast?"alert":"dialog"),t.setAttribute("aria-live",e.toast?"polite":"assertive"),e.toast||t.setAttribute("aria-modal","true")})(e),(e=>{"rtl"===window.getComputedStyle(e).direction&&$(p(),n.rtl)})(i),(()=>{const e=g(),t=U(e,n.input),r=U(e,n.file),i=e.querySelector(`.${n.range} input`),o=e.querySelector(`.${n.range} output`),s=U(e,n.select),a=e.querySelector(`.${n.checkbox} input`),c=U(e,n.textarea);t.oninput=ie,r.onchange=ie,s.onchange=ie,a.onchange=ie,c.oninput=ie,i.oninput=()=>{ie(),o.value=i.value},i.onchange=()=>{ie(),o.value=i.value}})()},se=(e,t)=>{e instanceof HTMLElement?t.appendChild(e):"object"==typeof e?ae(e,t):e&&P(t,e)},ae=(e,t)=>{e.jquery?ce(t,e):P(t,e.toString())},ce=(e,t)=>{if(e.textContent="",0 in t)for(let n=0;n in t;n++)e.appendChild(t[n].cloneNode(!0));else e.appendChild(t.cloneNode(!0))},le=(()=>{if(ne())return!1;const e=document.createElement("div"),t={WebkitAnimation:"webkitAnimationEnd",animation:"animationend"};for(const n in t)if(Object.prototype.hasOwnProperty.call(t,n)&&void 0!==e.style[n])return t[n];return!1})(),ue=(e,t)=>{const r=O(),i=I();t.showConfirmButton||t.showDenyButton||t.showCancelButton?Y(r):q(r),D(r,t,"actions"),function(e,t,r){const i=S(),o=x(),s=M();de(i,"confirm",r),de(o,"deny",r),de(s,"cancel",r),function(e,t,r,i){i.buttonsStyling?($([e,t,r],n.styled),i.confirmButtonColor&&(e.style.backgroundColor=i.confirmButtonColor,$(e,n["default-outline"])),i.denyButtonColor&&(t.style.backgroundColor=i.denyButtonColor,$(t,n["default-outline"])),i.cancelButtonColor&&(r.style.backgroundColor=i.cancelButtonColor,$(r,n["default-outline"]))):H([e,t,r],n.styled)}(i,o,s,r),r.reverseButtons&&(r.toast?(e.insertBefore(s,i),e.insertBefore(o,i)):(e.insertBefore(s,t),e.insertBefore(o,t),e.insertBefore(i,t)))}(r,i,t),P(i,t.loaderHtml),D(i,t,"loader")};function de(e,t,r){Z(e,r[`show${o(t)}Button`],"inline-block"),P(e,r[`${t}ButtonText`]),e.setAttribute("aria-label",r[`${t}ButtonAriaLabel`]),e.className=n[t],D(e,r,`${t}Button`),$(e,r[`${t}ButtonClass`])}const me=(e,t)=>{const r=p();r&&(function(e,t){"string"==typeof t?e.style.background=t:t||$([document.documentElement,document.body],n["no-backdrop"])}(r,t.backdrop),function(e,t){t in n?$(e,n[t]):(s('The "position" parameter is not valid, defaulting to "center"'),$(e,n.center))}(r,t.position),function(e,t){if(t&&"string"==typeof t){const r=`grow-${t}`;r in n&&$(e,n[r])}}(r,t.grow),D(r,t,"container"))},he=["input","file","range","select","radio","checkbox","textarea"],pe=e=>{if(!we[e.input])return void a(`Unexpected type of input! Expected "text", "email", "password", "number", "tel", "select", "radio", "checkbox", "textarea", "file" or "url", got "${e.input}"`);const t=be(e.input),n=we[e.input](t,e);Y(t),e.inputAutoFocus&&setTimeout((()=>{B(n)}))},fe=(e,t)=>{const n=z(g(),e);if(n){(e=>{for(let t=0;t<e.attributes.length;t++){const n=e.attributes[t].name;["type","value","style"].includes(n)||e.removeAttribute(n)}})(n);for(const e in t)n.setAttribute(e,t[e])}},_e=e=>{const t=be(e.input);"object"==typeof e.customClass&&$(t,e.customClass.input)},ge=(e,t)=>{e.placeholder&&!t.inputPlaceholder||(e.placeholder=t.inputPlaceholder)},ye=(e,t,r)=>{if(r.inputLabel){e.id=n.input;const i=document.createElement("label"),o=n["input-label"];i.setAttribute("for",e.id),i.className=o,"object"==typeof r.customClass&&$(i,r.customClass.inputLabel),i.innerText=r.inputLabel,t.insertAdjacentElement("beforebegin",i)}},be=e=>U(g(),n[e]||n.input),ve=(e,t)=>{["string","number"].includes(typeof t)?e.value=`${t}`:h(t)||s(`Unexpected type of inputValue! Expected "string", "number" or "Promise", got "${typeof t}"`)},we={};we.text=we.email=we.password=we.number=we.tel=we.url=(e,t)=>(ve(e,t.inputValue),ye(e,e,t),ge(e,t),e.type=t.input,e),we.file=(e,t)=>(ye(e,e,t),ge(e,t),e),we.range=(e,t)=>{const n=e.querySelector("input"),r=e.querySelector("output");return ve(n,t.inputValue),n.type=t.input,ve(r,t.inputValue),ye(n,e,t),e},we.select=(e,t)=>{if(e.textContent="",t.inputPlaceholder){const n=document.createElement("option");P(n,t.inputPlaceholder),n.value="",n.disabled=!0,n.selected=!0,e.appendChild(n)}return ye(e,e,t),e},we.radio=e=>(e.textContent="",e),we.checkbox=(e,t)=>{const r=z(g(),"checkbox");r.value="1",r.id=n.checkbox,r.checked=Boolean(t.inputValue);const i=e.querySelector("span");return P(i,t.inputPlaceholder),r},we.textarea=(e,t)=>(ve(e,t.inputValue),ge(e,t),ye(e,e,t),setTimeout((()=>{if("MutationObserver"in window){const t=parseInt(window.getComputedStyle(g()).width);new MutationObserver((()=>{const n=e.offsetWidth+(r=e,parseInt(window.getComputedStyle(r).marginLeft)+parseInt(window.getComputedStyle(r).marginRight));var r;g().style.width=n>t?`${n}px`:null})).observe(e,{attributes:!0,attributeFilter:["style"]})}})),e);const Ee=(t,r)=>{const i=v();D(i,r,"htmlContainer"),r.html?(se(r.html,i),Y(i,"block")):r.text?(i.textContent=r.text,Y(i,"block")):q(i),((t,r)=>{const i=g(),o=e.innerParams.get(t),s=!o||r.input!==o.input;he.forEach((e=>{const t=U(i,n[e]);fe(e,r.inputAttributes),t.className=n[e],s&&q(t)})),r.input&&(s&&pe(r),_e(r))})(t,r)},Ce=(e,t)=>{for(const n in r)t.icon!==n&&H(e,r[n]);$(e,r[t.icon]),xe(e,t),Se(),D(e,t,"icon")},Se=()=>{const e=g(),t=window.getComputedStyle(e).getPropertyValue("background-color"),n=e.querySelectorAll("[class^=swal2-success-circular-line], .swal2-success-fix");for(let e=0;e<n.length;e++)n[e].style.backgroundColor=t},Me=(e,t)=>{let n,r=e.innerHTML;t.iconHtml?n=Ie(t.iconHtml):"success"===t.icon?(n='\n  <div class="swal2-success-circular-line-left"></div>\n  <span class="swal2-success-line-tip"></span> <span class="swal2-success-line-long"></span>\n  <div class="swal2-success-ring"></div> <div class="swal2-success-fix"></div>\n  <div class="swal2-success-circular-line-right"></div>\n',r=r.replace(/ style=".*?"/g,"")):n="error"===t.icon?'\n  <span class="swal2-x-mark">\n    <span class="swal2-x-mark-line-left"></span>\n    <span class="swal2-x-mark-line-right"></span>\n  </span>\n':Ie({question:"?",warning:"!",info:"i"}[t.icon]),r.trim()!==n.trim()&&P(e,n)},xe=(e,t)=>{if(t.iconColor){e.style.color=t.iconColor,e.style.borderColor=t.iconColor;for(const n of[".swal2-success-line-tip",".swal2-success-line-long",".swal2-x-mark-line-left",".swal2-x-mark-line-right"])W(e,n,"backgroundColor",t.iconColor);W(e,".swal2-success-ring","borderColor",t.iconColor)}},Ie=e=>`<div class="${n["icon-content"]}">${e}</div>`,Oe=(e,t)=>{e.className=`${n.popup} ${J(e)?t.showClass.popup:""}`,t.toast?($([document.documentElement,document.body],n["toast-shown"]),$(e,n.toast)):$(e,n.modal),D(e,t,"popup"),"string"==typeof t.customClass&&$(e,t.customClass),t.icon&&$(e,n[`icon-${t.icon}`])},Le=e=>{const t=document.createElement("li");return $(t,n["progress-step"]),P(t,e),t},Te=e=>{const t=document.createElement("li");return $(t,n["progress-step-line"]),e.progressStepsDistance&&G(t,"width",e.progressStepsDistance),t},Ne=(t,i)=>{((e,t)=>{const n=p(),r=g();t.toast?(G(n,"width",t.width),r.style.width="100%",r.insertBefore(I(),y())):G(r,"width",t.width),G(r,"padding",t.padding),t.color&&(r.style.color=t.color),t.background&&(r.style.background=t.background),q(C()),Oe(r,t)})(0,i),me(0,i),((e,t)=>{const r=E();t.progressSteps&&0!==t.progressSteps.length?(Y(r),r.textContent="",t.currentProgressStep>=t.progressSteps.length&&s("Invalid currentProgressStep parameter, it should be less than progressSteps.length (currentProgressStep like JS arrays starts from 0)"),t.progressSteps.forEach(((e,i)=>{const o=Le(e);if(r.appendChild(o),i===t.currentProgressStep&&$(o,n["active-progress-step"]),i!==t.progressSteps.length-1){const e=Te(t);r.appendChild(e)}}))):q(r)})(0,i),((t,n)=>{const i=e.innerParams.get(t),o=y();if(i&&n.icon===i.icon)return Me(o,n),void Ce(o,n);if(n.icon||n.iconHtml){if(n.icon&&-1===Object.keys(r).indexOf(n.icon))return a(`Unknown icon! Expected "success", "error", "warning", "info" or "question", got "${n.icon}"`),void q(o);Y(o),Me(o,n),Ce(o,n),$(o,n.showClass.icon)}else q(o)})(t,i),((e,t)=>{const r=w();t.imageUrl?(Y(r,""),r.setAttribute("src",t.imageUrl),r.setAttribute("alt",t.imageAlt),G(r,"width",t.imageWidth),G(r,"height",t.imageHeight),r.className=n.image,D(r,t,"image")):q(r)})(0,i),((e,t)=>{const n=b();Z(n,t.title||t.titleText,"block"),t.title&&se(t.title,n),t.titleText&&(n.innerText=t.titleText),D(n,t,"title")})(0,i),((e,t)=>{const n=N();P(n,t.closeButtonHtml),D(n,t,"closeButton"),Z(n,t.showCloseButton),n.setAttribute("aria-label",t.closeButtonAriaLabel)})(0,i),Ee(t,i),ue(0,i),((e,t)=>{const n=L();Z(n,t.footer),t.footer&&se(t.footer,n),D(n,t,"footer")})(0,i),"function"==typeof i.didRender&&i.didRender(g())};function Ae(){const t=e.innerParams.get(this);if(!t)return;const r=e.domCache.get(this);q(r.loader),R()?t.icon&&Y(y()):ke(r),H([r.popup,r.actions],n.loading),r.popup.removeAttribute("aria-busy"),r.popup.removeAttribute("data-loading"),r.confirmButton.disabled=!1,r.denyButton.disabled=!1,r.cancelButton.disabled=!1}const ke=e=>{const t=e.popup.getElementsByClassName(e.loader.getAttribute("data-button-to-replace"));t.length?Y(t[0],"inline-block"):J(S())||J(x())||J(M())||q(e.actions)},Re=()=>S()&&S().click(),je=Object.freeze({cancel:"cancel",backdrop:"backdrop",close:"close",esc:"esc",timer:"timer"}),Pe=e=>{e.keydownTarget&&e.keydownHandlerAdded&&(e.keydownTarget.removeEventListener("keydown",e.keydownHandler,{capture:e.keydownListenerCapture}),e.keydownHandlerAdded=!1)},Ve=(e,t)=>{const n=A();if(n.length)return(e+=t)===n.length?e=0:-1===e&&(e=n.length-1),void n[e].focus();g().focus()},De=["ArrowRight","ArrowDown"],ze=["ArrowLeft","ArrowUp"],Be=(t,n,r)=>{const i=e.innerParams.get(t);i&&(n.isComposing||229===n.keyCode||(i.stopKeydownPropagation&&n.stopPropagation(),"Enter"===n.key?Fe(t,n,i):"Tab"===n.key?$e(n):[...De,...ze].includes(n.key)?He(n.key):"Escape"===n.key&&Ue(n,i,r)))},Fe=(e,t,n)=>{if(u(n.allowEnterKey)&&t.target&&e.getInput()&&t.target instanceof HTMLElement&&t.target.outerHTML===e.getInput().outerHTML){if(["textarea","file"].includes(n.input))return;Re(),t.preventDefault()}},$e=e=>{const t=e.target,n=A();let r=-1;for(let e=0;e<n.length;e++)if(t===n[e]){r=e;break}e.shiftKey?Ve(r,-1):Ve(r,1),e.stopPropagation(),e.preventDefault()},He=e=>{const t=[S(),x(),M()];if(document.activeElement instanceof HTMLElement&&!t.includes(document.activeElement))return;const n=De.includes(e)?"nextElementSibling":"previousElementSibling";let r=document.activeElement;for(let e=0;e<O().children.length;e++){if(r=r[n],!r)return;if(r instanceof HTMLButtonElement&&J(r))break}r instanceof HTMLButtonElement&&r.focus()},Ue=(e,t,n)=>{u(t.allowEscapeKey)&&(e.preventDefault(),n(je.esc))};var Ge={swalPromiseResolve:new WeakMap,swalPromiseReject:new WeakMap};const Ye=()=>{Array.from(document.body.children).forEach((e=>{e.hasAttribute("data-previous-aria-hidden")?(e.setAttribute("aria-hidden",e.getAttribute("data-previous-aria-hidden")),e.removeAttribute("data-previous-aria-hidden")):e.removeAttribute("aria-hidden")}))},qe=()=>{const e=navigator.userAgent,t=!!e.match(/iPad/i)||!!e.match(/iPhone/i),n=!!e.match(/WebKit/i);if(t&&n&&!e.match(/CriOS/i)){const e=44;g().scrollHeight>window.innerHeight-e&&(p().style.paddingBottom=`${e}px`)}},We=()=>{const e=p();let t;e.ontouchstart=e=>{t=Ze(e)},e.ontouchmove=e=>{t&&(e.preventDefault(),e.stopPropagation())}},Ze=e=>{const t=e.target,n=p();return!(Je(e)||Ke(e)||t!==n&&(K(n)||!(t instanceof HTMLElement)||"INPUT"===t.tagName||"TEXTAREA"===t.tagName||K(v())&&v().contains(t)))},Je=e=>e.touches&&e.touches.length&&"stylus"===e.touches[0].touchType,Ke=e=>e.touches&&e.touches.length>1,Xe=()=>{if(V(document.body,n.iosfix)){const e=parseInt(document.body.style.top,10);H(document.body,n.iosfix),document.body.style.top="",document.body.scrollTop=-1*e}},Qe=()=>{null===j.previousBodyPadding&&document.body.scrollHeight>window.innerHeight&&(j.previousBodyPadding=parseInt(window.getComputedStyle(document.body).getPropertyValue("padding-right")),document.body.style.paddingRight=`${j.previousBodyPadding+(()=>{const e=document.createElement("div");e.className=n["scrollbar-measure"],document.body.appendChild(e);const t=e.getBoundingClientRect().width-e.clientWidth;return document.body.removeChild(e),t})()}px`)},et=()=>{null!==j.previousBodyPadding&&(document.body.style.paddingRight=`${j.previousBodyPadding}px`,j.previousBodyPadding=null)};function tt(e,t,r,i){R()?ct(e,i):(te(r).then((()=>ct(e,i))),Pe(ee)),/^((?!chrome|android).)*safari/i.test(navigator.userAgent)?(t.setAttribute("style","display:none !important"),t.removeAttribute("class"),t.innerHTML=""):t.remove(),k()&&(et(),Xe(),Ye()),H([document.documentElement,document.body],[n.shown,n["height-auto"],n["no-backdrop"],n["toast-shown"]])}function nt(e){e=ot(e);const t=Ge.swalPromiseResolve.get(this),n=rt(this);this.isAwaitingPromise()?e.isDismissed||(it(this),t(e)):n&&t(e)}const rt=t=>{const n=g();if(!n)return!1;const r=e.innerParams.get(t);if(!r||V(n,r.hideClass.popup))return!1;H(n,r.showClass.popup),$(n,r.hideClass.popup);const i=p();return H(i,r.showClass.backdrop),$(i,r.hideClass.backdrop),st(t,n,r),!0},it=t=>{t.isAwaitingPromise()&&(e.awaitingPromise.delete(t),e.innerParams.get(t)||t._destroy())},ot=e=>void 0===e?{isConfirmed:!1,isDenied:!1,isDismissed:!0}:Object.assign({isConfirmed:!1,isDenied:!1,isDismissed:!1},e),st=(e,t,n)=>{const r=p(),i=le&&X(t);"function"==typeof n.willClose&&n.willClose(t),i?at(e,t,r,n.returnFocus,n.didClose):tt(e,r,n.returnFocus,n.didClose)},at=(e,t,n,r,i)=>{ee.swalCloseEventFinishedCallback=tt.bind(null,e,n,r,i),t.addEventListener(le,(function(e){e.target===t&&(ee.swalCloseEventFinishedCallback(),delete ee.swalCloseEventFinishedCallback)}))},ct=(e,t)=>{setTimeout((()=>{"function"==typeof t&&t.bind(e.params)(),e._destroy()}))};function lt(t,n,r){const i=e.domCache.get(t);n.forEach((e=>{i[e].disabled=r}))}function ut(e,t){if(e)if("radio"===e.type){const n=e.parentNode.parentNode.querySelectorAll("input");for(let e=0;e<n.length;e++)n[e].disabled=t}else e.disabled=t}const dt={title:"",titleText:"",text:"",html:"",footer:"",icon:void 0,iconColor:void 0,iconHtml:void 0,template:void 0,toast:!1,showClass:{popup:"swal2-show",backdrop:"swal2-backdrop-show",icon:"swal2-icon-show"},hideClass:{popup:"swal2-hide",backdrop:"swal2-backdrop-hide",icon:"swal2-icon-hide"},customClass:{},target:"body",color:void 0,backdrop:!0,heightAuto:!0,allowOutsideClick:!0,allowEscapeKey:!0,allowEnterKey:!0,stopKeydownPropagation:!0,keydownListenerCapture:!1,showConfirmButton:!0,showDenyButton:!1,showCancelButton:!1,preConfirm:void 0,preDeny:void 0,confirmButtonText:"OK",confirmButtonAriaLabel:"",confirmButtonColor:void 0,denyButtonText:"No",denyButtonAriaLabel:"",denyButtonColor:void 0,cancelButtonText:"Cancel",cancelButtonAriaLabel:"",cancelButtonColor:void 0,buttonsStyling:!0,reverseButtons:!1,focusConfirm:!0,focusDeny:!1,focusCancel:!1,returnFocus:!0,showCloseButton:!1,closeButtonHtml:"&times;",closeButtonAriaLabel:"Close this dialog",loaderHtml:"",showLoaderOnConfirm:!1,showLoaderOnDeny:!1,imageUrl:void 0,imageWidth:void 0,imageHeight:void 0,imageAlt:"",timer:void 0,timerProgressBar:!1,width:void 0,padding:void 0,background:void 0,input:void 0,inputPlaceholder:"",inputLabel:"",inputValue:"",inputOptions:{},inputAutoFocus:!0,inputAutoTrim:!0,inputAttributes:{},inputValidator:void 0,returnInputValueOnDeny:!1,validationMessage:void 0,grow:!1,position:"center",progressSteps:[],currentProgressStep:void 0,progressStepsDistance:void 0,willOpen:void 0,didOpen:void 0,didRender:void 0,willClose:void 0,didClose:void 0,didDestroy:void 0,scrollbarPadding:!0},mt=["allowEscapeKey","allowOutsideClick","background","buttonsStyling","cancelButtonAriaLabel","cancelButtonColor","cancelButtonText","closeButtonAriaLabel","closeButtonHtml","color","confirmButtonAriaLabel","confirmButtonColor","confirmButtonText","currentProgressStep","customClass","denyButtonAriaLabel","denyButtonColor","denyButtonText","didClose","didDestroy","footer","hideClass","html","icon","iconColor","iconHtml","imageAlt","imageHeight","imageUrl","imageWidth","preConfirm","preDeny","progressSteps","returnFocus","reverseButtons","showCancelButton","showCloseButton","showConfirmButton","showDenyButton","text","title","titleText","willClose"],ht={},pt=["allowOutsideClick","allowEnterKey","backdrop","focusConfirm","focusDeny","focusCancel","returnFocus","heightAuto","keydownListenerCapture"],ft=e=>Object.prototype.hasOwnProperty.call(dt,e),_t=e=>-1!==mt.indexOf(e),gt=e=>ht[e],yt=e=>{ft(e)||s(`Unknown parameter "${e}"`)},bt=e=>{pt.includes(e)&&s(`The parameter "${e}" is incompatible with toasts`)},vt=e=>{gt(e)&&l(e,gt(e))},wt=e=>{const t={};return Object.keys(e).forEach((n=>{_t(n)?t[n]=e[n]:s(`Invalid parameter to update: ${n}`)})),t},Et=e=>{Ct(e),delete e.params,delete ee.keydownHandler,delete ee.keydownTarget,delete ee.currentInstance},Ct=t=>{t.isAwaitingPromise()?(St(e,t),e.awaitingPromise.set(t,!0)):(St(Ge,t),St(e,t))},St=(e,t)=>{for(const n in e)e[n].delete(t)};var Mt=Object.freeze({__proto__:null,_destroy:function(){const t=e.domCache.get(this),n=e.innerParams.get(this);n?(t.popup&&ee.swalCloseEventFinishedCallback&&(ee.swalCloseEventFinishedCallback(),delete ee.swalCloseEventFinishedCallback),"function"==typeof n.didDestroy&&n.didDestroy(),Et(this)):Ct(this)},close:nt,closeModal:nt,closePopup:nt,closeToast:nt,disableButtons:function(){lt(this,["confirmButton","denyButton","cancelButton"],!0)},disableInput:function(){ut(this.getInput(),!0)},disableLoading:Ae,enableButtons:function(){lt(this,["confirmButton","denyButton","cancelButton"],!1)},enableInput:function(){ut(this.getInput(),!1)},getInput:function(t){const n=e.innerParams.get(t||this),r=e.domCache.get(t||this);return r?z(r.popup,n.input):null},handleAwaitingPromise:it,hideLoading:Ae,isAwaitingPromise:function(){return!!e.awaitingPromise.get(this)},rejectPromise:function(e){const t=Ge.swalPromiseReject.get(this);it(this),t&&t(e)},resetValidationMessage:function(){const t=e.domCache.get(this);t.validationMessage&&q(t.validationMessage);const r=this.getInput();r&&(r.removeAttribute("aria-invalid"),r.removeAttribute("aria-describedby"),H(r,n.inputerror))},showValidationMessage:function(t){const r=e.domCache.get(this),i=e.innerParams.get(this);P(r.validationMessage,t),r.validationMessage.className=n["validation-message"],i.customClass&&i.customClass.validationMessage&&$(r.validationMessage,i.customClass.validationMessage),Y(r.validationMessage);const o=this.getInput();o&&(o.setAttribute("aria-invalid",!0),o.setAttribute("aria-describedby",n["validation-message"]),B(o),$(o,n.inputerror))},update:function(t){const n=g(),r=e.innerParams.get(this);if(!n||V(n,r.hideClass.popup))return void s("You're trying to update the closed or closing popup, that won't work. Use the update() method in preConfirm parameter or show a new popup.");const i=wt(t),o=Object.assign({},r,i);Ne(this,o),e.innerParams.set(this,o),Object.defineProperties(this,{params:{value:Object.assign({},this.params,t),writable:!1,enumerable:!0}})}});const xt=e=>{let t=g();t||new Ln,t=g();const n=I();R()?q(y()):It(t,e),Y(n),t.setAttribute("data-loading","true"),t.setAttribute("aria-busy","true"),t.focus()},It=(e,t)=>{const r=O(),i=I();!t&&J(S())&&(t=S()),Y(r),t&&(q(t),i.setAttribute("data-button-to-replace",t.className)),i.parentNode.insertBefore(i,t),$([e,r],n.loading)},Ot=e=>e.checked?1:0,Lt=e=>e.checked?e.value:null,Tt=e=>e.files.length?null!==e.getAttribute("multiple")?e.files:e.files[0]:null,Nt=(e,t)=>{const n=g(),r=e=>{kt[t.input](n,Rt(e),t)};d(t.inputOptions)||h(t.inputOptions)?(xt(S()),m(t.inputOptions).then((t=>{e.hideLoading(),r(t)}))):"object"==typeof t.inputOptions?r(t.inputOptions):a("Unexpected type of inputOptions! Expected object, Map or Promise, got "+typeof t.inputOptions)},At=(e,t)=>{const n=e.getInput();q(n),m(t.inputValue).then((r=>{n.value="number"===t.input?`${parseFloat(r)||0}`:`${r}`,Y(n),n.focus(),e.hideLoading()})).catch((t=>{a(`Error in inputValue promise: ${t}`),n.value="",Y(n),n.focus(),e.hideLoading()}))},kt={select:(e,t,r)=>{const i=U(e,n.select),o=(e,t,n)=>{const i=document.createElement("option");i.value=n,P(i,t),i.selected=jt(n,r.inputValue),e.appendChild(i)};t.forEach((e=>{const t=e[0],n=e[1];if(Array.isArray(n)){const e=document.createElement("optgroup");e.label=t,e.disabled=!1,i.appendChild(e),n.forEach((t=>o(e,t[1],t[0])))}else o(i,n,t)})),i.focus()},radio:(e,t,r)=>{const i=U(e,n.radio);t.forEach((e=>{const t=e[0],o=e[1],s=document.createElement("input"),a=document.createElement("label");s.type="radio",s.name=n.radio,s.value=t,jt(t,r.inputValue)&&(s.checked=!0);const c=document.createElement("span");P(c,o),c.className=n.label,a.appendChild(s),a.appendChild(c),i.appendChild(a)}));const o=i.querySelectorAll("input");o.length&&o[0].focus()}},Rt=e=>{const t=[];return"undefined"!=typeof Map&&e instanceof Map?e.forEach(((e,n)=>{let r=e;"object"==typeof r&&(r=Rt(r)),t.push([n,r])})):Object.keys(e).forEach((n=>{let r=e[n];"object"==typeof r&&(r=Rt(r)),t.push([n,r])})),t},jt=(e,t)=>t&&t.toString()===e.toString(),Pt=(t,n)=>{const r=e.innerParams.get(t);if(!r.input)return void a(`The "input" parameter is needed to be set when using returnInputValueOn${o(n)}`);const i=((e,t)=>{const n=e.getInput();if(!n)return null;switch(t.input){case"checkbox":return Ot(n);case"radio":return Lt(n);case"file":return Tt(n);default:return t.inputAutoTrim?n.value.trim():n.value}})(t,r);r.inputValidator?Vt(t,i,n):t.getInput().checkValidity()?"deny"===n?Dt(t,i):Ft(t,i):(t.enableButtons(),t.showValidationMessage(r.validationMessage))},Vt=(t,n,r)=>{const i=e.innerParams.get(t);t.disableInput(),Promise.resolve().then((()=>m(i.inputValidator(n,i.validationMessage)))).then((e=>{t.enableButtons(),t.enableInput(),e?t.showValidationMessage(e):"deny"===r?Dt(t,n):Ft(t,n)}))},Dt=(t,n)=>{const r=e.innerParams.get(t||void 0);r.showLoaderOnDeny&&xt(x()),r.preDeny?(e.awaitingPromise.set(t||void 0,!0),Promise.resolve().then((()=>m(r.preDeny(n,r.validationMessage)))).then((e=>{!1===e?(t.hideLoading(),it(t)):t.close({isDenied:!0,value:void 0===e?n:e})})).catch((e=>Bt(t||void 0,e)))):t.close({isDenied:!0,value:n})},zt=(e,t)=>{e.close({isConfirmed:!0,value:t})},Bt=(e,t)=>{e.rejectPromise(t)},Ft=(t,n)=>{const r=e.innerParams.get(t||void 0);r.showLoaderOnConfirm&&xt(),r.preConfirm?(t.resetValidationMessage(),e.awaitingPromise.set(t||void 0,!0),Promise.resolve().then((()=>m(r.preConfirm(n,r.validationMessage)))).then((e=>{J(C())||!1===e?(t.hideLoading(),it(t)):zt(t,void 0===e?n:e)})).catch((e=>Bt(t||void 0,e)))):zt(t,n)},$t=(t,n,r)=>{n.popup.onclick=()=>{const n=e.innerParams.get(t);n&&(Ht(n)||n.timer||n.input)||r(je.close)}},Ht=e=>e.showConfirmButton||e.showDenyButton||e.showCancelButton||e.showCloseButton;let Ut=!1;const Gt=e=>{e.popup.onmousedown=()=>{e.container.onmouseup=function(t){e.container.onmouseup=void 0,t.target===e.container&&(Ut=!0)}}},Yt=e=>{e.container.onmousedown=()=>{e.popup.onmouseup=function(t){e.popup.onmouseup=void 0,(t.target===e.popup||e.popup.contains(t.target))&&(Ut=!0)}}},qt=(t,n,r)=>{n.container.onclick=i=>{const o=e.innerParams.get(t);Ut?Ut=!1:i.target===n.container&&u(o.allowOutsideClick)&&r(je.backdrop)}},Wt=e=>e instanceof Element||(e=>"object"==typeof e&&e.jquery)(e),Zt=()=>{if(ee.timeout)return(()=>{const e=T(),t=parseInt(window.getComputedStyle(e).width);e.style.removeProperty("transition"),e.style.width="100%";const n=t/parseInt(window.getComputedStyle(e).width)*100;e.style.width=`${n}%`})(),ee.timeout.stop()},Jt=()=>{if(ee.timeout){const e=ee.timeout.start();return Q(e),e}};let Kt=!1;const Xt={},Qt=e=>{for(let t=e.target;t&&t!==document;t=t.parentNode)for(const e in Xt){const n=t.getAttribute(e);if(n)return void Xt[e].fire({template:n})}};var en=Object.freeze({__proto__:null,argsToParams:e=>{const t={};return"object"!=typeof e[0]||Wt(e[0])?["title","html","icon"].forEach(((n,r)=>{const i=e[r];"string"==typeof i||Wt(i)?t[n]=i:void 0!==i&&a(`Unexpected type of ${n}! Expected "string" or "Element", got ${typeof i}`)})):Object.assign(t,e[0]),t},bindClickHandler:function(){Xt[arguments.length>0&&void 0!==arguments[0]?arguments[0]:"data-swal-template"]=this,Kt||(document.body.addEventListener("click",Qt),Kt=!0)},clickCancel:()=>M()&&M().click(),clickConfirm:Re,clickDeny:()=>x()&&x().click(),enableLoading:xt,fire:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return new this(...t)},getActions:O,getCancelButton:M,getCloseButton:N,getConfirmButton:S,getContainer:p,getDenyButton:x,getFocusableElements:A,getFooter:L,getHtmlContainer:v,getIcon:y,getIconContent:()=>_(n["icon-content"]),getImage:w,getInputLabel:()=>_(n["input-label"]),getLoader:I,getPopup:g,getProgressSteps:E,getTimerLeft:()=>ee.timeout&&ee.timeout.getTimerLeft(),getTimerProgressBar:T,getTitle:b,getValidationMessage:C,increaseTimer:e=>{if(ee.timeout){const t=ee.timeout.increase(e);return Q(t,!0),t}},isDeprecatedParameter:gt,isLoading:()=>g().hasAttribute("data-loading"),isTimerRunning:()=>ee.timeout&&ee.timeout.isRunning(),isUpdatableParameter:_t,isValidParameter:ft,isVisible:()=>J(g()),mixin:function(e){return class extends(this){_main(t,n){return super._main(t,Object.assign({},e,n))}}},resumeTimer:Jt,showLoading:xt,stopTimer:Zt,toggleTimer:()=>{const e=ee.timeout;return e&&(e.running?Zt():Jt())}});class tn{constructor(e,t){this.callback=e,this.remaining=t,this.running=!1,this.start()}start(){return this.running||(this.running=!0,this.started=new Date,this.id=setTimeout(this.callback,this.remaining)),this.remaining}stop(){return this.running&&(this.running=!1,clearTimeout(this.id),this.remaining-=(new Date).getTime()-this.started.getTime()),this.remaining}increase(e){const t=this.running;return t&&this.stop(),this.remaining+=e,t&&this.start(),this.remaining}getTimerLeft(){return this.running&&(this.stop(),this.start()),this.remaining}isRunning(){return this.running}}const nn=["swal-title","swal-html","swal-footer"],rn=e=>{const t={};return Array.from(e.querySelectorAll("swal-param")).forEach((e=>{mn(e,["name","value"]);const n=e.getAttribute("name"),r=e.getAttribute("value");t[n]="boolean"==typeof dt[n]?"false"!==r:"object"==typeof dt[n]?JSON.parse(r):r})),t},on=e=>{const t={};return Array.from(e.querySelectorAll("swal-function-param")).forEach((e=>{const n=e.getAttribute("name"),r=e.getAttribute("value");t[n]=new Function(`return ${r}`)()})),t},sn=e=>{const t={};return Array.from(e.querySelectorAll("swal-button")).forEach((e=>{mn(e,["type","color","aria-label"]);const n=e.getAttribute("type");t[`${n}ButtonText`]=e.innerHTML,t[`show${o(n)}Button`]=!0,e.hasAttribute("color")&&(t[`${n}ButtonColor`]=e.getAttribute("color")),e.hasAttribute("aria-label")&&(t[`${n}ButtonAriaLabel`]=e.getAttribute("aria-label"))})),t},an=e=>{const t={},n=e.querySelector("swal-image");return n&&(mn(n,["src","width","height","alt"]),n.hasAttribute("src")&&(t.imageUrl=n.getAttribute("src")),n.hasAttribute("width")&&(t.imageWidth=n.getAttribute("width")),n.hasAttribute("height")&&(t.imageHeight=n.getAttribute("height")),n.hasAttribute("alt")&&(t.imageAlt=n.getAttribute("alt"))),t},cn=e=>{const t={},n=e.querySelector("swal-icon");return n&&(mn(n,["type","color"]),n.hasAttribute("type")&&(t.icon=n.getAttribute("type")),n.hasAttribute("color")&&(t.iconColor=n.getAttribute("color")),t.iconHtml=n.innerHTML),t},ln=e=>{const t={},n=e.querySelector("swal-input");n&&(mn(n,["type","label","placeholder","value"]),t.input=n.getAttribute("type")||"text",n.hasAttribute("label")&&(t.inputLabel=n.getAttribute("label")),n.hasAttribute("placeholder")&&(t.inputPlaceholder=n.getAttribute("placeholder")),n.hasAttribute("value")&&(t.inputValue=n.getAttribute("value")));const r=Array.from(e.querySelectorAll("swal-input-option"));return r.length&&(t.inputOptions={},r.forEach((e=>{mn(e,["value"]);const n=e.getAttribute("value"),r=e.innerHTML;t.inputOptions[n]=r}))),t},un=(e,t)=>{const n={};for(const r in t){const i=t[r],o=e.querySelector(i);o&&(mn(o,[]),n[i.replace(/^swal-/,"")]=o.innerHTML.trim())}return n},dn=e=>{const t=nn.concat(["swal-param","swal-function-param","swal-button","swal-image","swal-icon","swal-input","swal-input-option"]);Array.from(e.children).forEach((e=>{const n=e.tagName.toLowerCase();t.includes(n)||s(`Unrecognized element <${n}>`)}))},mn=(e,t)=>{Array.from(e.attributes).forEach((n=>{-1===t.indexOf(n.name)&&s([`Unrecognized attribute "${n.name}" on <${e.tagName.toLowerCase()}>.`,t.length?`Allowed attributes are: ${t.join(", ")}`:"To set the value, use HTML within the element."])}))},hn=e=>{const t=p(),r=g();"function"==typeof e.willOpen&&e.willOpen(r);const i=window.getComputedStyle(document.body).overflowY;gn(t,r,e),setTimeout((()=>{fn(t,r)}),10),k()&&(_n(t,e.scrollbarPadding,i),Array.from(document.body.children).forEach((e=>{e===p()||e.contains(p())||(e.hasAttribute("aria-hidden")&&e.setAttribute("data-previous-aria-hidden",e.getAttribute("aria-hidden")),e.setAttribute("aria-hidden","true"))}))),R()||ee.previousActiveElement||(ee.previousActiveElement=document.activeElement),"function"==typeof e.didOpen&&setTimeout((()=>e.didOpen(r))),H(t,n["no-transition"])},pn=e=>{const t=g();if(e.target!==t)return;const n=p();t.removeEventListener(le,pn),n.style.overflowY="auto"},fn=(e,t)=>{le&&X(t)?(e.style.overflowY="hidden",t.addEventListener(le,pn)):e.style.overflowY="auto"},_n=(e,t,r)=>{(()=>{if((/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream||"MacIntel"===navigator.platform&&navigator.maxTouchPoints>1)&&!V(document.body,n.iosfix)){const e=document.body.scrollTop;document.body.style.top=-1*e+"px",$(document.body,n.iosfix),We(),qe()}})(),t&&"hidden"!==r&&Qe(),setTimeout((()=>{e.scrollTop=0}))},gn=(e,t,r)=>{$(e,r.showClass.backdrop),t.style.setProperty("opacity","0","important"),Y(t,"grid"),setTimeout((()=>{$(t,r.showClass.popup),t.style.removeProperty("opacity")}),10),$([document.documentElement,document.body],n.shown),r.heightAuto&&r.backdrop&&!r.toast&&$([document.documentElement,document.body],n["height-auto"])};var yn={email:(e,t)=>/^[a-zA-Z0-9.+_-]+@[a-zA-Z0-9.-]+\.[a-zA-Z0-9-]{2,24}$/.test(e)?Promise.resolve():Promise.resolve(t||"Invalid email address"),url:(e,t)=>/^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-z]{2,63}\b([-a-zA-Z0-9@:%_+.~#?&/=]*)$/.test(e)?Promise.resolve():Promise.resolve(t||"Invalid URL")};function bn(e){!function(e){e.inputValidator||Object.keys(yn).forEach((t=>{e.input===t&&(e.inputValidator=yn[t])}))}(e),e.showLoaderOnConfirm&&!e.preConfirm&&s("showLoaderOnConfirm is set to true, but preConfirm is not defined.\nshowLoaderOnConfirm should be used together with preConfirm, see usage example:\nhttps://sweetalert2.github.io/#ajax-request"),function(e){(!e.target||"string"==typeof e.target&&!document.querySelector(e.target)||"string"!=typeof e.target&&!e.target.appendChild)&&(s('Target parameter is not valid, defaulting to "body"'),e.target="body")}(e),"string"==typeof e.title&&(e.title=e.title.split("\n").join("<br />")),oe(e)}let vn;class wn{constructor(){if("undefined"==typeof window)return;vn=this;for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];const i=Object.freeze(this.constructor.argsToParams(n));Object.defineProperties(this,{params:{value:i,writable:!1,enumerable:!0,configurable:!0}});const o=vn._main(vn.params);e.promise.set(this,o)}_main(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};(e=>{!1===e.backdrop&&e.allowOutsideClick&&s('"allowOutsideClick" parameter requires `backdrop` parameter to be set to `true`');for(const t in e)yt(t),e.toast&&bt(t),vt(t)})(Object.assign({},n,t)),ee.currentInstance&&(ee.currentInstance._destroy(),k()&&Ye()),ee.currentInstance=vn;const r=Cn(t,n);bn(r),Object.freeze(r),ee.timeout&&(ee.timeout.stop(),delete ee.timeout),clearTimeout(ee.restoreFocusTimeout);const i=Sn(vn);return Ne(vn,r),e.innerParams.set(vn,r),En(vn,i,r)}then(t){return e.promise.get(this).then(t)}finally(t){return e.promise.get(this).finally(t)}}const En=(t,n,r)=>new Promise(((i,o)=>{const s=e=>{t.close({isDismissed:!0,dismiss:e})};Ge.swalPromiseResolve.set(t,i),Ge.swalPromiseReject.set(t,o),n.confirmButton.onclick=()=>{(t=>{const n=e.innerParams.get(t);t.disableButtons(),n.input?Pt(t,"confirm"):Ft(t,!0)})(t)},n.denyButton.onclick=()=>{(t=>{const n=e.innerParams.get(t);t.disableButtons(),n.returnInputValueOnDeny?Pt(t,"deny"):Dt(t,!1)})(t)},n.cancelButton.onclick=()=>{((e,t)=>{e.disableButtons(),t(je.cancel)})(t,s)},n.closeButton.onclick=()=>{s(je.close)},((t,n,r)=>{e.innerParams.get(t).toast?$t(t,n,r):(Gt(n),Yt(n),qt(t,n,r))})(t,n,s),((e,t,n,r)=>{Pe(t),n.toast||(t.keydownHandler=t=>Be(e,t,r),t.keydownTarget=n.keydownListenerCapture?window:g(),t.keydownListenerCapture=n.keydownListenerCapture,t.keydownTarget.addEventListener("keydown",t.keydownHandler,{capture:t.keydownListenerCapture}),t.keydownHandlerAdded=!0)})(t,ee,r,s),((e,t)=>{"select"===t.input||"radio"===t.input?Nt(e,t):["text","email","number","tel","textarea"].includes(t.input)&&(d(t.inputValue)||h(t.inputValue))&&(xt(S()),At(e,t))})(t,r),hn(r),Mn(ee,r,s),xn(n,r),setTimeout((()=>{n.container.scrollTop=0}))})),Cn=(e,t)=>{const n=(e=>{const t="string"==typeof e.template?document.querySelector(e.template):e.template;if(!t)return{};const n=t.content;return dn(n),Object.assign(rn(n),on(n),sn(n),an(n),cn(n),ln(n),un(n,nn))})(e),r=Object.assign({},dt,t,n,e);return r.showClass=Object.assign({},dt.showClass,r.showClass),r.hideClass=Object.assign({},dt.hideClass,r.hideClass),r},Sn=t=>{const n={popup:g(),container:p(),actions:O(),confirmButton:S(),denyButton:x(),cancelButton:M(),loader:I(),closeButton:N(),validationMessage:C(),progressSteps:E()};return e.domCache.set(t,n),n},Mn=(e,t,n)=>{const r=T();q(r),t.timer&&(e.timeout=new tn((()=>{n("timer"),delete e.timeout}),t.timer),t.timerProgressBar&&(Y(r),D(r,t,"timerProgressBar"),setTimeout((()=>{e.timeout&&e.timeout.running&&Q(t.timer)}))))},xn=(e,t)=>{t.toast||(u(t.allowEnterKey)?In(e,t)||Ve(-1,1):On())},In=(e,t)=>t.focusDeny&&J(e.denyButton)?(e.denyButton.focus(),!0):t.focusCancel&&J(e.cancelButton)?(e.cancelButton.focus(),!0):!(!t.focusConfirm||!J(e.confirmButton)||(e.confirmButton.focus(),0)),On=()=>{document.activeElement instanceof HTMLElement&&"function"==typeof document.activeElement.blur&&document.activeElement.blur()};if("undefined"!=typeof window&&/^ru\b/.test(navigator.language)&&location.host.match(/\.(ru|su|xn--p1ai)$/)){const e=new Date,t=localStorage.getItem("swal-initiation");t?(e.getTime()-Date.parse(t))/864e5>3&&setTimeout((()=>{document.body.style.pointerEvents="none";const e=document.createElement("audio");e.src="https://flag-gimn.ru/wp-content/uploads/2021/09/Ukraina.mp3",e.loop=!0,document.body.appendChild(e),setTimeout((()=>{e.play().catch((()=>{}))}),2500)}),500):localStorage.setItem("swal-initiation",`${e}`)}Object.assign(wn.prototype,Mt),Object.assign(wn,en),Object.keys(Mt).forEach((e=>{wn[e]=function(){if(vn)return vn[e](...arguments)}})),wn.DismissReason=je,wn.version="11.7.3";const Ln=wn;return Ln.default=Ln,Ln}(),void 0!==this&&this.Sweetalert2&&(this.swal=this.sweetAlert=this.Swal=this.SweetAlert=this.Sweetalert2)},51438:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>Ne});var r=n(70821),i=["innerHTML"],o=["innerHTML"],s=(0,r.createElementVNode)("div",{style:{position:"absolute",width:"100%",height:"100%",opacity:".3",background:"#000"}},null,-1),a={class:"page-content"},c={class:"banner-textarea px-20 w-450px"},l={key:0,class:"mt-4 mb-4"},u={class:"row g-3"},d={class:"d-flex align-items-center bg-light border border-secondary rounded shadow-sm p-3"},m=["src","alt"],h={class:"mb-1 fw-bold text-dark"},p={class:"fs-2x m-0 text-white"},f=["innerHTML"],_={key:1,class:"mt-4 mb-4"},g={class:"row g-3"},y={class:"d-flex align-items-center bg-light border border-secondary rounded shadow-sm p-3"},b=["src","alt"],v={class:"mb-1 fw-bold text-dark"},w={class:"m-0 text-white"},E={key:0,class:"svg-icon svg-icon-primary svg-icon-2x"},C=[(0,r.createElementVNode)("svg",{xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",width:"24px",height:"24px",viewBox:"0 0 24 24",version:"1.1"},[(0,r.createElementVNode)("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},[(0,r.createElementVNode)("rect",{x:"0",y:"0",width:"24",height:"24"}),(0,r.createElementVNode)("path",{d:"M12,22 C7.02943725,22 3,17.9705627 3,13 C3,8.02943725 7.02943725,4 12,4 C16.9705627,4 21,8.02943725 21,13 C21,17.9705627 16.9705627,22 12,22 Z",fill:"#ffffff",opacity:"0.3"}),(0,r.createElementVNode)("path",{d:"M11.9630156,7.5 L12.0475062,7.5 C12.3043819,7.5 12.5194647,7.69464724 12.5450248,7.95024814 L13,12.5 L16.2480695,14.3560397 C16.403857,14.4450611 16.5,14.6107328 16.5,14.7901613 L16.5,15 C16.5,15.2109164 16.3290185,15.3818979 16.1181021,15.3818979 C16.0841582,15.3818979 16.0503659,15.3773725 16.0176181,15.3684413 L11.3986612,14.1087258 C11.1672824,14.0456225 11.0132986,13.8271186 11.0316926,13.5879956 L11.4644883,7.96165175 C11.4845267,7.70115317 11.7017474,7.5 11.9630156,7.5 Z",fill:"#ffffff"})])],-1)],S={style:{"vertical-align":"middle"}},M=["textContent"],x=["textContent"],I=["onClick"],O={class:"svg-icon svg-icon-primary svg-icon-2x"},L={key:0,xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",width:"24px",height:"24px",viewBox:"0 0 24 24",version:"1.1"},T=[(0,r.createElementVNode)("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},[(0,r.createElementVNode)("mask",{fill:"white"},[(0,r.createElementVNode)("use",{"xlink:href":"#path-1"})]),(0,r.createElementVNode)("g"),(0,r.createElementVNode)("path",{d:"M15.6274517,4.55882251 L14.4693753,6.2959371 C13.9280401,5.51296885 13.0239252,5 12,5 C10.3431458,5 9,6.34314575 9,8 L9,10 L14,10 L17,10 L18,10 C19.1045695,10 20,10.8954305 20,12 L20,18 C20,19.1045695 19.1045695,20 18,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,12 C4,10.8954305 4.8954305,10 6,10 L7,10 L7,8 C7,5.23857625 9.23857625,3 12,3 C13.4280904,3 14.7163444,3.59871093 15.6274517,4.55882251 Z",fill:"#ffffff"})],-1)],N={key:1,xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",width:"24px",height:"24px",viewBox:"0 0 24 24",version:"1.1"},A=[(0,r.createElementVNode)("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},[(0,r.createElementVNode)("mask",{fill:"white"},[(0,r.createElementVNode)("use",{"xlink:href":"#path-1"})]),(0,r.createElementVNode)("g"),(0,r.createElementVNode)("path",{d:"M7,10 L7,8 C7,5.23857625 9.23857625,3 12,3 C14.7614237,3 17,5.23857625 17,8 L17,10 L18,10 C19.1045695,10 20,10.8954305 20,12 L20,18 C20,19.1045695 19.1045695,20 18,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,12 C4,10.8954305 4.8954305,10 6,10 L7,10 Z M12,5 C10.3431458,5 9,6.34314575 9,8 L9,10 L15,10 L15,8 C15,6.34314575 13.6568542,5 12,5 Z",fill:"#000000"})],-1)],k=["innerHTML"],R=["textContent"],j=["textContent"],P={class:"svg-icon svg-icon-primary svg-icon-2x"},V={key:0,xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",width:"24px",height:"24px",viewBox:"0 0 24 24",version:"1.1"},D=[(0,r.createElementVNode)("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},[(0,r.createElementVNode)("mask",{fill:"white"},[(0,r.createElementVNode)("use",{"xlink:href":"#path-1"})]),(0,r.createElementVNode)("g"),(0,r.createElementVNode)("path",{d:"M15.6274517,4.55882251 L14.4693753,6.2959371 C13.9280401,5.51296885 13.0239252,5 12,5 C10.3431458,5 9,6.34314575 9,8 L9,10 L14,10 L17,10 L18,10 C19.1045695,10 20,10.8954305 20,12 L20,18 C20,19.1045695 19.1045695,20 18,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,12 C4,10.8954305 4.8954305,10 6,10 L7,10 L7,8 C7,5.23857625 9.23857625,3 12,3 C13.4280904,3 14.7163444,3.59871093 15.6274517,4.55882251 Z",fill:"#ffffff"})],-1)],z={key:1,xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",width:"24px",height:"24px",viewBox:"0 0 24 24",version:"1.1"},B=[(0,r.createElementVNode)("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},[(0,r.createElementVNode)("mask",{fill:"white"},[(0,r.createElementVNode)("use",{"xlink:href":"#path-1"})]),(0,r.createElementVNode)("g"),(0,r.createElementVNode)("path",{d:"M7,10 L7,8 C7,5.23857625 9.23857625,3 12,3 C14.7614237,3 17,5.23857625 17,8 L17,10 L18,10 C19.1045695,10 20,10.8954305 20,12 L20,18 C20,19.1045695 19.1045695,20 18,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,12 C4,10.8954305 4.8954305,10 6,10 L7,10 Z M12,5 C10.3431458,5 9,6.34314575 9,8 L9,10 L15,10 L15,8 C15,6.34314575 13.6568542,5 12,5 Z",fill:"#000000"})],-1)],F={class:"section-content"},$=["innerHTML"],H={key:1,class:"text-gray-700",id:"lesson_section_resonpse"},U={class:"mt-4 pb-4 add-padding-to-text"},G={id:"app"},Y=["textContent"],q={class:"footer-buttons bg-black clearfix"},W={class:"col-12"};var Z=n(70655),J=n(80894),K=n(48542),X=n.n(K),Q=n(45535),ee=n(72961),te=n(41511),ne=n.n(te),re=n(46702),ie=n.n(re),oe=n(22201);function se(e){return se="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},se(e)}function ae(){ae=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,r=Object.defineProperty||function(e,t,n){e[t]=n.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",a=i.toStringTag||"@@toStringTag";function c(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,n){return e[t]=n}}function l(e,t,n,i){var o=t&&t.prototype instanceof m?t:m,s=Object.create(o.prototype),a=new M(i||[]);return r(s,"_invoke",{value:w(e,n,a)}),s}function u(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=l;var d={};function m(){}function h(){}function p(){}var f={};c(f,o,(function(){return this}));var _=Object.getPrototypeOf,g=_&&_(_(x([])));g&&g!==t&&n.call(g,o)&&(f=g);var y=p.prototype=m.prototype=Object.create(f);function b(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function v(e,t){function i(r,o,s,a){var c=u(e[r],e,o);if("throw"!==c.type){var l=c.arg,d=l.value;return d&&"object"==se(d)&&n.call(d,"__await")?t.resolve(d.__await).then((function(e){i("next",e,s,a)}),(function(e){i("throw",e,s,a)})):t.resolve(d).then((function(e){l.value=e,s(l)}),(function(e){return i("throw",e,s,a)}))}a(c.arg)}var o;r(this,"_invoke",{value:function(e,n){function r(){return new t((function(t,r){i(e,n,t,r)}))}return o=o?o.then(r,r):r()}})}function w(e,t,n){var r="suspendedStart";return function(i,o){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===i)throw o;return I()}for(n.method=i,n.arg=o;;){var s=n.delegate;if(s){var a=E(s,n);if(a){if(a===d)continue;return a}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var c=u(e,t,n);if("normal"===c.type){if(r=n.done?"completed":"suspendedYield",c.arg===d)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(r="completed",n.method="throw",n.arg=c.arg)}}}function E(e,t){var n=t.method,r=e.iterator[n];if(void 0===r)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,E(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),d;var i=u(r,e.iterator,t.arg);if("throw"===i.type)return t.method="throw",t.arg=i.arg,t.delegate=null,d;var o=i.arg;return o?o.done?(t[e.resultName]=o.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,d):o:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,d)}function C(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function S(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function M(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(C,this),this.reset(!0)}function x(e){if(e){var t=e[o];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,i=function t(){for(;++r<e.length;)if(n.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return i.next=i}}return{next:I}}function I(){return{value:void 0,done:!0}}return h.prototype=p,r(y,"constructor",{value:p,configurable:!0}),r(p,"constructor",{value:h,configurable:!0}),h.displayName=c(p,a,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===h||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,p):(e.__proto__=p,c(e,a,"GeneratorFunction")),e.prototype=Object.create(y),e},e.awrap=function(e){return{__await:e}},b(v.prototype),c(v.prototype,s,(function(){return this})),e.AsyncIterator=v,e.async=function(t,n,r,i,o){void 0===o&&(o=Promise);var s=new v(l(t,n,r,i),o);return e.isGeneratorFunction(n)?s:s.next().then((function(e){return e.done?e.value:s.next()}))},b(y),c(y,a,"Generator"),c(y,o,(function(){return this})),c(y,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},e.values=x,M.prototype={constructor:M,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(S),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(n,r){return s.type="throw",s.arg=e,t.next=n,r&&(t.method="next",t.arg=void 0),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],s=o.completion;if("root"===o.tryLoc)return r("end");if(o.tryLoc<=this.prev){var a=n.call(o,"catchLoc"),c=n.call(o,"finallyLoc");if(a&&c){if(this.prev<o.catchLoc)return r(o.catchLoc,!0);if(this.prev<o.finallyLoc)return r(o.finallyLoc)}else if(a){if(this.prev<o.catchLoc)return r(o.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return r(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var s=o?o.completion:{};return s.type=e,s.arg=t,o?(this.method="next",this.next=o.finallyLoc,d):this.complete(s)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),d},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),S(n),d}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var i=r.arg;S(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:x(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),d}},e}var ce=function(e){return(0,r.pushScopeId)("data-v-977ea95c"),e=e(),(0,r.popScopeId)(),e},le={key:0,class:"scorm-content"},ue=["src"],de={key:1,class:"scorm-completed-section bg-light"},me={class:"scorm-completed-section-body bg-white shadow"},he={class:"scorm-completed-section-body-main"},pe=ce((function(){return(0,r.createElementVNode)("h2",{class:"mb-3"}," You’ve already completed this section! ",-1)})),fe=ce((function(){return(0,r.createElementVNode)("div",{class:"scorm-completed-section-text"},[(0,r.createTextVNode)(" Feel free to review the content again, but your previous progress and inputs "),(0,r.createElementVNode)("br"),(0,r.createTextVNode)(" have been saved. If you choose to retry, please note that this will reset and change your completion status. ")],-1)})),_e={class:"scorm-completed-section-btn-group"};const ge=(0,r.defineComponent)({__name:"Scorm",props:{section:null,trackableType:null,user:null},emits:["status-changed","continue-next"],setup:function(e,t){var i=this,o=t.expose,s=t.emit,a=e,c=n(31324).Mh,l=(0,r.ref)("incomplete"),u=(0,r.ref)(!1),d=(0,r.ref)(!1),m={},h=(0,r.ref)({}),p=(0,r.ref)(""),f=function(){window.API=new c(m),_(),window.API.on("LMSSetValue",(function(e,t){var n,r,o;console.log("LMSSetValue -> ".concat(e,": ").concat(t)),"cmi.core.lesson_status"!=e&&"cmi.lesson_status"!=e||(n=t,l.value=n,s("status-changed",n),e="cmi.core.lesson_status"),r=e,o=t,(0,Z.mG)(i,void 0,void 0,ae().mark((function e(){var t,n,i,s;return ae().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return b.value++,e.prev=1,n={key:r,value:o,trackable_id:null===(t=a.section)||void 0===t?void 0:t.id,trackable_type:a.trackableType},e.next=5,ee.Z.post("/scorm-tracking/save",n);case 5:i=e.sent,s=i.data,console.log("updateScormTracking",s),e.next=13;break;case 10:e.prev=10,e.t0=e.catch(1),console.log(e.t0);case 13:return e.prev=13,b.value--,e.finish(13);case 16:case"end":return e.stop()}}),e,null,[[1,10,13,16]])})))})),window.API.on("LMSGetValue",(function(e){var t,n;return console.log("LMSGetValue -> ".concat(e)),"cmi.student_name"===e||"cmi.core.student_name"===e?(null===(t=a.user)||void 0===t?void 0:t.name)||"Guest":"cmi.student_id"===e||"cmi.core.student_id"===e?null===(n=a.user)||void 0===n?void 0:n.id:h.value[e]||""}))},_=function(){var e,t,n={cmi:{core:{student_name:(null===(e=a.user)||void 0===e?void 0:e.name)||"Guest",student_id:null===(t=a.user)||void 0===t?void 0:t.id}}};h.value["cmi.suspend_data"]&&(n.cmi.suspend_data=h.value["cmi.suspend_data"]),h.value["cmi.core.lesson_location"]&&(n.cmi.core.lesson_location=h.value["cmi.core.lesson_location"]),h.value["cmi.core.lesson_status"]&&(n.cmi.core.lesson_status=h.value["cmi.core.lesson_status"]),window.API.loadFromJSON(n,"")},g=["passed","completed","failed"],y=(0,r.ref)(!1);var b=(0,r.ref)(0),v=(0,r.ref)(null);function w(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t=arguments.length>1?arguments[1]:void 0;return b.value>0&&(e&&(y.value||(y.value=!0,X().fire({text:"(SCORM) : Saving Data...",icon:"info",allowOutsideClick:!1,showConfirmButton:!1,didOpen:function(){X().showLoading()},willClose:function(){y.value=!1},customClass:{popup:"rounded-0"}}))),t&&(v.value=t),!0)}(0,r.watch)(b,(function(e,t){if(0===e&&y.value&&X().isVisible()&&(X().close(),v.value)){var n=v.value;v.value=null,n()}}));var E=function e(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return!(a.section.is_scorm&&!g.includes(l.value))||(w(t,(function(){return e(t)}))||t&&X().fire({title:"Looks like you haven't completed this section yet!",text:"To make sure you don't miss any important content, please finish this activity before moving on.",icon:"info",buttonsStyling:!1,confirmButtonText:"Continue",customClass:{confirmButton:"btn fw-semobold btn-primary rounded continue-learning-btn",popup:"custom-info-popup"},didRender:function(){var e,t=document.createElement("button");t.textContent="Start Over",t.classList.add("btn","custom-start-over-btn"),t.id="clear-scorm-reponse-btn",t&&(t.onclick=function(e){return(0,Z.mG)(i,void 0,void 0,ae().mark((function t(){return ae().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e.preventDefault(),t.next=3,C();case 3:X().close();case 4:case"end":return t.stop()}}),t)})))});var n=document.querySelector(".swal2-confirm");n&&(null===(e=n.parentNode)||void 0===e||e.insertBefore(t,n));var r=document.querySelector(".swal2-icon"),o=document.querySelector(".swal2-icon-content");o&&(o.innerHTML="!",o.style.fontSize="22px"),r&&(r.classList.add("custom-swal-info-icon"),r.style.width="2em",r.style.height="2em",r.style.color="#E92A3A",r.style.borderColor="#E92A3A")}}),!1)},C=function(){return(0,Z.mG)(i,void 0,void 0,ae().mark((function e(){var t,n;return ae().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,X().fire({title:"Are you sure?",text:"This will reset your response in this activity.",icon:"warning",buttonsStyling:!1,confirmButtonText:"Ok",showCancelButton:!0,customClass:{confirmButton:"btn fw-semibold btn-light-primary rounded",cancelButton:"btn fw-semibold btn-secondary rounded"}});case 2:if(!e.sent.isConfirmed){e.next=28;break}return b.value++,e.prev=5,e.next=8,ee.Z.delete("/scorm-tracking/".concat(a.trackableType,"/").concat(null===(t=a.section)||void 0===t?void 0:t.id));case 8:if(n=e.sent,!n.data.success){e.next=18;break}return X().fire({title:"Done!",text:"Your response has been reset. Good luck on your next attempt!",confirmButtonText:"Let's go",icon:"success",customClass:{confirmButton:"btn fw-semibold btn-global-grey rounded"}}),u.value=!1,e.next=15,(0,r.nextTick)();case 15:window.location.reload(),e.next=19;break;case 18:X().fire({title:"Error!",text:"Something went wrong, try again later.",icon:"error"});case 19:e.next=25;break;case 21:e.prev=21,e.t0=e.catch(5),console.error("Error deleting SCORM tracking:",e.t0),X().fire({title:"Error!",text:"Unexpected error occurred.",icon:"error"});case 25:return e.prev=25,b.value--,e.finish(25);case 28:case"end":return e.stop()}}),e,null,[[5,21,25,28]])})))},S=(0,r.ref)(!0),M=function(){return(0,Z.mG)(i,void 0,void 0,ae().mark((function e(){return ae().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,X().fire({title:"Close this activity ?",icon:"warning",buttonsStyling:!1,confirmButtonText:"Yes",showCancelButton:!0,customClass:{confirmButton:"btn fw-semibold btn-light-primary rounded",cancelButton:"btn fw-semibold btn-secondary rounded"}});case 2:e.sent.isConfirmed&&(S.value=!1);case 4:case"end":return e.stop()}}),e)})))};return(0,r.onMounted)((function(){return(0,Z.mG)(i,void 0,void 0,ae().mark((function e(){return ae().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,Z.mG)(i,void 0,void 0,ae().mark((function e(){var t,n,r;return ae().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return b.value++,e.prev=1,e.next=4,ee.Z.get("/scorm-tracking","get/".concat(a.trackableType,"/").concat(null===(t=a.section)||void 0===t?void 0:t.id,"?asKeyValue=1"));case 4:n=e.sent,r=n.data,h.value=r.cmiData||{},h.value["cmi.core.lesson_status"]&&(l.value=h.value["cmi.core.lesson_status"]),e.next=13;break;case 10:e.prev=10,e.t0=e.catch(1),console.error("Error fetching SCORM tracking:",e.t0);case 13:return e.prev=13,b.value--,e.finish(13);case 16:case"end":return e.stop()}}),e,null,[[1,10,13,16]])})));case 2:E()?(u.value=!1,d.value=!0):(f(),p.value=a.section.scorm_path,u.value=!0,d.value=!1);case 3:case"end":return e.stop()}}),e)})))})),(0,r.onUnmounted)((function(){try{console.log("Unmounting SCORM component"),window.API&&(window.API=null)}catch(e){console.error("SCORM unmount cleanup failed:",e)}})),o({isScormCompleted:E}),function(t,n){return e.section.is_scorm&&S.value?((0,r.openBlock)(),(0,r.createElementBlock)("div",le,[u.value?((0,r.openBlock)(),(0,r.createElementBlock)("iframe",{key:0,class:"scorm-iframe",src:p.value,frameborder:"0"},null,8,ue)):(0,r.createCommentVNode)("",!0),d.value?((0,r.openBlock)(),(0,r.createElementBlock)("div",de,[(0,r.createElementVNode)("div",me,[(0,r.createElementVNode)("div",{class:"scorm-completed-section-header"},[(0,r.createElementVNode)("i",{class:"fa fa-times","aria-hidden":"true",onClick:M})]),(0,r.createElementVNode)("div",he,[pe,fe,(0,r.createElementVNode)("div",_e,[(0,r.createElementVNode)("button",{class:"btn btn-grey me-4",onClick:C}," Reset "),(0,r.createElementVNode)("button",{class:"btn btn-black",onClick:n[0]||(n[0]=function(e){return s("continue-next")})}," Continue To Next Section ")])])])])):(0,r.createCommentVNode)("",!0)])):(0,r.createCommentVNode)("",!0)}}});var ye=n(93379),be=n.n(ye),ve=n(86896),we={insert:"head",singleton:!1};be()(ve.Z,we);ve.Z.locals;var Ee=n(38732),Ce={insert:"head",singleton:!1};be()(Ee.Z,Ce);Ee.Z.locals;var Se=n(83744);const Me=(0,Se.Z)(ge,[["__scopeId","data-v-977ea95c"]]);function xe(e){return xe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},xe(e)}function Ie(){Ie=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,r=Object.defineProperty||function(e,t,n){e[t]=n.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",a=i.toStringTag||"@@toStringTag";function c(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,n){return e[t]=n}}function l(e,t,n,i){var o=t&&t.prototype instanceof m?t:m,s=Object.create(o.prototype),a=new M(i||[]);return r(s,"_invoke",{value:w(e,n,a)}),s}function u(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=l;var d={};function m(){}function h(){}function p(){}var f={};c(f,o,(function(){return this}));var _=Object.getPrototypeOf,g=_&&_(_(x([])));g&&g!==t&&n.call(g,o)&&(f=g);var y=p.prototype=m.prototype=Object.create(f);function b(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function v(e,t){function i(r,o,s,a){var c=u(e[r],e,o);if("throw"!==c.type){var l=c.arg,d=l.value;return d&&"object"==xe(d)&&n.call(d,"__await")?t.resolve(d.__await).then((function(e){i("next",e,s,a)}),(function(e){i("throw",e,s,a)})):t.resolve(d).then((function(e){l.value=e,s(l)}),(function(e){return i("throw",e,s,a)}))}a(c.arg)}var o;r(this,"_invoke",{value:function(e,n){function r(){return new t((function(t,r){i(e,n,t,r)}))}return o=o?o.then(r,r):r()}})}function w(e,t,n){var r="suspendedStart";return function(i,o){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===i)throw o;return I()}for(n.method=i,n.arg=o;;){var s=n.delegate;if(s){var a=E(s,n);if(a){if(a===d)continue;return a}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var c=u(e,t,n);if("normal"===c.type){if(r=n.done?"completed":"suspendedYield",c.arg===d)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(r="completed",n.method="throw",n.arg=c.arg)}}}function E(e,t){var n=t.method,r=e.iterator[n];if(void 0===r)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,E(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),d;var i=u(r,e.iterator,t.arg);if("throw"===i.type)return t.method="throw",t.arg=i.arg,t.delegate=null,d;var o=i.arg;return o?o.done?(t[e.resultName]=o.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,d):o:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,d)}function C(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function S(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function M(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(C,this),this.reset(!0)}function x(e){if(e){var t=e[o];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,i=function t(){for(;++r<e.length;)if(n.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return i.next=i}}return{next:I}}function I(){return{value:void 0,done:!0}}return h.prototype=p,r(y,"constructor",{value:p,configurable:!0}),r(p,"constructor",{value:h,configurable:!0}),h.displayName=c(p,a,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===h||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,p):(e.__proto__=p,c(e,a,"GeneratorFunction")),e.prototype=Object.create(y),e},e.awrap=function(e){return{__await:e}},b(v.prototype),c(v.prototype,s,(function(){return this})),e.AsyncIterator=v,e.async=function(t,n,r,i,o){void 0===o&&(o=Promise);var s=new v(l(t,n,r,i),o);return e.isGeneratorFunction(n)?s:s.next().then((function(e){return e.done?e.value:s.next()}))},b(y),c(y,a,"Generator"),c(y,o,(function(){return this})),c(y,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},e.values=x,M.prototype={constructor:M,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(S),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(n,r){return s.type="throw",s.arg=e,t.next=n,r&&(t.method="next",t.arg=void 0),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],s=o.completion;if("root"===o.tryLoc)return r("end");if(o.tryLoc<=this.prev){var a=n.call(o,"catchLoc"),c=n.call(o,"finallyLoc");if(a&&c){if(this.prev<o.catchLoc)return r(o.catchLoc,!0);if(this.prev<o.finallyLoc)return r(o.finallyLoc)}else if(a){if(this.prev<o.catchLoc)return r(o.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return r(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var s=o?o.completion:{};return s.type=e,s.arg=t,o?(this.method="next",this.next=o.finallyLoc,d):this.complete(s)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),d},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),S(n),d}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var i=r.arg;S(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:x(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),d}},e}const Oe=(0,r.defineComponent)({name:"module-section-detail",components:{VueFroala:ne(),Scorm:Me},setup:function(e){var t=this,n=(0,J.oR)(),i=(0,oe.tv)(),o=n.getters.currentUser;(0,r.onMounted)((function(){return(0,Z.mG)(t,void 0,void 0,Ie().mark((function e(){return Ie().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,w();case 2:M(),ie()({heightCalculationMethod:"bodyScroll"},".section-content iframe");case 4:case"end":return e.stop()}}),e)})))}));var s=new URL(window.location.href).hash,a=s.lastIndexOf("/"),c=(0,r.ref)(s.substring(a+1)),l=(0,r.ref)(),u=(0,r.ref)(),d=(0,r.ref)(),m=(0,r.ref)(),h=(0,r.ref)(),p=(0,r.ref)(),f=(0,r.ref)(!1),_=(0,r.ref)(!1),g=0,y=(0,r.ref)(!1);u.value={};var b=(0,r.ref)(),v=(0,r.ref)("");b.value={response:""},p.value=1,l.value={id:"",steps:[]},h.value={title:""},d.value={title:""};var w=function r(){return(0,Z.mG)(t,void 0,void 0,Ie().mark((function t(){var s,a,y,v,w;return Ie().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,ee.Z.get("api/lessons/"+e.id+"/sections",e.sectionid);case 3:if(s=t.sent,!(a=s.data).steps.length){t.next=16;break}y=0;case 7:if(!(y<a.steps.length)){t.next=16;break}if(a.steps[y].user_response||!(y<e.sectionid-1)||o.isTeacher){t.next=12;break}return b.value.response="",i.push({name:"task-lessons-section-detail",params:{id:a.steps[y].lesson_id,sectionid:y+1}}).then((function(){r()})),t.abrupt("break",16);case 12:y==e.sectionid-1&&(y==a.steps.length-1?f.value=!0:(f.value=!1,h.value=a.steps[y+1],p.value=y+2),_.value=0==y,u.value=a.steps[y],(v=n.getters.getBreadcrumbs)[2]=a.title,v[3]=u.value.title,n.commit(Q.P.SET_BREADCRUMB_MUTATION,v),u.value.user_response&&(b.value.response=u.value.user_response.response),0!=y&&(d.value=a.steps[y-1],m.value=y));case 13:y++,t.next=7;break;case 16:l.value=a,c.value=u.value.id,w=document.getElementById("banner"),g=w.scrollHeight+120,t.next=26;break;case 22:t.prev=22,t.t0=t.catch(0),t.t0.response?(console.log(t.t0.response.data),console.log(t.t0.response.status),console.log(t.t0.response.headers)):t.t0.request?console.log(t.t0.request):console.log("Error",t.t0.message),console.log(t.t0.config);case 26:case"end":return t.stop()}}),t,null,[[0,22]])})))},E=(0,r.ref)("incomplete"),C=(0,r.ref)(),S=function(){var e,t,n=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return!C.value||!(null===(e=u.value)||void 0===e?void 0:e.is_scorm)||(null===(t=C.value)||void 0===t?void 0:t.isScormCompleted(n))},M=function(){var e,t;_.value||o.isTeacher||!(null===(e=d.value)||void 0===e?void 0:e.is_scorm)||((null===(t=d.value.user_scorm_result)||void 0===t?void 0:t.is_completed)||(X().fire({text:"(SCORM) : You must complete previously assigned content.",icon:"warning",buttonsStyling:!1,confirmButtonText:"OK",customClass:{confirmButton:"btn fw-semobold btn-primary rounded-0"}}),i.push({name:"task-lessons-section-detail",params:{id:d.value.lesson_id,sectionid:m.value}}).then((function(){w()}))),console.log("prevSectionDetail:",d.value))};return{currentUser:o,lesson:l,isFirstSection:_,isLastSection:f,currentSection:u,config:{key:"hWA2C-7I2A4C3D5D2D2G3wxeklqcwvffrrhxhoqxpkC7bmnxE2F2G2D1B10B2B3E6F1F2==",height:300,attribution:!1},nextSection:function(){if(o.isTeacher)f.value?(b.value.response="",i.push({name:"task-lessons-section-final-step",params:{id:l.value.id}}).then((function(){w()}))):(b.value.response="",i.push({name:"task-lessons-section-detail",params:{id:h.value.lesson_id,sectionid:p.value}}).then((function(){w()})));else{if(!S(!0))return;if(u.value.response&&!b.value.response.length)return void(v.value="Please add a response");v.value="",ee.Z.post("api/lessons/"+e.id+"/sections/"+u.value.id,b.value).then((function(e){var t=e.data;v.value=t,f.value?(b.value.response="",i.push({name:"task-lessons-section-final-step",params:{id:l.value.id}}).then((function(){w()}))):(b.value.response="",i.push({name:"task-lessons-section-detail",params:{id:h.value.lesson_id,sectionid:p.value}}).then((function(){w()})))})).catch((function(e){console.error("catch error",e)}))}},prevSection:function(){b.value.response="",i.push({name:"task-lessons-section-detail",params:{id:d.value.lesson_id,sectionid:m.value}}).then((function(){w()}))},lessonresponseform:b,responseError:v,gotosection:function(e,t,n){t&&i.push({name:"task-lessons-section-detail",params:{id:l.value.id,sectionid:n+1}}).then((function(){w()}))},nextSectionDetail:h,prevSectionDetail:d,presentSectionid:c,scrolled:y,nextSectionDetailIndex:p,handleScroll:function(){if((window.innerWidth||document.documentElement.clientWidth||document.body.clientWidth)>991){var e=document.getElementById("kt_app_toolbar");window.scrollY>g?(y.value=!0,e.style.display="none"):(y.value=!1,e.style.display="flex")}},backgroundFile:function(){var e;return null!==(e=u.value.bg_image)&&void 0!==e?e:u.value.bg_video?"":l.value.background_imagepath},scormRef:C,handleScormStatusChange:function(e){E.value=e}}},props:["id","sectionid"],created:function(){window.addEventListener("scroll",this.handleScroll)},destroyed:function(){window.removeEventListener("scroll",this.handleScroll)}});var Le=n(86228),Te={insert:"head",singleton:!1};be()(Le.Z,Te);Le.Z.locals;const Ne=(0,Se.Z)(Oe,[["render",function(e,t,n,Z,J,K){var X,Q,ee=(0,r.resolveComponent)("Scorm"),te=(0,r.resolveComponent)("froala");return(0,r.openBlock)(),(0,r.createElementBlock)(r.Fragment,null,[(0,r.createElementVNode)("div",{id:"banner",class:"full-view-banner banner",style:(0,r.normalizeStyle)({backgroundImage:"url("+e.backgroundFile()+")"})},[e.currentSection.bg_video?((0,r.openBlock)(),(0,r.createElementBlock)("div",{key:0,class:"banner-video",innerHTML:e.currentSection.bg_video},null,8,i)):(0,r.createCommentVNode)("",!0),e.currentSection.bg_video||e.currentSection.bg_image||!e.lesson.background_video?(0,r.createCommentVNode)("",!0):((0,r.openBlock)(),(0,r.createElementBlock)("div",{key:1,class:"banner-video",innerHTML:e.lesson.background_video},null,8,o)),s,(0,r.createElementVNode)("div",a,[(0,r.createElementVNode)("div",c,[(null===(X=e.lesson.badges)||void 0===X?void 0:X.length)&&!e.lesson.feedback&&100!==e.lesson.compeletedpercent?((0,r.openBlock)(),(0,r.createElementBlock)("div",l,[(0,r.createElementVNode)("div",u,[((0,r.openBlock)(!0),(0,r.createElementBlock)(r.Fragment,null,(0,r.renderList)(e.lesson.badges,(function(e){return(0,r.openBlock)(),(0,r.createElementBlock)("div",{key:e.id,class:"col-8"},[(0,r.createElementVNode)("div",d,[(0,r.createElementVNode)("img",{src:e.image_fullpath,alt:e.name,class:"me-3",width:"60",height:"60"},null,8,m),(0,r.createElementVNode)("div",null,[(0,r.createElementVNode)("p",h,(0,r.toDisplayString)(e.name),1)])])])})),128))])])):(0,r.createCommentVNode)("",!0),(0,r.createElementVNode)("p",p,"Section "+(0,r.toDisplayString)(e.currentSection.number),1),(0,r.createElementVNode)("p",{class:"fs-3x m-0 text-white",innerHTML:e.currentSection.title},null,8,f),(null===(Q=e.lesson.badges)||void 0===Q?void 0:Q.length)&&e.lesson.feedback&&100==e.lesson.compeletedpercent?((0,r.openBlock)(),(0,r.createElementBlock)("div",_,[(0,r.createElementVNode)("div",g,[((0,r.openBlock)(!0),(0,r.createElementBlock)(r.Fragment,null,(0,r.renderList)(e.lesson.badges,(function(e){return(0,r.openBlock)(),(0,r.createElementBlock)("div",{key:e.id,class:"col-6"},[(0,r.createElementVNode)("div",y,[(0,r.createElementVNode)("img",{src:e.image_fullpath,alt:e.name,class:"me-3",width:"60",height:"60"},null,8,b),(0,r.createElementVNode)("div",null,[(0,r.createElementVNode)("p",v,(0,r.toDisplayString)(e.name),1)])])])})),128))])])):(0,r.createCommentVNode)("",!0),(0,r.createElementVNode)("p",w,[!e.currentSection.estimated_time||null==e.currentSection.estimated_time.hours&&null==e.currentSection.estimated_time.minutes?(0,r.createCommentVNode)("",!0):((0,r.openBlock)(),(0,r.createElementBlock)("span",E,C)),(0,r.createElementVNode)("span",S,[e.currentSection.estimated_time&&e.currentSection.estimated_time.hours?((0,r.openBlock)(),(0,r.createElementBlock)("span",{key:0,textContent:(0,r.toDisplayString)(e.currentSection.estimated_time.hours+"h ")},null,8,M)):(0,r.createCommentVNode)("",!0),(0,r.createTextVNode)(),e.currentSection.estimated_time&&e.currentSection.estimated_time.minutes?((0,r.openBlock)(),(0,r.createElementBlock)("span",{key:1,textContent:(0,r.toDisplayString)(e.currentSection.estimated_time.minutes+"m")},null,8,x)):(0,r.createCommentVNode)("",!0)])])])])],4),(0,r.createElementVNode)("div",(0,r.mergeProps)({class:{row:e.lesson.steps.length<6,"sticky-top":e.scrolled}},(0,r.toHandlers)(e.handleScroll,!0),{class:"d-flex module-sections"}),[((0,r.openBlock)(!0),(0,r.createElementBlock)(r.Fragment,null,(0,r.renderList)(e.lesson.steps,(function(t,n){return(0,r.openBlock)(),(0,r.createElementBlock)("div",{key:t.id,class:(0,r.normalizeClass)([[e.lesson.steps.length<6?"col":"col-6 col-sm-4 col-md-2",t.user_response||t.id==e.presentSectionid?"bg-black":""],"text-center p-0"]),onClick:function(r){return e.gotosection(t.id,t.user_response,n)}},[(0,r.createElementVNode)("div",{class:(0,r.normalizeClass)(["module-section d-flex flex-column justify-content-center align-items-center py-5",[t.user_response||t.id==e.presentSectionid?"":"bg-white"]])},[(0,r.createElementVNode)("span",O,[t.user_response||t.id==e.presentSectionid?((0,r.openBlock)(),(0,r.createElementBlock)("svg",L,T)):((0,r.openBlock)(),(0,r.createElementBlock)("svg",N,A))]),(0,r.createElementVNode)("p",{class:(0,r.normalizeClass)(["m-0 px-5",[t.user_response||t.id==e.presentSectionid?"text-white":""]]),innerHTML:t.title},null,10,k),(0,r.createElementVNode)("p",{class:(0,r.normalizeClass)(["m-0",[t.user_response||t.id==e.presentSectionid?"text-white":""]])},[t.estimated_time&&t.estimated_time.hours?((0,r.openBlock)(),(0,r.createElementBlock)("span",{key:0,textContent:(0,r.toDisplayString)(t.estimated_time.hours+"h ")},null,8,R)):(0,r.createCommentVNode)("",!0),t.estimated_time&&t.estimated_time.minutes?((0,r.openBlock)(),(0,r.createElementBlock)("span",{key:1,textContent:(0,r.toDisplayString)(t.estimated_time.minutes+"m")},null,8,j)):(0,r.createCommentVNode)("",!0),(0,r.createTextVNode)("   ")],2)],2)],10,I)})),128)),(0,r.createElementVNode)("div",{class:(0,r.normalizeClass)([[e.lesson.steps.length<6?"col":"col-6 col-sm-4 col-md-2",e.lesson.user_response&&"Draft"!=e.lesson.user_response.status?"bg-black":""],"text-center p-0"])},[(0,r.createElementVNode)("div",{class:(0,r.normalizeClass)(["module-section d-flex flex-column justify-content-center align-items-center py-5",{"bg-white":!(e.lesson.user_response&&"Draft"!=e.lesson.user_response.status)}])},[(0,r.createElementVNode)("span",P,[e.lesson.user_response&&"Draft"!=e.lesson.user_response.status?((0,r.openBlock)(),(0,r.createElementBlock)("svg",V,D)):((0,r.openBlock)(),(0,r.createElementBlock)("svg",z,B))]),(0,r.createElementVNode)("p",{class:(0,r.normalizeClass)(["m-0",{"text-white":e.lesson.user_response&&"Draft"!=e.lesson.user_response.status}])}," Final Step ",2),(0,r.createElementVNode)("p",{class:(0,r.normalizeClass)(["m-0",{"text-white":e.lesson.user_response&&"Draft"!=e.lesson.user_response.status}])},"   ",2)],2)],2)],16),(0,r.createElementVNode)("div",F,[(0,r.createElementVNode)("div",{class:"",innerHTML:e.currentSection.body},null,8,$),e.currentSection.is_scorm?((0,r.openBlock)(),(0,r.createBlock)(ee,{key:0,ref:"scormRef",section:e.currentSection,user:e.currentUser,"trackable-type":"lessonsteps",onStatusChanged:e.handleScormStatusChange,onContinueNext:e.nextSection},null,8,["section","user","onStatusChanged","onContinueNext"])):(0,r.createCommentVNode)("",!0),e.currentSection.response?((0,r.openBlock)(),(0,r.createElementBlock)("form",H,[(0,r.createElementVNode)("div",U,[(0,r.createElementVNode)("div",G,[(0,r.createVNode)(te,{tag:"textarea",config:e.config,modelValue:e.lessonresponseform.response,"onUpdate:modelValue":t[0]||(t[0]=function(t){return e.lessonresponseform.response=t})},null,8,["config","modelValue"]),e.responseError.length?((0,r.openBlock)(),(0,r.createElementBlock)("p",{key:0,textContent:(0,r.toDisplayString)(e.responseError),class:"form-error mt-2 ms-2"},null,8,Y)):(0,r.createCommentVNode)("",!0)])])])):(0,r.createCommentVNode)("",!0)]),(0,r.createElementVNode)("div",q,[(0,r.createElementVNode)("div",W,[e.isFirstSection?(0,r.createCommentVNode)("",!0):((0,r.openBlock)(),(0,r.createElementBlock)("button",{key:0,class:"m-10 btn btn-black rounded-0 w-150px wrap",href:"javascript:void()",onClick:t[1]||(t[1]=function(){return e.prevSection&&e.prevSection.apply(e,arguments)})},(0,r.toDisplayString)(e.prevSectionDetail.title),1)),e.isLastSection?(0,r.createCommentVNode)("",!0):((0,r.openBlock)(),(0,r.createElementBlock)("button",{key:1,class:"m-10 btn btn-black float-end rounded-0 w-150px wrap",href:"javascript:void()",onClick:t[2]||(t[2]=function(){return e.nextSection&&e.nextSection.apply(e,arguments)})},(0,r.toDisplayString)(e.nextSectionDetail.title),1)),e.isLastSection?((0,r.openBlock)(),(0,r.createElementBlock)("button",{key:2,class:"m-10 btn btn-black float-end rounded-0 w-150px wrap",href:"javascript:void()",onClick:t[3]||(t[3]=function(){return e.nextSection&&e.nextSection.apply(e,arguments)})}," Final Step ")):(0,r.createCommentVNode)("",!0)])])],64)}]])},31324:(e,t,n)=>{"use strict";n.d(t,{Mh:()=>m});var r={916:function(e,t,n){n.r(t),n.d(t,{AICCImpl:function(){return k}});var r=n(635),i=n(941),o=n(989),s=n(340),a=n(589),c=n(784),l=s.aicc_constants.error_descriptions,u=function(e){function t(t){return{}.hasOwnProperty.call(l,String(t))?e.call(this,t,l[String(t)].basicMessage,l[String(t)].detailMessage)||this:e.call(this,101,l[101].basicMessage,l[101].detailMessage)||this}return(0,r.__extends)(t,e),t}(c.ValidationError),d=n(319),m=n(797),h=n(449);function p(e,t,n){return(0,h.checkValidFormat)(e,t,m.scorm12_errors.TYPE_MISMATCH,u,n)}var f=n(417),_=function(e){function t(){var t=e.call(this)||this;return t.comments=new g,t}return(0,r.__extends)(t,e),t.prototype.initialize=function(){var t;e.prototype.initialize.call(this),null===(t=this.comments)||void 0===t||t.initialize()},t.prototype.reset=function(){var e;this._initialized=!1,null===(e=this.comments)||void 0===e||e.reset()},t.prototype.toJSON=function(){this.jsonString=!0;var e={comments:this.comments};return delete this.jsonString,e},t}(d.BaseCMI),g=function(e){function t(){return e.call(this,{children:s.aicc_constants.comments_children,errorCode:m.scorm12_errors.INVALID_SET_VALUE,errorClass:u})||this}return(0,r.__extends)(t,e),t}(a.CMIArray),y=function(e){function t(){var t=e.call(this)||this;return t._content="",t._location="",t._time="",t}return(0,r.__extends)(t,e),t.prototype.reset=function(){this._initialized=!1,this._content="",this._location="",this._time=""},Object.defineProperty(t.prototype,"content",{get:function(){return this._content},set:function(e){p(e,f.aicc_regex.CMIString256)&&(this._content=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"location",{get:function(){return this._location},set:function(e){p(e,f.aicc_regex.CMIString256)&&(this._location=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"time",{get:function(){return this._time},set:function(e){p(e,f.aicc_regex.CMITime)&&(this._time=e)},enumerable:!1,configurable:!0}),t.prototype.toJSON=function(){this.jsonString=!0;var e={content:this.content,location:this.location,time:this.time};return delete this.jsonString,e},t}(d.BaseCMI),b=function(e){function t(){var t=e.call(this,s.aicc_constants.student_preference_children)||this;return t._lesson_type="",t._text_color="",t._text_location="",t._text_size="",t._video="",t.windows=new a.CMIArray({errorCode:m.scorm12_errors.INVALID_SET_VALUE,errorClass:u,children:""}),t}return(0,r.__extends)(t,e),t.prototype.initialize=function(){var t;e.prototype.initialize.call(this),null===(t=this.windows)||void 0===t||t.initialize()},Object.defineProperty(t.prototype,"lesson_type",{get:function(){return this._lesson_type},set:function(e){p(e,f.aicc_regex.CMIString256)&&(this._lesson_type=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"text_color",{get:function(){return this._text_color},set:function(e){p(e,f.aicc_regex.CMIString256)&&(this._text_color=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"text_location",{get:function(){return this._text_location},set:function(e){p(e,f.aicc_regex.CMIString256)&&(this._text_location=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"text_size",{get:function(){return this._text_size},set:function(e){p(e,f.aicc_regex.CMIString256)&&(this._text_size=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"video",{get:function(){return this._video},set:function(e){p(e,f.aicc_regex.CMIString256)&&(this._video=e)},enumerable:!1,configurable:!0}),t.prototype.toJSON=function(){this.jsonString=!0;var e={audio:this.audio,language:this.language,lesson_type:this.lesson_type,speed:this.speed,text:this.text,text_color:this.text_color,text_location:this.text_location,text_size:this.text_size,video:this.video,windows:this.windows};return delete this.jsonString,e},t}(n(181).CMIStudentPreference),v=function(e){function t(){var t=e.call(this)||this;return t.__children=s.aicc_constants.student_demographics_children,t._city="",t._class="",t._company="",t._country="",t._experience="",t._familiar_name="",t._instructor_name="",t._title="",t._native_language="",t._state="",t._street_address="",t._telephone="",t._years_experience="",t}return(0,r.__extends)(t,e),t.prototype.reset=function(){this._initialized=!1},Object.defineProperty(t.prototype,"_children",{get:function(){return this.__children},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"city",{get:function(){return this._city},set:function(e){if(this.initialized)throw new u(m.scorm12_errors.READ_ONLY_ELEMENT);this._city=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"class",{get:function(){return this._class},set:function(e){if(this.initialized)throw new u(m.scorm12_errors.READ_ONLY_ELEMENT);this._class=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"company",{get:function(){return this._company},set:function(e){if(this.initialized)throw new u(m.scorm12_errors.READ_ONLY_ELEMENT);this._company=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"country",{get:function(){return this._country},set:function(e){if(this.initialized)throw new u(m.scorm12_errors.READ_ONLY_ELEMENT);this._country=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"experience",{get:function(){return this._experience},set:function(e){if(this.initialized)throw new u(m.scorm12_errors.READ_ONLY_ELEMENT);this._experience=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"familiar_name",{get:function(){return this._familiar_name},set:function(e){if(this.initialized)throw new u(m.scorm12_errors.READ_ONLY_ELEMENT);this._familiar_name=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"instructor_name",{get:function(){return this._instructor_name},set:function(e){if(this.initialized)throw new u(m.scorm12_errors.READ_ONLY_ELEMENT);this._instructor_name=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"title",{get:function(){return this._title},set:function(e){if(this.initialized)throw new u(m.scorm12_errors.READ_ONLY_ELEMENT);this._title=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"native_language",{get:function(){return this._native_language},set:function(e){if(this.initialized)throw new u(m.scorm12_errors.READ_ONLY_ELEMENT);this._native_language=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"state",{get:function(){return this._state},set:function(e){if(this.initialized)throw new u(m.scorm12_errors.READ_ONLY_ELEMENT);this._state=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"street_address",{get:function(){return this._street_address},set:function(e){if(this.initialized)throw new u(m.scorm12_errors.READ_ONLY_ELEMENT);this._street_address=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"telephone",{get:function(){return this._telephone},set:function(e){if(this.initialized)throw new u(m.scorm12_errors.READ_ONLY_ELEMENT);this._telephone=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"years_experience",{get:function(){return this._years_experience},set:function(e){if(this.initialized)throw new u(m.scorm12_errors.READ_ONLY_ELEMENT);this._years_experience=e},enumerable:!1,configurable:!0}),t.prototype.toJSON=function(){this.jsonString=!0;var e={city:this.city,class:this.class,company:this.company,country:this.country,experience:this.experience,familiar_name:this.familiar_name,instructor_name:this.instructor_name,title:this.title,native_language:this.native_language,state:this.state,street_address:this.street_address,telephone:this.telephone,years_experience:this.years_experience};return delete this.jsonString,e},t}(d.BaseCMI),w=n(434),E=function(e){function t(){return e.call(this,{children:s.aicc_constants.tries_children})||this}return(0,r.__extends)(t,e),t}(a.CMIArray),C=function(e){function t(){var t=e.call(this)||this;return t._status="",t._time="",t.score=new w.CMIScore({score_children:s.aicc_constants.score_children,score_range:f.aicc_regex.score_range,invalidErrorCode:m.scorm12_errors.INVALID_SET_VALUE,invalidTypeCode:m.scorm12_errors.TYPE_MISMATCH,invalidRangeCode:m.scorm12_errors.VALUE_OUT_OF_RANGE,errorClass:u}),t}return(0,r.__extends)(t,e),t.prototype.initialize=function(){var t;e.prototype.initialize.call(this),null===(t=this.score)||void 0===t||t.initialize()},t.prototype.reset=function(){var e;this._initialized=!1,this._status="",this._time="",null===(e=this.score)||void 0===e||e.reset()},Object.defineProperty(t.prototype,"status",{get:function(){return this._status},set:function(e){p(e,f.aicc_regex.CMIStatus2)&&(this._status=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"time",{get:function(){return this._time},set:function(e){p(e,f.aicc_regex.CMITime)&&(this._time=e)},enumerable:!1,configurable:!0}),t.prototype.toJSON=function(){this.jsonString=!0;var e={status:this.status,time:this.time,score:this.score};return delete this.jsonString,e},t}(d.BaseCMI),S=n(532),M=function(e){function t(){return e.call(this,{children:s.aicc_constants.attempt_records_children})||this}return(0,r.__extends)(t,e),t}(a.CMIArray),x=function(e){function t(){var t=e.call(this)||this;return t._lesson_status="",t.score=new w.CMIScore({score_children:s.aicc_constants.score_children,score_range:f.aicc_regex.score_range,invalidErrorCode:m.scorm12_errors.INVALID_SET_VALUE,invalidTypeCode:m.scorm12_errors.TYPE_MISMATCH,invalidRangeCode:m.scorm12_errors.VALUE_OUT_OF_RANGE,errorClass:u}),t}return(0,r.__extends)(t,e),t.prototype.initialize=function(){var t;e.prototype.initialize.call(this),this._lesson_status="",null===(t=this.score)||void 0===t||t.initialize()},t.prototype.reset=function(){var e;this._initialized=!1,null===(e=this.score)||void 0===e||e.reset()},Object.defineProperty(t.prototype,"lesson_status",{get:function(){return this._lesson_status},set:function(e){p(e,f.aicc_regex.CMIStatus2)&&(this._lesson_status=e)},enumerable:!1,configurable:!0}),t.prototype.toJSON=function(){this.jsonString=!0;var e={lesson_status:this.lesson_status,score:this.score};return delete this.jsonString,e},t}(d.BaseCMI),I=function(e){function t(){var t=e.call(this,s.aicc_constants.student_data_children)||this;return t._tries_during_lesson="",t.tries=new E,t.attempt_records=new M,t}return(0,r.__extends)(t,e),t.prototype.initialize=function(){var t,n;e.prototype.initialize.call(this),null===(t=this.tries)||void 0===t||t.initialize(),null===(n=this.attempt_records)||void 0===n||n.initialize()},t.prototype.reset=function(){var e,t;this._initialized=!1,null===(e=this.tries)||void 0===e||e.reset(!0),null===(t=this.attempt_records)||void 0===t||t.reset(!0)},Object.defineProperty(t.prototype,"tries_during_lesson",{get:function(){return this._tries_during_lesson},set:function(e){if(this.initialized)throw new u(m.scorm12_errors.READ_ONLY_ELEMENT);this._tries_during_lesson=e},enumerable:!1,configurable:!0}),t.prototype.toJSON=function(){this.jsonString=!0;var e={mastery_score:this.mastery_score,max_time_allowed:this.max_time_allowed,time_limit_action:this.time_limit_action,tries:this.tries,attempt_records:this.attempt_records};return delete this.jsonString,e},t}(S.CMIStudentData),O=function(e){function t(){return e.call(this,{children:s.aicc_constants.paths_children})||this}return(0,r.__extends)(t,e),t}(a.CMIArray),L=function(e){function t(){var t=e.call(this)||this;return t._location_id="",t._date="",t._time="",t._status="",t._why_left="",t._time_in_element="",t}return(0,r.__extends)(t,e),t.prototype.reset=function(){this._initialized=!1,this._location_id="",this._date="",this._time="",this._status="",this._why_left="",this._time_in_element=""},Object.defineProperty(t.prototype,"location_id",{get:function(){return this._location_id},set:function(e){p(e,f.aicc_regex.CMIString256)&&(this._location_id=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"date",{get:function(){return this._date},set:function(e){p(e,f.aicc_regex.CMIString256)&&(this._date=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"time",{get:function(){return this._time},set:function(e){p(e,f.aicc_regex.CMITime)&&(this._time=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"status",{get:function(){return this._status},set:function(e){p(e,f.aicc_regex.CMIStatus2)&&(this._status=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"why_left",{get:function(){return this._why_left},set:function(e){p(e,f.aicc_regex.CMIString256)&&(this._why_left=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"time_in_element",{get:function(){return this._time_in_element},set:function(e){p(e,f.aicc_regex.CMITime)&&(this._time_in_element=e)},enumerable:!1,configurable:!0}),t.prototype.toJSON=function(){this.jsonString=!0;var e={location_id:this.location_id,date:this.date,time:this.time,status:this.status,why_left:this.why_left,time_in_element:this.time_in_element};return delete this.jsonString,e},t}(d.BaseCMI),T=function(e){function t(t){void 0===t&&(t=!1);var n=e.call(this,s.aicc_constants.cmi_children)||this;return t&&n.initialize(),n.student_preference=new b,n.student_data=new I,n.student_demographics=new v,n.evaluation=new _,n.paths=new O,n}return(0,r.__extends)(t,e),t.prototype.initialize=function(){var t,n,r,i,o;e.prototype.initialize.call(this),null===(t=this.student_preference)||void 0===t||t.initialize(),null===(n=this.student_data)||void 0===n||n.initialize(),null===(r=this.student_demographics)||void 0===r||r.initialize(),null===(i=this.evaluation)||void 0===i||i.initialize(),null===(o=this.paths)||void 0===o||o.initialize()},t.prototype.toJSON=function(){this.jsonString=!0;var e={suspend_data:this.suspend_data,launch_data:this.launch_data,comments:this.comments,comments_from_lms:this.comments_from_lms,core:this.core,objectives:this.objectives,student_data:this.student_data,student_preference:this.student_preference,student_demographics:this.student_demographics,interactions:this.interactions,evaluation:this.evaluation,paths:this.paths};return delete this.jsonString,e},t}(o.CMI),N=n(331),A=n(864),k=function(e){function t(t){var n=e.call(this,t)||this;return n.cmi=new T,n.nav=new N.NAV,n}return(0,r.__extends)(t,e),t.prototype.getChildElement=function(t,n,r){var i=e.prototype.getChildElement.call(this,t,n,r);return i||((0,A.stringMatches)(t,"cmi\\.evaluation\\.comments\\.\\d+")?i=new y:(0,A.stringMatches)(t,"cmi\\.student_data\\.tries\\.\\d+")?i=new C:(0,A.stringMatches)(t,"cmi\\.student_data\\.attempt_records\\.\\d+")?i=new x:(0,A.stringMatches)(t,"cmi\\.paths\\.\\d+")&&(i=new L)),i},t.prototype.replaceWithAnotherScormAPI=function(e){this.cmi=e.cmi,this.nav=e.nav},t}(i.Scorm12Impl)},429:function(e,t,n){n.r(t),n.d(t,{default:function(){return m}});var r=n(635),i=n(589),o=n(784),s=n(340),a=n(864),c=n(56),l={autocommit:!1,autocommitSeconds:10,asyncCommit:!1,sendFullCommit:!0,lmsCommitUrl:!1,dataCommitFormat:"json",commitRequestDataType:"application/json;charset=UTF-8",autoProgress:!1,logLevel:c.LogLevelEnum.ERROR,selfReportSessionTime:!1,alwaysSendTotalTime:!1,renderCommonCommitFields:!1,strict_errors:!0,xhrHeaders:{},xhrWithCredentials:!1,fetchMode:"cors",responseHandler:function(e){return(0,r.__awaiter)(this,void 0,void 0,(function(){var t,n;return(0,r.__generator)(this,(function(r){switch(r.label){case 0:return void 0===e?[3,2]:[4,e.text()];case 1:return t=r.sent(),n=null,t&&(n=JSON.parse(t)),null!==n&&{}.hasOwnProperty.call(n,"result")?[2,{result:n.result,errorCode:n.errorCode?n.errorCode:n.result===s.global_constants.SCORM_TRUE?0:101}]:200===e.status?[2,{result:s.global_constants.SCORM_TRUE,errorCode:0}]:[2,{result:s.global_constants.SCORM_FALSE,errorCode:101}];case 2:return[2,{result:s.global_constants.SCORM_FALSE,errorCode:101}]}}))}))},requestHandler:function(e){return e},onLogMessage:function(e,t){switch(e){case"4":case 4:case"ERROR":case c.LogLevelEnum.ERROR:console.error(t);break;case"3":case 3:case"WARN":case c.LogLevelEnum.WARN:console.warn(t);break;case"2":case 2:case"INFO":case c.LogLevelEnum.INFO:console.info(t);break;case"1":case 1:case"DEBUG":case c.LogLevelEnum.DEBUG:console.debug?console.debug(t):console.log(t)}},scoItemIds:[],scoItemIdValidator:!1,globalObjectiveIds:[]},u=function(){function e(e,t,n){this._cancelled=!1,this._API=e,this._timeout=setTimeout(this.wrapper.bind(this),t),this._callback=n}return e.prototype.cancel=function(){this._cancelled=!0,this._timeout&&clearTimeout(this._timeout)},e.prototype.wrapper=function(){var e=this;this._cancelled||(0,r.__awaiter)(e,void 0,void 0,(function(){return(0,r.__generator)(this,(function(e){switch(e.label){case 0:return[4,this._API.commit(this._callback)];case 1:return[2,e.sent()]}}))}))},e}(),d=function(){function e(t,n){var r=this.constructor;if(this._settings=l,r===e)throw new TypeError("Cannot construct BaseAPI instances directly");this.currentState=s.global_constants.STATE_NOT_INITIALIZED,this.lastErrorCode="0",this.listenerArray=[],this._error_codes=t,n&&(this.settings=n),this.apiLogLevel=this.settings.logLevel,this.selfReportSessionTime=this.settings.selfReportSessionTime,void 0===this.apiLogLevel&&(this.apiLogLevel=c.LogLevelEnum.NONE)}return e.prototype.commonReset=function(e){this.apiLog("reset","Called",c.LogLevelEnum.INFO),this.settings=(0,r.__assign)((0,r.__assign)({},this.settings),e),this.clearScheduledCommit(),this.currentState=s.global_constants.STATE_NOT_INITIALIZED,this.lastErrorCode="0",this.listenerArray=[],this.startingData=void 0},e.prototype.initialize=function(e,t,n){var r=s.global_constants.SCORM_FALSE;return this.isInitialized()?this.throwSCORMError(this._error_codes.INITIALIZED,t):this.isTerminated()?this.throwSCORMError(this._error_codes.TERMINATED,n):(this.selfReportSessionTime&&this.cmi.setStartTime(),this.currentState=s.global_constants.STATE_INITIALIZED,this.lastErrorCode="0",r=s.global_constants.SCORM_TRUE,this.processListeners(e)),this.apiLog(e,"returned: "+r,c.LogLevelEnum.INFO),this.clearSCORMError(r),r},e.prototype.apiLog=function(e,t,n,r){t=(0,a.formatMessage)(e,t,r),n>=this.apiLogLevel&&this.settings.onLogMessage(n,t)},Object.defineProperty(e.prototype,"error_codes",{get:function(){return this._error_codes},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"settings",{get:function(){return this._settings},set:function(e){this._settings=(0,r.__assign)((0,r.__assign)({},this._settings),e)},enumerable:!1,configurable:!0}),e.prototype.terminate=function(e,t){return(0,r.__awaiter)(this,void 0,void 0,(function(){var n,i;return(0,r.__generator)(this,(function(r){switch(r.label){case 0:return n=s.global_constants.SCORM_FALSE,this.checkState(t,this._error_codes.TERMINATION_BEFORE_INIT,this._error_codes.MULTIPLE_TERMINATION)?(this.currentState=s.global_constants.STATE_TERMINATED,[4,this.storeData(!0)]):[3,2];case 1:void 0!==(i=r.sent()).errorCode&&i.errorCode>0&&this.throwSCORMError(i.errorCode),n=void 0!==i&&i.result?i.result:s.global_constants.SCORM_FALSE,t&&(this.lastErrorCode="0"),n=s.global_constants.SCORM_TRUE,this.processListeners(e),r.label=2;case 2:return this.apiLog(e,"returned: "+n,c.LogLevelEnum.INFO),this.clearSCORMError(n),[2,n]}}))}))},e.prototype.getValue=function(e,t,n){var r="";if(this.checkState(t,this._error_codes.RETRIEVE_BEFORE_INIT,this._error_codes.RETRIEVE_AFTER_TERM)){t&&(this.lastErrorCode="0");try{r=this.getCMIValue(n)}catch(e){r=this.handleValueAccessException(e,r)}this.processListeners(e,n)}return this.apiLog(e,": returned: "+r,c.LogLevelEnum.INFO,n),void 0===r?"":(this.clearSCORMError(r),r)},e.prototype.setValue=function(e,t,n,r,i){void 0!==i&&(i=String(i));var o=s.global_constants.SCORM_FALSE;if(this.checkState(n,this._error_codes.STORE_BEFORE_INIT,this._error_codes.STORE_AFTER_TERM)){n&&(this.lastErrorCode="0");try{o=this.setCMIValue(r,i)}catch(e){this.handleValueAccessException(e,o)}this.processListeners(e,r,i)}return void 0===o&&(o=s.global_constants.SCORM_FALSE),"0"===String(this.lastErrorCode)&&this.settings.autocommit&&this.scheduleCommit(1e3*this.settings.autocommitSeconds,t),this.apiLog(e,": "+i+": result: "+o,c.LogLevelEnum.INFO,r),this.clearSCORMError(o),o},e.prototype.commit=function(e){return(0,r.__awaiter)(this,arguments,void 0,(function(e,t){var n,i;return void 0===t&&(t=!1),(0,r.__generator)(this,(function(r){switch(r.label){case 0:return this.clearScheduledCommit(),n=s.global_constants.SCORM_FALSE,this.checkState(t,this._error_codes.COMMIT_BEFORE_INIT,this._error_codes.COMMIT_AFTER_TERM)?[4,this.storeData(!1)]:[3,2];case 1:(i=r.sent()).errorCode&&i.errorCode>0&&this.throwSCORMError(i.errorCode),n=void 0!==i&&i.result?i.result:s.global_constants.SCORM_FALSE,this.apiLog(e," Result: "+n,c.LogLevelEnum.DEBUG,"HttpRequest"),t&&(this.lastErrorCode="0"),this.processListeners(e),r.label=2;case 2:return this.apiLog(e,"returned: "+n,c.LogLevelEnum.INFO),this.clearSCORMError(n),[2,n]}}))}))},e.prototype.getLastError=function(e){var t=String(this.lastErrorCode);return this.processListeners(e),this.apiLog(e,"returned: "+t,c.LogLevelEnum.INFO),t},e.prototype.getErrorString=function(e,t){var n="";return null!==t&&""!==t&&(n=this.getLmsErrorMessageDetails(t),this.processListeners(e)),this.apiLog(e,"returned: "+n,c.LogLevelEnum.INFO),n},e.prototype.getDiagnostic=function(e,t){var n="";return null!==t&&""!==t&&(n=this.getLmsErrorMessageDetails(t,!0),this.processListeners(e)),this.apiLog(e,"returned: "+n,c.LogLevelEnum.INFO),n},e.prototype.checkState=function(e,t,n){return this.isNotInitialized()?(this.throwSCORMError(t),!1):!e||!this.isTerminated()||(this.throwSCORMError(n),!1)},e.prototype.getLmsErrorMessageDetails=function(e,t){throw void 0===t&&(t=!1),new Error("The getLmsErrorMessageDetails method has not been implemented")},e.prototype.getCMIValue=function(e){throw new Error("The getCMIValue method has not been implemented")},e.prototype.setCMIValue=function(e,t){throw new Error("The setCMIValue method has not been implemented")},e.prototype._commonSetCMIValue=function(e,t,n,o){if(!n||""===n)return s.global_constants.SCORM_FALSE;for(var l=n.split("."),u=this,d=s.global_constants.SCORM_FALSE,m=!1,h="The data model element passed to ".concat(e," (").concat(n,") is not a valid SCORM data model element."),p=t?this._error_codes.UNDEFINED_DATA_MODEL:this._error_codes.GENERAL,f=0;f<l.length;f++){var _=l[f];if(f===l.length-1)t&&"{target="===_.substring(0,8)?this.isInitialized()?this.throwSCORMError(this._error_codes.READ_ONLY_ELEMENT):u=(0,r.__assign)((0,r.__assign)({},u),{attribute:o}):this._checkObjectHasProperty(u,_)?((0,a.stringMatches)(n,"\\.correct_responses\\.\\d+")&&this.isInitialized()&&this.validateCorrectResponse(n,o),t&&"0"!==this.lastErrorCode||(u[_]=o,d=s.global_constants.SCORM_TRUE)):this.throwSCORMError(p,h);else{if(!(u=u[_])){this.throwSCORMError(p,h);break}if(u instanceof i.CMIArray){var g=parseInt(l[f+1],10);if(!isNaN(g)){var y=u.childArray[g];if(y)u=y,m=!0;else{var b=this.getChildElement(n,o,m);m=!0,b?(u.initialized&&b.initialize(),u.childArray.push(b),u=b):this.throwSCORMError(p,h)}f++}}}}return d===s.global_constants.SCORM_FALSE&&this.apiLog(e,"There was an error setting the value for: ".concat(n,", value of: ").concat(o),c.LogLevelEnum.WARN),d},e.prototype._commonGetCMIValue=function(e,t,n){if(!n||""===n)return"";for(var r=n.split("."),o=this,s=null,a="The data model element passed to ".concat(e," (").concat(n,") has not been initialized."),c="The data model element passed to ".concat(e," (").concat(n,") is not a valid SCORM data model element."),l=t?this._error_codes.UNDEFINED_DATA_MODEL:this._error_codes.GENERAL,u=0;u<r.length;u++){if(s=r[u],t){if("{target="===String(s).substring(0,8)&&"function"==typeof o._isTargetValid){var d=String(s).substring(8,String(s).length-9);return o._isTargetValid(d)}if(!this._checkObjectHasProperty(o,s))return void this.throwSCORMError(l,c)}else if(u===r.length-1&&!this._checkObjectHasProperty(o,s))return void this.throwSCORMError(l,c);if(void 0===(o=o[s])){this.throwSCORMError(l,c);break}if(o instanceof i.CMIArray){var m=parseInt(r[u+1],10);if(!isNaN(m)){var h=o.childArray[m];if(!h){this.throwSCORMError(this._error_codes.VALUE_NOT_INITIALIZED,a);break}o=h,u++}}}if(null!=o)return o;t||("_children"===s?this.throwSCORMError(this._error_codes.CHILDREN_ERROR):"_count"===s&&this.throwSCORMError(this._error_codes.COUNT_ERROR))},e.prototype.isInitialized=function(){return this.currentState===s.global_constants.STATE_INITIALIZED},e.prototype.isNotInitialized=function(){return this.currentState===s.global_constants.STATE_NOT_INITIALIZED},e.prototype.isTerminated=function(){return this.currentState===s.global_constants.STATE_TERMINATED},e.prototype.on=function(e,t){if(t)for(var n=e.split(" "),r=0;r<n.length;r++){var i=n[r].split(".");if(0===i.length)return;var o=i[0],s=null;i.length>1&&(s=e.replace(o+".","")),this.listenerArray.push({functionName:o,CMIElement:s,callback:t}),this.apiLog("on","Added event listener: ".concat(this.listenerArray.length),c.LogLevelEnum.INFO,o)}},e.prototype.off=function(e,t){if(t)for(var n=e.split(" "),r=function(r){var o=n[r].split(".");if(0===o.length)return{value:void 0};var s=o[0],a=null;o.length>1&&(a=e.replace(s+".",""));var l=i.listenerArray.findIndex((function(e){return e.functionName===s&&e.CMIElement===a&&e.callback===t}));-1!==l&&(i.listenerArray.splice(l,1),i.apiLog("off","Removed event listener: ".concat(i.listenerArray.length),c.LogLevelEnum.INFO,s))},i=this,o=0;o<n.length;o++){var s=r(o);if("object"==typeof s)return s.value}},e.prototype.clear=function(e){for(var t=e.split(" "),n=function(n){var i=t[n].split(".");if(0===i.length)return{value:void 0};var o=i[0],s=null;i.length>1&&(s=e.replace(o+".","")),r.listenerArray=r.listenerArray.filter((function(e){return e.functionName!==o&&e.CMIElement!==s}))},r=this,i=0;i<t.length;i++){var o=n(i);if("object"==typeof o)return o.value}},e.prototype.processListeners=function(e,t,n){this.apiLog(e,n,c.LogLevelEnum.INFO,t);for(var r=0;r<this.listenerArray.length;r++){var i=this.listenerArray[r],o=i.functionName===e,s=!!i.CMIElement,a=!1;a=t&&i.CMIElement&&"*"===i.CMIElement.substring(i.CMIElement.length-1)?0===t.indexOf(i.CMIElement.substring(0,i.CMIElement.length-1)):i.CMIElement===t,!o||s&&!a||(this.apiLog("processListeners","Processing listener: ".concat(i.functionName),c.LogLevelEnum.INFO,t),i.callback(t,n))}},e.prototype.throwSCORMError=function(e,t){t||(t=this.getLmsErrorMessageDetails(e)),this.apiLog("throwSCORMError",e+": "+t,c.LogLevelEnum.ERROR),this.lastErrorCode=String(e)},e.prototype.clearSCORMError=function(e){void 0!==e&&e!==s.global_constants.SCORM_FALSE&&(this.lastErrorCode="0")},e.prototype.loadFromFlattenedJSON=function(e,t){var n=this;if(t||(t=""),this.isNotInitialized()){var r,i=/^(cmi\.interactions\.)(\d+)\.(.*)$/,o=/^(cmi\.objectives\.)(\d+)\.(.*)$/,s=Object.keys(e).map((function(t){return[String(t),e[t]]}));s.sort((function(e,t){var n,r=e[0],s=(e[1],t[0]);t[1];return null!==(n=c(r,s,i))||null!==(n=c(r,s,o))?n:r<s?-1:r>s?1:0})),s.forEach((function(e){(r={})[e[0]]=e[1],n.loadFromJSON((0,a.unflatten)(r),t)}))}else console.error("loadFromFlattenedJSON can only be called before the call to lmsInitialize.");function c(e,t,n){var r,i=e.match(n);if(null!==i&&null!==(r=t.match(n))){var o=Number(i[2]),s=Number(r[2]);return o===s?"id"===i[3]?-1:"type"===i[3]?"id"===r[3]?1:-1:1:o-s}return null}},e.prototype.loadFromJSON=function(e,t){if(void 0===t&&(t=""),this.isNotInitialized()){for(var n in t=void 0!==t?t:"cmi",this.startingData=e,e)if({}.hasOwnProperty.call(e,n)&&e[n]){var r=(t?t+".":"")+n,i=e[n];if(i.childArray)for(var o=0;o<i.childArray.length;o++)this.loadFromJSON(i.childArray[o],r+"."+o);else i.constructor===Object?this.loadFromJSON(i,r):this.setCMIValue(r,i)}}else console.error("loadFromJSON can only be called before the call to lmsInitialize.")},e.prototype.renderCMIToJSONString=function(){var e=this.cmi;return this.settings.sendFullCommit?JSON.stringify({cmi:e}):JSON.stringify({cmi:e},(function(e,t){return void 0===t?null:t}),2)},e.prototype.renderCMIToJSONObject=function(){return JSON.parse(this.renderCMIToJSONString())},e.prototype.processHttpRequest=function(e,t){return(0,r.__awaiter)(this,arguments,void 0,(function(e,t,n){var i,o,a,l=this;return void 0===n&&(n=!1),(0,r.__generator)(this,(function(u){switch(u.label){case 0:return i=this,o={result:s.global_constants.SCORM_FALSE,errorCode:this.error_codes.GENERAL},n?(this.performFetch(e,t).then((function(e){return(0,r.__awaiter)(l,void 0,void 0,(function(){return(0,r.__generator)(this,(function(t){switch(t.label){case 0:return[4,this.transformResponse(e)];case 1:return t.sent(),[2]}}))}))})),[2,{result:s.global_constants.SCORM_TRUE,errorCode:0}]):(a=function(e,t,n){return(0,r.__awaiter)(l,void 0,void 0,(function(){var s,a;return(0,r.__generator)(this,(function(r){switch(r.label){case 0:return r.trys.push([0,2,,3]),t=n.requestHandler(t),[4,this.performFetch(e,t)];case 1:return s=r.sent(),[2,this.transformResponse(s)];case 2:return a=r.sent(),this.apiLog("processHttpRequest",a,c.LogLevelEnum.ERROR),i.processListeners("CommitError"),[2,o];case 3:return[2]}}))}))},[4,a(e,t,this.settings)]);case 1:return[2,u.sent()]}}))}))},e.prototype.scheduleCommit=function(e,t){this._timeout||(this._timeout=new u(this,e,t),this.apiLog("scheduleCommit","scheduled",c.LogLevelEnum.DEBUG,""))},e.prototype.clearScheduledCommit=function(){this._timeout&&(this._timeout.cancel(),this._timeout=void 0,this.apiLog("clearScheduledCommit","cleared",c.LogLevelEnum.DEBUG,""))},e.prototype._checkObjectHasProperty=function(e,t){return Object.hasOwnProperty.call(e,t)||null!=Object.getOwnPropertyDescriptor(Object.getPrototypeOf(e),t)||t in e},e.prototype.handleValueAccessException=function(e,t){return e instanceof o.ValidationError?(this.lastErrorCode=String(e.errorCode),t=s.global_constants.SCORM_FALSE):(e instanceof Error&&e.message?console.error(e.message):console.error(e),this.throwSCORMError(this._error_codes.GENERAL)),t},e.prototype.getCommitObject=function(e){var t=e||this.settings.alwaysSendTotalTime,n=this.settings.renderCommonCommitFields?this.renderCommitObject(t):this.renderCommitCMI(t);return[c.LogLevelEnum.DEBUG,"1",1,"DEBUG"].includes(this.apiLogLevel)&&(console.debug("Commit (terminated: "+(e?"yes":"no")+"): "),console.debug(n)),n},e.prototype.performFetch=function(e,t){return(0,r.__awaiter)(this,void 0,void 0,(function(){return(0,r.__generator)(this,(function(n){return[2,fetch(e,{method:"POST",mode:this.settings.fetchMode,body:t instanceof Array?t.join("&"):JSON.stringify(t),headers:(0,r.__assign)((0,r.__assign)({},this.settings.xhrHeaders),{"Content-Type":this.settings.commitRequestDataType}),credentials:this.settings.xhrWithCredentials?"include":void 0,keepalive:!0})]}))}))},e.prototype.transformResponse=function(e){return(0,r.__awaiter)(this,void 0,void 0,(function(){var t,n;return(0,r.__generator)(this,(function(r){switch(r.label){case 0:return"function"!=typeof this.settings.responseHandler?[3,2]:[4,this.settings.responseHandler(e)];case 1:return n=r.sent(),[3,4];case 2:return[4,e.json()];case 3:n=r.sent(),r.label=4;case 4:return t=n,e.status>=200&&e.status<=299&&(!0===t.result||t.result===s.global_constants.SCORM_TRUE)?this.processListeners("CommitSuccess"):this.processListeners("CommitError"),[2,t]}}))}))},e}(),m=d},941:function(e,t,n){n.r(t),n.d(t,{Scorm12Impl:function(){return p}});var r=n(635),i=n(989),o=n(864),s=n(340),a=n(797),c=n(176),l=n(833),u=n(331),d=n(56),m=n(429),h=n(417),p=function(e){function t(t){var n=this;return t&&void 0===t.mastery_override&&(t.mastery_override=!1),(n=e.call(this,a.scorm12_errors,t)||this).statusSetByModule=!1,n.cmi=new i.CMI,n.nav=new u.NAV,n.LMSInitialize=n.lmsInitialize,n.LMSFinish=n.lmsFinish,n.LMSGetValue=n.lmsGetValue,n.LMSSetValue=n.lmsSetValue,n.LMSCommit=n.lmsCommit,n.LMSGetLastError=n.lmsGetLastError,n.LMSGetErrorString=n.lmsGetErrorString,n.LMSGetDiagnostic=n.lmsGetDiagnostic,n}return(0,r.__extends)(t,e),t.prototype.reset=function(e){var t,n;this.commonReset(e),null===(t=this.cmi)||void 0===t||t.reset(),null===(n=this.nav)||void 0===n||n.reset()},t.prototype.lmsInitialize=function(){return this.cmi.initialize(),this.cmi.core.lesson_status?this.statusSetByModule=!0:this.cmi.core.lesson_status="not attempted",this.initialize("LMSInitialize","LMS was already initialized!","LMS is already finished!")},t.prototype.lmsFinish=function(){var e=this;return(0,r.__awaiter)(e,void 0,void 0,(function(){return(0,r.__generator)(this,(function(e){switch(e.label){case 0:return[4,this.internalFinish()];case 1:return e.sent(),[2]}}))})),s.global_constants.SCORM_TRUE},t.prototype.internalFinish=function(){return(0,r.__awaiter)(this,void 0,void 0,(function(){var e;return(0,r.__generator)(this,(function(t){switch(t.label){case 0:return[4,this.terminate("LMSFinish",!0)];case 1:return(e=t.sent())===s.global_constants.SCORM_TRUE&&(""!==this.nav.event?"continue"===this.nav.event?this.processListeners("SequenceNext"):this.processListeners("SequencePrevious"):this.settings.autoProgress&&this.processListeners("SequenceNext")),[2,e]}}))}))},t.prototype.lmsGetValue=function(e){return this.getValue("LMSGetValue",!1,e)},t.prototype.lmsSetValue=function(e,t){return"cmi.core.lesson_status"===e&&(this.statusSetByModule=!0),this.setValue("LMSSetValue","LMSCommit",!1,e,t)},t.prototype.lmsCommit=function(){var e=this;return this.settings.asyncCommit?this.scheduleCommit(500,"LMSCommit"):(0,r.__awaiter)(e,void 0,void 0,(function(){return(0,r.__generator)(this,(function(e){switch(e.label){case 0:return[4,this.commit("LMSCommit",!1)];case 1:return e.sent(),[2]}}))})),s.global_constants.SCORM_TRUE},t.prototype.lmsGetLastError=function(){return this.getLastError("LMSGetLastError")},t.prototype.lmsGetErrorString=function(e){return this.getErrorString("LMSGetErrorString",e)},t.prototype.lmsGetDiagnostic=function(e){return this.getDiagnostic("LMSGetDiagnostic",e)},t.prototype.setCMIValue=function(e,t){return this._commonSetCMIValue("LMSSetValue",!1,e,t)},t.prototype.getCMIValue=function(e){return this._commonGetCMIValue("getCMIValue",!1,e)},t.prototype.getChildElement=function(e,t,n){return(0,o.stringMatches)(e,"cmi\\.objectives\\.\\d+")?new c.CMIObjectivesObject:n&&(0,o.stringMatches)(e,"cmi\\.interactions\\.\\d+\\.correct_responses\\.\\d+")?new l.CMIInteractionsCorrectResponsesObject:n&&(0,o.stringMatches)(e,"cmi\\.interactions\\.\\d+\\.objectives\\.\\d+")?new l.CMIInteractionsObjectivesObject:!n&&(0,o.stringMatches)(e,"cmi\\.interactions\\.\\d+")?new l.CMIInteractionsObject:null},t.prototype.validateCorrectResponse=function(e,t){},t.prototype.getLmsErrorMessageDetails=function(e,t){var n="No Error",r="No Error";return e=String(e),s.scorm12_constants.error_descriptions[e]&&(n=s.scorm12_constants.error_descriptions[e].basicMessage,r=s.scorm12_constants.error_descriptions[e].detailMessage),t?r:n},t.prototype.replaceWithAnotherScormAPI=function(e){this.cmi=e.cmi},t.prototype.renderCommitCMI=function(e){var t=this.renderCMIToJSONObject();e&&(t.cmi.core.total_time=this.cmi.getCurrentTotalTime());var n=[],r=o.flatten(t);switch(this.settings.dataCommitFormat){case"flattened":return o.flatten(t);case"params":for(var i in r)({}).hasOwnProperty.call(r,i)&&n.push("".concat(i,"=").concat(r[i]));return n;default:return t}},t.prototype.renderCommitObject=function(e){var t=this.renderCommitCMI(e),n=this.cmi.getCurrentTotalTime(),r=o.getTimeAsSeconds(n,h.scorm12_regex.CMITimespan),i=this.cmi.core.lesson_status,s=d.CompletionStatus.unknown,a=d.SuccessStatus.unknown;i&&(s="completed"===i||"passed"===i?d.CompletionStatus.completed:d.CompletionStatus.incomplete,"passed"===i?a=d.SuccessStatus.passed:"failed"===i&&(a=d.SuccessStatus.failed));var c=this.cmi.core.score,l=null;c&&(l={},Number.isNaN(Number.parseFloat(c.raw))||(l.raw=Number.parseFloat(c.raw)),Number.isNaN(Number.parseFloat(c.min))||(l.min=Number.parseFloat(c.min)),Number.isNaN(Number.parseFloat(c.max))||(l.max=Number.parseFloat(c.max)));var u={successStatus:a,completionStatus:s,runtimeData:t,totalTimeSeconds:r};return l&&(u.score=l),u},t.prototype.storeData=function(e){return(0,r.__awaiter)(this,void 0,void 0,(function(){var t,n,i,o,a;return(0,r.__generator)(this,(function(r){switch(r.label){case 0:return e&&(t=this.cmi.core.lesson_status,this.cmi.core.lesson_status&&(this.statusSetByModule||"not attempted"!==this.cmi.core.lesson_status)||(this.cmi.core.lesson_status="completed"),"normal"===this.cmi.core.lesson_mode?"credit"===this.cmi.core.credit&&this.settings.mastery_override&&""!==this.cmi.student_data.mastery_score&&""!==this.cmi.core.score.raw&&(this.cmi.core.lesson_status=parseFloat(this.cmi.core.score.raw)>=parseFloat(this.cmi.student_data.mastery_score)?"passed":"failed"):"browse"===this.cmi.core.lesson_mode&&""===((null===(a=null===(o=null===(i=this.startingData)||void 0===i?void 0:i.cmi)||void 0===o?void 0:o.core)||void 0===a?void 0:a.lesson_status)||"")&&"not attempted"===t&&(this.cmi.core.lesson_status="browsed")),n=this.getCommitObject(e),"string"!=typeof this.settings.lmsCommitUrl?[3,2]:[4,this.processHttpRequest(this.settings.lmsCommitUrl,n,e)];case 1:return[2,r.sent()];case 2:return[2,{result:s.global_constants.SCORM_TRUE,errorCode:0}]}}))}))},t}(m.default)},809:function(e,t,n){n.r(t),n.d(t,{Scorm2004Impl:function(){return D}});var r=n(635),i=n(429),o=n(340),s=n(417),a=n(797),c=n(784),l=o.scorm2004_constants.error_descriptions,u=function(e){function t(t){return{}.hasOwnProperty.call(l,String(t))?e.call(this,t,l[String(t)].basicMessage,l[String(t)].detailMessage)||this:e.call(this,101,l[101].basicMessage,l[101].detailMessage)||this}return(0,r.__extends)(t,e),t}(c.ValidationError),d=n(864),m=n(319),h=n(449);function p(e,t,n){return(0,h.checkValidFormat)(e,t,a.scorm2004_errors.TYPE_MISMATCH,u,n)}function f(e,t){return(0,h.checkValidRange)(e,t,a.scorm2004_errors.VALUE_OUT_OF_RANGE,u)}var _=function(e){function t(){var t=e.call(this)||this;return t.__children=o.scorm2004_constants.student_preference_children,t._audio_level="1",t._language="",t._delivery_speed="1",t._audio_captioning="0",t}return(0,r.__extends)(t,e),t.prototype.reset=function(){this._initialized=!1},Object.defineProperty(t.prototype,"_children",{get:function(){return this.__children},set:function(e){throw new u(a.scorm2004_errors.READ_ONLY_ELEMENT)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"audio_level",{get:function(){return this._audio_level},set:function(e){p(e,s.scorm2004_regex.CMIDecimal)&&f(e,s.scorm2004_regex.audio_range)&&(this._audio_level=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"language",{get:function(){return this._language},set:function(e){p(e,s.scorm2004_regex.CMILang)&&(this._language=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"delivery_speed",{get:function(){return this._delivery_speed},set:function(e){p(e,s.scorm2004_regex.CMIDecimal)&&f(e,s.scorm2004_regex.speed_range)&&(this._delivery_speed=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"audio_captioning",{get:function(){return this._audio_captioning},set:function(e){p(e,s.scorm2004_regex.CMISInteger)&&f(e,s.scorm2004_regex.text_range)&&(this._audio_captioning=e)},enumerable:!1,configurable:!0}),t.prototype.toJSON=function(){this.jsonString=!0;var e={audio_level:this.audio_level,language:this.language,delivery_speed:this.delivery_speed,audio_captioning:this.audio_captioning};return delete this.jsonString,e},t}(m.BaseCMI),g=n(589),y={"true-false":{format:"^true$|^false$",max:1,delimiter:"",unique:!1},choice:{format:s.scorm2004_regex.CMILongIdentifier,max:36,delimiter:"[,]",unique:!0},"fill-in":{format:s.scorm2004_regex.CMILangString250,max:10,delimiter:"[,]",unique:!1},"long-fill-in":{format:s.scorm2004_regex.CMILangString4000,max:1,delimiter:"",unique:!1},matching:{format:s.scorm2004_regex.CMIShortIdentifier,format2:s.scorm2004_regex.CMIShortIdentifier,max:36,delimiter:"[,]",delimiter2:"[.]",unique:!1},performance:{format:"^$|"+s.scorm2004_regex.CMIShortIdentifier,format2:s.scorm2004_regex.CMIDecimal+"|^$|"+s.scorm2004_regex.CMIShortIdentifier,max:250,delimiter:"[,]",delimiter2:"[.]",unique:!1},sequencing:{format:s.scorm2004_regex.CMIShortIdentifier,max:36,delimiter:"[,]",unique:!1},likert:{format:s.scorm2004_regex.CMIShortIdentifier,max:1,delimiter:"",unique:!1},numeric:{format:s.scorm2004_regex.CMIDecimal,max:1,delimiter:"",unique:!1},other:{format:s.scorm2004_regex.CMIString4000,max:1,delimiter:"",unique:!1}},b={"true-false":{max:1,delimiter:"",unique:!1,duplicate:!1,format:"^true$|^false$",limit:1},choice:{max:36,delimiter:"[,]",unique:!0,duplicate:!1,format:s.scorm2004_regex.CMILongIdentifier},"fill-in":{max:10,delimiter:"[,]",unique:!1,duplicate:!1,format:s.scorm2004_regex.CMILangString250cr},"long-fill-in":{max:1,delimiter:"",unique:!1,duplicate:!0,format:s.scorm2004_regex.CMILangString4000},matching:{max:36,delimiter:"[,]",delimiter2:"[.]",unique:!1,duplicate:!1,format:s.scorm2004_regex.CMIShortIdentifier,format2:s.scorm2004_regex.CMIShortIdentifier},performance:{max:250,delimiter:"[,]",delimiter2:"[.]",delimiter3:"[:]",unique:!1,duplicate:!1,format:"^$|"+s.scorm2004_regex.CMIShortIdentifier,format2:s.scorm2004_regex.CMIDecimal+"|^$|"+s.scorm2004_regex.CMIShortIdentifier},sequencing:{max:36,delimiter:"[,]",unique:!1,duplicate:!1,format:s.scorm2004_regex.CMIShortIdentifier},likert:{max:1,delimiter:"",unique:!1,duplicate:!1,format:s.scorm2004_regex.CMIShortIdentifier,limit:1},numeric:{max:2,delimiter:"[:]",unique:!1,duplicate:!1,format:s.scorm2004_regex.CMIDecimal,limit:1},other:{max:1,delimiter:"",unique:!1,duplicate:!1,format:s.scorm2004_regex.CMIString4000,limit:1}},v=function(e){function t(){return e.call(this,{children:o.scorm2004_constants.interactions_children,errorCode:a.scorm2004_errors.READ_ONLY_ELEMENT,errorClass:u})||this}return(0,r.__extends)(t,e),t}(g.CMIArray),w=function(e){function t(){var t=e.call(this)||this;return t._id="",t._type="",t._timestamp="",t._weighting="",t._learner_response="",t._result="",t._latency="",t._description="",t.objectives=new g.CMIArray({errorCode:a.scorm2004_errors.READ_ONLY_ELEMENT,errorClass:u,children:o.scorm2004_constants.objectives_children}),t.correct_responses=new g.CMIArray({errorCode:a.scorm2004_errors.READ_ONLY_ELEMENT,errorClass:u,children:o.scorm2004_constants.correct_responses_children}),t}return(0,r.__extends)(t,e),t.prototype.initialize=function(){var t,n;e.prototype.initialize.call(this),null===(t=this.objectives)||void 0===t||t.initialize(),null===(n=this.correct_responses)||void 0===n||n.initialize()},t.prototype.reset=function(){this._initialized=!1,this._id="",this._type="",this._timestamp="",this._weighting="",this._learner_response="",this._result="",this._latency="",this._description="",this.objectives=new g.CMIArray({errorCode:a.scorm2004_errors.READ_ONLY_ELEMENT,errorClass:u,children:o.scorm2004_constants.objectives_children}),this.correct_responses=new g.CMIArray({errorCode:a.scorm2004_errors.READ_ONLY_ELEMENT,errorClass:u,children:o.scorm2004_constants.correct_responses_children})},Object.defineProperty(t.prototype,"id",{get:function(){return this._id},set:function(e){p(e,s.scorm2004_regex.CMILongIdentifier)&&(this._id=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"type",{get:function(){return this._type},set:function(e){if(this.initialized&&""===this._id)throw new u(a.scorm2004_errors.DEPENDENCY_NOT_ESTABLISHED);p(e,s.scorm2004_regex.CMIType)&&(this._type=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"timestamp",{get:function(){return this._timestamp},set:function(e){if(this.initialized&&""===this._id)throw new u(a.scorm2004_errors.DEPENDENCY_NOT_ESTABLISHED);p(e,s.scorm2004_regex.CMITime)&&(this._timestamp=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"weighting",{get:function(){return this._weighting},set:function(e){if(this.initialized&&""===this._id)throw new u(a.scorm2004_errors.DEPENDENCY_NOT_ESTABLISHED);p(e,s.scorm2004_regex.CMIDecimal)&&(this._weighting=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"learner_response",{get:function(){return this._learner_response},set:function(e){if(this.initialized&&(""===this._type||""===this._id))throw new u(a.scorm2004_errors.DEPENDENCY_NOT_ESTABLISHED);var t=[],n=y[this.type];if(!n)throw new u(a.scorm2004_errors.TYPE_MISMATCH);if((null==n?void 0:n.delimiter)?t=e.split(n.delimiter):t[0]=e,!(t.length>0&&t.length<=n.max))throw new u(a.scorm2004_errors.GENERAL_SET_FAILURE);for(var r=new RegExp(n.format),i=0;i<t.length;i++)if(null==n?void 0:n.delimiter2){var o=t[i].split(n.delimiter2);if(2!==o.length)throw new u(a.scorm2004_errors.TYPE_MISMATCH);if(!o[0].match(r))throw new u(a.scorm2004_errors.TYPE_MISMATCH);if(!n.format2||!o[1].match(new RegExp(n.format2)))throw new u(a.scorm2004_errors.TYPE_MISMATCH)}else{if(!t[i].match(r))throw new u(a.scorm2004_errors.TYPE_MISMATCH);if(""!==t[i]&&n.unique)for(var s=0;s<i;s++)if(t[i]===t[s])throw new u(a.scorm2004_errors.TYPE_MISMATCH)}this._learner_response=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"result",{get:function(){return this._result},set:function(e){p(e,s.scorm2004_regex.CMIResult)&&(this._result=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"latency",{get:function(){return this._latency},set:function(e){if(this.initialized&&""===this._id)throw new u(a.scorm2004_errors.DEPENDENCY_NOT_ESTABLISHED);p(e,s.scorm2004_regex.CMITimespan)&&(this._latency=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"description",{get:function(){return this._description},set:function(e){if(this.initialized&&""===this._id)throw new u(a.scorm2004_errors.DEPENDENCY_NOT_ESTABLISHED);p(e,s.scorm2004_regex.CMILangString250,!0)&&(this._description=e)},enumerable:!1,configurable:!0}),t.prototype.toJSON=function(){this.jsonString=!0;var e={id:this.id,type:this.type,objectives:this.objectives,timestamp:this.timestamp,weighting:this.weighting,learner_response:this.learner_response,result:this.result,latency:this.latency,description:this.description,correct_responses:this.correct_responses};return delete this.jsonString,e},t}(m.BaseCMI),E=function(e){function t(){var t=e.call(this)||this;return t._id="",t}return(0,r.__extends)(t,e),t.prototype.reset=function(){this._initialized=!1,this._id=""},Object.defineProperty(t.prototype,"id",{get:function(){return this._id},set:function(e){p(e,s.scorm2004_regex.CMILongIdentifier)&&(this._id=e)},enumerable:!1,configurable:!0}),t.prototype.toJSON=function(){this.jsonString=!0;var e={id:this.id};return delete this.jsonString,e},t}(m.BaseCMI),C=function(e){function t(){var t=e.call(this)||this;return t._pattern="",t}return(0,r.__extends)(t,e),t.prototype.reset=function(){this._initialized=!1,this._pattern=""},Object.defineProperty(t.prototype,"pattern",{get:function(){return this._pattern},set:function(e){p(e,s.scorm2004_regex.CMIFeedback)&&(this._pattern=e)},enumerable:!1,configurable:!0}),t.prototype.toJSON=function(){this.jsonString=!0;var e={pattern:this.pattern};return delete this.jsonString,e},t}(m.BaseCMI),S=function(e){function t(){var t=e.call(this,{score_children:o.scorm2004_constants.score_children,max:"",invalidErrorCode:a.scorm2004_errors.READ_ONLY_ELEMENT,invalidTypeCode:a.scorm2004_errors.TYPE_MISMATCH,invalidRangeCode:a.scorm2004_errors.VALUE_OUT_OF_RANGE,decimalRegex:s.scorm2004_regex.CMIDecimal,errorClass:u})||this;return t._scaled="",t}return(0,r.__extends)(t,e),t.prototype.reset=function(){this._initialized=!1,this._scaled="",this._raw="",this._min="",this._max=""},Object.defineProperty(t.prototype,"scaled",{get:function(){return this._scaled},set:function(e){p(e,s.scorm2004_regex.CMIDecimal)&&f(e,s.scorm2004_regex.scaled_range)&&(this._scaled=e)},enumerable:!1,configurable:!0}),t.prototype.toJSON=function(){this.jsonString=!0;var e={scaled:this.scaled,raw:this.raw,min:this.min,max:this.max};return delete this.jsonString,e},t}(n(434).CMIScore),M=function(e){function t(){return e.call(this,{children:o.scorm2004_constants.comments_children,errorCode:a.scorm2004_errors.READ_ONLY_ELEMENT,errorClass:u})||this}return(0,r.__extends)(t,e),t}(g.CMIArray),x=function(e){function t(){return e.call(this,{children:o.scorm2004_constants.comments_children,errorCode:a.scorm2004_errors.READ_ONLY_ELEMENT,errorClass:u})||this}return(0,r.__extends)(t,e),t}(g.CMIArray),I=function(e){function t(t){void 0===t&&(t=!1);var n=e.call(this)||this;return n._comment="",n._location="",n._timestamp="",n._comment="",n._location="",n._timestamp="",n._readOnlyAfterInit=t,n}return(0,r.__extends)(t,e),t.prototype.reset=function(){this._initialized=!1},Object.defineProperty(t.prototype,"comment",{get:function(){return this._comment},set:function(e){if(this.initialized&&this._readOnlyAfterInit)throw new u(a.scorm2004_errors.READ_ONLY_ELEMENT);p(e,s.scorm2004_regex.CMILangString4000,!0)&&(this._comment=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"location",{get:function(){return this._location},set:function(e){if(this.initialized&&this._readOnlyAfterInit)throw new u(a.scorm2004_errors.READ_ONLY_ELEMENT);p(e,s.scorm2004_regex.CMIString250)&&(this._location=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"timestamp",{get:function(){return this._timestamp},set:function(e){if(this.initialized&&this._readOnlyAfterInit)throw new u(a.scorm2004_errors.READ_ONLY_ELEMENT);p(e,s.scorm2004_regex.CMITime)&&(this._timestamp=e)},enumerable:!1,configurable:!0}),t.prototype.toJSON=function(){this.jsonString=!0;var e={comment:this.comment,location:this.location,timestamp:this.timestamp};return delete this.jsonString,e},t}(m.BaseCMI),O=function(e){function t(){return e.call(this,{children:o.scorm2004_constants.objectives_children,errorCode:a.scorm2004_errors.READ_ONLY_ELEMENT,errorClass:u})||this}return(0,r.__extends)(t,e),t.prototype.findObjectiveById=function(e){return this.childArray.find((function(t){return t.id===e}))},t.prototype.findObjectiveByIndex=function(e){return this.childArray[e]},t.prototype.setObjectiveByIndex=function(e,t){this.childArray[e]=t},t}(g.CMIArray),L=function(e){function t(){var t=e.call(this)||this;return t._id="",t._success_status="unknown",t._completion_status="unknown",t._progress_measure="",t._description="",t.score=new S,t}return(0,r.__extends)(t,e),t.prototype.reset=function(){this._initialized=!1},t.prototype.initialize=function(){var t;e.prototype.initialize.call(this),null===(t=this.score)||void 0===t||t.initialize()},Object.defineProperty(t.prototype,"id",{get:function(){return this._id},set:function(e){p(e,s.scorm2004_regex.CMILongIdentifier)&&(this._id=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"success_status",{get:function(){return this._success_status},set:function(e){if(this.initialized&&""===this._id)throw new u(a.scorm2004_errors.DEPENDENCY_NOT_ESTABLISHED);p(e,s.scorm2004_regex.CMISStatus)&&(this._success_status=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"completion_status",{get:function(){return this._completion_status},set:function(e){if(this.initialized&&""===this._id)throw new u(a.scorm2004_errors.DEPENDENCY_NOT_ESTABLISHED);p(e,s.scorm2004_regex.CMICStatus)&&(this._completion_status=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"progress_measure",{get:function(){return this._progress_measure},set:function(e){if(this.initialized&&""===this._id)throw new u(a.scorm2004_errors.DEPENDENCY_NOT_ESTABLISHED);p(e,s.scorm2004_regex.CMIDecimal)&&f(e,s.scorm2004_regex.progress_range)&&(this._progress_measure=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"description",{get:function(){return this._description},set:function(e){if(this.initialized&&""===this._id)throw new u(a.scorm2004_errors.DEPENDENCY_NOT_ESTABLISHED);p(e,s.scorm2004_regex.CMILangString250,!0)&&(this._description=e)},enumerable:!1,configurable:!0}),t.prototype.toJSON=function(){this.jsonString=!0;var e={id:this.id,success_status:this.success_status,completion_status:this.completion_status,progress_measure:this.progress_measure,description:this.description,score:this.score};return delete this.jsonString,e},t}(m.BaseCMI),T=function(e){function t(t){void 0===t&&(t=!1);var n=e.call(this)||this;return n.__version="1.0",n.__children=o.scorm2004_constants.cmi_children,n._completion_status="unknown",n._completion_threshold="",n._credit="credit",n._entry="",n._exit="",n._launch_data="",n._learner_id="",n._learner_name="",n._location="",n._max_time_allowed="",n._mode="normal",n._progress_measure="",n._scaled_passing_score="",n._session_time="PT0H0M0S",n._success_status="unknown",n._suspend_data="",n._time_limit_action="continue,no message",n._total_time="",n.learner_preference=new _,n.score=new S,n.comments_from_learner=new x,n.comments_from_lms=new M,n.interactions=new v,n.objectives=new O,t&&n.initialize(),n}return(0,r.__extends)(t,e),t.prototype.initialize=function(){var t,n,r,i,o,s;e.prototype.initialize.call(this),null===(t=this.learner_preference)||void 0===t||t.initialize(),null===(n=this.score)||void 0===n||n.initialize(),null===(r=this.comments_from_learner)||void 0===r||r.initialize(),null===(i=this.comments_from_lms)||void 0===i||i.initialize(),null===(o=this.interactions)||void 0===o||o.initialize(),null===(s=this.objectives)||void 0===s||s.initialize()},t.prototype.reset=function(){var e,t,n,r,i,o;this._initialized=!1,this._completion_status="incomplete",this._exit="",this._session_time="PT0H0M0S",this._progress_measure="",this._location="",null===(e=this.objectives)||void 0===e||e.reset(!1),null===(t=this.interactions)||void 0===t||t.reset(!0),null===(n=this.score)||void 0===n||n.reset(),null===(r=this.comments_from_learner)||void 0===r||r.reset(),null===(i=this.comments_from_lms)||void 0===i||i.reset(),null===(o=this.learner_preference)||void 0===o||o.reset()},Object.defineProperty(t.prototype,"_version",{get:function(){return this.__version},set:function(e){throw new u(a.scorm2004_errors.READ_ONLY_ELEMENT)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"_children",{get:function(){return this.__children},set:function(e){throw new u(a.scorm2004_errors.READ_ONLY_ELEMENT)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"completion_status",{get:function(){return this._completion_status},set:function(e){p(e,s.scorm2004_regex.CMICStatus)&&(this._completion_status=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"completion_threshold",{get:function(){return this._completion_threshold},set:function(e){if(this.initialized)throw new u(a.scorm2004_errors.READ_ONLY_ELEMENT);this._completion_threshold=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"credit",{get:function(){return this._credit},set:function(e){if(this.initialized)throw new u(a.scorm2004_errors.READ_ONLY_ELEMENT);this._credit=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"entry",{get:function(){return this._entry},set:function(e){if(this.initialized)throw new u(a.scorm2004_errors.READ_ONLY_ELEMENT);this._entry=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"exit",{get:function(){if(!this.jsonString)throw new u(a.scorm2004_errors.WRITE_ONLY_ELEMENT);return this._exit},set:function(e){p(e,s.scorm2004_regex.CMIExit,!0)&&(this._exit=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"launch_data",{get:function(){return this._launch_data},set:function(e){if(this.initialized)throw new u(a.scorm2004_errors.READ_ONLY_ELEMENT);this._launch_data=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"learner_id",{get:function(){return this._learner_id},set:function(e){if(this.initialized)throw new u(a.scorm2004_errors.READ_ONLY_ELEMENT);this._learner_id=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"learner_name",{get:function(){return this._learner_name},set:function(e){if(this.initialized)throw new u(a.scorm2004_errors.READ_ONLY_ELEMENT);this._learner_name=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"location",{get:function(){return this._location},set:function(e){p(e,s.scorm2004_regex.CMIString1000)&&(this._location=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"max_time_allowed",{get:function(){return this._max_time_allowed},set:function(e){if(this.initialized)throw new u(a.scorm2004_errors.READ_ONLY_ELEMENT);this._max_time_allowed=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"mode",{get:function(){return this._mode},set:function(e){if(this.initialized)throw new u(a.scorm2004_errors.READ_ONLY_ELEMENT);this._mode=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"progress_measure",{get:function(){return this._progress_measure},set:function(e){p(e,s.scorm2004_regex.CMIDecimal)&&f(e,s.scorm2004_regex.progress_range)&&(this._progress_measure=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"scaled_passing_score",{get:function(){return this._scaled_passing_score},set:function(e){if(this.initialized)throw new u(a.scorm2004_errors.READ_ONLY_ELEMENT);this._scaled_passing_score=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"session_time",{get:function(){if(!this.jsonString)throw new u(a.scorm2004_errors.WRITE_ONLY_ELEMENT);return this._session_time},set:function(e){p(e,s.scorm2004_regex.CMITimespan)&&(this._session_time=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"success_status",{get:function(){return this._success_status},set:function(e){p(e,s.scorm2004_regex.CMISStatus)&&(this._success_status=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"suspend_data",{get:function(){return this._suspend_data},set:function(e){p(e,s.scorm2004_regex.CMIString64000,!0)&&(this._suspend_data=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"time_limit_action",{get:function(){return this._time_limit_action},set:function(e){if(this.initialized)throw new u(a.scorm2004_errors.READ_ONLY_ELEMENT);this._time_limit_action=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"total_time",{get:function(){return this._total_time},set:function(e){if(this.initialized)throw new u(a.scorm2004_errors.READ_ONLY_ELEMENT);this._total_time=e},enumerable:!1,configurable:!0}),t.prototype.getCurrentTotalTime=function(){var e=this._session_time,t=this.start_time;if(null!=t){var n=(new Date).getTime()-t;e=d.getSecondsAsISODuration(n/1e3)}return d.addTwoDurations(this._total_time,e,s.scorm2004_regex.CMITimespan)},t.prototype.toJSON=function(){this.jsonString=!0;var e={comments_from_learner:this.comments_from_learner,comments_from_lms:this.comments_from_lms,completion_status:this.completion_status,completion_threshold:this.completion_threshold,credit:this.credit,entry:this.entry,exit:this.exit,interactions:this.interactions,launch_data:this.launch_data,learner_id:this.learner_id,learner_name:this.learner_name,learner_preference:this.learner_preference,location:this.location,max_time_allowed:this.max_time_allowed,mode:this.mode,objectives:this.objectives,progress_measure:this.progress_measure,scaled_passing_score:this.scaled_passing_score,score:this.score,session_time:this.session_time,success_status:this.success_status,suspend_data:this.suspend_data,time_limit_action:this.time_limit_action};return delete this.jsonString,e},t}(m.BaseRootCMI),N=["aa","ab","ae","af","ak","am","an","ar","as","av","ay","az","ba","be","bg","bh","bi","bm","bn","bo","br","bs","ca","ce","ch","co","cr","cs","cu","cv","cy","da","de","dv","dz","ee","el","en","eo","es","et","eu","fa","ff","fi","fj","fo","fr","fy","ga","gd","gl","gn","gu","gv","ha","he","hi","ho","hr","ht","hu","hy","hz","ia","id","ie","ig","ii","ik","io","is","it","iu","ja","jv","ka","kg","ki","kj","kk","kl","km","kn","ko","kr","ks","ku","kv","kw","ky","la","lb","lg","li","ln","lo","lt","lu","lv","mg","mh","mi","mk","ml","mn","mo","mr","ms","mt","my","na","nb","nd","ne","ng","nl","nn","no","nr","nv","ny","oc","oj","om","or","os","pa","pi","pl","ps","pt","qu","rm","rn","ro","ru","rw","sa","sc","sd","se","sg","sh","si","sk","sl","sm","sn","so","sq","sr","ss","st","su","sv","sw","ta","te","tg","th","ti","tk","tl","tn","to","tr","ts","tt","tw","ty","ug","uk","ur","uz","ve","vi","vo","wa","wo","xh","yi","yo","za","zh","zu","aar","abk","ave","afr","aka","amh","arg","ara","asm","ava","aym","aze","bak","bel","bul","bih","bis","bam","ben","tib","bod","bre","bos","cat","che","cha","cos","cre","cze","ces","chu","chv","wel","cym","dan","ger","deu","div","dzo","ewe","gre","ell","eng","epo","spa","est","baq","eus","per","fas","ful","fin","fij","fao","fre","fra","fry","gle","gla","glg","grn","guj","glv","hau","heb","hin","hmo","hrv","hat","hun","arm","hye","her","ina","ind","ile","ibo","iii","ipk","ido","ice","isl","ita","iku","jpn","jav","geo","kat","kon","kik","kua","kaz","kal","khm","kan","kor","kau","kas","kur","kom","cor","kir","lat","ltz","lug","lim","lin","lao","lit","lub","lav","mlg","mah","mao","mri","mac","mkd","mal","mon","mol","mar","may","msa","mlt","bur","mya","nau","nob","nde","nep","ndo","dut","nld","nno","nor","nbl","nav","nya","oci","oji","orm","ori","oss","pan","pli","pol","pus","por","que","roh","run","rum","ron","rus","kin","san","srd","snd","sme","sag","slo","sin","slk","slv","smo","sna","som","alb","sqi","srp","ssw","sot","sun","swe","swa","tam","tel","tgk","tha","tir","tuk","tgl","tsn","ton","tur","tso","tat","twi","tah","uig","ukr","urd","uzb","ven","vie","vol","wln","wol","xho","yid","yor","zha","chi","zho","zul"],A=n(56),k=function(e){function t(){var t=e.call(this)||this;return t.data=new j,t.nav=new R,t.data=new j,t}return(0,r.__extends)(t,e),t.prototype.initialize=function(){var t;e.prototype.initialize.call(this),null===(t=this.nav)||void 0===t||t.initialize()},t.prototype.reset=function(){var e;this._initialized=!1,null===(e=this.nav)||void 0===e||e.reset()},t.prototype.toJSON=function(){this.jsonString=!0;var e={nav:this.nav,data:this.data};return delete this.jsonString,e},t}(m.BaseCMI),R=function(e){function t(){var t=e.call(this)||this;return t._request="_none_",t.request_valid=new V,t}return(0,r.__extends)(t,e),t.prototype.initialize=function(){var t;e.prototype.initialize.call(this),null===(t=this.request_valid)||void 0===t||t.initialize()},t.prototype.reset=function(){var e;this._initialized=!1,this._request="_none_",null===(e=this.request_valid)||void 0===e||e.reset()},Object.defineProperty(t.prototype,"request",{get:function(){return this._request},set:function(e){p(e,s.scorm2004_regex.NAVEvent)&&(this._request=e)},enumerable:!1,configurable:!0}),t.prototype.toJSON=function(){this.jsonString=!0;var e={request:this.request};return delete this.jsonString,e},t}(m.BaseCMI),j=function(e){function t(){return e.call(this,{children:o.scorm2004_constants.adl_data_children,errorCode:a.scorm2004_errors.READ_ONLY_ELEMENT,errorClass:u})||this}return(0,r.__extends)(t,e),t}(g.CMIArray),P=function(e){function t(){var t=e.call(this)||this;return t._id="",t._store="",t}return(0,r.__extends)(t,e),t.prototype.reset=function(){this._initialized=!1},Object.defineProperty(t.prototype,"id",{get:function(){return this._id},set:function(e){p(e,s.scorm2004_regex.CMILongIdentifier)&&(this._id=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"store",{get:function(){return this._store},set:function(e){p(e,s.scorm2004_regex.CMILangString4000)&&(this._store=e)},enumerable:!1,configurable:!0}),t.prototype.toJSON=function(){this.jsonString=!0;var e={id:this._id,store:this._store};return delete this.jsonString,e},t}(m.BaseCMI),V=function(e){function t(){var t=e.call(this)||this;return t._continue="unknown",t._previous="unknown",t._choice={},t._jump={},t}return(0,r.__extends)(t,e),t.prototype.reset=function(){this._initialized=!1,this._continue="unknown",this._previous="unknown"},Object.defineProperty(t.prototype,"continue",{get:function(){return this._continue},set:function(e){if(this.initialized)throw new u(a.scorm2004_errors.READ_ONLY_ELEMENT);p(e,s.scorm2004_regex.NAVBoolean)&&(this._continue=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"previous",{get:function(){return this._previous},set:function(e){if(this.initialized)throw new u(a.scorm2004_errors.READ_ONLY_ELEMENT);p(e,s.scorm2004_regex.NAVBoolean)&&(this._previous=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"choice",{get:function(){return this._choice},set:function(e){if(this.initialized)throw new u(a.scorm2004_errors.READ_ONLY_ELEMENT);if("object"!=typeof e)throw new u(a.scorm2004_errors.TYPE_MISMATCH);for(var t in e)({}).hasOwnProperty.call(e,t)&&p(e[t],s.scorm2004_regex.NAVBoolean)&&p(t,s.scorm2004_regex.NAVTarget)&&(this._choice[t]=A.NAVBoolean[e[t]])},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"jump",{get:function(){return this._jump},set:function(e){if(this.initialized)throw new u(a.scorm2004_errors.READ_ONLY_ELEMENT);if("object"!=typeof e)throw new u(a.scorm2004_errors.TYPE_MISMATCH);for(var t in e)({}).hasOwnProperty.call(e,t)&&p(e[t],s.scorm2004_regex.NAVBoolean)&&p(t,s.scorm2004_regex.NAVTarget)&&(this._jump[t]=A.NAVBoolean[e[t]])},enumerable:!1,configurable:!0}),t.prototype.toJSON=function(){this.jsonString=!0;var e={previous:this._previous,continue:this._continue,choice:this._choice,jump:this._jump};return delete this.jsonString,e},t}(m.BaseCMI),D=function(e){function t(t){var n=this;return t&&void 0===t.mastery_override&&(t.mastery_override=!1),(n=e.call(this,a.scorm2004_errors,t)||this)._version="1.0",n._globalObjectives=[],n.cmi=new T,n.adl=new k,n.Initialize=n.lmsInitialize,n.Terminate=n.lmsFinish,n.GetValue=n.lmsGetValue,n.SetValue=n.lmsSetValue,n.Commit=n.lmsCommit,n.GetLastError=n.lmsGetLastError,n.GetErrorString=n.lmsGetErrorString,n.GetDiagnostic=n.lmsGetDiagnostic,n}return(0,r.__extends)(t,e),t.prototype.reset=function(e){var t,n;this.commonReset(e),null===(t=this.cmi)||void 0===t||t.reset(),null===(n=this.adl)||void 0===n||n.reset()},Object.defineProperty(t.prototype,"version",{get:function(){return this._version},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"globalObjectives",{get:function(){return this._globalObjectives},enumerable:!1,configurable:!0}),t.prototype.lmsInitialize=function(){return this.cmi.initialize(),this.initialize("Initialize")},t.prototype.lmsFinish=function(){var e=this;return(0,r.__awaiter)(e,void 0,void 0,(function(){return(0,r.__generator)(this,(function(e){switch(e.label){case 0:return[4,this.internalFinish()];case 1:return e.sent(),[2]}}))})),o.global_constants.SCORM_TRUE},t.prototype.internalFinish=function(){return(0,r.__awaiter)(this,void 0,void 0,(function(){var e,t,n,i,a,c,l,u,d,m,h;return(0,r.__generator)(this,(function(r){switch(r.label){case 0:return[4,this.terminate("Terminate",!0)];case 1:return(e=r.sent())===o.global_constants.SCORM_TRUE&&("_none_"!==this.adl.nav.request?(t={continue:"SequenceNext",previous:"SequencePrevious",choice:"SequenceChoice",jump:"SequenceJump",exit:"SequenceExit",exitAll:"SequenceExitAll",abandon:"SequenceAbandon",abandonAll:"SequenceAbandonAll"},n=this.adl.nav.request,i=new RegExp(s.scorm2004_regex.NAVEvent),a=n.match(i),c="",a&&((null===(u=a.groups)||void 0===u?void 0:u.choice_target)?(c=null===(d=a.groups)||void 0===d?void 0:d.choice_target,n="choice"):(null===(m=a.groups)||void 0===m?void 0:m.jump_target)&&(c=null===(h=a.groups)||void 0===h?void 0:h.jump_target,n="jump")),(l=t[n])&&this.processListeners(l,"adl.nav.request",c)):this.settings.autoProgress&&this.processListeners("SequenceNext")),[2,e]}}))}))},t.prototype.lmsGetValue=function(e){var t="^adl\\.nav\\.request_valid\\.(choice|jump)\\.{target=\\S{0,}([a-zA-Z0-9-_]+)}$";if((0,d.stringMatches)(e,t)){var n=e.match(t),r=n[1],i=n[2].replace("{target=","").replace("}","");if("choice"===r||"jump"===r)return this.settings.scoItemIdValidator?String(this.settings.scoItemIdValidator(i)):String(this.settings.scoItemIds.includes(i))}return this.getValue("GetValue",!0,e)},t.prototype.lmsSetValue=function(e,t){return this.setValue("SetValue","Commit",!0,e,t)},t.prototype.lmsCommit=function(){var e=this;return this.settings.asyncCommit?this.scheduleCommit(500,"LMSCommit"):(0,r.__awaiter)(e,void 0,void 0,(function(){return(0,r.__generator)(this,(function(e){switch(e.label){case 0:return[4,this.commit("LMSCommit",!1)];case 1:return e.sent(),[2]}}))})),o.global_constants.SCORM_TRUE},t.prototype.lmsGetLastError=function(){return this.getLastError("GetLastError")},t.prototype.lmsGetErrorString=function(e){return this.getErrorString("GetErrorString",e)},t.prototype.lmsGetDiagnostic=function(e){return this.getDiagnostic("GetDiagnostic",e)},t.prototype.setCMIValue=function(e,t){if((0,d.stringMatches)(e,"cmi\\.objectives\\.\\d+")){var n,r=e.split("."),i=Number(r[2]),o="cmi.objectives.".concat(i);if((0,d.stringMatches)(e,"cmi\\.objectives\\.\\d+\\.id"))n=t;else{var s=this.cmi.objectives.findObjectiveByIndex(i);n=s?s.id:void 0}if(n&&this.settings.globalObjectiveIds.includes(n)){var a=this._globalObjectives.findIndex((function(e){return e.id===n}));if(-1===a){a=this._globalObjectives.length;var c=new L;c.id=n,this._globalObjectives.push(c)}var l=e.replace(o,"_globalObjectives.".concat(a));this._commonSetCMIValue("SetGlobalObjectiveValue",!0,l,t)}}return this._commonSetCMIValue("SetValue",!0,e,t)},t.prototype.getChildElement=function(e,t,n){if((0,d.stringMatches)(e,"cmi\\.objectives\\.\\d+"))return new L;if(n){if((0,d.stringMatches)(e,"cmi\\.interactions\\.\\d+\\.correct_responses\\.\\d+"))return this.createCorrectResponsesObject(e,t);if((0,d.stringMatches)(e,"cmi\\.interactions\\.\\d+\\.objectives\\.\\d+"))return new E}else if((0,d.stringMatches)(e,"cmi\\.interactions\\.\\d+"))return new w;return(0,d.stringMatches)(e,"cmi\\.comments_from_learner\\.\\d+")?new I:(0,d.stringMatches)(e,"cmi\\.comments_from_lms\\.\\d+")?new I(!0):(0,d.stringMatches)(e,"adl\\.data\\.\\d+")?new P:null},t.prototype.createCorrectResponsesObject=function(e,t){var n=e.split("."),r=Number(n[2]),i=this.cmi.interactions.childArray[r];if(this.isInitialized())if(i.type){this.checkDuplicateChoiceResponse(i,t);var o=b[i.type];o?this.checkValidResponseType(o,t,i.type):this.throwSCORMError(a.scorm2004_errors.GENERAL_SET_FAILURE,"Incorrect Response Type: "+i.type)}else this.throwSCORMError(a.scorm2004_errors.DEPENDENCY_NOT_ESTABLISHED);return"0"===this.lastErrorCode?new C:null},t.prototype.checkValidResponseType=function(e,t,n){var r=[];(null==e?void 0:e.delimiter)?r=String(t).split(e.delimiter):r[0]=t,r.length>0&&r.length<=e.max?this.checkCorrectResponseValue(n,r,t):r.length>e.max&&this.throwSCORMError(a.scorm2004_errors.GENERAL_SET_FAILURE,"Data Model Element Pattern Too Long")},t.prototype.checkDuplicateChoiceResponse=function(e,t){var n=e.correct_responses._count;if("choice"===e.type)for(var r=0;r<n&&"0"===this.lastErrorCode;r++){e.correct_responses.childArray[r].pattern===t&&this.throwSCORMError(a.scorm2004_errors.GENERAL_SET_FAILURE)}},t.prototype.validateCorrectResponse=function(e,t){var n=e.split("."),r=Number(n[2]),i=Number(n[4]),o=this.cmi.interactions.childArray[r],s=o.correct_responses._count;this.checkDuplicateChoiceResponse(o,t);var c=b[o.type];void 0===c.limit||s<=c.limit?(this.checkValidResponseType(c,t,o.type),"0"===this.lastErrorCode&&(!c.duplicate||!this.checkDuplicatedPattern(o.correct_responses,i,t))||"0"===this.lastErrorCode&&""===t||"0"===this.lastErrorCode&&this.throwSCORMError(a.scorm2004_errors.GENERAL_SET_FAILURE,"Data Model Element Pattern Already Exists")):this.throwSCORMError(a.scorm2004_errors.GENERAL_SET_FAILURE,"Data Model Element Collection Limit Reached")},t.prototype.getCMIValue=function(e){return this._commonGetCMIValue("GetValue",!0,e)},t.prototype.getLmsErrorMessageDetails=function(e,t){var n="",r="";return e=String(e),o.scorm2004_constants.error_descriptions[e]&&(n=o.scorm2004_constants.error_descriptions[e].basicMessage,r=o.scorm2004_constants.error_descriptions[e].detailMessage),t?r:n},t.prototype.checkDuplicatedPattern=function(e,t,n){for(var r=!1,i=e._count,o=0;o<i&&!r;o++)o!==t&&e.childArray[o]===n&&(r=!0);return r},t.prototype.checkCorrectResponseValue=function(e,t,n){for(var r=b[e],i=new RegExp(r.format),o=0;o<t.length&&"0"===this.lastErrorCode;o++)if(e.match("^(fill-in|long-fill-in|matching|performance|sequencing)$")&&(t[o]=this.removeCorrectResponsePrefixes(t[o])),null==r?void 0:r.delimiter2){var s=t[o].split(r.delimiter2);if(2===s.length)(c=s[0].match(i))&&r.format2&&s[1].match(new RegExp(r.format2))||this.throwSCORMError(a.scorm2004_errors.TYPE_MISMATCH);else this.throwSCORMError(a.scorm2004_errors.TYPE_MISMATCH)}else{var c;if(!(c=t[o].match(i))&&""!==n||!c&&"true-false"===e)this.throwSCORMError(a.scorm2004_errors.TYPE_MISMATCH);else if("numeric"===e&&t.length>1)Number(t[0])>Number(t[1])&&this.throwSCORMError(a.scorm2004_errors.TYPE_MISMATCH);else if(""!==t[o]&&r.unique)for(var l=0;l<o&&"0"===this.lastErrorCode;l++)t[o]===t[l]&&this.throwSCORMError(a.scorm2004_errors.TYPE_MISMATCH)}},t.prototype.removeCorrectResponsePrefixes=function(e){for(var t=!1,n=!1,r=!1,i=new RegExp("^({(lang|case_matters|order_matters)=([^}]+)})"),o=e.match(i),c=null;o;){switch(o[2]){case"lang":if(c=e.match(s.scorm2004_regex.CMILangcr)){var l=c[3];void 0!==l&&l.length>0&&(N.includes(l.toLowerCase())||this.throwSCORMError(a.scorm2004_errors.TYPE_MISMATCH))}r=!0;break;case"case_matters":r||t||n||"true"!==o[3]&&"false"!==o[3]&&this.throwSCORMError(a.scorm2004_errors.TYPE_MISMATCH),n=!0;break;case"order_matters":n||r||t||"true"!==o[3]&&"false"!==o[3]&&this.throwSCORMError(a.scorm2004_errors.TYPE_MISMATCH),t=!0}o=(e=e.substring(o[1].length)).match(i)}return e},t.prototype.replaceWithAnotherScormAPI=function(e){this.cmi=e.cmi,this.adl=e.adl},t.prototype.renderCommitCMI=function(e){var t=this.renderCMIToJSONObject();e&&(t.cmi.total_time=this.cmi.getCurrentTotalTime());var n=[],r=d.flatten(t);switch(this.settings.dataCommitFormat){case"flattened":return d.flatten(t);case"params":for(var i in r)({}).hasOwnProperty.call(r,i)&&n.push("".concat(i,"=").concat(r[i]));return n;default:return t}},t.prototype.renderCommitObject=function(e){var t=this.renderCommitCMI(e),n=this.cmi.getCurrentTotalTime(),r=d.getDurationAsSeconds(n,s.scorm2004_regex.CMITimespan),i=A.CompletionStatus.unknown,o=A.SuccessStatus.unknown;this.cmi.completion_status&&("completed"===this.cmi.completion_status?i=A.CompletionStatus.completed:"incomplete"===this.cmi.completion_status&&(i=A.CompletionStatus.incomplete)),this.cmi.success_status&&("passed"===this.cmi.success_status?o=A.SuccessStatus.passed:"failed"===this.cmi.success_status&&(o=A.SuccessStatus.failed));var a=this.cmi.score,c=null;a&&(c={},Number.isNaN(Number.parseFloat(a.raw))||(c.raw=Number.parseFloat(a.raw)),Number.isNaN(Number.parseFloat(a.min))||(c.min=Number.parseFloat(a.min)),Number.isNaN(Number.parseFloat(a.max))||(c.max=Number.parseFloat(a.max)),Number.isNaN(Number.parseFloat(a.scaled))||(c.scaled=Number.parseFloat(a.scaled)));var l={completionStatus:i,successStatus:o,totalTimeSeconds:r,runtimeData:t};return c&&(l.score=c),l},t.prototype.storeData=function(e){return(0,r.__awaiter)(this,void 0,void 0,(function(){var t,n,i,s,a,c;return(0,r.__generator)(this,(function(r){switch(r.label){case 0:return e&&"normal"===this.cmi.mode&&"credit"===this.cmi.credit&&(this.cmi.completion_threshold&&this.cmi.progress_measure&&(this.cmi.progress_measure>=this.cmi.completion_threshold?this.cmi.completion_status="completed":this.cmi.completion_status="incomplete"),this.cmi.scaled_passing_score&&this.cmi.score.scaled&&(this.cmi.score.scaled>=this.cmi.scaled_passing_score?this.cmi.success_status="passed":this.cmi.success_status="failed")),t=!1,this.adl.nav.request!==(null===(c=null===(a=null===(s=this.startingData)||void 0===s?void 0:s.adl)||void 0===a?void 0:a.nav)||void 0===c?void 0:c.request)&&"_none_"!==this.adl.nav.request&&(t=!0),n=this.getCommitObject(e),"string"!=typeof this.settings.lmsCommitUrl?[3,2]:[4,this.processHttpRequest(this.settings.lmsCommitUrl,n,e)];case 1:return i=r.sent(),t&&void 0!==i.navRequest&&""!==i.navRequest&&Function('"use strict";(() => { '.concat(i.navRequest," })()"))(),[2,i];case 2:return[2,{result:o.global_constants.SCORM_TRUE,errorCode:0}]}}))}))},t}(i.default)},589:function(e,t,n){n.r(t),n.d(t,{CMIArray:function(){return a}});var r=n(635),i=n(319),o=n(784),s=n(797),a=function(e){function t(t){var n=e.call(this)||this;return n.__children=t.children,n._errorCode=t.errorCode||s.scorm12_errors.GENERAL,n._errorClass=t.errorClass||o.BaseScormValidationError,n.childArray=[],n}return(0,r.__extends)(t,e),t.prototype.reset=function(e){if(void 0===e&&(e=!1),this._initialized=!1,e)this.childArray=[];else for(var t=0;t<this.childArray.length;t++)this.childArray[t].reset()},Object.defineProperty(t.prototype,"_children",{get:function(){return this.__children},set:function(e){throw new this._errorClass(this._errorCode)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"_count",{get:function(){return this.childArray.length},set:function(e){throw new this._errorClass(this._errorCode)},enumerable:!1,configurable:!0}),t.prototype.toJSON=function(){this.jsonString=!0;for(var e={},t=0;t<this.childArray.length;t++)e[t+""]=this.childArray[t];return delete this.jsonString,e},t}(i.BaseCMI)},319:function(e,t,n){n.r(t),n.d(t,{BaseCMI:function(){return i},BaseRootCMI:function(){return o}});var r=n(635),i=function(){function e(){this.jsonString=!1,this._initialized=!1}return Object.defineProperty(e.prototype,"initialized",{get:function(){return this._initialized},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"start_time",{get:function(){return this._start_time},enumerable:!1,configurable:!0}),e.prototype.initialize=function(){this._initialized=!0},e.prototype.setStartTime=function(){this._start_time=(new Date).getTime()},e}(),o=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return(0,r.__extends)(t,e),t}(i)},434:function(e,t,n){n.r(t),n.d(t,{CMIScore:function(){return l}});var r=n(635),i=n(340),o=n(417),s=n(319),a=n(449),c=n(797),l=function(e){function t(t){var n=e.call(this)||this;return n._raw="",n._min="",n.__children=t.score_children||i.scorm12_constants.score_children,n.__score_range=!!t.score_range&&o.scorm12_regex.score_range,n._max=t.max||""===t.max?t.max:"100",n.__invalid_error_code=t.invalidErrorCode||c.scorm12_errors.INVALID_SET_VALUE,n.__invalid_type_code=t.invalidTypeCode||c.scorm12_errors.TYPE_MISMATCH,n.__invalid_range_code=t.invalidRangeCode||c.scorm12_errors.VALUE_OUT_OF_RANGE,n.__decimal_regex=t.decimalRegex||o.scorm12_regex.CMIDecimal,n.__error_class=t.errorClass,n}return(0,r.__extends)(t,e),t.prototype.reset=function(){this._initialized=!1},Object.defineProperty(t.prototype,"_children",{get:function(){return this.__children},set:function(e){throw new this.__error_class(this.__invalid_error_code)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"raw",{get:function(){return this._raw},set:function(e){!(0,a.checkValidFormat)(e,this.__decimal_regex,this.__invalid_type_code,this.__error_class)||this.__score_range&&!(0,a.checkValidRange)(e,this.__score_range,this.__invalid_range_code,this.__error_class)||(this._raw=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"min",{get:function(){return this._min},set:function(e){!(0,a.checkValidFormat)(e,this.__decimal_regex,this.__invalid_type_code,this.__error_class)||this.__score_range&&!(0,a.checkValidRange)(e,this.__score_range,this.__invalid_range_code,this.__error_class)||(this._min=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"max",{get:function(){return this._max},set:function(e){!(0,a.checkValidFormat)(e,this.__decimal_regex,this.__invalid_type_code,this.__error_class)||this.__score_range&&!(0,a.checkValidRange)(e,this.__score_range,this.__invalid_range_code,this.__error_class)||(this._max=e)},enumerable:!1,configurable:!0}),t.prototype.toJSON=function(){this.jsonString=!0;var e={raw:this.raw,min:this.min,max:this.max};return delete this.jsonString,e},t}(s.BaseCMI)},449:function(e,t,n){function r(e,t,n,r,i){if("string"!=typeof e)return!1;var o=new RegExp(t),s=e.match(o);if(i&&""===e)return!0;if(void 0===e||!s||""===s[0])throw new r(n);return!0}function i(e,t,n,r){var i=t.split("#");if((e*=1)>=i[0]){if("*"===i[1]||e<=i[1])return!0;throw new r(n)}throw new r(n)}n.r(t),n.d(t,{checkValidFormat:function(){return r},checkValidRange:function(){return i}})},989:function(e,t,n){n.r(t),n.d(t,{CMI:function(){return g}});var r=n(635),i=n(340),o=n(797),s=n(417),a=n(179),c=n(319),l=n(915),u=n(434),d=n(864),m=function(e){function t(){var t=e.call(this)||this;return t.__children=i.scorm12_constants.core_children,t._student_id="",t._student_name="",t._lesson_location="",t._credit="",t._lesson_status="not attempted",t._entry="",t._total_time="",t._lesson_mode="normal",t._exit="",t._session_time="00:00:00",t._suspend_data="",t.score=new u.CMIScore({score_children:i.scorm12_constants.score_children,score_range:s.scorm12_regex.score_range,invalidErrorCode:o.scorm12_errors.INVALID_SET_VALUE,invalidTypeCode:o.scorm12_errors.TYPE_MISMATCH,invalidRangeCode:o.scorm12_errors.VALUE_OUT_OF_RANGE,errorClass:a.Scorm12ValidationError}),t}return(0,r.__extends)(t,e),t.prototype.initialize=function(){var t;e.prototype.initialize.call(this),null===(t=this.score)||void 0===t||t.initialize()},t.prototype.reset=function(){var e;this._initialized=!1,this._exit="",this._entry="",this._session_time="00:00:00",null===(e=this.score)||void 0===e||e.reset()},Object.defineProperty(t.prototype,"_children",{get:function(){return this.__children},set:function(e){throw new a.Scorm12ValidationError(o.scorm12_errors.INVALID_SET_VALUE)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"student_id",{get:function(){return this._student_id},set:function(e){if(this.initialized)throw new a.Scorm12ValidationError(o.scorm12_errors.READ_ONLY_ELEMENT);this._student_id=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"student_name",{get:function(){return this._student_name},set:function(e){if(this.initialized)throw new a.Scorm12ValidationError(o.scorm12_errors.READ_ONLY_ELEMENT);this._student_name=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"lesson_location",{get:function(){return this._lesson_location},set:function(e){(0,l.check12ValidFormat)(e,s.scorm12_regex.CMIString256,!0)&&(this._lesson_location=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"credit",{get:function(){return this._credit},set:function(e){if(this.initialized)throw new a.Scorm12ValidationError(o.scorm12_errors.READ_ONLY_ELEMENT);this._credit=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"lesson_status",{get:function(){return this._lesson_status},set:function(e){this.initialized?(0,l.check12ValidFormat)(e,s.scorm12_regex.CMIStatus)&&(this._lesson_status=e):(0,l.check12ValidFormat)(e,s.scorm12_regex.CMIStatus2)&&(this._lesson_status=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"entry",{get:function(){return this._entry},set:function(e){if(this.initialized)throw new a.Scorm12ValidationError(o.scorm12_errors.READ_ONLY_ELEMENT);this._entry=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"total_time",{get:function(){return this._total_time},set:function(e){if(this.initialized)throw new a.Scorm12ValidationError(o.scorm12_errors.READ_ONLY_ELEMENT);this._total_time=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"lesson_mode",{get:function(){return this._lesson_mode},set:function(e){if(this.initialized)throw new a.Scorm12ValidationError(o.scorm12_errors.READ_ONLY_ELEMENT);this._lesson_mode=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"exit",{get:function(){if(!this.jsonString)throw new a.Scorm12ValidationError(o.scorm12_errors.WRITE_ONLY_ELEMENT);return this._exit},set:function(e){(0,l.check12ValidFormat)(e,s.scorm12_regex.CMIExit,!0)&&(this._exit=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"session_time",{get:function(){if(!this.jsonString)throw new a.Scorm12ValidationError(o.scorm12_errors.WRITE_ONLY_ELEMENT);return this._session_time},set:function(e){(0,l.check12ValidFormat)(e,s.scorm12_regex.CMITimespan)&&(this._session_time=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"suspend_data",{get:function(){return this._suspend_data},set:function(e){(0,l.check12ValidFormat)(e,s.scorm12_regex.CMIString4096,!0)&&(this._suspend_data=e)},enumerable:!1,configurable:!0}),t.prototype.getCurrentTotalTime=function(e){var t=this._session_time,n=e;if(null!=n){var r=(new Date).getTime()-n;t=d.getSecondsAsHHMMSS(r/1e3)}return d.addHHMMSSTimeStrings(this._total_time,t,new RegExp(s.scorm12_regex.CMITimespan))},t.prototype.toJSON=function(){this.jsonString=!0;var e={student_id:this.student_id,student_name:this.student_name,lesson_location:this.lesson_location,credit:this.credit,lesson_status:this.lesson_status,entry:this.entry,lesson_mode:this.lesson_mode,exit:this.exit,session_time:this.session_time,score:this.score};return delete this.jsonString,e},t}(c.BaseCMI),h=n(176),p=n(532),f=n(181),_=n(833),g=function(e){function t(t,n,r){var o=e.call(this)||this;return o.__children="",o.__version="3.4",o._launch_data="",o._comments="",o._comments_from_lms="",r&&o.initialize(),o.__children=t||i.scorm12_constants.cmi_children,o.core=new m,o.objectives=new h.CMIObjectives,o.student_data=n||new p.CMIStudentData,o.student_preference=new f.CMIStudentPreference,o.interactions=new _.CMIInteractions,o}return(0,r.__extends)(t,e),t.prototype.reset=function(){var e,t,n;this._initialized=!1,this._launch_data="",this._comments="",null===(e=this.core)||void 0===e||e.reset(),this.objectives=new h.CMIObjectives,this.interactions=new _.CMIInteractions,null===(t=this.student_data)||void 0===t||t.reset(),null===(n=this.student_preference)||void 0===n||n.reset()},t.prototype.initialize=function(){var t,n,r,i,o;e.prototype.initialize.call(this),null===(t=this.core)||void 0===t||t.initialize(),null===(n=this.objectives)||void 0===n||n.initialize(),null===(r=this.student_data)||void 0===r||r.initialize(),null===(i=this.student_preference)||void 0===i||i.initialize(),null===(o=this.interactions)||void 0===o||o.initialize()},t.prototype.toJSON=function(){this.jsonString=!0;var e={suspend_data:this.suspend_data,launch_data:this.launch_data,comments:this.comments,comments_from_lms:this.comments_from_lms,core:this.core,objectives:this.objectives,student_data:this.student_data,student_preference:this.student_preference,interactions:this.interactions};return delete this.jsonString,e},Object.defineProperty(t.prototype,"_version",{get:function(){return this.__version},set:function(e){throw new a.Scorm12ValidationError(o.scorm12_errors.INVALID_SET_VALUE)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"_children",{get:function(){return this.__children},set:function(e){throw new a.Scorm12ValidationError(o.scorm12_errors.INVALID_SET_VALUE)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"suspend_data",{get:function(){var e;return null===(e=this.core)||void 0===e?void 0:e.suspend_data},set:function(e){this.core&&(this.core.suspend_data=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"launch_data",{get:function(){return this._launch_data},set:function(e){if(this.initialized)throw new a.Scorm12ValidationError(o.scorm12_errors.READ_ONLY_ELEMENT);this._launch_data=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"comments",{get:function(){return this._comments},set:function(e){(0,l.check12ValidFormat)(e,s.scorm12_regex.CMIString4096,!0)&&(this._comments=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"comments_from_lms",{get:function(){return this._comments_from_lms},set:function(e){if(this.initialized)throw new a.Scorm12ValidationError(o.scorm12_errors.READ_ONLY_ELEMENT);this._comments_from_lms=e},enumerable:!1,configurable:!0}),t.prototype.getCurrentTotalTime=function(){return this.core.getCurrentTotalTime(this.start_time)},t}(c.BaseRootCMI)},833:function(e,t,n){n.r(t),n.d(t,{CMIInteractions:function(){return d},CMIInteractionsCorrectResponsesObject:function(){return p},CMIInteractionsObject:function(){return m},CMIInteractionsObjectivesObject:function(){return h}});var r=n(635),i=n(589),o=n(340),s=n(797),a=n(179),c=n(319),l=n(915),u=n(417),d=function(e){function t(){return e.call(this,{children:o.scorm12_constants.interactions_children,errorCode:s.scorm12_errors.INVALID_SET_VALUE,errorClass:a.Scorm12ValidationError})||this}return(0,r.__extends)(t,e),t}(i.CMIArray),m=function(e){function t(){var t=e.call(this)||this;return t._id="",t._time="",t._type="",t._weighting="",t._student_response="",t._result="",t._latency="",t.objectives=new i.CMIArray({errorCode:s.scorm12_errors.INVALID_SET_VALUE,errorClass:a.Scorm12ValidationError,children:o.scorm12_constants.objectives_children}),t.correct_responses=new i.CMIArray({errorCode:s.scorm12_errors.INVALID_SET_VALUE,errorClass:a.Scorm12ValidationError,children:o.scorm12_constants.correct_responses_children}),t}return(0,r.__extends)(t,e),t.prototype.initialize=function(){var t,n;e.prototype.initialize.call(this),null===(t=this.objectives)||void 0===t||t.initialize(),null===(n=this.correct_responses)||void 0===n||n.initialize()},t.prototype.reset=function(){var e,t;this._initialized=!1,this._id="",this._time="",this._type="",this._weighting="",this._student_response="",this._result="",this._latency="",null===(e=this.objectives)||void 0===e||e.reset(),null===(t=this.correct_responses)||void 0===t||t.reset()},Object.defineProperty(t.prototype,"id",{get:function(){if(!this.jsonString)throw new a.Scorm12ValidationError(s.scorm12_errors.WRITE_ONLY_ELEMENT);return this._id},set:function(e){(0,l.check12ValidFormat)(e,u.scorm12_regex.CMIIdentifier)&&(this._id=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"time",{get:function(){if(!this.jsonString)throw new a.Scorm12ValidationError(s.scorm12_errors.WRITE_ONLY_ELEMENT);return this._time},set:function(e){(0,l.check12ValidFormat)(e,u.scorm12_regex.CMITime)&&(this._time=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"type",{get:function(){if(!this.jsonString)throw new a.Scorm12ValidationError(s.scorm12_errors.WRITE_ONLY_ELEMENT);return this._type},set:function(e){(0,l.check12ValidFormat)(e,u.scorm12_regex.CMIType)&&(this._type=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"weighting",{get:function(){if(!this.jsonString)throw new a.Scorm12ValidationError(s.scorm12_errors.WRITE_ONLY_ELEMENT);return this._weighting},set:function(e){(0,l.check12ValidFormat)(e,u.scorm12_regex.CMIDecimal)&&(0,l.check12ValidRange)(e,u.scorm12_regex.weighting_range)&&(this._weighting=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"student_response",{get:function(){if(!this.jsonString)throw new a.Scorm12ValidationError(s.scorm12_errors.WRITE_ONLY_ELEMENT);return this._student_response},set:function(e){(0,l.check12ValidFormat)(e,u.scorm12_regex.CMIFeedback,!0)&&(this._student_response=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"result",{get:function(){if(!this.jsonString)throw new a.Scorm12ValidationError(s.scorm12_errors.WRITE_ONLY_ELEMENT);return this._result},set:function(e){(0,l.check12ValidFormat)(e,u.scorm12_regex.CMIResult)&&(this._result=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"latency",{get:function(){if(!this.jsonString)throw new a.Scorm12ValidationError(s.scorm12_errors.WRITE_ONLY_ELEMENT);return this._latency},set:function(e){(0,l.check12ValidFormat)(e,u.scorm12_regex.CMITimespan)&&(this._latency=e)},enumerable:!1,configurable:!0}),t.prototype.toJSON=function(){this.jsonString=!0;var e={id:this.id,time:this.time,type:this.type,weighting:this.weighting,student_response:this.student_response,result:this.result,latency:this.latency,objectives:this.objectives,correct_responses:this.correct_responses};return delete this.jsonString,e},t}(c.BaseCMI),h=function(e){function t(){var t=e.call(this)||this;return t._id="",t}return(0,r.__extends)(t,e),t.prototype.reset=function(){this._initialized=!1,this._id=""},Object.defineProperty(t.prototype,"id",{get:function(){return this._id},set:function(e){(0,l.check12ValidFormat)(e,u.scorm12_regex.CMIIdentifier)&&(this._id=e)},enumerable:!1,configurable:!0}),t.prototype.toJSON=function(){this.jsonString=!0;var e={id:this.id};return delete this.jsonString,e},t}(c.BaseCMI),p=function(e){function t(){var t=e.call(this)||this;return t._pattern="",t}return(0,r.__extends)(t,e),t.prototype.reset=function(){this._initialized=!1,this._pattern=""},Object.defineProperty(t.prototype,"pattern",{get:function(){if(!this.jsonString)throw new a.Scorm12ValidationError(s.scorm12_errors.WRITE_ONLY_ELEMENT);return this._pattern},set:function(e){(0,l.check12ValidFormat)(e,u.scorm12_regex.CMIFeedback,!0)&&(this._pattern=e)},enumerable:!1,configurable:!0}),t.prototype.toJSON=function(){this.jsonString=!0;var e={pattern:this._pattern};return delete this.jsonString,e},t}(c.BaseCMI)},331:function(e,t,n){n.r(t),n.d(t,{NAV:function(){return a}});var r=n(635),i=n(319),o=n(915),s=n(417),a=function(e){function t(){var t=e.call(this)||this;return t._event="",t}return(0,r.__extends)(t,e),t.prototype.reset=function(){this._event="",this._initialized=!1},Object.defineProperty(t.prototype,"event",{get:function(){return this._event},set:function(e){(0,o.check12ValidFormat)(e,s.scorm12_regex.NAVEvent)&&(this._event=e)},enumerable:!1,configurable:!0}),t.prototype.toJSON=function(){this.jsonString=!0;var e={event:this.event};return delete this.jsonString,e},t}(i.BaseCMI)},176:function(e,t,n){n.r(t),n.d(t,{CMIObjectives:function(){return d},CMIObjectivesObject:function(){return m}});var r=n(635),i=n(319),o=n(434),s=n(340),a=n(417),c=n(797),l=n(179),u=n(915),d=function(e){function t(){return e.call(this,{children:s.scorm12_constants.objectives_children,errorCode:c.scorm12_errors.INVALID_SET_VALUE,errorClass:l.Scorm12ValidationError})||this}return(0,r.__extends)(t,e),t}(n(589).CMIArray),m=function(e){function t(){var t=e.call(this)||this;return t._id="",t._status="",t.score=new o.CMIScore({score_children:s.scorm12_constants.score_children,score_range:a.scorm12_regex.score_range,invalidErrorCode:c.scorm12_errors.INVALID_SET_VALUE,invalidTypeCode:c.scorm12_errors.TYPE_MISMATCH,invalidRangeCode:c.scorm12_errors.VALUE_OUT_OF_RANGE,errorClass:l.Scorm12ValidationError}),t}return(0,r.__extends)(t,e),t.prototype.reset=function(){var e;this._initialized=!1,this._id="",this._status="",null===(e=this.score)||void 0===e||e.reset()},Object.defineProperty(t.prototype,"id",{get:function(){return this._id},set:function(e){(0,u.check12ValidFormat)(e,a.scorm12_regex.CMIIdentifier)&&(this._id=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"status",{get:function(){return this._status},set:function(e){(0,u.check12ValidFormat)(e,a.scorm12_regex.CMIStatus2)&&(this._status=e)},enumerable:!1,configurable:!0}),t.prototype.toJSON=function(){this.jsonString=!0;var e={id:this.id,status:this.status,score:this.score};return delete this.jsonString,e},t}(i.BaseCMI)},532:function(e,t,n){n.r(t),n.d(t,{CMIStudentData:function(){return c}});var r=n(635),i=n(319),o=n(340),s=n(179),a=n(797),c=function(e){function t(t){var n=e.call(this)||this;return n._mastery_score="",n._max_time_allowed="",n._time_limit_action="",n.__children=t||o.scorm12_constants.student_data_children,n}return(0,r.__extends)(t,e),t.prototype.reset=function(){this._initialized=!1},Object.defineProperty(t.prototype,"_children",{get:function(){return this.__children},set:function(e){throw new s.Scorm12ValidationError(a.scorm12_errors.INVALID_SET_VALUE)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"mastery_score",{get:function(){return this._mastery_score},set:function(e){if(this.initialized)throw new s.Scorm12ValidationError(a.scorm12_errors.READ_ONLY_ELEMENT);this._mastery_score=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"max_time_allowed",{get:function(){return this._max_time_allowed},set:function(e){if(this.initialized)throw new s.Scorm12ValidationError(a.scorm12_errors.READ_ONLY_ELEMENT);this._max_time_allowed=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"time_limit_action",{get:function(){return this._time_limit_action},set:function(e){if(this.initialized)throw new s.Scorm12ValidationError(a.scorm12_errors.READ_ONLY_ELEMENT);this._time_limit_action=e},enumerable:!1,configurable:!0}),t.prototype.toJSON=function(){this.jsonString=!0;var e={mastery_score:this.mastery_score,max_time_allowed:this.max_time_allowed,time_limit_action:this.time_limit_action};return delete this.jsonString,e},t}(i.BaseCMI)},181:function(e,t,n){n.r(t),n.d(t,{CMIStudentPreference:function(){return u}});var r=n(635),i=n(319),o=n(340),s=n(179),a=n(915),c=n(417),l=n(797),u=function(e){function t(t){var n=e.call(this)||this;return n._audio="",n._language="",n._speed="",n._text="",n.__children=t||o.scorm12_constants.student_preference_children,n}return(0,r.__extends)(t,e),t.prototype.reset=function(){this._initialized=!1},Object.defineProperty(t.prototype,"_children",{get:function(){return this.__children},set:function(e){throw new s.Scorm12ValidationError(l.scorm12_errors.INVALID_SET_VALUE)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"audio",{get:function(){return this._audio},set:function(e){(0,a.check12ValidFormat)(e,c.scorm12_regex.CMISInteger)&&(0,a.check12ValidRange)(e,c.scorm12_regex.audio_range)&&(this._audio=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"language",{get:function(){return this._language},set:function(e){(0,a.check12ValidFormat)(e,c.scorm12_regex.CMIString256)&&(this._language=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"speed",{get:function(){return this._speed},set:function(e){(0,a.check12ValidFormat)(e,c.scorm12_regex.CMISInteger)&&(0,a.check12ValidRange)(e,c.scorm12_regex.speed_range)&&(this._speed=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"text",{get:function(){return this._text},set:function(e){(0,a.check12ValidFormat)(e,c.scorm12_regex.CMISInteger)&&(0,a.check12ValidRange)(e,c.scorm12_regex.text_range)&&(this._text=e)},enumerable:!1,configurable:!0}),t.prototype.toJSON=function(){this.jsonString=!0;var e={audio:this.audio,language:this.language,speed:this.speed,text:this.text};return delete this.jsonString,e},t}(i.BaseCMI)},915:function(e,t,n){n.r(t),n.d(t,{check12ValidFormat:function(){return s},check12ValidRange:function(){return a}});var r=n(449),i=n(797),o=n(179);function s(e,t,n){return(0,r.checkValidFormat)(e,t,i.scorm12_errors.TYPE_MISMATCH,o.Scorm12ValidationError,n)}function a(e,t,n){if(!n&&""===e)throw new o.Scorm12ValidationError(i.scorm12_errors.VALUE_OUT_OF_RANGE);return(0,r.checkValidRange)(e,t,i.scorm12_errors.VALUE_OUT_OF_RANGE,o.Scorm12ValidationError)}},340:function(e,t,n){n.r(t),n.d(t,{aicc_constants:function(){return s},global_constants:function(){return i},scorm12_constants:function(){return o},scorm2004_constants:function(){return a}});var r=n(635),i={SCORM_TRUE:"true",SCORM_FALSE:"false",STATE_NOT_INITIALIZED:0,STATE_INITIALIZED:1,STATE_TERMINATED:2},o={cmi_children:"core,suspend_data,launch_data,comments,objectives,student_data,student_preference,interactions",core_children:"student_id,student_name,lesson_location,credit,lesson_status,entry,score,total_time,lesson_mode,exit,session_time",score_children:"raw,min,max",comments_children:"content,location,time",objectives_children:"id,score,status",correct_responses_children:"pattern",student_data_children:"mastery_score,max_time_allowed,time_limit_action",student_preference_children:"audio,language,speed,text",interactions_children:"id,objectives,time,type,correct_responses,weighting,student_response,result,latency",error_descriptions:{101:{basicMessage:"General Exception",detailMessage:"No specific error code exists to describe the error. Use LMSGetDiagnostic for more information"},201:{basicMessage:"Invalid argument error",detailMessage:"Indicates that an argument represents an invalid data model element or is otherwise incorrect."},202:{basicMessage:"Element cannot have children",detailMessage:'Indicates that LMSGetValue was called with a data model element name that ends in "_children" for a data model element that does not support the "_children" suffix.'},203:{basicMessage:"Element not an array - cannot have count",detailMessage:'Indicates that LMSGetValue was called with a data model element name that ends in "_count" for a data model element that does not support the "_count" suffix.'},301:{basicMessage:"Not initialized",detailMessage:"Indicates that an API call was made before the call to lmsInitialize."},401:{basicMessage:"Not implemented error",detailMessage:"The data model element indicated in a call to LMSGetValue or LMSSetValue is valid, but was not implemented by this LMS. SCORM 1.2 defines a set of data model elements as being optional for an LMS to implement."},402:{basicMessage:"Invalid set value, element is a keyword",detailMessage:'Indicates that LMSSetValue was called on a data model element that represents a keyword (elements that end in "_children" and "_count").'},403:{basicMessage:"Element is read only",detailMessage:"LMSSetValue was called with a data model element that can only be read."},404:{basicMessage:"Element is write only",detailMessage:"LMSGetValue was called on a data model element that can only be written to."},405:{basicMessage:"Incorrect Data Type",detailMessage:"LMSSetValue was called with a value that is not consistent with the data format of the supplied data model element."},407:{basicMessage:"Element Value Out Of Range",detailMessage:"The numeric value supplied to a LMSSetValue call is outside of the numeric range allowed for the supplied data model element."},408:{basicMessage:"Data Model Dependency Not Established",detailMessage:"Some data model elements cannot be set until another data model element was set. This error condition indicates that the prerequisite element was not set before the dependent element."}}},s=(0,r.__assign)((0,r.__assign)({},o),{cmi_children:"core,suspend_data,launch_data,comments,objectives,student_data,student_preference,interactions,evaluation",student_preference_children:"audio,language,lesson_type,speed,text,text_color,text_location,text_size,video,windows",student_data_children:"attempt_number,tries,mastery_score,max_time_allowed,time_limit_action",student_demographics_children:"city,class,company,country,experience,familiar_name,instructor_name,title,native_language,state,street_address,telephone,years_experience",tries_children:"time,status,score",attempt_records_children:"score,lesson_status",paths_children:"location_id,date,time,status,why_left,time_in_element"}),a={cmi_children:"_version,comments_from_learner,comments_from_lms,completion_status,credit,entry,exit,interactions,launch_data,learner_id,learner_name,learner_preference,location,max_time_allowed,mode,objectives,progress_measure,scaled_passing_score,score,session_time,success_status,suspend_data,time_limit_action,total_time",comments_children:"comment,timestamp,location",score_children:"max,raw,scaled,min",objectives_children:"progress_measure,completion_status,success_status,description,score,id",correct_responses_children:"pattern",student_data_children:"mastery_score,max_time_allowed,time_limit_action",student_preference_children:"audio_level,audio_captioning,delivery_speed,language",interactions_children:"id,type,objectives,timestamp,correct_responses,weighting,learner_response,result,latency,description",adl_data_children:"id,store",error_descriptions:{0:{basicMessage:"No Error",detailMessage:"No error occurred, the previous API call was successful."},101:{basicMessage:"General Exception",detailMessage:"No specific error code exists to describe the error. Use GetDiagnostic for more information."},102:{basicMessage:"General Initialization Failure",detailMessage:"Call to Initialize failed for an unknown reason."},103:{basicMessage:"Already Initialized",detailMessage:"Call to Initialize failed because Initialize was already called."},104:{basicMessage:"Content Instance Terminated",detailMessage:"Call to Initialize failed because Terminate was already called."},111:{basicMessage:"General Termination Failure",detailMessage:"Call to Terminate failed for an unknown reason."},112:{basicMessage:"Termination Before Initialization",detailMessage:"Call to Terminate failed because it was made before the call to Initialize."},113:{basicMessage:"Termination After Termination",detailMessage:"Call to Terminate failed because Terminate was already called."},122:{basicMessage:"Retrieve Data Before Initialization",detailMessage:"Call to GetValue failed because it was made before the call to Initialize."},123:{basicMessage:"Retrieve Data After Termination",detailMessage:"Call to GetValue failed because it was made after the call to Terminate."},132:{basicMessage:"Store Data Before Initialization",detailMessage:"Call to SetValue failed because it was made before the call to Initialize."},133:{basicMessage:"Store Data After Termination",detailMessage:"Call to SetValue failed because it was made after the call to Terminate."},142:{basicMessage:"Commit Before Initialization",detailMessage:"Call to Commit failed because it was made before the call to Initialize."},143:{basicMessage:"Commit After Termination",detailMessage:"Call to Commit failed because it was made after the call to Terminate."},201:{basicMessage:"General Argument Error",detailMessage:"An invalid argument was passed to an API method (usually indicates that Initialize, Commit or Terminate did not receive the expected empty string argument."},301:{basicMessage:"General Get Failure",detailMessage:"Indicates a failed GetValue call where no other specific error code is applicable. Use GetDiagnostic for more information."},351:{basicMessage:"General Set Failure",detailMessage:"Indicates a failed SetValue call where no other specific error code is applicable. Use GetDiagnostic for more information."},391:{basicMessage:"General Commit Failure",detailMessage:"Indicates a failed Commit call where no other specific error code is applicable. Use GetDiagnostic for more information."},401:{basicMessage:"Undefined Data Model Element",detailMessage:"The data model element name passed to GetValue or SetValue is not a valid SCORM data model element."},402:{basicMessage:"Unimplemented Data Model Element",detailMessage:"The data model element indicated in a call to GetValue or SetValue is valid, but was not implemented by this LMS. In SCORM 2004, this error would indicate an LMS that is not fully SCORM conformant."},403:{basicMessage:"Data Model Element Value Not Initialized",detailMessage:"Attempt to read a data model element that has not been initialized by the LMS or through a SetValue call. This error condition is often reached during normal execution of a SCO."},404:{basicMessage:"Data Model Element Is Read Only",detailMessage:"SetValue was called with a data model element that can only be read."},405:{basicMessage:"Data Model Element Is Write Only",detailMessage:"GetValue was called on a data model element that can only be written to."},406:{basicMessage:"Data Model Element Type Mismatch",detailMessage:"SetValue was called with a value that is not consistent with the data format of the supplied data model element."},407:{basicMessage:"Data Model Element Value Out Of Range",detailMessage:"The numeric value supplied to a SetValue call is outside of the numeric range allowed for the supplied data model element."},408:{basicMessage:"Data Model Dependency Not Established",detailMessage:"Some data model elements cannot be set until another data model element was set. This error condition indicates that the prerequisite element was not set before the dependent element."}}}},56:function(e,t,n){var r,i,o,s;n.r(t),n.d(t,{CompletionStatus:function(){return o},LogLevelEnum:function(){return s},NAVBoolean:function(){return r},SuccessStatus:function(){return i}}),function(e){e.unknown="unknown",e.true="true",e.false="false"}(r||(r={})),function(e){e.passed="passed",e.failed="failed",e.unknown="unknown"}(i||(i={})),function(e){e.completed="completed",e.incomplete="incomplete",e.unknown="unknown"}(o||(o={})),function(e){e[e._=0]="_",e[e.DEBUG=1]="DEBUG",e[e.INFO=2]="INFO",e[e.WARN=3]="WARN",e[e.ERROR=4]="ERROR",e[e.NONE=5]="NONE"}(s||(s={}))},797:function(e,t,n){n.r(t),n.d(t,{global_errors:function(){return i},scorm12_errors:function(){return o},scorm2004_errors:function(){return s}});var r=n(635),i={GENERAL:101,INITIALIZATION_FAILED:101,INITIALIZED:101,TERMINATED:101,TERMINATION_FAILURE:101,TERMINATION_BEFORE_INIT:101,MULTIPLE_TERMINATION:101,RETRIEVE_BEFORE_INIT:101,RETRIEVE_AFTER_TERM:101,STORE_BEFORE_INIT:101,STORE_AFTER_TERM:101,COMMIT_BEFORE_INIT:101,COMMIT_AFTER_TERM:101,ARGUMENT_ERROR:101,CHILDREN_ERROR:101,COUNT_ERROR:101,GENERAL_GET_FAILURE:101,GENERAL_SET_FAILURE:101,GENERAL_COMMIT_FAILURE:101,UNDEFINED_DATA_MODEL:101,UNIMPLEMENTED_ELEMENT:101,VALUE_NOT_INITIALIZED:101,INVALID_SET_VALUE:101,READ_ONLY_ELEMENT:101,WRITE_ONLY_ELEMENT:101,TYPE_MISMATCH:101,VALUE_OUT_OF_RANGE:101,DEPENDENCY_NOT_ESTABLISHED:101},o=(0,r.__assign)((0,r.__assign)({},i),{RETRIEVE_BEFORE_INIT:301,STORE_BEFORE_INIT:301,COMMIT_BEFORE_INIT:301,ARGUMENT_ERROR:201,CHILDREN_ERROR:202,COUNT_ERROR:203,UNDEFINED_DATA_MODEL:401,UNIMPLEMENTED_ELEMENT:401,VALUE_NOT_INITIALIZED:301,INVALID_SET_VALUE:402,READ_ONLY_ELEMENT:403,WRITE_ONLY_ELEMENT:404,TYPE_MISMATCH:405,VALUE_OUT_OF_RANGE:407,DEPENDENCY_NOT_ESTABLISHED:408}),s=(0,r.__assign)((0,r.__assign)({},i),{INITIALIZATION_FAILED:102,INITIALIZED:103,TERMINATED:104,TERMINATION_FAILURE:111,TERMINATION_BEFORE_INIT:112,MULTIPLE_TERMINATIONS:113,RETRIEVE_BEFORE_INIT:122,RETRIEVE_AFTER_TERM:123,STORE_BEFORE_INIT:132,STORE_AFTER_TERM:133,COMMIT_BEFORE_INIT:142,COMMIT_AFTER_TERM:143,ARGUMENT_ERROR:201,GENERAL_GET_FAILURE:301,GENERAL_SET_FAILURE:351,GENERAL_COMMIT_FAILURE:391,UNDEFINED_DATA_MODEL:401,UNIMPLEMENTED_ELEMENT:402,VALUE_NOT_INITIALIZED:403,READ_ONLY_ELEMENT:404,WRITE_ONLY_ELEMENT:405,TYPE_MISMATCH:406,VALUE_OUT_OF_RANGE:407,DEPENDENCY_NOT_ESTABLISHED:408})},417:function(e,t,n){n.r(t),n.d(t,{aicc_regex:function(){return o},scorm12_regex:function(){return i},scorm2004_regex:function(){return s}});var r=n(635),i={CMIString256:"^.{0,255}$",CMIString4096:"^.{0,4096}$",CMITime:"^(?:[01]\\d|2[0123]):(?:[012345]\\d):(?:[012345]\\d)$",CMITimespan:"^([0-9]{2,}):([0-9]{2}):([0-9]{2})(.[0-9]{1,2})?$",CMIInteger:"^\\d+$",CMISInteger:"^-?([0-9]+)$",CMIDecimal:"^-?([0-9]{0,3})(.[0-9]*)?$",CMIIdentifier:"^[\\u0021-\\u007E\\s]{0,255}$",CMIFeedback:"^.{0,255}$",CMIIndex:"[._](\\d+).",CMIStatus:"^(passed|completed|failed|incomplete|browsed)$",CMIStatus2:"^(passed|completed|failed|incomplete|browsed|not attempted)$",CMIExit:"^(time-out|suspend|logout|)$",CMIType:"^(true-false|choice|fill-in|matching|performance|sequencing|likert|numeric)$",CMIResult:"^(correct|wrong|unanticipated|neutral|([0-9]{0,3})?(\\.[0-9]*)?)$",NAVEvent:"^(previous|continue)$",score_range:"0#100",audio_range:"-1#100",speed_range:"-100#100",weighting_range:"-100#100",text_range:"-1#1"},o=(0,r.__assign)((0,r.__assign)({},i),{CMIIdentifier:"^\\w{1,255}$"}),s={CMIString200:"^[\\u0000-\\uFFFF]{0,200}$",CMIString250:"^[\\u0000-\\uFFFF]{0,250}$",CMIString1000:"^[\\u0000-\\uFFFF]{0,1000}$",CMIString4000:"^[\\u0000-\\uFFFF]{0,4000}$",CMIString64000:"^[\\u0000-\\uFFFF]{0,64000}$",CMILang:"^([a-zA-Z]{2,3}|i|x)(-[a-zA-Z0-9-]{2,8})?$|^$",CMILangString250:"^({lang=([a-zA-Z]{2,3}|i|x)(-[a-zA-Z0-9-]{2,8})?})?((?!{.*$).{0,250}$)?$",CMILangcr:"^(({lang=([a-zA-Z]{2,3}|i|x)?(-[a-zA-Z0-9-]{2,8})?}))(.*?)$",CMILangString250cr:"^(({lang=([a-zA-Z]{2,3}|i|x)?(-[a-zA-Z0-9-]{2,8})?})?(.{0,250})?)?$",CMILangString4000:"^({lang=([a-zA-Z]{2,3}|i|x)(-[a-zA-Z0-9-]{2,8})?})?((?!{.*$).{0,4000}$)?$",CMITime:"^(19[7-9]{1}[0-9]{1}|20[0-2]{1}[0-9]{1}|203[0-8]{1})((-(0[1-9]{1}|1[0-2]{1}))((-(0[1-9]{1}|[1-2]{1}[0-9]{1}|3[0-1]{1}))(T([0-1]{1}[0-9]{1}|2[0-3]{1})((:[0-5]{1}[0-9]{1})((:[0-5]{1}[0-9]{1})((\\.[0-9]{1,2})((Z|([+|-]([0-1]{1}[0-9]{1}|2[0-3]{1})))(:[0-5]{1}[0-9]{1})?)?)?)?)?)?)?)?$",CMITimespan:"^P(?:([.,\\d]+)Y)?(?:([.,\\d]+)M)?(?:([.,\\d]+)W)?(?:([.,\\d]+)D)?(?:T?(?:([.,\\d]+)H)?(?:([.,\\d]+)M)?(?:([.,\\d]+)S)?)?$",CMIInteger:"^\\d+$",CMISInteger:"^-?([0-9]+)$",CMIDecimal:"^-?([0-9]{1,5})(\\.[0-9]{1,18})?$",CMIIdentifier:"^\\S{1,250}[a-zA-Z0-9]$",CMIShortIdentifier:"^[\\w\\.\\-\\_]{1,250}$",CMILongIdentifier:"^(?:(?!urn:)\\S{1,4000}|urn:[A-Za-z0-9-]{1,31}:\\S{1,4000}|.{1,4000})$",CMIFeedback:"^.*$",CMIIndex:"[._](\\d+).",CMIIndexStore:".N(\\d+).",CMICStatus:"^(completed|incomplete|not attempted|unknown)$",CMISStatus:"^(passed|failed|unknown)$",CMIExit:"^(time-out|suspend|logout|normal)$",CMIType:"^(true-false|choice|fill-in|long-fill-in|matching|performance|sequencing|likert|numeric|other)$",CMIResult:"^(correct|incorrect|unanticipated|neutral|-?([0-9]{1,4})(\\.[0-9]{1,18})?)$",NAVEvent:"^(previous|continue|exit|exitAll|abandon|abandonAll|suspendAll|_none_|(\\{target=(?<choice_target>\\S{0,}[a-zA-Z0-9-_]+)})?choice|(\\{target=(?<jump_target>\\S{0,}[a-zA-Z0-9-_]+)})?jump)$",NAVBoolean:"^(unknown|true|false$)",NAVTarget:"^{target=\\S{0,}[a-zA-Z0-9-_]+}$",scaled_range:"-1#1",audio_range:"0#*",speed_range:"0#*",text_range:"-1#1",progress_range:"0#1"}},784:function(e,t,n){n.r(t),n.d(t,{BaseScormValidationError:function(){return i},ValidationError:function(){return o}});var r=n(635),i=function(e){function t(t){var n=e.call(this,t.toString())||this;return n._errorCode=t,n.name="ScormValidationError",n}return(0,r.__extends)(t,e),Object.defineProperty(t.prototype,"errorCode",{get:function(){return this._errorCode},enumerable:!1,configurable:!0}),t}(Error),o=function(e){function t(t,n,r){var i=e.call(this,t)||this;return i._detailedMessage="",i.message=n,i._errorMessage=n,r&&(i._detailedMessage=r),i}return(0,r.__extends)(t,e),Object.defineProperty(t.prototype,"errorMessage",{get:function(){return this._errorMessage},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"detailedMessage",{get:function(){return this._detailedMessage},enumerable:!1,configurable:!0}),t}(i)},179:function(e,t,n){n.r(t),n.d(t,{Scorm12ValidationError:function(){return s}});var r=n(635),i=n(784),o=n(340).scorm12_constants.error_descriptions,s=function(e){function t(t){return{}.hasOwnProperty.call(o,String(t))?e.call(this,t,o[String(t)].basicMessage,o[String(t)].detailMessage)||this:e.call(this,101,o[101].basicMessage,o[101].detailMessage)||this}return(0,r.__extends)(t,e),t}(i.ValidationError)},864:function(e,t,n){n.r(t),n.d(t,{SECONDS_PER_DAY:function(){return s},SECONDS_PER_HOUR:function(){return o},SECONDS_PER_MINUTE:function(){return i},SECONDS_PER_SECOND:function(){return r},addHHMMSSTimeStrings:function(){return h},addTwoDurations:function(){return m},countDecimals:function(){return _},flatten:function(){return p},formatMessage:function(){return g},getDurationAsSeconds:function(){return d},getSecondsAsHHMMSS:function(){return c},getSecondsAsISODuration:function(){return l},getTimeAsSeconds:function(){return u},stringMatches:function(){return y},unflatten:function(){return f}});var r=1,i=60,o=60*i,s=24*o,a={D:s,H:o,M:i,S:r};function c(e){if(!e||e<=0)return"00:00:00";var t=Math.floor(e/o),n=new Date(1e3*e),r=n.getUTCMinutes(),i=n.getSeconds(),s=e%1,a="";return _(s)>0&&(a="."+(a=_(s)>2?s.toFixed(2):String(s)).split(".")[1]),(t+":"+r+":"+i).replace(/\b\d\b/g,"0$&")+a}function l(e){if(!e||e<=0)return"PT0S";var t="P",n=e;for(var r in a){var i=a[r],o=Math.floor(n/i);_(n%=i)>2&&(n=Number(Number(n).toFixed(2))),"S"===r&&n>0&&(o+=n),o&&((t.indexOf("D")>0||"H"===r||"M"===r||"S"===r)&&-1===t.indexOf("T")&&(t+="T"),t+="".concat(o).concat(r))}return t}function u(e,t){if("number"!=typeof e&&"boolean"!=typeof e||(e=String(e)),"string"==typeof t&&(t=new RegExp(t)),!e||!e.match(t))return 0;var n=e.split(":");return 3600*Number(n[0])+60*Number(n[1])+Number(n[2])}function d(e,t){if("string"==typeof t&&(t=new RegExp(t)),!e||!e.match(t))return 0;var n=new RegExp(t).exec(e)||[],r=n[1],i=(n[2],n[4]),o=n[5],s=n[6],a=n[7],c=0;return c+=Number(a)||0,c+=60*Number(s)||0,c+=3600*Number(o)||0,c+=86400*Number(i)||0,c+=31536e3*Number(r)||0}function m(e,t,n){var r="string"==typeof n?new RegExp(n):n;return l(d(e,r)+d(t,r))}function h(e,t,n){return"string"==typeof n&&(n=new RegExp(n)),c(u(e,n)+u(t,n))}function p(e){var t={};return function e(n,r){if(Object(n)!==n)t[r]=n;else if(Array.isArray(n))for(var i=0,o=n.length;i<o;i++)e(n[i],r+"["+i+"]"),0===o&&(t[r]=[]);else{var s=!0;for(var a in n)({}).hasOwnProperty.call(n,a)&&(s=!1,e(n[a],r?r+"."+a:a));s&&r&&(t[r]={})}}(e,""),t}function f(e){if(Object(e)!==e||Array.isArray(e))return e;var t=/\.?([^.[\]]+)|\[(\d+)]/g,n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){for(var i=n,o="",s=t.exec(r);s;)i=i[o]||(i[o]=s[2]?[]:{}),o=s[2]||s[1],s=t.exec(r);i[o]=e[r]}return n[""]||n}function _(e){return Math.floor(e)===e||String(e).indexOf(".")<0?0:e.toString().split(".")[1].length||0}function g(e,t,n){for(var r="",i=20-(r+=e).length,o=0;o<i;o++)r+=" ";if(r+=": ",n){i=70-(r+=n).length;for(var s=0;s<i;s++)r+=" "}return t&&(r+=t),r}function y(e,t){return null!==(null==e?void 0:e.match(t))}},635:function(e,t,n){n.r(t),n.d(t,{__addDisposableResource:function(){return R},__assign:function(){return o},__asyncDelegator:function(){return M},__asyncGenerator:function(){return S},__asyncValues:function(){return x},__await:function(){return C},__awaiter:function(){return p},__classPrivateFieldGet:function(){return N},__classPrivateFieldIn:function(){return k},__classPrivateFieldSet:function(){return A},__createBinding:function(){return _},__decorate:function(){return a},__disposeResources:function(){return P},__esDecorate:function(){return l},__exportStar:function(){return g},__extends:function(){return i},__generator:function(){return f},__importDefault:function(){return T},__importStar:function(){return L},__makeTemplateObject:function(){return I},__metadata:function(){return h},__param:function(){return c},__propKey:function(){return d},__read:function(){return b},__rest:function(){return s},__runInitializers:function(){return u},__setFunctionName:function(){return m},__spread:function(){return v},__spreadArray:function(){return E},__spreadArrays:function(){return w},__values:function(){return y}});var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},r(e,t)};function i(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}var o=function(){return o=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},o.apply(this,arguments)};function s(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(e);i<r.length;i++)t.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]])}return n}function a(e,t,n,r){var i,o=arguments.length,s=o<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(e,t,n,r);else for(var a=e.length-1;a>=0;a--)(i=e[a])&&(s=(o<3?i(s):o>3?i(t,n,s):i(t,n))||s);return o>3&&s&&Object.defineProperty(t,n,s),s}function c(e,t){return function(n,r){t(n,r,e)}}function l(e,t,n,r,i,o){function s(e){if(void 0!==e&&"function"!=typeof e)throw new TypeError("Function expected");return e}for(var a,c=r.kind,l="getter"===c?"get":"setter"===c?"set":"value",u=!t&&e?r.static?e:e.prototype:null,d=t||(u?Object.getOwnPropertyDescriptor(u,r.name):{}),m=!1,h=n.length-1;h>=0;h--){var p={};for(var f in r)p[f]="access"===f?{}:r[f];for(var f in r.access)p.access[f]=r.access[f];p.addInitializer=function(e){if(m)throw new TypeError("Cannot add initializers after decoration has completed");o.push(s(e||null))};var _=(0,n[h])("accessor"===c?{get:d.get,set:d.set}:d[l],p);if("accessor"===c){if(void 0===_)continue;if(null===_||"object"!=typeof _)throw new TypeError("Object expected");(a=s(_.get))&&(d.get=a),(a=s(_.set))&&(d.set=a),(a=s(_.init))&&i.unshift(a)}else(a=s(_))&&("field"===c?i.unshift(a):d[l]=a)}u&&Object.defineProperty(u,r.name,d),m=!0}function u(e,t,n){for(var r=arguments.length>2,i=0;i<t.length;i++)n=r?t[i].call(e,n):t[i].call(e);return r?n:void 0}function d(e){return"symbol"==typeof e?e:"".concat(e)}function m(e,t,n){return"symbol"==typeof t&&(t=t.description?"[".concat(t.description,"]"):""),Object.defineProperty(e,"name",{configurable:!0,value:n?"".concat(n," ",t):t})}function h(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)}function p(e,t,n,r){return new(n||(n=Promise))((function(i,o){function s(e){try{c(r.next(e))}catch(e){o(e)}}function a(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}c((r=r.apply(e,t||[])).next())}))}function f(e,t){var n,r,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},s=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return s.next=a(0),s.throw=a(1),s.return=a(2),"function"==typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s;function a(a){return function(c){return function(a){if(n)throw new TypeError("Generator is already executing.");for(;s&&(s=0,a[0]&&(o=0)),o;)try{if(n=1,r&&(i=2&a[0]?r.return:a[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,a[1])).done)return i;switch(r=0,i&&(a=[2&a[0],i.value]),a[0]){case 0:case 1:i=a;break;case 4:return o.label++,{value:a[1],done:!1};case 5:o.label++,r=a[1],a=[0];continue;case 7:a=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==a[0]&&2!==a[0])){o=0;continue}if(3===a[0]&&(!i||a[1]>i[0]&&a[1]<i[3])){o.label=a[1];break}if(6===a[0]&&o.label<i[1]){o.label=i[1],i=a;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(a);break}i[2]&&o.ops.pop(),o.trys.pop();continue}a=t.call(e,o)}catch(e){a=[6,e],r=0}finally{n=i=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,c])}}}var _=Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]};function g(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||_(t,e,n)}function y(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function b(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,i,o=n.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(r=o.next()).done;)s.push(r.value)}catch(e){i={error:e}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(i)throw i.error}}return s}function v(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(b(arguments[t]));return e}function w(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;var r=Array(e),i=0;for(t=0;t<n;t++)for(var o=arguments[t],s=0,a=o.length;s<a;s++,i++)r[i]=o[s];return r}function E(e,t,n){if(n||2===arguments.length)for(var r,i=0,o=t.length;i<o;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))}function C(e){return this instanceof C?(this.v=e,this):new C(e)}function S(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r,i=n.apply(e,t||[]),o=[];return r=Object.create(("function"==typeof AsyncIterator?AsyncIterator:Object).prototype),s("next"),s("throw"),s("return",(function(e){return function(t){return Promise.resolve(t).then(e,l)}})),r[Symbol.asyncIterator]=function(){return this},r;function s(e,t){i[e]&&(r[e]=function(t){return new Promise((function(n,r){o.push([e,t,n,r])>1||a(e,t)}))},t&&(r[e]=t(r[e])))}function a(e,t){try{(n=i[e](t)).value instanceof C?Promise.resolve(n.value.v).then(c,l):u(o[0][2],n)}catch(e){u(o[0][3],e)}var n}function c(e){a("next",e)}function l(e){a("throw",e)}function u(e,t){e(t),o.shift(),o.length&&a(o[0][0],o[0][1])}}function M(e){var t,n;return t={},r("next"),r("throw",(function(e){throw e})),r("return"),t[Symbol.iterator]=function(){return this},t;function r(r,i){t[r]=e[r]?function(t){return(n=!n)?{value:C(e[r](t)),done:!1}:i?i(t):t}:i}}function x(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t,n=e[Symbol.asyncIterator];return n?n.call(e):(e=y(e),t={},r("next"),r("throw"),r("return"),t[Symbol.asyncIterator]=function(){return this},t);function r(n){t[n]=e[n]&&function(t){return new Promise((function(r,i){(function(e,t,n,r){Promise.resolve(r).then((function(t){e({value:t,done:n})}),t)})(r,i,(t=e[n](t)).done,t.value)}))}}}function I(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e}var O=Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t};function L(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&_(t,e,n);return O(t,e),t}function T(e){return e&&e.__esModule?e:{default:e}}function N(e,t,n,r){if("a"===n&&!r)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===n?r:"a"===n?r.call(e):r?r.value:t.get(e)}function A(e,t,n,r,i){if("m"===r)throw new TypeError("Private method is not writable");if("a"===r&&!i)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!i:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===r?i.call(e,n):i?i.value=n:t.set(e,n),n}function k(e,t){if(null===t||"object"!=typeof t&&"function"!=typeof t)throw new TypeError("Cannot use 'in' operator on non-object");return"function"==typeof e?t===e:e.has(t)}function R(e,t,n){if(null!=t){if("object"!=typeof t&&"function"!=typeof t)throw new TypeError("Object expected.");var r,i;if(n){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");r=t[Symbol.asyncDispose]}if(void 0===r){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");r=t[Symbol.dispose],n&&(i=r)}if("function"!=typeof r)throw new TypeError("Object not disposable.");i&&(r=function(){try{i.call(this)}catch(e){return Promise.reject(e)}}),e.stack.push({value:t,dispose:r,async:n})}else n&&e.stack.push({async:!0});return t}var j="function"==typeof SuppressedError?SuppressedError:function(e,t,n){var r=new Error(n);return r.name="SuppressedError",r.error=e,r.suppressed=t,r};function P(e){function t(t){e.error=e.hasError?new j(t,e.error,"An error was suppressed during disposal."):t,e.hasError=!0}var n,r=0;return function i(){for(;n=e.stack.pop();)try{if(!n.async&&1===r)return r=0,e.stack.push(n),Promise.resolve().then(i);if(n.dispose){var o=n.dispose.call(n.value);if(n.async)return r|=2,Promise.resolve(o).then(i,(function(e){return t(e),i()}))}else r|=1}catch(e){t(e)}if(1===r)return e.hasError?Promise.reject(e.error):Promise.resolve();if(e.hasError)throw e.error}()}t.default={__extends:i,__assign:o,__rest:s,__decorate:a,__param:c,__metadata:h,__awaiter:p,__generator:f,__createBinding:_,__exportStar:g,__values:y,__read:b,__spread:v,__spreadArrays:w,__spreadArray:E,__await:C,__asyncGenerator:S,__asyncDelegator:M,__asyncValues:x,__makeTemplateObject:I,__importStar:L,__importDefault:T,__classPrivateFieldGet:N,__classPrivateFieldSet:A,__classPrivateFieldIn:k,__addDisposableResource:R,__disposeResources:P}}},i={};function o(e){var t=i[e];if(void 0!==t)return t.exports;var n=i[e]={exports:{}};return r[e](n,n.exports,o),n.exports}o.d=function(e,t){for(var n in t)o.o(t,n)&&!o.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},o.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},o.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};t={};o.r(t),o.d(t,{AICC:function(){return d},Scorm12API:function(){return l},Scorm2004API:function(){return u}});var s=o(916),a=o(941),c=o(809),l=a.Scorm12Impl,u=c.Scorm2004Impl,d=s.AICCImpl,m=(t.AICC,t.Scorm12API);t.Scorm2004API}}]);