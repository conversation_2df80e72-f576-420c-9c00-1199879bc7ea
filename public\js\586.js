/*! For license information please see 586.js.LICENSE.txt */
"use strict";(self.webpackChunk=self.webpackChunk||[]).push([[586],{56922:(e,t,n)=>{n.d(t,{Z:()=>i});var r=n(1519),o=n.n(r)()((function(e){return e[1]}));o.push([e.id,".wrap{max-width:55ch;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.banner_detail_box{left:20%;position:absolute;top:50%;transform:translate(-50%,-50%)}.response-upload-input>input{width:104px}.response-upload-input>label{background-color:#fff;border:1px solid #e4e6ef;border-left:none;color:#5e6278;flex-grow:1;font-size:1.1rem;font-weight:500;line-height:1.5;overflow:hidden;padding:.775rem 1rem;text-overflow:ellipsis;transition:border-color .15s ease-in-out,box-shadow .15s ease-in-out;white-space:nowrap}.response-upload-input>input:focus+label{border-color:#b5b5c3}.btn-white-custom{background:#fff;color:#000}.btn-white-custom:hover,.btn.btn-white-custom:hover:not(.btn-active){background-color:#000!important;color:#fff!important}.btn-white-custom:disabled{background-color:#fff;opacity:1}.pointer{cursor:pointer}.overlay{overflow:overlay}.related{right:5%!important}div#kt_app_content{padding-bottom:0;padding-top:0}.btn-white{border:1px solid #000!important}.btn-white:hover,.btn.btn-white:hover:not(.btn-active){background-color:#000!important;color:#fff!important}::-webkit-scrollbar-thumb,::-webkit-scrollbar-thumb:hover{background:#000!important}.module-sections{margin-left:-30px;margin-right:-30px;overflow:auto hidden}.app-content{padding:0}.full-page{margin-left:-20px;margin-right:-20px}.banner{background-color:#000;background-position:50%;background-repeat:no-repeat;background-size:cover;display:block;min-height:calc(45.25vw - 149px);overflow:hidden;position:relative}.full-view-banner{margin-left:-30px;margin-right:-30px}.banner-video{height:100%}.banner-video>video{left:50%;position:absolute;top:50%;transform:translate(-50%,-50%);width:101%!important}.page-content{padding:0 15px;position:absolute;top:40%;width:100%}.module-section{border-bottom:1px solid;border-left:1px solid;border-top:1px solid;cursor:pointer;height:100px}.module-sections>.text-center:last-of-type>.module-section{border-right:1px solid}@media (max-width:1280px){.banner{height:56.25vw}.banner_detail_box{left:40%}.banner-video>video{height:100%!important;width:calc(65vw + 65vh)!important}}@media (max-width:991px){.full-view-banner,.module-sections{margin-left:-20px;margin-right:-20px}.full-view-banner{margin-top:58.16px}}@media (max-width:991px) and (min-width:768px) and (orientation:portrait){.banner{height:86.25vw}.banner-video>video{height:100%!important;width:calc(66vw + 66vh)!important}}@media (max-width:991px) and (orientation:landscape){.banner-video>video{height:auto!important;width:calc(70vw + 70vh)!important}}@media (max-width:767px){.full-page{margin-left:0;margin-right:0}.banner{height:calc(100vh - 300px)}.banner_detail_box{left:50%}}@media (max-width:575px){div#kt_app_content{padding-top:30px}.banner_detail_box{width:70vw!important}.full-view-banner{margin-top:0}.banner-video>video{height:100%!important;width:calc(90vw + 90vh)!important}}",""]);const i=o},19586:(e,t,n)=>{n.r(t),n.d(t,{default:()=>D});var r=n(70821),o=["innerHTML"],i=(0,r.createElementVNode)("div",{style:{position:"absolute",width:"100%",height:"100%",opacity:".3",background:"#000"}},null,-1),a={class:"banner_detail_box w-450px"},l=(0,r.createElementVNode)("h1",{class:"fw-normal text-light"},"Final Step",-1),s={class:"display-4 fw-normal text-light"},c={key:0},u={key:0,class:"row mt-5"},d={class:"col-12 fs-6 text-light d-flex response-upload-input"},p={for:"taskfiles"},h=["textContent"],f={class:"row mt-5"},m={class:"col-sm-12"},v=["disabled"],g={class:"svg-icon svg-icon-primary svg-icon-2x"},w={key:0,xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",width:"24px",height:"24px",viewBox:"0 0 24 24",version:"1.1"},b=[(0,r.createElementVNode)("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},[(0,r.createElementVNode)("mask",{fill:"white"},[(0,r.createElementVNode)("use",{"xlink:href":"#path-1"})]),(0,r.createElementVNode)("g"),(0,r.createElementVNode)("path",{d:"M15.6274517,4.55882251 L14.4693753,6.2959371 C13.9280401,5.51296885 13.0239252,5 12,5 C10.3431458,5 9,6.34314575 9,8 L9,10 L14,10 L17,10 L18,10 C19.1045695,10 20,10.8954305 20,12 L20,18 C20,19.1045695 19.1045695,20 18,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,12 C4,10.8954305 4.8954305,10 6,10 L7,10 L7,8 C7,5.23857625 9.23857625,3 12,3 C13.4280904,3 14.7163444,3.59871093 15.6274517,4.55882251 Z",fill:"#ffffff"})],-1)],k={key:1,xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",width:"24px",height:"24px",viewBox:"0 0 24 24",version:"1.1"},y=[(0,r.createElementVNode)("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},[(0,r.createElementVNode)("mask",{fill:"white"},[(0,r.createElementVNode)("use",{"xlink:href":"#path-1"})]),(0,r.createElementVNode)("g"),(0,r.createElementVNode)("path",{d:"M7,10 L7,8 C7,5.23857625 9.23857625,3 12,3 C14.7614237,3 17,5.23857625 17,8 L17,10 L18,10 C19.1045695,10 20,10.8954305 20,12 L20,18 C20,19.1045695 19.1045695,20 18,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,12 C4,10.8954305 4.8954305,10 6,10 L7,10 Z M12,5 C10.3431458,5 9,6.34314575 9,8 L9,10 L15,10 L15,8 C15,6.34314575 13.6568542,5 12,5 Z",fill:"#000000"})],-1)],x=["innerHTML"],E=["textContent"],L=["textContent"],C={class:"module-section d-flex flex-column justify-content-center align-items-center py-5"},N={class:"svg-icon svg-icon-primary svg-icon-2x"},_={key:0,xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",width:"24px",height:"24px",viewBox:"0 0 24 24",version:"1.1"},V=[(0,r.createElementVNode)("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},[(0,r.createElementVNode)("mask",{fill:"white"},[(0,r.createElementVNode)("use",{"xlink:href":"#path-1"})]),(0,r.createElementVNode)("g"),(0,r.createElementVNode)("path",{d:"M15.6274517,4.55882251 L14.4693753,6.2959371 C13.9280401,5.51296885 13.0239252,5 12,5 C10.3431458,5 9,6.34314575 9,8 L9,10 L14,10 L17,10 L18,10 C19.1045695,10 20,10.8954305 20,12 L20,18 C20,19.1045695 19.1045695,20 18,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,12 C4,10.8954305 4.8954305,10 6,10 L7,10 L7,8 C7,5.23857625 9.23857625,3 12,3 C13.4280904,3 14.7163444,3.59871093 15.6274517,4.55882251 Z",fill:"#ffffff"})],-1)],B={key:1,xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",width:"24px",height:"24px",viewBox:"0 0 24 24",version:"1.1"},S=[(0,r.createElementVNode)("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},[(0,r.createElementVNode)("mask",{fill:"white"},[(0,r.createElementVNode)("use",{"xlink:href":"#path-1"})]),(0,r.createElementVNode)("g"),(0,r.createElementVNode)("path",{d:"M7,10 L7,8 C7,5.23857625 9.23857625,3 12,3 C14.7614237,3 17,5.23857625 17,8 L17,10 L18,10 C19.1045695,10 20,10.8954305 20,12 L20,18 C20,19.1045695 19.1045695,20 18,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,12 C4,10.8954305 4.8954305,10 6,10 L7,10 Z M12,5 C10.3431458,5 9,6.34314575 9,8 L9,10 L15,10 L15,8 C15,6.34314575 13.6568542,5 12,5 Z",fill:"#000000"})],-1)];var j=n(70655),O=n(72961),Z=n(80894),M=n(22201);function T(e){return T="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},T(e)}function z(){z=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,r=Object.defineProperty||function(e,t,n){e[t]=n.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",l=o.toStringTag||"@@toStringTag";function s(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(e){s=function(e,t,n){return e[t]=n}}function c(e,t,n,o){var i=t&&t.prototype instanceof p?t:p,a=Object.create(i.prototype),l=new C(o||[]);return r(a,"_invoke",{value:y(e,n,l)}),a}function u(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=c;var d={};function p(){}function h(){}function f(){}var m={};s(m,i,(function(){return this}));var v=Object.getPrototypeOf,g=v&&v(v(N([])));g&&g!==t&&n.call(g,i)&&(m=g);var w=f.prototype=p.prototype=Object.create(m);function b(e){["next","throw","return"].forEach((function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function k(e,t){function o(r,i,a,l){var s=u(e[r],e,i);if("throw"!==s.type){var c=s.arg,d=c.value;return d&&"object"==T(d)&&n.call(d,"__await")?t.resolve(d.__await).then((function(e){o("next",e,a,l)}),(function(e){o("throw",e,a,l)})):t.resolve(d).then((function(e){c.value=e,a(c)}),(function(e){return o("throw",e,a,l)}))}l(s.arg)}var i;r(this,"_invoke",{value:function(e,n){function r(){return new t((function(t,r){o(e,n,t,r)}))}return i=i?i.then(r,r):r()}})}function y(e,t,n){var r="suspendedStart";return function(o,i){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===o)throw i;return _()}for(n.method=o,n.arg=i;;){var a=n.delegate;if(a){var l=x(a,n);if(l){if(l===d)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var s=u(e,t,n);if("normal"===s.type){if(r=n.done?"completed":"suspendedYield",s.arg===d)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(r="completed",n.method="throw",n.arg=s.arg)}}}function x(e,t){var n=t.method,r=e.iterator[n];if(void 0===r)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,x(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),d;var o=u(r,e.iterator,t.arg);if("throw"===o.type)return t.method="throw",t.arg=o.arg,t.delegate=null,d;var i=o.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,d):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,d)}function E(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function L(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function C(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(E,this),this.reset(!0)}function N(e){if(e){var t=e[i];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,o=function t(){for(;++r<e.length;)if(n.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return o.next=o}}return{next:_}}function _(){return{value:void 0,done:!0}}return h.prototype=f,r(w,"constructor",{value:f,configurable:!0}),r(f,"constructor",{value:h,configurable:!0}),h.displayName=s(f,l,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===h||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,s(e,l,"GeneratorFunction")),e.prototype=Object.create(w),e},e.awrap=function(e){return{__await:e}},b(k.prototype),s(k.prototype,a,(function(){return this})),e.AsyncIterator=k,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new k(c(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},b(w),s(w,l,"Generator"),s(w,i,(function(){return this})),s(w,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},e.values=N,C.prototype={constructor:C,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(L),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(n,r){return a.type="throw",a.arg=e,t.next=n,r&&(t.method="next",t.arg=void 0),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return r("end");if(i.tryLoc<=this.prev){var l=n.call(i,"catchLoc"),s=n.call(i,"finallyLoc");if(l&&s){if(this.prev<i.catchLoc)return r(i.catchLoc,!0);if(this.prev<i.finallyLoc)return r(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return r(i.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return r(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,d):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),d},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),L(n),d}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;L(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:N(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),d}},e}const F=(0,r.defineComponent)({name:"skillstraining-final-step",components:{},setup:function(e){var t=(0,Z.oR)(),n=(0,M.yj)(),o=(0,M.tv)(),i=t.getters.currentUser;(0,r.onMounted)((function(){v()}));var a=(0,r.ref)(),l=(0,r.ref)(),s=(0,r.ref)(),c=(0,r.ref)(),u=(0,r.ref)(),d=(0,r.ref)(),p=(0,r.ref)();l.value={id:1,background_imagepath:null,background_video:null,response:!1,steps:[]},s.value=0,c.value=n.params.id;var h=(0,r.ref)();var f=(0,r.ref)(),m=(0,r.ref)("");f.value={response:"",nofile:!l.value.response};var v=function(){O.Z.get("api/skillstraining",c.value).then((function(e){var t=e.data;if(t.steps.length)for(var n=0;n<t.steps.length;n++)t.steps[n].user_response||(s.value=t.steps[n].id);l.value=t})).catch((function(e){e.response}))};return{currentUser:i,skillstraining:l,toggleRelated:function(){p.value=!p.value},currentskillstraining:c,showRelatedSkillstrainingList:p,latestStep:s,favouriteSkillstraining:function(e){u.value={id:e},O.Z.post("api/skillstraining/"+e+"/fav",u.value).then((function(e){var t=e.data;l.value.favourite=t.favourite})).catch((function(e){e.response}))},getlatestStep:function(e){if(e.length)for(var t=0;t<e.length;t++)if(!e[t].user_response)return e[t].id},saveResponse:function(){var t;return(0,j.mG)(this,void 0,void 0,z().mark((function n(){return z().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(l.value.response&&a.value)try{f.value.response=a.value,f.value.nofile=!l.value.response,O.Z.upload("api/skillstraining/"+e.id+"/submit-task",f.value).then((function(e){var t=e.data;void 0!==t.error?m.value=t.error:o.push({name:"task-skillstraining-view-response",params:{id:l.value.id}}).then((function(){}))})).catch((function(e){e.response}))}catch(e){console.error(e),null===(t=h.value)||void 0===t||t.reset(),a.value=null}else l.value.response&&!a.value?m.value="Please upload your task before submitting.":l.value.response||O.Z.upload("api/skillstraining/"+e.id+"/submit-task",f.value).then((function(e){var t=e.data;void 0!==t.error?m.value=t.error:o.push({name:"task-skillstraining-view-response",params:{id:l.value.id}}).then((function(){}))})).catch((function(e){e.response}));case 1:case"end":return n.stop()}}),n)})))},onFileChanged:function(e){var t=e.target;t&&t.files&&(a.value=t.files[0],d.value=t.files[0].name)},responseError:m,fileName:d}},props:["id"]});var G=n(93379),P=n.n(G),R=n(56922),U={insert:"head",singleton:!1};P()(R.Z,U);R.Z.locals;const D=(0,n(83744).Z)(F,[["render",function(e,t,n,j,O,Z){var M;return(0,r.openBlock)(),(0,r.createElementBlock)(r.Fragment,null,[(0,r.createElementVNode)("div",{class:"full-view-banner banner",style:(0,r.normalizeStyle)({backgroundImage:"url("+e.skillstraining.background_imagepath+")"})},[e.skillstraining.background_videoid?((0,r.openBlock)(),(0,r.createElementBlock)("div",{key:0,class:"banner-video",innerHTML:e.skillstraining.background_videoid},null,8,o)):(0,r.createCommentVNode)("",!0),i,(0,r.createElementVNode)("div",a,[l,(0,r.createElementVNode)("h1",s,[e.skillstraining.response?((0,r.openBlock)(),(0,r.createElementBlock)("span",c,"Upload & ")):(0,r.createCommentVNode)("",!0),(0,r.createTextVNode)("Submit")]),e.skillstraining.response?((0,r.openBlock)(),(0,r.createElementBlock)("div",u,[(0,r.createElementVNode)("div",d,[(0,r.createElementVNode)("input",{type:"file",id:"taskfiles",class:"form-control rounded-0",onChange:t[0]||(t[0]=function(t){return e.onFileChanged(t)})},null,32),(0,r.createElementVNode)("label",p,(0,r.toDisplayString)(null!==(M=e.fileName)&&void 0!==M?M:"Upload your task"),1)]),e.responseError.length?((0,r.openBlock)(),(0,r.createElementBlock)("p",{key:0,textContent:(0,r.toDisplayString)(e.responseError),class:"form-error mt-2 ms-2"},null,8,h)):(0,r.createCommentVNode)("",!0)])):(0,r.createCommentVNode)("",!0),(0,r.createElementVNode)("div",f,[(0,r.createElementVNode)("div",m,[(0,r.createElementVNode)("button",{disabled:!e.currentUser.isStudent,onClick:t[1]||(t[1]=function(t){return e.saveResponse()}),class:"btn btn-white-custom btn-lg rounded-0 w-100 p-md-5"}," Submit Task ",8,v)])])])],4),(0,r.createElementVNode)("div",{class:(0,r.normalizeClass)([{row:e.skillstraining.steps.length<6},"d-flex module-sections"])},[((0,r.openBlock)(!0),(0,r.createElementBlock)(r.Fragment,null,(0,r.renderList)(e.skillstraining.steps,(function(t){return(0,r.openBlock)(),(0,r.createElementBlock)("div",{key:t.id,class:(0,r.normalizeClass)([[e.skillstraining.steps.length<6?"col":"col-6 col-sm-4 col-md-2",t.user_response?"bg-black":""],"text-center p-0"])},[(0,r.createElementVNode)("div",{class:(0,r.normalizeClass)(["module-section d-flex flex-column justify-content-center align-items-center py-5",{"bg-white":!t.user_response}])},[(0,r.createElementVNode)("span",g,[t.user_response?((0,r.openBlock)(),(0,r.createElementBlock)("svg",w,b)):((0,r.openBlock)(),(0,r.createElementBlock)("svg",k,y))]),(0,r.createElementVNode)("p",{class:(0,r.normalizeClass)(["m-0 px-5",{"text-white":t.user_response}]),innerHTML:t.title},null,10,x),(0,r.createElementVNode)("p",{class:(0,r.normalizeClass)(["m-0",{"text-white":t.user_response}])},[t.estimated_time&&t.estimated_time.hours?((0,r.openBlock)(),(0,r.createElementBlock)("span",{key:0,textContent:(0,r.toDisplayString)(t.estimated_time.hours+"h ")},null,8,E)):(0,r.createCommentVNode)("",!0),t.estimated_time&&t.estimated_time.minutes?((0,r.openBlock)(),(0,r.createElementBlock)("span",{key:1,textContent:(0,r.toDisplayString)(t.estimated_time.minutes+"m")},null,8,L)):(0,r.createCommentVNode)("",!0)],2)],2)],2)})),128)),(0,r.createElementVNode)("div",{class:(0,r.normalizeClass)(["text-center p-0",[e.skillstraining.steps.length<6?"col":"col-6 col-sm-4 col-md-2",e.skillstraining.user_response?"bg-black":""]])},[(0,r.createElementVNode)("div",C,[(0,r.createElementVNode)("span",N,[e.skillstraining.user_response?((0,r.openBlock)(),(0,r.createElementBlock)("svg",_,V)):((0,r.openBlock)(),(0,r.createElementBlock)("svg",B,S))]),(0,r.createElementVNode)("p",{class:(0,r.normalizeClass)(["m-0",{"text-white":e.skillstraining.user_response}])},"Submit",2)])],2)],2)],64)}]])}}]);