<template>
    <div class="full-view-banner banner" v-bind:style="{ 'backgroundImage': 'url(' + banner.imagefullpath + ')' }">
        <div v-if="banner.video" class="banner-video" v-html="banner.video"></div>
        <div style="position:absolute;width:100%;height:100%;opacity:.3;background:#DFDFDF;"></div>
        <div class="position-absolute bottom-0 p-18 mb-18 start-0 text-black w-100">
            <h1 class="fw-normal display-4 mb-2 fs-4x">Courses</h1>
            <h3 class="fw-normal" style="font-size: 18px;">View, manage and add all courses for your company in one
                place.</h3>
        </div>
    </div>
    <div class="full-view-banner row bg-black black-strip" id="FilteredSection">
        <div class="col-sm-12 py-15"></div>
    </div>
    <div class="mt-12">
        <div class="px-3 pt-3 bg-white rounded shadow-sm">
            <div class="d-md-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center mt-4">
                    <div class="position-relative w-md-300px me-md-2">
                        <span
                            class="svg-icon svg-icon-3 svg-icon-gray-500 position-absolute top-50 translate-middle ms-6">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2" rx="1"
                                    transform="rotate(45 17.0365 15.1223)" fill="currentColor"></rect>
                                <path
                                    d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z"
                                    fill="currentColor"></path>
                            </svg>
                        </span>
                        <input v-model="searchQuery" @keyup.enter="onSearch"
                            class="form-control form-control-solid ps-10 " type="text" placeholder="Search Courses"
                            autocomplete="off" />
                    </div>
                </div>


                <div class="d-flex col-8 flex-column flex-md-row justify-content-end align-items-center px-2">
                    <div class="mb-5 me-3 col-12 col-md-3 mt-5 mt-md-0">
                        <label class="form-label fw-semibold">Course Type</label>
                        <Field v-slot="{ field }" name="difficulty">
                            <Multiselect class="form-control form-control-solid py-2 fs-6" v-model="filters.courseType" v-bind="field"
                                :searchable="false" placeholder="Course Type" :resolve-on-load="false"
                                :options="courseTypes"></Multiselect>
                        </Field>
                    </div>

                    <div class="mb-5 me-3 col-12 col-md-3">
                        <label class="form-label fw-semibold">Level</label>
                        <Field v-slot="{ field }" name="difficulty">
                            <Multiselect class=" form-control form-control-solid py-2 fs-6" v-model="filters.difficulty" v-bind="field"
                                :searchable="false" placeholder="Difficulty Level" :resolve-on-load="false"
                                :options="difficulty"></Multiselect>
                        </Field>
                    </div>

                    <div class="mb-5 me-3 col-12 col-md-2">
                        <button type="button"
                            class="btn btn-light d-flex justify-content-center align-items-center py-auto mt-8 w-100"
                            data-toggle="button" aria-pressed="false" autocomplete="off" @click="applyFilter">
                            Filter
                        </button>
                    </div>
                </div>
            </div>
        </div>


        <div class="d-flex flex-wrap flex-stack py-7">
            <div class="d-flex flex-wrap align-items-center justify-content-center gap-4 my-1">
                <h3 class="fw-bold my-0 text-center">
                    <!-- <span v-if="loading">Loading...</span> -->
                    <!-- <span v-else>{{ paginatedCourses.length }} Courses Found</span> -->
                    <span>{{ pagination.total }} Courses Found</span>
                </h3>
                <p class="my-0 text-center text-gray-600" v-if="!loading">
                    Showing <span v-if="pagination.to">{{ pagination.to }}</span> of {{ pagination.total }}
                </p>
            </div>
            <div class="d-flex flex-wrap my-1">
                <ul class="nav nav-pills me-6 mb-2 mb-sm-0">
                    <li class="nav-item m-0">
                        <a class="btn btn-sm btn-icon btn-light btn-color-muted btn-active-primary me-3 active"
                         :class="{ active: currentTab === 'grid' }" data-bs-toggle="tab" href="#kt_project_users_card_pane" >
                            <span class="svg-icon svg-icon-2"> <svg xmlns="http://www.w3.org/2000/svg" width="24px"
                                    height="24px" viewBox="0 0 24 24">
                                    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                        <rect x="5" y="5" width="5" height="5" rx="1" fill="currentColor" />
                                        <rect x="14" y="5" width="5" height="5" rx="1" fill="currentColor"
                                            opacity="0.3" />
                                        <rect x="5" y="14" width="5" height="5" rx="1" fill="currentColor"
                                            opacity="0.3" />
                                        <rect x="14" y="14" width="5" height="5" rx="1" fill="currentColor"
                                            opacity="0.3" />
                                    </g>
                                </svg> </span>
                        </a>
                    </li>

                    <li class="nav-item m-0">
                        <a class="btn btn-sm btn-icon btn-light btn-color-muted btn-active-primary me-3
                        " :class="{ active: currentTab === 'list' }"
                         data-bs-toggle="tab" href="#kt_project_users_table_pane">
                            <span class="svg-icon svg-icon-2"> <svg width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M21 7H3C2.4 7 2 6.6 2 6V4C2 3.4 2.4 3 3 3H21C21.6 3 22 3.4 22 4V6C22 6.6 21.6 7 21 7Z"
                                        fill="currentColor" />
                                    <path opacity="0.3"
                                        d="M21 14H3C2.4 14 2 13.6 2 13V11C2 10.4 2.4 10 3 10H21C21.6 10 22 10.4 22 11V13C22 13.6 21.6 14 21 14Z"
                                        fill="currentColor" />
                                    <path opacity="0.3"
                                        d="M21 21H3C2.4 21 2 20.6 2 20V18C2 17.4 2.4 17 3 17H21C21.6 17 22 17.4 22 18V20C22 20.6 21.6 21 21 21Z"
                                        fill="currentColor" />
                                </svg> </span>
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <div v-if="isCoursesEmpty" class="text-center py-10">
            <div class="mb-5">Looks like you don't have any courses yet.</div>
            <div class="mb-5">To add a course either click ‘Add Course’ or ‘Contact Us’ for more information.</div>
            <button class="btn btn-light" @click="onContactUs">Contact Us</button>
        </div>

        <div class="tab-content" v-if="!isCoursesEmpty"> 
            <BlockView :courses="paginatedCourses" :loading="loading" />
            <ListView :courses="paginatedCourses" :loading="loading" />
            <div class="d-flex justify-content-end py-10" v-if="!loading && pagination.total > 0 ">
                <TablePagination :total-pages="pagination.last_page" :total="pagination.total"
                    :per-page="pagination.per_page" :current-page="pagination.current_page"
                    @page-change="onPageChange" />
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, computed, watch } from 'vue';
import BlockView from "@/components/employer-courses/CoursesBlockView.vue";
import ListView from "@/components/employer-courses/CoursesListView.vue";
import TablePagination from "@/components/kt-datatable/table-partials/table-content/table-footer/TablePagination.vue";
import ApiService from "@/core/services/ApiService";
import {
    Field
} from "vee-validate";
import Multiselect from '@vueform/multiselect';

interface Student {
    id?: number;
    name?: string;
    email?: string;
    location?: string | null;
    year?: string | null;
    initials?: string | null;
    industriesCount?: number;
    jobsCount?: number;
    companiesCount?: number;
}

interface PaginationMeta {
    current_page: number;
    from: number;
    last_page: number;
    per_page: number;
    to: number;
    total: number;
}

interface PaginationLinks {
    first: string;
    last: string;
    prev: string | null;
    next: string | null;
}

interface StudentsResponse {
    data: { data: Student[] };
    meta: PaginationMeta;
    links: PaginationLinks;
}



export default defineComponent({
    components: {
        BlockView,
        ListView,
        TablePagination,
        Field,
        Multiselect,
    },
    setup() {
        const banner = ref();
        const students = ref<Student[]>([]);
        const paginationMeta = ref<PaginationMeta>({
            current_page: 1,
            from: 0,
            last_page: 1,
            per_page: 8,
            to: 0,
            total: 0,
        });
        const paginationLinks = ref<PaginationLinks>({
            first: '',
            last: '',
            prev: null,
            next: null,
        });
        const loading = ref(false);
        const searchQuery = ref('');
        const debouncedSearch = ref('');
        let debounceTimeout: any = null;

        const currentTab = ref('all-students');
        const filters = ref({
            courseType: "",
            difficulty: "",
        });
        const filteredCourses = ref<any[]>([]);

        const companyModules = ref({
            lessons: [],
            virtual_work_experiences: [],
            skills_trainings: []
        });

        const pagination = ref({
            current_page: 1,
            per_page: 8,
            last_page: 1,
            total: 0,
            to: 0,
            from: 0
        });

        const paginatedCourses = ref<any[]>([]);

        const courseTypes = [
            { value: "Virtual Work Experience", label: "Virtual Work Experience" },
            { value: "Lessons", label: "Lessons" },
            { value: "Skills Training", label: "Skills Training" },
        ];

        const difficulty = [
            { value: "Beginner", label: "Beginner" },
            { value: "Intermediate", label: "Intermediate" },
            { value: "Expert", label: "Expert" },
        ];

        banner.value = {
            'trailer_video': null,
            'video': null,
        }
        const fetchCompanyModules = async (page = 1) => {
            loading.value = true;
            try {
                const params = new URLSearchParams();
                if (filters.value.courseType) params.append('courseType', filters.value.courseType);
                if (filters.value.difficulty) params.append('difficulty', filters.value.difficulty);
                if (debouncedSearch.value) params.append('search', debouncedSearch.value);
                params.append('per_page', pagination.value.per_page.toString());
                params.append('page', page.toString());

                const response = await ApiService.get(`employer/get-company-modules?${params.toString()}`);
                // Backend now returns a single paginated array and meta
                const data = response.data.data || [];
                const meta = response.data.meta || {};
                paginatedCourses.value = data.map((item: any) => {
                    // Try to infer courseType if not present
                    let courseType = item.module_type || item.courseType;
                    if (!courseType) {
                        if (item.hasOwnProperty('order')) courseType = 'Lessons';
                        else if (item.hasOwnProperty('template_type') || item.hasOwnProperty('workexperience_template_id')) courseType = 'Virtual Work Experience';
                        else courseType = 'Skills Training';
                    }
                    return {
                        ...item,
                        courseType,
                        difficulty: item.level || item.difficulty || '',
                    };
                });
                pagination.value.current_page = meta.current_page || 1;
                pagination.value.last_page = meta.last_page || 1;
                pagination.value.total = meta.total || paginatedCourses.value.length;
                pagination.value.from = meta.from || ((pagination.value.total > 0) ? ((pagination.value.current_page - 1) * pagination.value.per_page + 1) : 0);
                pagination.value.to = meta.to || ((pagination.value.total > 0) ? Math.min(pagination.value.from + pagination.value.per_page - 1, pagination.value.total) : 0);
            } catch (error) {
                paginatedCourses.value = [];
                pagination.value = { current_page: 1, per_page: pagination.value.per_page, last_page: 1, total: 0, from: 0, to: 0 };
            } finally {
                loading.value = false;
            }
        };

        const onPageChange = (page: number) => {
            fetchCompanyModules(page);
        };

        const applyFilter = () => {
            pagination.value.current_page = 1;
            fetchCompanyModules(1);
        };

        const isCoursesEmpty = computed(() => {
            return paginatedCourses.value.length === 0 && !loading.value;
        });

        // When searchQuery changes, debounce and trigger applyFilter
        watch(searchQuery, (newVal) => {
            if (debounceTimeout) clearTimeout(debounceTimeout);
            debounceTimeout = setTimeout(() => {
                debouncedSearch.value = newVal;
                applyFilter(); // Trigger backend filter on search
            }, 300); // 300ms debounce
        });

        const scrollToSection = (sectionId: string) => {
            const section = document.getElementById(sectionId);

            if (section) {
                section.scrollIntoView({ behavior: 'smooth' });
            }
        }

        onMounted(() => {
            fetchCompanyModules();
        });

        return {
            banner,
            loading,
            searchQuery,
            currentTab,
            scrollToSection,
            courseTypes,
            difficulty,
            filters,
            filteredCourses,
            isCoursesEmpty,
            applyFilter,
            paginationMeta,
            fetchCompanyModules,
            paginatedCourses,
            pagination,
            onPageChange,
        }
    },
})
</script>

<style>
.banner {
    background-color: #bbb;
    display: block;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    position: relative;
    overflow: hidden;
    min-height: calc(56.25vw - 149px);
}

.full-view-banner {
    margin-left: -30px;
    margin-right: -30px;
}

.banner-video {
    height: 100%;
}

.banner-video>video {
    width: 101% !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}


.nav-tabs .nav-link {
    border: none !important;
    color: #A1A5B7;
    background-color: transparent !important;
}

.nav-tabs .nav-link:hover {
    color: #000;
    background-color: transparent !important;
}

.nav-tabs .nav-link.active {
    color: #000;
    font-weight: bold;
    border-bottom: 2px solid #000 !important;
    background-color: transparent !important;
    box-shadow: none !important;
}


@media (max-width: 1280px) {
    .banner {
        height: 56.25vw;
    }

    .banner_detail_box {
        left: 40%;
    }

    .banner-video>video {
        height: 100% !important;
        width: calc(65vw + 65vh) !important;
    }
}


@media (max-width: 991px) {

    .full-view-banner,
    .module-sections {
        margin-left: -20px;
        margin-right: -20px;
    }

    .full-view-banner {
        margin-top: 58.16px;
    }

}



@media (max-width: 991px) and (min-width: 768px) and (orientation:portrait) {
    .banner {
        height: 86.25vw;
    }

    .banner-video>video {
        height: 100% !important;
        width: calc(66vw + 66vh) !important;
    }
}

@media (max-width: 991px) and (orientation:landscape) {
    .banner-video>video {
        height: auto !important;
        width: calc(70vw + 70vh) !important;
    }
}

@media (max-width: 767px) {

    .banner {
        height: calc(100vh - 300px);
    }

    .banner_detail_box {
        left: 50%;
    }

}

@media (max-width: 575px) {
    .full-view-banner {
        margin-top: 0;
    }

    .banner_detail_box {
        width: 70vw !important;
    }

    .banner-video>video {
        height: 100% !important;
        width: calc(90vw + 90vh) !important;
    }

}
</style>