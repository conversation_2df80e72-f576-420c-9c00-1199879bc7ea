<template>
    <div class="full-view-banner banner" v-bind:style="{
        backgroundImage: 'url(' + lesson.background_imagepath + ')',
    }">
        <div v-if="lesson.background_video" class="banner-video" v-html="lesson.background_video"></div>
        <div style="
                position: absolute;
                width: 100%;
                height: 100%;
                opacity: 0.3;
                background: #000;
            "></div>
        <div class="banner_detail_box w-450px">
            <div v-if="lesson.badge && !lesson.feedback && lesson.compeletedpercent !== 100" class="mt-4 mb-4">
                <div class="row g-3">
                    <div class="col-6">
                        <div class="d-flex align-items-center mb-10">
                            <img :src="lesson.badge.image_fullpath" :alt="lesson.badge.name" class="me-3" width="25" />
                            <div>
                                <p class="mb-1 fw-normal text-light fs-4">
                                    {{ lesson.badge.name }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <h1 class="fw-normal text-light">Lesson</h1>
            <h1 class="display-4 fw-normal mb-4 text-light" v-html="lesson.title"></h1>
            <div class="row text-light align-items-center">
                <div class="col-md-4 col-lg-3" v-if="
                    lesson.estimated_time &&
                    (lesson.estimated_time.hours ||
                        lesson.estimated_time.minutes)
                ">
                    <i class="fa-regular fa-clock text-white me-2"></i>
                    <span v-if="
                        lesson.estimated_time && lesson.estimated_time.hours
                    " v-text="lesson.estimated_time.hours + 'h '"></span>
                    <span v-if="
                        lesson.estimated_time &&
                        lesson.estimated_time.minutes
                    " v-text="lesson.estimated_time.minutes + 'm'"></span>
                </div>
                <div class="col-md-4 col-lg-3" v-if="lesson.level">
                    <i class="fa fa-chart-simple text-white me-2"></i>
                    <span v-text="lesson.level"></span>
                </div>
                <div class="col-md-5 col-lg-5 mt-lg-0 mt-md-3">
                    <!-- <span class="fs-6 text-light">{{ lesson.compeletedpercent }}%</span> -->



                    <!-- <span v-if="
                        lesson.compeletedpercent === 100
                    " class="text-light px-5 py-2 rounded-pill w-100" style="background-color: #0062ff">
                        Completed
                    </span>

                    <span v-else-if="
                        lesson.compeletedpercent > 0 &&
                        lesson.compeletedpercent < 100
                    " class="text-dark px-5 py-2 rounded-pill" style="background-color: #e9ff1f">
                        {{ lesson.compeletedpercent }}% Completed
                    </span> -->
                    <ScormResultStatusBadge
                        :status="scormResult?.lesson_status"
                        :completion-percentage="lesson.compeletedpercent"
                    />
                </div>
            </div>
            <AnzscoDetails :tags-grouped="anzscoTagsGrouped" />
            <div class="row mt-5">
                <div class="col-sm-6 fs-6 text-light p-2" v-for="tag in lesson.tagged" :key="tag.id">
                    <i class="fa fa-check text-white"></i> {{ tag.tag_name }}
                </div>
            </div>
            <div class="row mt-5" v-if="lesson.foreground_video && lesson.compeletedpercent === 0">
                <div class="col-8 col-sm-6 col-md-10">
                    <button type="button" class="btn btn-black-custom btn-lg rounded-0 w-100" data-bs-toggle="modal" data-bs-target="#kt_modal_trailer" @click="onShowModal">
                        Watch Trailer
                        <img src="media/icons/play-circle-white.svg" alt="play" class="white-icon" />
                        <img src="media/icons/play-circle-black.svg" alt="play" class="black-icon" style="display: none" />
                    </button>
                </div>
            </div>
            <div class="row mt-5" v-if="
                !lesson.user_response ||
                lesson.user_response.status == 'Draft'
            ">
                <div class="col-8 col-sm-6 col-md-10">
                    <router-link class="btn btn-white-custom text-black btn-lg border-1 rounded-0 w-100" :to="{
                        name: 'task-lessons-section-detail',
                        params: {
                            id: currentlesson,
                            sectionid: latestStep,
                        },
                    }">
                        <span v-if="!lesson.hasresponse &&  lesson.compeletedpercent < 100 "> Get Started </span>

                        <span v-if="lesson.hasresponse">Continue</span>
                    </router-link>
                </div>
            </div>
            <div class="row mt-5" v-if="
                lesson.user_response &&
                lesson.user_response.status == 'Submitted'
            ">
                <div class="col-sm-6 col-md-10">
                    <router-link class="btn btn-white-custom btn-lg border-1 rounded-0 w-100" :to="{
                        name: 'task-lessons-view-response',
                        params: { id: currentlesson },
                    }">
                        View Response
                    </router-link>
                </div>
                <div class="col-sm-6 col-md-2 text-center my-auto" v-if="lesson.hasresponse">
                    <div v-if="lesson.compeletedpercent >= 100">
                        <p class="cursor-pointer fs-5 text-light d-flex gap-1 my-auto" @click="openResetModal">
                            <i class="fa-solid fa-rotate-right fs-5 text-light my-auto "></i> Reset
                        </p>
                    </div>
                </div>
            </div>
            <div class="row my-5" v-if="lesson.hasresponse">
                <div class="col-8 col-sm-6 col-md-10 text-center">
                    <router-link style="font-size: 12px !important" class="p-5 text-light fs-11px" :to="{
                        name: 'task-lessons-section-detail',
                        params: { id: currentlesson, sectionid: 1 },
                    }">
                        Edit Response
                    </router-link>
                </div>

            </div>


            <div class="row row-cols-3">

                <div v-if="lesson.hasresponse  && lesson.user_response.badge_key && lesson.compeletedpercent === 100" class="col my-auto">
                    <div class="row g-3 mt-2">
                        <div class="col-12">
                            <div class="d-flex align-items-center cursor-pointer" @click="openBadgeModal(lesson.user_response.badge_key)">
                                <img :src="lesson.badge.image_fullpath" :alt="lesson.badge.name" class="me-3" width="25" />
                                <div class="overflow-hidden">
                                    <p class="fw-bold text-light my-auto">
                                        View Badge
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div v-if="lesson.feedback" class="col my-auto">
                    <div class="row g-3 mt-2">
                        <div class="col-12">
                            <div class="d-flex align-items-center cursor-pointer w-fit-content" @click="openFeedbackModal">
                                <i class="fa-solid fa-comments text-light me-2" width="25"></i>
                                <div>
                                    <p class="fw-bold text-light my-auto">
                                        View Feedback
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div v-if="lesson.compeletedpercent === 100 && scormResult?.lesson_status" class="col my-auto">
                    <div class="row g-3 mt-2">
                        <div class="col-12">
                            <div class="d-flex align-items-center cursor-pointer w-fit-content" @click="openScormModal">
                                <i class="fa-solid fa-clipboard text-light me-2" width="25"></i>
                                <div>
                                    <p class="fw-bold text-light my-auto">
                                        View Results
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- <div v-if="lesson.response && lesson.user_response.response_path">
                    <div class="mt-4 mb-4 cursor-pointer" data-bs-toggle="modal" data-bs-target="#kt_modal_viewFile" @click="loadFile">
                        <div class="fw-bold">
                            <div>
                                <i class="fa-solid fa-file text-light"></i> <span class="mx-1"> View File </span>
                            </div>
                        </div>
                    </div>
                </div> -->
            </div>

        </div>
    </div>
    <div class="sticky-bottom">
        <div class="row" v-if="lesson.relatedlesson.length">
            <div class="col-12 position-relative">
                <!-- Related Moduled Work here -->
                <div class="bg-dark m-0 position-absolute w-300px bottom-0 end-0 pointer text-center">
                    <div class="text-white p-4 pointer" @click="toggleRelated">
                        Related Modules
                        <i class="fa text-white ms-2" :class="showRelatedLessonsList
                                ? 'fa-angle-down'
                                : 'fa-angle-up'
                            "></i>
                    </div>
                    <div class="related-overlay" :class="{ 'slide-up': showRelatedLessonsList }">
                        <template v-if="showRelatedLessonsList" v-for="relatedlesson in lesson.relatedlesson" :key="relatedlesson.id">
                            <div class="related-card pb-5 px-10">
                                <router-link class="d-block" :to="{
                                    name: 'task-lessons-detail',
                                    params: { id: relatedlesson.id },
                                }">
                                    <div class="mb-3" style="
                                            height: 235px;
                                            background-color: white;
                                            background-size: 100%;
                                        " v-bind:style="{
                                            backgroundImage:
                                                'url(' +
                                                relatedlesson.tileimage_fullpath +
                                                ')',
                                        }"></div>
                                </router-link>
                                <div class="m-0 d-flex related-tile-content text-white">
                                    <p class="float-start fs-7 wrap" v-text="relatedlesson.title"></p>
                                    <p class="float-end text-white">
                                        {{ relatedlesson.compeletedpercent }}%
                                    </p>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
                <!-- Related Moduled Work here -->
            </div>
        </div>
        <div class="row black-strip bg-black">
            <div class="col-8 p-10">
                <router-link class="fs-4 m-0 text-white" :to="{ name: 'tasks-lessons-list' }">
                    <span class="svg-icon svg-icon-primary svg-icon-2x">
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                            <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                <polygon points="0 0 24 0 24 24 0 24" />
                                <path d="M6.70710678,15.7071068 C6.31658249,16.0976311 5.68341751,16.0976311 5.29289322,15.7071068 C4.90236893,15.3165825 4.90236893,14.6834175 5.29289322,14.2928932 L11.2928932,8.29289322 C11.6714722,7.91431428 12.2810586,7.90106866 12.6757246,8.26284586 L18.6757246,13.7628459 C19.0828436,14.1360383 19.1103465,14.7686056 18.7371541,15.1757246 C18.3639617,15.5828436 17.7313944,15.6103465 17.3242754,15.2371541 L12.0300757,10.3841378 L6.70710678,15.7071068 Z" fill="#ffffff" fill-rule="nonzero" transform="translate(12.000003, 11.999999) scale(-1, 1) rotate(-270.000000) translate(-12.000003, -11.999999) " />
                            </g>
                        </svg>
                    </span>
                    Back to Lessons
                </router-link>
            </div>
            <div class="col-4 text-right p-10">
                <span class="cursor-pointer svg-icon svg-icon-primary svg-icon-2x float-end" @click="favouriteLesson(lesson.id)">
                    <i class="fa-solid fa-heart text-white fs-1" v-if="lesson.favourite"></i>
                    <i class="fa-regular fa-heart text-white fs-1" v-if="!lesson.favourite"></i>
                </span>

                <span v-if="lesson.audio" @click="toggleAudio" class="cursor-pointer svg-icon svg-icon-primary svg-icon-2x float-end me-5">
                    <i class="fa-solid fa-headphones text-white fs-1" title="Audio Instructions"></i>
                </span>

                <span v-if="lesson.worksheets.length" class="cursor-pointer svg-icon svg-icon-primary svg-icon-2x float-end me-5" data-bs-toggle="modal" data-bs-target="#kt_modal_worksheet">
                    <i class="bi bi-file-earmark text-white fs-1" title="Worksheets"></i>
                </span>

                <span v-if="
                    currentUser.isTeacher && lesson.teacher_resources.length
                " class="cursor-pointer svg-icon svg-icon-primary svg-icon-2x float-end me-5" data-bs-toggle="modal" data-bs-target="#kt_modal_teacherResources">
                    <i class="bi bi-box2 text-white fs-1" title="Teacher Resources"></i>
                </span>

                <span v-if="currentUser.isTeacher && lesson.curriculum" class="cursor-pointer svg-icon svg-icon-primary svg-icon-2x float-end me-5" data-bs-toggle="modal" data-bs-target="#kt_modal_curriculum">
                    <i class="bi bi-list-ul text-white fs-1" title="Curriculum"></i>
                </span>

                <span class="svg-icon svg-icon-primary svg-icon-2x dropdown float-end">
                </span>
                <!-- <div class="row">
                    <div class="col-3">
                        <div class="about-icon">3</div>
                    </div>
                    <div class="col-9 about-wrap pl-0">
                        <p>Upload your completed work and download your skills certificate. </p>
                    </div>
                </div> -->
            </div>

            <div class="row" id="audio" v-bind:style="{ height: audioHeight + 'px' }">
                <div class="col-12 text-center">
                    <audio controls controlsList="nodownload">
                        <source :src="lesson.audiofullpath" type="audio/mpeg" />
                        Your browser does not support the audio element.
                    </audio>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="kt_modal_trailer" tabindex="-1" style="display: none" aria-hidden="true" @click="onShowModal">
        <div class="modal-dialog modal-dialog-centered mw-900px">
            <div class="modal-content rounded-0">
                <div class="modal-body p-0" v-html="lesson.foreground_video"></div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="kt_modal_reset_responses" tabindex="-1" style="display: none" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-md">
            <div class="modal-content rounded-0">
                <div class="modal-body">
                    <p>
                        Do you really want to reset your response? Doing this
                        will clear your answers and also any feedback that has
                        been provided.
                    </p>
                    <button type="button" class="btn btn-primary btn-sm rounded-0" data-bs-dismiss="modal" @click="resetLesson(lesson.id)">
                        Yes
                    </button>
                    <button type="button" class="btn btn-sm btn-primary rounded-0 m-5" data-bs-dismiss="modal">
                        No
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div v-if="lesson.worksheets.length" class="modal fade" id="kt_modal_worksheet" tabindex="-1" style="display: none" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-xl">
            <div class="modal-content rounded-0">
                <div class="modal-body">
                    <h3>Worksheets</h3>
                    <ul class="list-inline profile-cards new-cards">
                        <li class="text-center hover-colored" v-for="worksheet in lesson.worksheets" :key="worksheet.id">
                            <a :href="'/teacherresources/' +
                                worksheet.id +
                                '/download'
                                ">
                                <div class="percentage">
                                    <img :src="worksheet.imagefullpath" :alt="worksheet.title" class="img-fluid" />
                                    <i class="fa fa-download bg-blue blue-check text-white"></i>
                                </div>
                                <div class="topic text-master">
                                    {{ worksheet.title }}
                                </div>
                            </a>
                        </li>
                    </ul>
                    <button type="button" class="btn btn-sm btn-primary rounded-0 m-5 float-end" data-bs-dismiss="modal">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div v-if="currentUser.isTeacher && lesson.teacher_resources.length" class="modal fade" id="kt_modal_teacherResources" tabindex="-1" style="display: none" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-xl">
            <div class="modal-content rounded-0">
                <div class="modal-body">
                    <h3>Teacher Resources</h3>
                    <ul class="list-inline profile-cards new-cards">
                        <li class="text-center hover-colored" v-for="teacher_resoucse in lesson.teacher_resources" :key="teacher_resoucse.id">
                            <a :href="'/teacherresources/' +
                                teacher_resoucse.id +
                                '/download'
                                ">
                                <div class="percentage">
                                    <img :src="teacher_resoucse.imagefullpath" :alt="teacher_resoucse.title" class="img-fluid" />
                                    <i class="fa fa-download bg-blue blue-check text-white"></i>
                                </div>
                                <div class="topic text-master">
                                    {{ teacher_resoucse.title }}
                                </div>
                            </a>
                        </li>
                    </ul>
                    <button type="button" class="btn btn-sm btn-primary rounded-0 m-5 float-end" data-bs-dismiss="modal">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div v-if="currentUser.isTeacher && lesson.curriculum" class="modal fade" id="kt_modal_curriculum" tabindex="-1" style="display: none" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-xl">
            <div class="modal-content rounded-0">
                <div class="modal-body">
                    <h3>Curriculum</h3>
                    <div v-html="lesson.curriculum"></div>
                    <button type="button" class="btn btn-sm btn-primary rounded-0 m-5 float-end" data-bs-dismiss="modal">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>

     <div class="modal fade" id="kt_modal_feedback" tabindex="-1" style="display: none" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered mw-600px">
            <div class="modal-content rounded-0" style="height: 80vh">
                <div class="modal-header text-white">
                    <h5 class="modal-title">Feedback</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4 bg-gray-50 text-left">
                    <div class="p-4 bg-white" style="height: 90%">
                        <p v-html="lesson.feedback" class="text-gray-700"></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <ScormResultModal
        ref="scormModalRef"
        v-if="scormResult?.lesson_status"
        :module="lesson"
        trackable-type="lessonsteps"
        :redo-success-route="{
            name: `task-lessons-section-detail`,
            params: {
                id: lesson.scorm_scoring_step_result?.lesson_id,
                sectionid: lesson.scorm_scoring_step_result?.number,
            },
        }"
        @viewBagdeDetails="openBadgeModal(lesson.user_response.badge_key)"
    />

    <BadgeModal :selectedBadge="selectedBadge"   @shareBadge="openShareBadgeModal" />
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, computed } from "vue";
import { Mutations, Actions } from "@/store/enums/StoreEnums";

import ApiService from "@/core/services/ApiService";
import { useStore } from "vuex";
import { useRouter, useRoute } from "vue-router";
import Swal from "sweetalert2/dist/sweetalert2.min.js";
import BadgeModal from "../Badges/BadgeModal.vue";
import AnzscoDetails from "@/components/modules/detail/AnzscoDetails.vue";
import ScormResultModal from "@/components/modules/response/ScormResultModal.vue";
import ScormResultStatusBadge from "@/components/modules/response/ScormResultStatusBadge.vue";

// Import Bootstrap
import * as bootstrap from 'bootstrap';

interface Badge {
    id: number;
    name: string;
    description?: string;
    image_fullpath: string;
    formatted_issue_date: string;
    formatted_expiration_date: string;
    video: string;
}
export default defineComponent({
    name: "lessons-detail",
    components: {
        BadgeModal,
        ScormResultModal,
        AnzscoDetails,
        ScormResultStatusBadge,
    },
    setup() {
        const store = useStore();
        const route = useRoute();
        const currentUser = store.getters.currentUser;
        onMounted(() => {
            fetchLessonDetail();
        });
        const lesson = ref();
        const latestStep = ref();
        const currentlesson = ref();
        const favLesson = ref();
        const setLesson = ref();
        const showRelatedLessonsList = ref();
        const selectedBadge = ref<Badge | null>(null);
        lesson.value = {
            id: 1,
            background_imagepath: null,
            background_video: null,
            firststepresponse: {
                id: 0,
            },
            worksheets: {},
            teacher_resources: {},
            relatedlesson: {},
            audio: [],
            user_response: {
                id: null,
                filename: "",
                response_path: "",
                activity_responses: {},
                badge_key : {}
            },
        };
        latestStep.value = 1;
        currentlesson.value = route.params.id;

        const fetchLessonDetail = () => {
            ApiService.get(`api/lessons`, currentlesson.value)
                .then(({ data }) => {
                    if (data.steps.length) {
                        for (let i = 0; i < data.steps.length; i++) {
                            if (
                                !data.steps[i]["user_response"] ||
                                i == data.steps.length - 1
                            ) {
                                latestStep.value = i + 1;
                                break;
                            }
                        }
                    }
                    lesson.value = data;
                    var breadcrumbs = store.getters.getBreadcrumbs;
                    breadcrumbs[2] = data.title;
                    store.commit(
                        Mutations.SET_BREADCRUMB_MUTATION,
                        breadcrumbs
                    );
                })
                .catch(({ }) => { });
        };
        const toggleRelated = () => {
            showRelatedLessonsList.value = !showRelatedLessonsList.value;
        };
        // const getlatestStep = (lessonsteps) => {
        //     if (lessonsteps.length) {
        //         for (let i = 1; i <= lessonsteps.length; i++) {
        //             if (!lessonsteps[i]["user_response"]) {
        //                 return i;
        //             }
        //         }
        //     }
        // };
        const favouriteLesson = (id) => {
            favLesson.value = {
                id: id,
            };
            ApiService.post(`api/lessons/` + id + `/fav`, favLesson.value)
                .then(({ data }) => {
                    lesson.value.favourite = data.favourite;
                })
                .catch(({ response }) => { });
        };
        const resetLesson = (id) => {
            setLesson.value = {
                id: id,
            };
            ApiService.post(`api/lessons/` + id + `/reset`, setLesson.value)
                .then(({ data }) => {
                    Swal.fire({
                        text: "This lesson and your previous responses have been reset.",
                        icon: "success",
                        buttonsStyling: false,
                        confirmButtonText: "Ok",
                        customClass: {
                            confirmButton:
                                "btn fw-semobold btn-light-primary rounded-0",
                        },
                    }).then(() => {
                        window.location.reload();
                    });
                    // lesson.value.favourite = data.favourite;
                })
                .catch(({ response }) => { });
        };

        const showAudio = ref(false);
        const audioHeight = ref(0);
        const toggleAudio = () => {
            const audio = document.getElementById("audio") as HTMLInputElement;
            if (showAudio.value) {
                audioHeight.value = 0;
                audio.style.margin = "0";
            } else {
                audio.classList.remove("d-none");
                audioHeight.value = audio.scrollHeight;
                audio.style.margin = "0px 0px 20px 0px";
            }
            showAudio.value = !showAudio.value;
        };

        const openBadgeModal = (badge: Badge) => {
            selectedBadge.value = badge;
            // Use Bootstrap's Modal API to show the modal
            const modalElement = document.getElementById('kt_modal_badge');
            if (modalElement) {
                // Check if there's an existing modal instance
                let bsModal = bootstrap.Modal.getInstance(modalElement);
                if (!bsModal) {
                    // If no instance exists, create a new one
                    bsModal = new bootstrap.Modal(modalElement, {
                        backdrop: 'static' // Prevents closing when clicking outside
                    });
                }
                bsModal.show();
            }
        };

        const openShareBadgeModal = (badge: Badge) => {
            selectedBadge.value = badge;
            // Use Bootstrap's Modal API to show the modal
            const modalElement = document.getElementById('kt_modal_share_badge');
            if (modalElement) {
                // Check if there's an existing modal instance
                let bsModal = bootstrap.Modal.getInstance(modalElement);
                if (!bsModal) {
                    // If no instance exists, create a new one
                    bsModal = new bootstrap.Modal(modalElement, {
                        backdrop: 'static' // Prevents closing when clicking outside
                    });
                }
                bsModal.show();
            }
        };

        const anzscoTagsGrouped = computed(() => {
            return lesson.value.anzsco_tag_names_grouped;
        });
        const scormModalRef = ref();
        const openScormModal = () => {
            scormModalRef.value?.openModal();
        }
        const scormResult = computed(() => {
            return lesson.value?.scorm_scoring_step_result?.user_scorm_result;
        });

        const openFeedbackModal = () => {
            // Use Bootstrap's Modal API to show the modal
            const modalElement = document.getElementById('kt_modal_feedback');
            if (modalElement) {
                // Check if there's an existing modal instance
                let bsModal = bootstrap.Modal.getInstance(modalElement);
                if (!bsModal) {
                    // If no instance exists, create a new one
                    bsModal = new bootstrap.Modal(modalElement, {
                        backdrop: 'static' // Prevents closing when clicking outside
                    });
                }
                bsModal.show();
            }
        };

        const openResetModal = () => {
            // Use Bootstrap's Modal API to show the modal
            const modalElement = document.getElementById('kt_modal_reset_responses');
            if (modalElement) {
                // Check if there's an existing modal instance
                let bsModal = bootstrap.Modal.getInstance(modalElement);
                if (!bsModal) {
                    // If no instance exists, create a new one
                    bsModal = new bootstrap.Modal(modalElement, {
                        backdrop: 'static' // Prevents closing when clicking outside
                    });
                }
                bsModal.show();
            }
        };

        return {
            currentUser,
            lesson,
            toggleRelated,
            currentlesson,
            showRelatedLessonsList,
            latestStep,
            favouriteLesson,
            resetLesson,
            // getlatestStep,
            toggleAudio,
            audioHeight,
            selectedBadge,
            openBadgeModal,
            openShareBadgeModal,
            anzscoTagsGrouped,
            scormModalRef,
            openScormModal,
            scormResult,
            openFeedbackModal,
            openResetModal,
        };
    },
    methods: {
        onHideModal() {
            // Access the video element and pause it
            const videoElement = document.querySelector(
                "#kt_modal_trailer video"
            ) as HTMLMediaElement;
            if (videoElement) {
                videoElement.pause();
            }
        },
        onShowModal() {
            // Access the video element and pause it
            const videoElement = document.querySelector(
                "#kt_modal_trailer video"
            ) as HTMLMediaElement;
            if (videoElement) {
                videoElement.play();
            }
        },
    },
    props: ["id"],
});
</script>

<style>
#audio {
    overflow: hidden;
    transition: height 0.5s ease;
}

/* Worksheet Css Starts */
.new-cards li {
    padding: 0 15px;
    width: 220px;
}

.profile-cards {
    margin: 0 -5px;
}

.list-inline {
    padding-left: 0;
    list-style: none;
}

.new-cards li,
.profile-cards li,
.template-tiles li {
    vertical-align: top;
}

.profile-cards li {
    width: 200px;
    display: inline-table;
    margin-top: 10px;
}

.list-inline>li {
    /* display: inline-block; */
    /* padding-right: 5px; */
    /* padding-left: 5px; */
}

.hover-colored .percentage {
    overflow: hidden;
    position: relative;
}

.profile-cards .percentage {
    background-position: center;
    height: 190px;
    line-height: 190px;
    background-size: 100%;
    background-repeat: no-repeat;
    color: #fff;
    transition: all 0.2s ease;
}

.img-fluid {
    max-width: 100%;
}

.hover-colored .percentage> :not(.tile-label) {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
}

.blue-check {
    line-height: 26px;
    width: 25px;
    height: 25px;
}

.bg-blue {
    background-color: #0a0afd !important;
}

.blue-check {
    line-height: 26px !important;
    width: 25px;
    height: 25px;
}

.profile-cards .topic {
    font-weight: 700;
    margin: 10px 0;
    line-height: 15px;
}

.text-master {
    color: #000 !important;
}

/* Worksheet Css Ends */

.swal2-popup {
    border-radius: 0px;
}

.wrap {
    overflow: hidden;
    max-width: 75ch;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.banner_detail_box {
    position: absolute;
    top: 50%;
    left: 20%;
    transform: translate(-50%, -50%);
}

.modal-backdrop {
    opacity: 0.8 !important;
}

#kt_modal_trailer .modal-content {
    background-color: transparent;
}

.sticky-bottom {
    z-index: auto;
}

.fa-heart:hover {
    font-weight: 900 !important;
}

.btn-black-custom,
.btn-white-custom {
    height: 55px;
    line-height: 55px;
    padding: 0 !important;
}

.btn-black-custom>i,
.btn-white-custom>i {
    font-size: 30px;
    vertical-align: -9px;
    padding-right: 0;
}

.btn-black-custom>img,
.btn-white-custom>img {
    width: 29px;
    margin-left: 5px;
    vertical-align: -8px;
}

.btn-black-custom:hover,
.btn-white-custom {
    background-color: #fff !important;
    color: #000 !important;
}

.btn-black-custom,
.btn-white-custom:hover {
    background-color: #000 !important;
    color: #fff !important;
}

.btn-black-custom:hover *,
.btn-white-custom * {
    color: #000 !important;
}

.btn-black-custom *,
.btn-white-custom:hover * {
    color: #fff !important;
}

.btn-black-custom:hover>.white-icon {
    display: none;
}

.btn-black-custom:hover>.black-icon {
    display: inline !important;
}

.pointer {
    cursor: pointer;
}

.related-overlay {
    overflow: overlay;
    height: 0px;
    transition: height 0.3s;
}

.slide-up {
    height: calc(100vh - 320px) !important;
}

.related {
    right: 5% !important;
}

.related-tile-content>p:first-child {
    flex: 75%;
}

.banner {
    background-color: #000;
    /* background-image: url("/images/vwe/home-parallax.jpg"); */
    display: block;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    position: relative;
    overflow: hidden;
    min-height: calc(56.25vw - 149px);
    /*min-height: calc(56.25vw - 332px);*/
}

.banner-video {
    height: 100%;
}

.banner-video>video {
    /* height: 100%; */
    width: 101% !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.full-view-banner,
.black-strip {
    margin-left: -30px;
    margin-right: -30px;
}

.bg-dark {
    background: #000 !important;
    margin-left: -30px;
    margin-right: -30px;
}

div#kt_app_content {
    padding-top: 0px;
    padding-bottom: 0px;
}

@media (max-width: 1280px) {
    .banner {
        height: 56.25vw;
    }

    .banner_detail_box {
        left: 40%;
    }

    .banner-video>video {
        height: 100% !important;
        width: calc(65vw + 65vh) !important;
    }
}

@media (max-width: 991px) {

    .full-view-banner,
    .black-strip {
        margin-left: -20px;
        margin-right: -20px;
    }

    .full-view-banner {
        margin-top: 58.16px;
    }
}

@media (max-width: 991px) and (min-width: 768px) and (orientation: portrait) {
    .slide-up {
        height: calc(100vw - 220px) !important;
    }

    .banner {
        height: 86.25vw;
    }

    .banner-video>video {
        height: 100% !important;
        width: calc(66vw + 66vh) !important;
    }
}

@media (max-width: 991px) and (orientation: landscape) {
    .banner-video>video {
        height: auto !important;
        width: calc(70vw + 70vh) !important;
    }
}

@media (max-width: 767px) {
    /* .full-view-banner {
                            margin-left: -30px;
                            margin-right: -30px;
                        } */

    .banner {
        height: calc(100vh - 300px);
    }

    .banner_detail_box {
        left: 50%;
    }
}

@media (max-width: 575px) {
    div#kt_app_content {
        padding-top: 30px;
    }

    .banner_detail_box {
        width: 70vw !important;
    }

    .banner {
        height: calc(100vh - 242px);
    }

    .banner-video>video {
        height: 100% !important;
        width: calc(90vw + 90vh) !important;
    }

    .full-view-banner {
        margin-top: 0;
    }
}

</style>