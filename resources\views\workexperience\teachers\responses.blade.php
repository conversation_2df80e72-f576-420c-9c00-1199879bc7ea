@extends('layouts.admin', ['noGreyBg' => 'no-grey-bg'])
@section('pageTitle', 'Virtual Work Experience Responses')
@section('breadcrumbs', Breadcrumbs::render('workexperience-responses'))
@section('content')
    <style>

    </style>
    @if (session()->has('message'))
        <div class="alert alert-success text-center">
            <a href="#" class="close" data-dismiss="alert" aria-label="close"></a>
            {{ session()->get('message') }}
        </div>
    @endif

    <div class="row">
        <div class="col-md-12">
            <div class="card card-default">
                <div class="card-header separator">
                    <div class="card-title">
                        <img src="{{ asset('images/favicon.png') }}" alt="*" class="card-title-X">
                        <div class="card-title  custom-card-title">Virtual Work Experience</div>
                    </div>
                </div>
                <div class="card-block">
                    <form method="GET">
                        <div class="row clearfix">
                            <div class="col-sm-3 col-md-4">
                                <div class="form-group form-group-default">
                                    <label>Student's name</label>
                                    <input type="text" name="name" value="{{ old('name') }}" class="form-control">
                                </div>
                            </div>
                            <div class="col-sm-6 col-md-4">
                                <div class="form-group form-group-default form-group-default-select2">
                                    <label>Task</label>
                                    <select class="full-width" name="template" data-init-plugin="select2" data-placeholder="Select.." data-allowClear="true">
                                        <option value="">Any</option>
                                        @foreach ($templates as $template)
                                            <option value="{{ $template->id }}" @if (old('template') == $template->id) selected="selected" @endif>{{ $template->title }}</option>
                                        @endforeach
                                    </select>

                                </div>
                            </div>
                            <div class="col-sm-6 col-md-2">
                                <div class="form-group form-group-default form-group-default-select2">
                                    <label>Year</label>
                                    <select class="full-width" name="standard" data-init-plugin="select2" data-placeholder="Select..">
                                        <option value="">Any</option>
                                        @foreach ($standards as $standard)
                                            <option value="{{ $standard->id }}" @if (old('standard') == $standard->id) selected="selected" @endif>{{ $standard->title }}</option>
                                        @endforeach

                                    </select>
                                </div>
                            </div>
                            {{-- <div class="col-sm-6 col-md-2">
                                <div class="form-group form-group-default form-group-default-select2">
                                    <label>Feedback</label>
                                    <select class="full-width" name="feedback" data-init-plugin="select2" data-placeholder="Select..">
                                        <option value="">Any</option>
                                        <option value="yes" @if (old('feedback') == 'yes') selected="selected" @endif>Yes
                                        </option>
                                        <option value="no" @if (old('feedback') == 'no') selected="selected" @endif>No
                                        </option>
                                        @if (Auth::user()->isAdmin())
                                            <option value="pending" @if (old('feedback') == 'pending') selected="selected" @endif>
                                                Pending</option>
                                        @endif

                                    </select>
                                </div>
                            </div> --}}
                            <div class="col-sm-6 col-md-2">
                                <div class="form-group form-group-default form-group-default-select2">
                                    <label>Approved</label>
                                    <select class="full-width" name="approve" data-init-plugin="select2" data-placeholder="Select..">
                                        <option value="">Any</option>
                                        <option value="yes" @if (old('approve') == '1') selected="selected" @endif>Yes
                                        </option>
                                        <option value="no" @if (old('approve') == '0') selected="selected" @endif>No
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row clearfix">
                            @if ((Auth::user()->isTeacher() && Auth::user()->school->campuses()->count() > 1) || (Auth::user()->isStaff() && Auth::user()->organisation->campuses()->count() > 1))
                                <div class="col-sm-6 col-md-2">
                                    <div class="form-group form-group-default form-group-default-select2">
                                        <label>Campuses</label>
                                        <select class="full-width" name="campus" data-init-plugin="select2">
                                            <option value="">Any</option>
                                            @foreach ($campuses as $campus)
                                                <option value="{{ $campus->id }}" @if (old('campus') == $campus->id) selected="selected" @endif>{{ $campus->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            @endif
                            <div class="col-sm-12 col-md-12 text-right">
                                <button type="submit" class="btn btn-primary btn-custom-sm">Search</button>
                            </div>
                        </div>
                    </form>
                    <div class="table-responsive pt-4">
                        <div class="my-2">
                            <a class="btn btn-primary" href="/we/export?fullname={{ old('name') }}&template={{ old('template') }}&year={{ old('standard') }}&feedback={{ old('feedback') }}&campus={{ old('campus') }}">Export All <i class="fa fa-download"></i></a>
                            </div>
                        <table class="table table-hover dataTable no-footer" id="teachers-table">
                            <thead>
                                <tr>
                                    <th>First Name</th>
                                    <th>Last Name</th>
                                    <th>Year</th> @if ((Auth::user()->isTeacher() && Auth::user()->school->campuses()->exists()) || (Auth::user()->isStaff() && Auth::user()->organisation->campuses()->exists()))
                                        <th>Campus</th>
                                    @endif
                                    <th>Template</th>
                                    <th>Submitted On</th>
                                    <th class="dontexport">Approved</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse ($responses as $key => $response)
                                    <tr>
                                        <td>
                                            {{ @$response->student->profile->firstname }}
                                            <input type="hidden" name="student" id="student" value="{{ @$response->student->name }}">
                                        </td>
                                        <td>
                                            {{ @$response->student->profile->lastname }}
                                        </td>
                                        <td>{{ @$response->student->profile->class->title == 'I’ve finished high school'? 'Graduated ' . @$response->student->profile->graduate_year: @$response->student->profile->class->title }}
                                        </td>
                                        @if ((Auth::user()->isTeacher() && Auth::user()->school->campuses()->exists()) || (Auth::user()->isStaff() && Auth::user()->organisation->campuses()->exists()))
                                            <td>
                                                {{ @$response->student->campuses->first()->name }}
                                            </td>
                                        @endif
                                        <td>{{ $response->template->title }}</td>
                                        <td>{{ $response->submitted_at }}</td>
                                        <td>
                                            @if ($response->approve)
                                                <i class="fa fa-check text-blue"></i>
                                            @else
                                                <i class="fa fa-times text-danger"></i>
                                            @endif
                                        </td>
                                        <td>
                                            @if ($response)
                                                {{-- <a class="m-r-10" href="{{ route('workexperience.responsedownload', $response->id) }}" data-toggle="tooltip" title="Download student response">
                                                    <i class="fa fa-cloud-download"></i>
                                                </a> --}}
                                                <a class="m-r-10" href="/#/tasks/vwe/{{ $response->template_id }}/students/{{ $response->student->id }}" data-toggle="tooltip" title="View Response" target="_blank">
                                                    <i class="fa fa-file-text-o"></i>
                                                </a>
                                                @if ($response->feedback && $response->approve)
                                                    <a href="#" class="m-r-10" data-toggle="modal" data-target="#feedbackModal" data-id="{{ $response->id }}">
                                                        <i class="fa fa-commenting-o " data-toggle="tooltip" title="View feedback"></i>
                                                    </a>
                                                @endif
                                                <a class="m-r-10" href="/#/tasks/vwe/{{ $response->template_id }}" data-toggle="tooltip" title="View Module" target="_blank">
                                                    <i class="fa fa-eye text-black"></i>
                                                </a>
                                                <a href="{{ url('workexperiencetemplates/responses/' . $response->id) }}" data-method="delete" data-token="{{ csrf_token() }}" data-confirm="Are you sure?">
                                                    <i class="fa fa-trash-o text-danger" data-toggle="tooltip" title="Delete response"></i>
                                                </a>
                                            @else
                                                <i class="fa fa-times" title="Incomplete" data-toggle="tooltip"></i>
                                            @endif
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="7" class="text-center">Sorry! No result found.</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                        {{ $responses->appends(array_filter(['name' => old('name'),'template' => old('template'),'standard' => old('standard'),'feedback' => old('feedback')]))->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
    @push('modals')
        <div class="modal fade slide-up" id="feedbackModal" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content-wrapper">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title mb-2" id="exampleModalLabel">Feedback</h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body" id="feedback">
                        </div>
                        <div class="modal-footer">
                            <a href="" id="downloadFeedback" class="btn btn-primary" target="_blank">Download <i class="fa fa-download"></i></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endpush
    @push('scriptslib')
        <script src="https://cdn.ckeditor.com/4.14.1/standard/ckeditor.js"></script>
    @endpush
    @push('scripts')
        <script>
            jQuery(document).ready(function() {
                var oTable = $('#teachers-table').DataTable({
                    "paging": false,
                    "ordering": false,
                    searching: false,
                    stateSave: true,
                    dom: "<'row'<'col-md-12'f<'hidden-xs'l>B>>" + "<'table-responsive'>",
                    "lengthMenu": [
                        [10, 25, 50, 100/* , -1 */],
                        [10, 25, 50, 100/* , "All" */]
                    ],
                    autoWidth: false,
                    buttons: [/* {
                        extend: 'csv',
                        text: '<i class="fa fa-download"></i> CSV',
                        exportOptions: {
                            columns: 'th:not(:last-child):not(.dontexport)'
                        }
                    } */],

                });

                var $trigger;
                jQuery('#feedbackModal').on('show.bs.modal', function(e) {
                    $trigger = $(e.relatedTarget);
                    jQuery("#downloadFeedback").attr("href", "/vwe-certificate/" + $trigger.data('id'));
                    jQuery("#formFeedback").attr("action", "/workexperiencetemplates/feedback/" + $trigger.data('id'));
                    jQuery('#feedback').val('');
                    jQuery.ajax({
                        url: "/workexperiencetemplates/feedback/" + $trigger.data('id'),
                        success: function(data) {
                            jQuery('#feedback').html(data);
                        }
                    });
                });
                $('#feedbackModal').on('shown.bs.modal', function() {
                    $(document).off('focusin.modal');
                });

                jQuery('#formFeedback').validate({
                    rules: {
                        year: {
                            required: true
                        },
                        feedback: {
                            required: function() {
                                CKEDITOR.instances.feedback.updateElement();
                            },
                        }
                    },
                });
            });
        </script>
    @endpush
@endsection
