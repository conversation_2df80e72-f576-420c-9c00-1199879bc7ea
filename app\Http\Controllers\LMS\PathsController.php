<?php

namespace App\Http\Controllers\LMS;

use App\Http\Controllers\Controller;
use App\Services\ANZSCO;
use Illuminate\Http\Request;

class PathsController extends Controller
{
    protected $anzsco;

    public function __construct(
        ANZSCO $anzsco
    )
    {
        $this->anzsco = $anzsco;
    }

    public function myPath()
    {
        $res = $this->anzsco->getModulesSuggestionsForSelectedJobs(auth()->id());
        
        if (!$res['success']) {
            return redirect()->to('/')->with('message', $res['message'] ?? '');  
        }

        $selectedJobsWithModules = $res['data'] ?? collect();

        return view('paths.my-path', compact('selectedJobsWithModules'));
    }

    public function myPathModules ()
    {
        $pathsModules = $this->anzsco->getPathsModulesToDo(auth()->id());
        
        return response()->json([
            'success' => true,
            'modules' => $pathsModules
        ]);
    }
}
