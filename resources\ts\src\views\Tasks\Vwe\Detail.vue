<template>
    <div
        class="full-view-banner banner"
        v-bind:style="{
            backgroundImage: 'url(' + vwe.background_imagepath + ')',
        }"
    >
        <div
            v-if="vwe.background_videoid"
            class="banner-video"
            v-html="vwe.background_videoid"
        ></div>
        <div
            style="
                position: absolute;
                width: 100%;
                height: 100%;
                opacity: 0.3;
                background: #000;
            "
        ></div>
        <div class="banner_detail_box w-450px">

            <div v-if="vwe.badge && !vwe.feedback && vwe.compeletedpercent !== 100" class="mt-4 mb-4">
                <div class="row g-3">
                    <div class="col-6">
                        <div class="d-flex align-items-center mb-10">
                            <img :src="vwe.badge.image_fullpath" :alt="vwe.badge.name" class="me-3" width="25"/>
                            <div>
                                <p class="mb-1 fw-bold text-light">
                                    {{ vwe.badge.name }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <h1 class="fw-normal text-light">Virtual Work Experience</h1>
            <h1
                class="display-4 fw-normal mb-4 text-light"
                v-html="vwe.title"
            ></h1>
            <div class="row text-light align-items-center">
                <div
                    class="col-md-4 col-lg-3"
                    v-if="
                        vwe.estimated_time &&
                        (vwe.estimated_time.hours || vwe.estimated_time.minutes)
                    "
                >
                    <i class="fa-regular fa-clock text-white me-2"></i>
                    <span
                        v-if="vwe.estimated_time && vwe.estimated_time.hours"
                        v-text="vwe.estimated_time.hours + 'h '"
                    ></span>
                    <span
                        v-if="vwe.estimated_time && vwe.estimated_time.minutes"
                        v-text="vwe.estimated_time.minutes + 'm'"
                    ></span>
                </div>
                <div class="col-md-4 col-lg-3" v-if="vwe.level">
                    <i class="fa fa-chart-simple text-white me-2"></i>
                    <span v-text="vwe.level"></span>
                </div>
                <div class="col-md-5 col-lg-5 mt-lg-0 mt-md-3">

                    <!-- <span v-if=" vwe.compeletedpercent === 100 && vwe?.user_response?.approve !== 1 ">

                        <span class="text-dark px-5 py-2 rounded-pill w-auto d-inline-block"
                            style="background-color: #CDD6DD;">
                            Submitted For Review
                        </span>
                    </span>

                    <span
                        v-if="vwe.compeletedpercent === 100 && vwe?.user_response?.approve == 1"
                        class="fs-6 text-light px-5 py-2 rounded-pill w-100"
                        style="background-color: #0062ff"
                    >
                        Completed
                    </span>
                    <span
                        v-else-if="
                            vwe.compeletedpercent > 0 &&
                            vwe.compeletedpercent < 100
                        "
                        class="fs-6 text-dark px-5 py-2 rounded-pill"
                        style="background-color: #e9ff1f"
                    >
                        {{ vwe.compeletedpercent }}% Completed
                    </span> -->
                    <ScormResultStatusBadge
                        :status="scormResult?.lesson_status"
                        :completion-percentage="vwe.compeletedpercent"
                    />
                </div>
            </div>
            <AnzscoDetails :tags-grouped="anzscoTagsGrouped" />
            <div class="row mt-5">
                <div
                    class="col-sm-6 fs-6 text-light p-2"
                    v-for="tag in vwe.tagged"
                    :key="tag.id"
                >
                    <i class="fa fa-check text-white"></i> {{ tag.tag_name }}
                </div>
            </div>
            <div class="row mt-5" v-if="vwe.foreground_videoid && vwe.compeletedpercent === 0">
                <div class="col-8 col-sm-6 col-md-10">
                    <button
                        type="button"
                        class="btn btn-black-custom btn-lg rounded-0 w-100"
                        data-bs-toggle="modal"
                        data-bs-target="#kt_modal_trailer"
                        @click="onShowModal"
                    >
                        Watch Trailer
                        <img
                            src="media/icons/play-circle-white.svg"
                            alt="play"
                            class="white-icon"
                        />
                        <img
                            src="media/icons/play-circle-black.svg"
                            alt="play"
                            class="black-icon"
                            style="display: none"
                        />
                    </button>
                </div>
            </div>
            <div
                class="row mt-5"
                v-if="!vwe.user_response || vwe.user_response.status == 'Draft'"
            >
                <div class="col-8 col-sm-6 col-md-10">
                    <router-link
                        class="btn btn-white-custom text-black btn-lg border-1 rounded-0 w-100"
                        :to="{
                            name: 'task-vwe-section-detail',
                            params: { id: currentvwe, sectionid: latestStep },
                        }"
                    >
                        <span
                            v-if="
                                !vwe.hasresponse && vwe.compeletedpercent < 100
                            "
                            >Get Started</span
                        >
                        <span v-if="vwe.hasresponse">Continue</span>
                    </router-link>
                </div>
            </div>
            <div
                class="row mt-5"
                v-if="
                    vwe.user_response && vwe.user_response.status == 'Submitted'
                "
            >
                <div class="col-8 col-sm-6 col-md-10">
                    <router-link
                        class="btn btn-white-custom btn-lg border-1 rounded-0 w-100"
                        :to="{
                            name: 'task-vwe-view-response',
                            params: { id: currentvwe },
                        }"
                    >
                        View Response
                    </router-link>
                </div>
                <div class="col-sm-6 col-md-2 text-center my-auto" v-if="vwe.hasresponse">
                    <div v-if="vwe.compeletedpercent >= 100">
                        <p class="cursor-pointer fs-5 text-light d-flex gap-1 my-auto" data-bs-toggle="modal" data-bs-target="#kt_modal_reset_responses">
                            <i class="fa-solid fa-rotate-right fs-5 text-light my-auto "></i> Reset
                        </p>
                    </div>
                </div>

            </div>
            <div class="row my-5" v-if="vwe.hasresponse">
                <div class="col-8 col-sm-6 col-md-10 text-center">
                    <router-link
                        style="font-size: 12px !important"
                        class="p-5 text-light fs-11px"
                        :to="{
                            name: 'task-vwe-section-detail',
                            params: { id: currentvwe, sectionid: 1 },
                        }"
                    >
                        Edit Response
                    </router-link>
                </div>

            </div>

            <div class="row row-cols-3">

                <div v-if="vwe.hasresponse && vwe.user_response.badge_key && vwe?.user_response?.approve === 1 && vwe.compeletedpercent === 100" class="col my-auto">
                    <div class="row g-3 mt-2">
                        <div class="col-12">
                            <div class="d-flex align-items-center cursor-pointer" @click="openBadgeModal(vwe.user_response.badge_key)">
                                <img :src="vwe.badge.image_fullpath" :alt="vwe.badge.name" class="me-3" width="25" />
                                <div class="overflow-hidden">
                                    <p class="fw-bold text-light my-auto">
                                        View Badge
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div v-if="vwe?.user_response?.approve === 1" class="col my-auto">
                    <div class="row g-3 mt-2">
                        <div class="col-12">
                            <div class="d-flex align-items-center cursor-pointer" data-bs-toggle="modal" data-bs-target="#kt_modal_viewFile" @click="loadCertificate">
                                <i class="fa-solid fa-file text-light me-2" width="25"></i>
                                <div>
                                    <p class="fw-bold text-light my-auto">
                                        View Certificate
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div v-if="
                    vwe.user_response &&
                    vwe.user_response.feedback &&
                    vwe.user_response.approve === 1
                " class="col my-auto">
                    <div class="row g-3 mt-2">
                        <div class="col-12">
                            <div class="d-flex align-items-center cursor-pointer" data-bs-toggle="modal" data-bs-target="#kt_modal_feedback">
                                <i class="fa-solid fa-comments text-light me-2" width="25"></i>
                                <div>
                                    <p class="fw-bold text-light my-auto">
                                        View Feedback
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div v-if="vwe.compeletedpercent === 100 && scormResult?.lesson_status" class="col my-auto">
                    <div class="row g-3 mt-2">
                        <div class="col-12">
                            <div class="d-flex align-items-center cursor-pointer w-fit-content" @click="openScormModal">
                                <i class="fa-solid fa-clipboard text-light me-2" width="25"></i>
                                <div>
                                    <p class="fw-bold text-light my-auto">
                                        View Results
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>

        </div>
    </div>
    <div class="sticky-bottom">
        <div v-if="vwe.relatedModules.length" class="row">
            <div class="col-12 position-relative">
                <!-- Related Moduled Work here -->
                <div
                    class="bg-dark m-0 position-absolute w-300px bottom-0 end-0 pointer text-center"
                >
                    <div class="text-white p-4 pointer" @click="toggleRelated">
                        Related Modules
                        <i
                            class="fa text-white ms-2"
                            :class="
                                showRelatedModuleList
                                    ? 'fa-angle-down'
                                    : 'fa-angle-up'
                            "
                        ></i>
                    </div>
                    <div
                        class="related-overlay"
                        :class="{ 'slide-up': showRelatedModuleList }"
                    >
                        <template
                            v-if="showRelatedModuleList"
                            v-for="relatedModule in vwe.relatedModules"
                            :key="relatedModule.id"
                        >
                            <div class="related-card pb-5 px-10">
                                <router-link
                                    class="d-block"
                                    :to="{
                                        name: 'task-skillstraining-detail',
                                        params: { id: relatedModule.id },
                                    }"
                                >
                                    <div
                                        class="mb-3"
                                        style="
                                            height: 235px;
                                            background-color: white;
                                            background-size: 100%;
                                        "
                                        v-bind:style="{
                                            backgroundImage:
                                                'url(' +
                                                relatedModule.tileimage_fullpath +
                                                ')',
                                        }"
                                    ></div>
                                </router-link>
                                <div
                                    class="m-0 d-flex related-tile-content text-white"
                                >
                                    <p
                                        class="float-start fs-7 wrap"
                                        v-text="relatedModule.title"
                                    ></p>
                                    <p class="float-end text-white">
                                        {{ relatedModule.compeletedpercent }}%
                                    </p>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
                <!-- Related Moduled Work here -->
            </div>
        </div>
        <div class="row black-strip bg-black">
            <div class="col-8 p-10">
                <router-link
                    class="fs-4 m-0 text-white"
                    :to="{ name: 'tasks-vwe-list' }"
                >
                    <span class="svg-icon svg-icon-primary svg-icon-2x">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            xmlns:xlink="http://www.w3.org/1999/xlink"
                            width="24px"
                            height="24px"
                            viewBox="0 0 24 24"
                            version="1.1"
                        >
                            <g
                                stroke="none"
                                stroke-width="1"
                                fill="none"
                                fill-rule="evenodd"
                            >
                                <polygon points="0 0 24 0 24 24 0 24" />
                                <path
                                    d="M6.70710678,15.7071068 C6.31658249,16.0976311 5.68341751,16.0976311 5.29289322,15.7071068 C4.90236893,15.3165825 4.90236893,14.6834175 5.29289322,14.2928932 L11.2928932,8.29289322 C11.6714722,7.91431428 12.2810586,7.90106866 12.6757246,8.26284586 L18.6757246,13.7628459 C19.0828436,14.1360383 19.1103465,14.7686056 18.7371541,15.1757246 C18.3639617,15.5828436 17.7313944,15.6103465 17.3242754,15.2371541 L12.0300757,10.3841378 L6.70710678,15.7071068 Z"
                                    fill="#ffffff"
                                    fill-rule="nonzero"
                                    transform="translate(12.000003, 11.999999) scale(-1, 1) rotate(-270.000000) translate(-12.000003, -11.999999) "
                                />
                            </g>
                        </svg>
                    </span>
                    Back to Virtual Work Experience
                </router-link>
            </div>
            <div class="col-4 text-right p-10">
                <span
                    class="cursor-pointer svg-icon svg-icon-primary svg-icon-2x float-end"
                    @click="favouriteVwe(vwe.id)"
                >
                    <i
                        class="fa-solid fa-heart text-white fs-1"
                        v-if="vwe.favourite"
                    ></i>
                    <i
                        class="fa-regular fa-heart text-white fs-1"
                        v-if="!vwe.favourite"
                    ></i>
                </span>

                <span
                    v-if="vwe.audio"
                    @click="toggleAudio"
                    class="cursor-pointer svg-icon svg-icon-primary svg-icon-2x float-end me-5"
                >
                    <i
                        class="fa-solid fa-headphones text-white fs-1"
                        title="Audio Instructions"
                    ></i>
                </span>

                <span
                    v-if="vwe.worksheets.length"
                    class="cursor-pointer svg-icon svg-icon-primary svg-icon-2x float-end me-5"
                    data-bs-toggle="modal"
                    data-bs-target="#kt_modal_worksheet"
                >
                    <i
                        class="bi bi-file-earmark text-white fs-1"
                        data-bs-toggle="tooltip"
                        title="Worksheets"
                    ></i>
                </span>

                <span
                    v-if="currentUser.isTeacher && vwe.teacher_resources.length"
                    class="cursor-pointer svg-icon svg-icon-primary svg-icon-2x float-end me-5"
                    data-bs-toggle="modal"
                    data-bs-target="#kt_modal_teacherResources"
                >
                    <i
                        class="bi bi-box2 text-white fs-1"
                        title="Teacher Resources"
                    ></i>
                </span>

                <span
                    v-if="currentUser.isTeacher && vwe.curriculum"
                    class="cursor-pointer svg-icon svg-icon-primary svg-icon-2x float-end me-5"
                    data-bs-toggle="modal"
                    data-bs-target="#kt_modal_curriculum"
                >
                    <i
                        class="bi bi-list-ul text-white fs-1"
                        title="Curriculum"
                    ></i>
                </span>

                <span
                    class="svg-icon svg-icon-primary svg-icon-2x dropdown float-end"
                >
                </span>
                <!-- <div class="row">
                    <div class="col-3">
                        <div class="about-icon">3</div>
                    </div>
                    <div class="col-9 about-wrap pl-0">
                        <p>Upload your completed work and download your virtual work experience certificate. </p>
                    </div>
                </div> -->
            </div>

            <div
                class="row"
                id="audio"
                v-bind:style="{ height: audioHeight + 'px' }"
            >
                <div class="col-12 text-center">
                    <audio controls controlsList="nodownload">
                        <source :src="vwe.audiofullpath" type="audio/mpeg" />
                        Your browser does not support the audio element.
                    </audio>
                </div>
            </div>
        </div>
    </div>
    <div
        class="modal fade"
        id="kt_modal_trailer"
        tabindex="-1"
        style="display: none"
        aria-hidden="true"
        @click="onHideModal"
    >
        <div class="modal-dialog modal-dialog-centered mw-900px">
            <div class="modal-content rounded-0">
                <div
                    class="modal-body p-0"
                    v-html="vwe.foreground_videoid"
                ></div>
            </div>
        </div>
    </div>
    <div
        class="modal fade"
        id="kt_modal_reset_responses"
        tabindex="-1"
        style="display: none"
        aria-hidden="true"
    >
        <div class="modal-dialog modal-dialog-centered modal-md">
            <div class="modal-content rounded-0">
                <div class="modal-body">
                    <p>
                        Do you really want to reset your response? Doing this
                        will clear your answers and also any feedback that has
                        been provided.
                    </p>
                    <button
                        type="button"
                        class="btn btn-primary btn-sm rounded-0"
                        data-bs-dismiss="modal"
                        @click="resetVwe(vwe.id)"
                    >
                        Yes
                    </button>
                    <button
                        type="button"
                        class="btn btn-sm btn-primary rounded-0 m-5"
                        data-bs-dismiss="modal"
                    >
                        No
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div
        v-if="vwe.worksheets.length"
        class="modal fade"
        id="kt_modal_worksheet"
        tabindex="-1"
        style="display: none"
        aria-hidden="true"
    >
        <div class="modal-dialog modal-dialog-centered modal-xl">
            <div class="modal-content rounded-0">
                <div class="modal-body">
                    <h3>Worksheets</h3>
                    <ul class="list-inline profile-cards new-cards">
                        <li
                            class="text-center hover-colored"
                            v-for="worksheet in vwe.worksheets"
                            :key="worksheet.id"
                        >
                            <a
                                :href="
                                    '/teacherresources/' +
                                    worksheet.id +
                                    '/download'
                                "
                            >
                                <div class="percentage">
                                    <img
                                        :src="worksheet.imagefullpath"
                                        :alt="worksheet.title"
                                        class="img-fluid"
                                    />
                                    <i
                                        class="fa fa-download bg-blue blue-check text-white"
                                    ></i>
                                </div>
                                <div class="topic text-master">
                                    {{ worksheet.title }}
                                </div>
                            </a>
                        </li>
                    </ul>
                    <button
                        type="button"
                        class="btn btn-sm btn-primary rounded-0 m-5 float-end"
                        data-bs-dismiss="modal"
                    >
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div
        v-if="currentUser.isTeacher && vwe.teacher_resources.length"
        class="modal fade"
        id="kt_modal_teacherResources"
        tabindex="-1"
        style="display: none"
        aria-hidden="true"
    >
        <div class="modal-dialog modal-dialog-centered modal-xl">
            <div class="modal-content rounded-0">
                <div class="modal-body">
                    <h3>Teacher Resources</h3>
                    <ul class="list-inline profile-cards new-cards">
                        <li
                            class="text-center hover-colored"
                            v-for="teacher_resoucse in vwe.teacher_resources"
                            :key="teacher_resoucse.id"
                        >
                            <a
                                :href="
                                    '/teacherresources/' +
                                    teacher_resoucse.id +
                                    '/download'
                                "
                            >
                                <div class="percentage">
                                    <img
                                        :src="teacher_resoucse.imagefullpath"
                                        :alt="teacher_resoucse.title"
                                        class="img-fluid"
                                    />
                                    <i
                                        class="fa fa-download bg-blue blue-check text-white"
                                    ></i>
                                </div>
                                <div class="topic text-master">
                                    {{ teacher_resoucse.title }}
                                </div>
                            </a>
                        </li>
                    </ul>
                    <button
                        type="button"
                        class="btn btn-sm btn-primary rounded-0 m-5 float-end"
                        data-bs-dismiss="modal"
                    >
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div
        v-if="currentUser.isTeacher && vwe.curriculum"
        class="modal fade"
        id="kt_modal_curriculum"
        tabindex="-1"
        style="display: none"
        aria-hidden="true"
    >
        <div class="modal-dialog modal-dialog-centered modal-xl">
            <div class="modal-content rounded-0">
                <div class="modal-body">
                    <h3>Curriculum</h3>
                    <div v-html="vwe.curriculum"></div>
                    <button
                        type="button"
                        class="btn btn-sm btn-primary rounded-0 m-5 float-end"
                        data-bs-dismiss="modal"
                    >
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>

    <ScormResultModal
        ref="scormModalRef"
        v-if="scormResult?.lesson_status"
        :module="vwe"
        trackable-type="steps"
        :redo-success-route="{
            name: `task-vwe-section-detail`,
            params: {
                id: vwe.scorm_scoring_step_result?.template_id,
                sectionid: vwe.scorm_scoring_step_result?.number,
            },
        }"
        @viewBagdeDetails="openBadgeModal(vwe.user_response?.badge_key)"
    />

    <BadgeModal :selectedBadge="selectedBadge"  @shareBadge="openShareBadgeModal" />

    <div class="modal fade" id="kt_modal_viewFile" tabindex="-1" aria-hidden="true">
        <div :class="['modal-dialog modal-dialog-centered', isFullscreen ? 'custom-fullscreen-modal' : 'mw-1200px']">
            <div class="modal-content rounded-0 mt-5">
                <div class="modal-header py-3">
                    <h5 class="modal-title">Certificate Preview</h5>
                    <div>
                        <span class="mx-4 cursor-pointer" @click="toggleFullscreen">
                            <i v-if="isFullscreen" class="fa-solid fa-compress text-black"></i>
                            <i v-else class="fa-solid fa-expand text-black"></i>
                        </span>
                        <a v-if="vwe?.user_response?.approve !== 1" :href="'/certificate-download/' + vwe?.user_response?.id" target="_blank" class="text-secondary mx-2"><i class="fa-solid fa-download text-black"></i></a>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                </div>
                <div class="modal-body bg-black p-1 text-white text-center">
                    <iframe v-if="certificateUrl" :src="certificateUrl" class="w-100" :style="{ height: isFullscreen ? '90vh' : '80vh', border: 'none' }" allowfullscreen></iframe>

                    <p v-else>Loading...</p>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="kt_modal_feedback" tabindex="-1" style="display: none" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered mw-600px">
            <div class="modal-content rounded-0" style="height: 80vh">
                <div class="modal-header text-white">
                    <h5 class="modal-title">Feedback</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4 bg-gray-50 text-left">
                    <div class="p-4 bg-white" style="height: 90%">
                        <p v-html="vwe?.user_response?.feedback" class="text-gray-700"></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

</template>
<script lang="ts">
import { defineComponent, ref, onMounted, computed } from "vue";
import { Mutations, Actions } from "@/store/enums/StoreEnums";

import ApiService from "@/core/services/ApiService";
import { useStore } from "vuex";
import { useRouter, useRoute } from "vue-router";
import Swal from "sweetalert2/dist/sweetalert2.min.js";
import BadgeModal from "../Badges/BadgeModal.vue";
// SCORM AND ANZSCO - START
import AnzscoDetails from "@/components/modules/detail/AnzscoDetails.vue";
import ScormResultModal from "@/components/modules/response/ScormResultModal.vue";
import ScormResultStatusBadge from "@/components/modules/response/ScormResultStatusBadge.vue";
// SCORM AND ANZSCO - END

// Import Bootstrap
import * as bootstrap from 'bootstrap';

interface Badge {
    id: number;
    name: string;
    description?: string;
    image_fullpath: string;
    formatted_issue_date: string;
    formatted_expiration_date: string;
}

export default defineComponent({
    name: "vwe-training-detail",
    components: {
        BadgeModal,
        // SCORM AND ANZSCO
        ScormResultModal,
        AnzscoDetails,
        ScormResultStatusBadge,
        // SCORM AND ANZSCO - END
    },
    setup() {
        const store = useStore();
        const route = useRoute();
        const currentUser = store.getters.currentUser;
        onMounted(() => {
            fetchVweDetail();
        });
        const vwe = ref();
        const latestStep = ref();
        const currentvwe = ref();
        const favVwe = ref();
        const setVwe = ref();
        const showRelatedModuleList = ref();
        const selectedBadge = ref<Badge | Object>({});

        vwe.value = {
            id: 1,
            background_imagepath: null,
            background_videoid: null,
            firststepresponse: {
                id: 0,
            },
            worksheets: {},
            teacher_resources: {},
            relatedModules: {},
            audio: [],
            user_response: {
                id: "",
                step_responses: {},
                badge_key : {}
            },
        };
        latestStep.value = 1;
        currentvwe.value = route.params.id;
        const fetchVweDetail = () => {
            ApiService.get(`api/vwe`, currentvwe.value)
                .then(({ data }) => {
                    if (data.steps.length) {
                        for (let i = 0; i < data.steps.length; i++) {
                            if (
                                !data.steps[i]["user_response"] ||
                                i == data.steps.length - 1
                            ) {
                                latestStep.value = i + 1;
                                break;
                            }
                        }
                    }
                    vwe.value = data;
                    var breadcrumbs = store.getters.getBreadcrumbs;
                    breadcrumbs[2] = data.title;
                    store.commit(
                        Mutations.SET_BREADCRUMB_MUTATION,
                        breadcrumbs
                    );
                })
                .catch(({}) => {});
        };
        const toggleRelated = () => {
            showRelatedModuleList.value = !showRelatedModuleList.value;
        };

        const favouriteVwe = (id) => {
            favVwe.value = {
                id: id,
            };
            ApiService.post(`api/vwe/` + id + `/fav`, favVwe.value)
                .then(({ data }) => {
                    vwe.value.favourite = data.favourite;
                })
                .catch(({ response }) => {});
        };
        const resetVwe = (id) => {
            setVwe.value = {
                id: id,
            };
            ApiService.post(`api/vwe/` + id + `/reset`, setVwe.value)
                .then(({ data }) => {
                    Swal.fire({
                        text: "This Virtul Work Experience and your previous responses have been reset.",
                        icon: "success",
                        buttonsStyling: false,
                        confirmButtonText: "Ok",
                        customClass: {
                            confirmButton:
                                "btn fw-semobold btn-light-primary rounded-0",
                        },
                    }).then(() => {
                        window.location.reload();
                    });
                    // vwe.value.favourite = data.favourite;
                })
                .catch(({ response }) => {});
        };

        const showAudio = ref(false);
        const audioHeight = ref(0);
        const toggleAudio = () => {
            const audio = document.getElementById("audio") as HTMLInputElement;
            if (showAudio.value) {
                audioHeight.value = 0;
                audio.style.margin = "0";
            } else {
                audio.classList.remove("d-none");
                audioHeight.value = audio.scrollHeight;
                audio.style.margin = "0px 0px 20px 0px";
            }
            showAudio.value = !showAudio.value;
        };

        const openBadgeModal = (badge: Badge) => {
            selectedBadge.value = badge;
            // Use Bootstrap's Modal API to show the modal
            const modalElement = document.getElementById('kt_modal_badge');
            if (modalElement) {
                // Check if there's an existing modal instance
                let bsModal = bootstrap.Modal.getInstance(modalElement);
                if (!bsModal) {
                    // If no instance exists, create a new one
                    bsModal = new bootstrap.Modal(modalElement, {
                        backdrop: 'static' // Prevents closing when clicking outside
                    });
                }
                bsModal.show();
            }
        };

        const openShareBadgeModal = (badge: Badge) => {
            selectedBadge.value = badge;
            // Use Bootstrap's Modal API to show the modal
            const modalElement = document.getElementById('kt_modal_share_badge');
            if (modalElement) {
                // Check if there's an existing modal instance
                let bsModal = bootstrap.Modal.getInstance(modalElement);
                if (!bsModal) {
                    // If no instance exists, create a new one
                    bsModal = new bootstrap.Modal(modalElement, {
                        backdrop: 'static' // Prevents closing when clicking outside
                    });
                }
                bsModal.show();
            }
        };

        const isFullscreen = ref(false);
        const certificateUrl = ref('');

        const toggleFullscreen = () => {
            isFullscreen.value = !isFullscreen.value;
        };

        const loadCertificate = async () => {
            console.log("Triggered", vwe);
            console.log("Triggered", vwe.value);

            if (!vwe.value || !vwe.value.user_response) {
                console.error("vwe.user_response is missing!");
                return;
            }

            console.log("Triggered ID:", vwe.value.user_response.id);

            const fileId = vwe.value.user_response.id;
            certificateUrl.value = `/certificate-download/${fileId}?preview=true`;
        };

        // SCORM AND ANZSCO - START
        const anzscoTagsGrouped = computed(() => {
            return vwe.value.anzsco_tag_names_grouped;
        });
        const scormModalRef = ref();
        const openScormModal = () => {
            scormModalRef.value?.openModal();
        }
        const scormResult = computed(() => {
            return vwe.value?.scorm_scoring_step_result?.user_scorm_result;
        });
        // SCORM AND ANZSCO - END

        return {
            currentUser,
            vwe,
            toggleRelated,
            currentvwe,
            showRelatedModuleList,
            latestStep,
            favouriteVwe,
            resetVwe,
            toggleAudio,
            audioHeight,
            selectedBadge,
            openBadgeModal,
            openShareBadgeModal,
            toggleFullscreen,
            isFullscreen,
            certificateUrl,
            loadCertificate,
            // SCORM AND ANZSCO
            anzscoTagsGrouped,
            scormModalRef,
            openScormModal,
            scormResult,
            // SCORM AND ANZSCO - END
        };
    },
    methods: {
        onHideModal() {
            // Access the video element and pause it
            const videoElement = document.querySelector(
                "#kt_modal_trailer video"
            ) as HTMLMediaElement;
            if (videoElement) {
                videoElement.pause();
            }
        },
        onShowModal() {
            // Access the video element and pause it
            const videoElement = document.querySelector(
                "#kt_modal_trailer video"
            ) as HTMLMediaElement;
            if (videoElement) {
                videoElement.play();
            }
        },
    },
    props: ["id"],
});
</script>

<style>
#audio {
    overflow: hidden;
    transition: height 0.5s ease;
}

/* Worksheet Css Starts */
.new-cards li {
    padding: 0 15px;
    width: 220px;
}

.profile-cards {
    margin: 0 -5px;
}

.list-inline {
    padding-left: 0;
    list-style: none;
}

.new-cards li,
.profile-cards li,
.template-tiles li {
    vertical-align: top;
}

.profile-cards li {
    width: 200px;
    display: inline-table;
    margin-top: 10px;
}

.list-inline > li {
    /* display: inline-block; */
    /* padding-right: 5px; */
    /* padding-left: 5px; */
}

.hover-colored .percentage {
    overflow: hidden;
    position: relative;
}

.profile-cards .percentage {
    background-position: center;
    height: 190px;
    line-height: 190px;
    background-size: 100%;
    background-repeat: no-repeat;
    color: #fff;
    transition: all 0.2s ease;
}

.img-fluid {
    max-width: 100%;
}

.hover-colored .percentage > :not(.tile-label) {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
}

.blue-check {
    line-height: 26px;
    width: 25px;
    height: 25px;
}

.bg-blue {
    background-color: #0a0afd !important;
}

.blue-check {
    line-height: 26px !important;
    width: 25px;
    height: 25px;
}

.profile-cards .topic {
    font-weight: 700;
    margin: 10px 0;
    line-height: 15px;
}

.text-master {
    color: #000 !important;
}

/* Worksheet Css Ends */

.swal2-popup {
    border-radius: 0px;
}

.wrap {
    overflow: hidden;
    max-width: 75ch;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.banner_detail_box {
    position: absolute;
    top: 50%;
    left: 20%;
    transform: translate(-50%, -50%);
}

.modal-backdrop {
    opacity: 0.8 !important;
}

#kt_modal_trailer .modal-content {
    background-color: transparent;
}

.sticky-bottom {
    z-index: auto;
}

.fa-heart:hover {
    font-weight: 900 !important;
}

.btn-black-custom,
.btn-white-custom {
    height: 55px;
    line-height: 55px;
    padding: 0 !important;
}

.btn-black-custom > i,
.btn-white-custom > i {
    font-size: 30px;
    vertical-align: -9px;
    padding-right: 0;
}

.btn-black-custom > img,
.btn-white-custom > img {
    width: 29px;
    margin-left: 5px;
    vertical-align: -8px;
}

.btn-black-custom:hover,
.btn-white-custom {
    background-color: #fff !important;
    color: #000 !important;
}

.btn-black-custom,
.btn-white-custom:hover {
    background-color: #000 !important;
    color: #fff !important;
}

.btn-black-custom:hover *,
.btn-white-custom * {
    color: #000 !important;
}

.btn-black-custom *,
.btn-white-custom:hover * {
    color: #fff !important;
}

.btn-black-custom:hover > .white-icon {
    display: none;
}

.btn-black-custom:hover > .black-icon {
    display: inline !important;
}

.pointer {
    cursor: pointer;
}

.related-overlay {
    overflow: overlay;
    height: 0px;
    transition: height 0.3s;
}

.slide-up {
    height: calc(100vh - 320px) !important;
}

.related {
    right: 5% !important;
}

.related-tile-content > p:first-child {
    flex: 75%;
}

.banner {
    background-color: #000;
    /* background-image: url("/images/vwe/home-parallax.jpg"); */
    display: block;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    position: relative;
    overflow: hidden;
    min-height: calc(56.25vw - 149px);
    /*min-height: calc(56.25vw - 332px);*/
}

.banner-video {
    height: 100%;
}

.banner-video > video {
    /* height: 100%; */
    width: 101% !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.full-view-banner,
.black-strip {
    margin-left: -30px;
    margin-right: -30px;
}

.bg-dark {
    background: #000 !important;
    margin-left: -30px;
    margin-right: -30px;
}

div#kt_app_content {
    padding-top: 0px;
    padding-bottom: 0px;
}

.mw-1200px {
    max-width: 1200px;
}

@media (max-width: 1280px) {
    .banner {
        height: 56.25vw;
    }

    .banner_detail_box {
        left: 40%;
    }

    .banner-video > video {
        height: 100% !important;
        width: calc(65vw + 65vh) !important;
    }
}

@media (min-width: 992px) {
    /* .app-content {
        padding-left: 70px;
    } */

    /* .app-footer {
        padding-left: 140px;
    } */
}

@media (max-width: 991px) {
    .full-view-banner,
    .black-strip {
        margin-left: -20px;
        margin-right: -20px;
    }

    .full-view-banner {
        margin-top: 58.16px;
    }
}

@media (max-width: 991px) and (min-width: 768px) and (orientation: portrait) {
    .slide-up {
        height: calc(100vw - 220px) !important;
    }

    .banner {
        height: 86.25vw;
    }

    .banner-video > video {
        height: 100% !important;
        width: calc(66vw + 66vh) !important;
    }
}

@media (max-width: 991px) and (orientation: landscape) {
    .banner-video > video {
        height: auto !important;
        width: calc(70vw + 70vh) !important;
    }
}

@media (max-width: 767px) {
    /* .full-view-banner {
                                margin-left: -30px;
                                margin-right: -30px;
                            } */

    .banner {
        height: calc(100vh - 300px);
    }

    .banner_detail_box {
        left: 50%;
    }
}

@media (max-width: 575px) {
    div#kt_app_content {
        padding-top: 30px;
    }

    .banner_detail_box {
        width: 70vw !important;
    }

    .banner {
        height: calc(100vh - 242px);
    }

    .banner-video > video {
        height: 100% !important;
        width: calc(90vw + 90vh) !important;
    }

    .full-view-banner {
        margin-top: 0;
    }
}
</style>
