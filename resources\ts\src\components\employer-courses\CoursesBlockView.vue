<template>
    <div id="kt_project_users_card_pane" class="tab-pane fade show active">
        <div class="row g-6 g-xl-9">
            <div class="col-md-3 col-xxl-3" v-for="course in courses" :key="course.id">
                <div class="border border-hover-primary p-5 rounded mb-7 d-flex position-relative">

                    <div class="dropdown position-absolute top-0 end-0 m-2">
    <button class="btn btn-link text-dark p-0" type="button" data-bs-toggle="dropdown" aria-expanded="false">
      <i class="bi bi-three-dots fs-5"></i>
    </button>
   
 
  <div class="dropdown-menu menu-rounded menu-gray-600 menu-state-bg-light-primary fs-7 w-175px py-4" data-kt-menu="true">
                                        <div class="menu-item px-3">
                                            <a :href="`/#/courses/insights/${course.id}?type=${course.courseType}`" target="_blank" class="menu-link px-3">
                                                View Insights
                                            </a>
                                        </div>
                                        <div class="menu-item px-3">
                                            <a href="#" target="_blank" class="menu-link px-3">
                                                View Module
                                            </a>
                                        </div>
                                    </div>
                                </div>
                    <!-- Image on the left -->
                    <div class="me-4">
                        <img :src="course.tileimage_fullpath" alt="Course Image" style="width: 130px; height: 130px; object-fit: cover; border-radius: 5px; background-color: #e0e0e0;" />
                    </div>
                    <!-- Content on the right -->
                    <div class="flex-grow-1 mx-10">
                        <h5 class="fw-bold mt-2">{{ course.title }}</h5>
                        <span class="badge bg-dark text-white mt-3">{{ course.courseType }}</span>
                        <div class="d-flex align-items-center mt-5">
                            <i class="bi bi-bar-chart-fill me-2"></i>
                            <span>{{ course.level }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- <div class="col-md-3 col-xxl-3" v-for="course in courses" :key="course.id" >
        
                <div class="card card-flush border border-hover-primary mb-7">
                    <div class="card-body p-5">  
                        <div class="row gx-9 h-100">
                            <div class="col-sm-6 mb-10 mb-sm-0">
                                <img :src="course.tileimage_fullpath" class="bgi-no-repeat bgi-position-center bgi-size-cover card-rounded min-h-400px min-h-sm-100 h-75 w-100" alt="Course Image" style="width: 130px; height: 130px; object-fit: cover; border-radius: 5px; background-color: #e0e0e0;" />
                            </div>
                
                            <div class="col-sm-6">
                                <div class="d-flex flex-column h-75 py-3">
                                    <div class="">
                                        <div class="d-flex flex-stack mb-6">
                                            <div class="me-5">
                                                <span class="fs-5 fw-bold me-2 d-block lh-1 pb-1">{{ course.title }}</span>
                                            </div>
                                        </div>
                                    </div>
                
                                    <div class="">
                                        <span class="badge bg-dark text-white mb-8">{{ course.courseType }}</span>
                                    </div>
                
                                    <div class="d-flex flex-stack mt-auto bd-highlight">  

                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-bar-chart-fill me-2"></i>
                                            <span>{{ course.level }}</span>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div> -->

            <!-- <div class="col-md-3 col-xxl-3" >
                <div class="card card-flush border border-hover-primary mb-7">
                    <div class="card-body p-5">  
                        <div class="row gx-9 h-100">
                            <div class="col-sm-6 mb-10 mb-sm-0">
                                <img src="https://tcd-staging.s3.ap-southeast-2.amazonaws.com/attachments/workexperience/PRGdoyMHNYZMo5ORKL0IPZCipcIxZgVv7RmZEs6b.jpg" class="bgi-no-repeat bgi-position-center bgi-size-cover card-rounded min-h-400px min-h-sm-100 h-75 w-100" alt="Course Image" style="width: 130px; height: 130px; object-fit: cover; border-radius: 5px; background-color: #e0e0e0;" />
                            </div>
                
                            <div class="col-sm-6">
                                <div class="d-flex flex-column h-75 py-3">
                                    <div class="">
                                        <div class="d-flex flex-stack mb-6">
                                            <div class="me-5">
                                                <span class="fs-5 fw-bold me-2 d-block lh-1 pb-1">fdfdsfsdfsdfs</span>
                                            </div>
                                        </div>
                                    </div>
                
                                    <div class="">
                                        <span class="badge bg-dark text-white mb-8">Lesson fgdfgdf dsffsdf</span>
                                    </div>
                
                                    <div class="d-flex flex-stack mt-auto bd-highlight">  

                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-bar-chart-fill me-2"></i>
                                            <span>ffdfsdfsdfsdf</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div> -->

        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue';

export interface Course {
    id: number;
    title?: string;
    tileimage_fullpath?: string;
    courseType?: string;
    level?: string;
}

export default defineComponent({
    name: 'CoursesBlockView',
    props: {
        courses: {
            type: Array as PropType<Course[]>,
            required: true,
            default: () => []
        },
        loading: {
            type: Boolean,
            default: false
        }
    }
});
</script>