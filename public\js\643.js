/*! For license information please see 643.js.LICENSE.txt */
"use strict";(self.webpackChunk=self.webpackChunk||[]).push([[643],{57881:(e,t,r)=>{r.d(t,{Z:()=>o});var n=r(1519),a=r.n(n)()((function(e){return e[1]}));a.push([e.id,".nav-link[data-v-4350f266]{border:none;color:var(--kt-text-muted)}.nav-link.active[data-v-4350f266],.nav-link[data-v-4350f266]:active,.nav-link[data-v-4350f266]:hover{color:#000}",""]);const o=a},82643:(e,t,r)=>{r.r(t),r.d(t,{default:()=>W});var n=r(70821),a=r(6154),o=r(12954);function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function c(){c=function(){return e};var e={},t=Object.prototype,r=t.hasOwnProperty,n=Object.defineProperty||function(e,t,r){e[t]=r.value},a="function"==typeof Symbol?Symbol:{},o=a.iterator||"@@iterator",l=a.asyncIterator||"@@asyncIterator",u=a.toStringTag||"@@toStringTag";function s(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(e){s=function(e,t,r){return e[t]=r}}function d(e,t,r,a){var o=t&&t.prototype instanceof p?t:p,i=Object.create(o.prototype),c=new S(a||[]);return n(i,"_invoke",{value:V(e,r,c)}),i}function f(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}e.wrap=d;var v={};function p(){}function m(){}function h(){}var y={};s(y,o,(function(){return this}));var g=Object.getPrototypeOf,b=g&&g(g(_([])));b&&b!==t&&r.call(b,o)&&(y=b);var E=h.prototype=p.prototype=Object.create(y);function N(e){["next","throw","return"].forEach((function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function w(e,t){function a(n,o,c,l){var u=f(e[n],e,o);if("throw"!==u.type){var s=u.arg,d=s.value;return d&&"object"==i(d)&&r.call(d,"__await")?t.resolve(d.__await).then((function(e){a("next",e,c,l)}),(function(e){a("throw",e,c,l)})):t.resolve(d).then((function(e){s.value=e,c(s)}),(function(e){return a("throw",e,c,l)}))}l(u.arg)}var o;n(this,"_invoke",{value:function(e,r){function n(){return new t((function(t,n){a(e,r,t,n)}))}return o=o?o.then(n,n):n()}})}function V(e,t,r){var n="suspendedStart";return function(a,o){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===a)throw o;return C()}for(r.method=a,r.arg=o;;){var i=r.delegate;if(i){var c=x(i,r);if(c){if(c===v)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var l=f(e,t,r);if("normal"===l.type){if(n=r.done?"completed":"suspendedYield",l.arg===v)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(n="completed",r.method="throw",r.arg=l.arg)}}}function x(e,t){var r=t.method,n=e.iterator[r];if(void 0===n)return t.delegate=null,"throw"===r&&e.iterator.return&&(t.method="return",t.arg=void 0,x(e,t),"throw"===t.method)||"return"!==r&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+r+"' method")),v;var a=f(n,e.iterator,t.arg);if("throw"===a.type)return t.method="throw",t.arg=a.arg,t.delegate=null,v;var o=a.arg;return o?o.done?(t[e.resultName]=o.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,v):o:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,v)}function k(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function L(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function S(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(k,this),this.reset(!0)}function _(e){if(e){var t=e[o];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,a=function t(){for(;++n<e.length;)if(r.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=void 0,t.done=!0,t};return a.next=a}}return{next:C}}function C(){return{value:void 0,done:!0}}return m.prototype=h,n(E,"constructor",{value:h,configurable:!0}),n(h,"constructor",{value:m,configurable:!0}),m.displayName=s(h,u,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===m||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,h):(e.__proto__=h,s(e,u,"GeneratorFunction")),e.prototype=Object.create(E),e},e.awrap=function(e){return{__await:e}},N(w.prototype),s(w.prototype,l,(function(){return this})),e.AsyncIterator=w,e.async=function(t,r,n,a,o){void 0===o&&(o=Promise);var i=new w(d(t,r,n,a),o);return e.isGeneratorFunction(r)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},N(E),s(E,u,"Generator"),s(E,o,(function(){return this})),s(E,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},e.values=_,S.prototype={constructor:S,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(L),!e)for(var t in this)"t"===t.charAt(0)&&r.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function n(r,n){return i.type="throw",i.arg=e,t.next=r,n&&(t.method="next",t.arg=void 0),!!n}for(var a=this.tryEntries.length-1;a>=0;--a){var o=this.tryEntries[a],i=o.completion;if("root"===o.tryLoc)return n("end");if(o.tryLoc<=this.prev){var c=r.call(o,"catchLoc"),l=r.call(o,"finallyLoc");if(c&&l){if(this.prev<o.catchLoc)return n(o.catchLoc,!0);if(this.prev<o.finallyLoc)return n(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return n(o.catchLoc,!0)}else{if(!l)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return n(o.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var a=this.tryEntries[n];if(a.tryLoc<=this.prev&&r.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var o=a;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=e,i.arg=t,o?(this.method="next",this.next=o.finallyLoc,v):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),L(r),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var a=n.arg;L(r)}return a}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,r){return this.delegate={iterator:_(e),resultName:t,nextLoc:r},"next"===this.method&&(this.arg=void 0),v}},e}function l(e,t,r,n,a,o,i){try{var c=e[o](i),l=c.value}catch(e){return void r(e)}c.done?t(l):Promise.resolve(l).then(n,a)}var u=function(e){return(0,n.pushScopeId)("data-v-4350f266"),e=e(),(0,n.popScopeId)(),e},s={class:"row mt-5"},d={class:"col-md-10 col-lg-8 mx-auto"},f=u((function(){return(0,n.createElementVNode)("p",{class:"fs-2"},"Verify Credential",-1)})),v={class:"d-flex align-items-center justify-content-center"},p={class:"position-relative w-75 me-2"},m={class:"d-flex align-items-center w-25"},h=["disabled"],y={class:"mt-20"},g={key:0,class:"text-center fs-4"},b=u((function(){return(0,n.createElementVNode)("i",{class:"bi bi-exclamation-circle text-primary fs-3 me-2"},null,-1)})),E={key:1,class:"fs-5"},N={class:"nav nav-tabs border-0"},w={class:"nav-item"},V={class:"tab-content",id:"myTabContent"},x={class:"card"},k={class:"card-body"},L={class:"d-flex align-items-center justify-content-between p-10 border border-secondary border-solid rounded"},S={key:0,class:"fw-bold mb-5 mt-5"},_={key:0},C={class:"mt-10"},B={class:"mb-2"},D=u((function(){return(0,n.createElementVNode)("span",{class:"text-gray-700"},"Issued To: ",-1)})),j={class:"mb-2"},T=u((function(){return(0,n.createElementVNode)("span",{class:"text-gray-700"},"Module Name: ",-1)})),O={class:"mb-2"},I=u((function(){return(0,n.createElementVNode)("span",{class:"text-gray-700"},"Credential ID: ",-1)})),P={class:"mb-2"},G=u((function(){return(0,n.createElementVNode)("span",{class:"text-gray-700"},"Issue Date: ",-1)})),F={key:0,class:"mb-2"},Z=u((function(){return(0,n.createElementVNode)("span",{class:"text-gray-700"},"Expiry Date: ",-1)})),z={class:"mb-2"},A=u((function(){return(0,n.createElementVNode)("span",{class:"text-gray-700"},"Module Type: ",-1)})),M=["src"],Y={class:"card"},K={class:"card-body"},U=u((function(){return(0,n.createElementVNode)("h4",null,"Certificate Details",-1)}));const q={__name:"CheckCredentails",setup:function(e){var t=(0,n.ref)(""),r=(0,n.ref)(!1),i=(0,n.ref)(null),u=(0,n.ref)(null),q=(0,n.ref)("badge"),H=function(){var e,n=(e=c().mark((function e(){var n;return c().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(i.value=null,u.value=null,t.value){e.next=5;break}return i.value="Please enter a credential ID",e.abrupt("return");case 5:return e.prev=6,r.value=!0,e.next=10,a.Z.post("/api/verify-credential",{credential_id:t.value});case 10:"success"===(n=e.sent).data.status?(u.value=n.data.badgeKey,console.log("badgeData",u)):i.value=n.message||"Credential not matched",e.next=17;break;case 14:e.prev=14,e.t0=e.catch(6),i.value="An error occurred while verifying the credential";case 17:return e.prev=17,r.value=!1,e.finish(17);case 20:case"end":return e.stop()}}),e,null,[[6,14,17,20]])})),function(){var t=this,r=arguments;return new Promise((function(n,a){var o=e.apply(t,r);function i(e){l(o,n,a,i,c,"next",e)}function c(e){l(o,n,a,i,c,"throw",e)}i(void 0)}))});return function(){return n.apply(this,arguments)}}();return function(e,a){return(0,n.openBlock)(),(0,n.createElementBlock)("div",s,[(0,n.createElementVNode)("div",d,[f,(0,n.createElementVNode)("div",v,[(0,n.createElementVNode)("div",p,[(0,n.createVNode)((0,n.unref)(o.gN),{modelValue:t.value,"onUpdate:modelValue":a[0]||(a[0]=function(e){return t.value=e}),class:"form-control form-control-solid ps-10",type:"text",placeholder:"Enter Credential ID",name:"scholarshipName",autocomplete:"off"},null,8,["modelValue"])]),(0,n.createElementVNode)("div",m,[(0,n.createElementVNode)("button",{onClick:H,disabled:r.value,class:"btn btn-secondary w-100 me-5"},(0,n.toDisplayString)(r.value?"Searching...":"Search"),9,h)])]),(0,n.createElementVNode)("div",y,[i.value?((0,n.openBlock)(),(0,n.createElementBlock)("p",g,[b,(0,n.createTextVNode)(" "+(0,n.toDisplayString)(i.value),1)])):(0,n.createCommentVNode)("",!0),u.value?((0,n.openBlock)(),(0,n.createElementBlock)("div",E,[(0,n.createElementVNode)("ul",N,[(0,n.createElementVNode)("li",w,[(0,n.createElementVNode)("a",{class:(0,n.normalizeClass)(["nav-link ps-0",{active:"badge"===q.value}]),onClick:a[1]||(a[1]=function(e){return q.value="badge"}),href:"javascript:void(0)"},"Digital Badge",2)])]),(0,n.createElementVNode)("div",V,[(0,n.createElementVNode)("div",{class:(0,n.normalizeClass)(["tab-pane fade",{"show active":"badge"===q.value}])},[(0,n.createElementVNode)("div",x,[(0,n.createElementVNode)("div",k,[(0,n.createElementVNode)("div",L,[(0,n.createElementVNode)("div",null,[(0,n.createElementVNode)("div",null,[(0,n.createElementVNode)("h1",null,(0,n.toDisplayString)(u.value.badge.name),1),u.value.badge.companies?((0,n.openBlock)(),(0,n.createElementBlock)("p",S,[(0,n.createTextVNode)("Verified by "),((0,n.openBlock)(!0),(0,n.createElementBlock)(n.Fragment,null,(0,n.renderList)(u.value.badge.companies,(function(e,t){return(0,n.openBlock)(),(0,n.createElementBlock)("span",{key:e.id},[(0,n.createElementVNode)("u",null,(0,n.toDisplayString)(e.name),1),t!==u.value.badge.companies.length-1?((0,n.openBlock)(),(0,n.createElementBlock)("span",_," + ")):(0,n.createCommentVNode)("",!0)])})),128))])):(0,n.createCommentVNode)("",!0)]),(0,n.createElementVNode)("div",C,[(0,n.createElementVNode)("p",B,[D,(0,n.createTextVNode)((0,n.toDisplayString)(u.value.badgeable.student.name),1)]),(0,n.createElementVNode)("p",j,[T,(0,n.createTextVNode)((0,n.toDisplayString)(u.value.module_name),1)]),(0,n.createElementVNode)("p",O,[I,(0,n.createTextVNode)((0,n.toDisplayString)(u.value.credential_id),1)]),(0,n.createElementVNode)("p",P,[G,(0,n.createTextVNode)((0,n.toDisplayString)(u.value.issue_date),1)]),u.value.expiration_date?((0,n.openBlock)(),(0,n.createElementBlock)("p",F,[Z,(0,n.createTextVNode)((0,n.toDisplayString)(u.value.expiration_date),1)])):(0,n.createCommentVNode)("",!0),(0,n.createElementVNode)("p",z,[A,(0,n.createTextVNode)((0,n.toDisplayString)(u.value.module_type),1)])])]),(0,n.createElementVNode)("div",null,[u.value?((0,n.openBlock)(),(0,n.createElementBlock)("img",{key:0,src:u.value.badge.image_fullpath,width:"200",height:"200"},null,8,M)):(0,n.createCommentVNode)("",!0)])])])])],2),(0,n.createElementVNode)("div",{class:(0,n.normalizeClass)(["tab-pane fade",{"show active":"certificate"===q.value}])},[(0,n.createElementVNode)("div",Y,[(0,n.createElementVNode)("div",K,[U,(0,n.createElementVNode)("pre",null,(0,n.toDisplayString)(u.value.certificate),1)])])],2)])])):(0,n.createCommentVNode)("",!0)])])])}}};var H=r(93379),J=r.n(H),Q=r(57881),R={insert:"head",singleton:!1};J()(Q.Z,R);Q.Z.locals;const W=(0,r(83744).Z)(q,[["__scopeId","data-v-4350f266"]])}}]);