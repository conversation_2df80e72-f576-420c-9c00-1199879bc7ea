<?php

namespace App\Http\Controllers;

use App\SpecialistTask;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Database\Eloquent\Builder;


class SpecialistTaskController extends Controller
{
    public function search(Request $request)
    {
        try {
            $search = $request->search;
            $data = null;

            if($search) {
                $data = SpecialistTask::query()
                        ->with(['clusterFamily'])
                        ->when($request->search, function($q) use($request) {
                            return $q->where(function($q) use ($request) {
                                return $q->whereRaw('LOWER(name) like ?', ['%'. strtolower($request->search) . '%'])
                                        ->orWhereRaw('LOWER(skill_statement) like ?', ['%'. strtolower($request->search) . '%']);
                            });
                        })
                        ->orderBy('name')
                        ->when(!is_null($request->paginate) && !is_null($request->per_page), function(Builder $q) use($request) {
                            return $q->paginate($request->per_page, ['*'], 'page', $request->page);
                        }, function($q) {
                            return $q->get();
                        });
            }

            return response()->json([
                'success' => $data != null,
                'message' => $data != null ? 'Data Found' : 'Data Not Found',
                'data' => $data
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'data' => null
            ]);
        }
    }
}
