<template>
    <div class="col-xl-4">
        <div class="card card-flush h-xl-100 rounded-0">
            <div class="card-header border-0 pt-5">
                <h3 class="card-title align-items-start flex-column">
                    <span v-if="(currentUser.isStudent == true || currentUser.studentView)" class="card-label fw-bold text-dark">Current Tasks</span>
                    <span v-if="(currentUser.isParent == true) && (currentUser.hasPremiumAccess == 1)" class="card-label fw-bold text-dark">Active Tasks</span>
                    <span v-if="(currentUser.isParent == true) && (currentUser.hasPremiumAccess == 1)" class="text-muted mt-1 fw-semibold fs-7">
                        <span v-if="(!currentUser.sessionChild)">Modules they are currently working on or have recently completed.</span>
                    <span v-if="(currentUser.sessionChild)">Current and recently completed modules by {{ currentUser.sessionChild.firstname }}.</span>
                    </span>
                    <span v-if="(currentUser.isStudent == true || currentUser.studentView)" class="text-muted mt-1 fw-semibold fs-7">Completed Lessons, Virtual Work Experience and Skills Training modules.</span>
                </h3>
            </div>
            <div class="card-body pt-5">
                <template v-for="(task) in  tasksList" :key="task.id">
                    <div class="d-flex flex-stack">
                        <div class="d-flex align-items-center me-3">
                            <a :href="task.url" class="symbol symbol-40px me-4">
                                <div class="symbol-label fs-2 rounded-0" v-bind:style="{ 'background-image': 'url(' + task.banner + ')' }"></div>
                            </a>
                            <!-- <a :href="task.url" class="">
                                <img :src="task.banner" class="me-4 w-40px" alt="">
                            </a> -->
                            <div class="flex-grow-1">
                                <a :href="task.url" class="text-gray-800 text-hover-primary fs-6 fw-bold lh-0" v-text="task.title"></a>
                                <span class="text-gray-400 fw-semibold d-block fs-7">{{ task.type }}</span>
                            </div>
                        </div>
                        <div class="d-flex align-items-center w-100 mw-125px">
                            <div class="progress h-6px w-100 me-2 bg-light-dark">
                                <div v-if="(currentUser.isStudent == true || currentUser.studentView)" class="progress-bar bg-dark" role="progressbar" style="width: 100%" aria-valuenow="65" aria-valuemin="0" aria-valuemax="100">
                                </div>
                                <div v-if="(currentUser.isParent == true) && (currentUser.hasPremiumAccess == 1)" class="progress-bar bg-dark" role="progressbar" style="width: 100%" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100">
                                </div>
                            </div>
                            <span v-if="(currentUser.isStudent == true || currentUser.studentView)" class="text-gray-400 fw-semibold">100%</span>
                            <span v-if="(currentUser.isParent == true) && (currentUser.hasPremiumAccess == 1)" class="text-gray-400 fw-semibold">100%</span>
                        </div>
                    </div>
                    <div class="separator separator-dashed my-3"></div>
                </template>
                <template v-if="(!tasksList.length && (currentUser.isStudent || currentUser.studentView))">
                    <div class="d-flex flex-column text-muted">
                        <div v-if="currentUser.hasVweAccess" class="task-add-box d-flex flex-row align-items-center cursor-pointer" @click="goto('exploreworkexperience')">
                            <p class="m-4 lh-1 bg-light px-4" style="font-size:3rem;">+</p>
                            <p class=" m-0 fs-7">Complete First Virtual Work Experience</p>
                        </div>
                        <div v-if="currentUser.hasSkillsTrainingAccess" class="task-add-box d-flex flex-row my-5 align-items-center cursor-pointer" @click="goto('wew/skillstraining')">
                            <p class="m-4 lh-1 bg-light px-4" style="font-size:3rem;">+</p>
                            <p class=" m-0 fs-7">Complete First Skills Training</p>
                        </div>
                        <div v-if="currentUser.hasLessonsAccess" class="task-add-box d-flex flex-row align-items-center cursor-pointer" @click="goto('tasks')">
                            <p class="m-4 lh-1 bg-light px-4" style="font-size:3rem;">+</p>
                            <p class=" m-0 fs-7">Complete First Lesson</p>
                        </div>
                    </div>
                </template>
                <template v-if="(!tasksList.length && currentUser.isParent && currentUser.hasPremiumAccess)">
                    <div class="d-flex flex-column text-muted">
                        <div class="task-add-box d-flex flex-row align-items-center">
                            <p class=" m-0 fs-8 p-8">No active or recently completed tasks yet.</p>
                        </div>
                    </div>
                </template>
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-center justify-content-sm-end  gap-2">
                    <a v-if="(currentUser.isStudent == true || currentUser.studentView) && currentUser.hasVweAccess"
                    class="btn btn-sm btn-light text-black rounded-0 p-2 px-lg-5"
                    @click="goto('exploreworkexperience')">
                    View VWE
                    </a>

                    <a v-if="(currentUser.isStudent == true || currentUser.studentView) && currentUser.hasSkillsTrainingAccess"
                    class="btn btn-sm btn-light text-black rounded-0 p-2 px-lg-5"
                    @click="goto('wew/skillstraining')">
                    View Skills
                    </a>

                    <a v-if="(currentUser.isStudent == true || currentUser.studentView) && currentUser.hasLessonsAccess"
                    class="btn btn-sm btn-light text-black rounded-0 p-2 px-lg-5"
                    @click="goto('tasks')">
                    View Lessons
                    </a>

                    <a v-if="(currentUser.isParent && currentUser.hasPremiumAccess) && currentUser.hasLessonsAccess"
                    class="btn btn-sm btn-light text-black rounded-0"
                    @click="goto('tasks')">
                    View All Tasks
                    </a>
                </div>
            </div>
        </div>
    </div>
</template>
<script lang="ts">
import { defineComponent, ref, onMounted, nextTick, computed } from "vue";
import { Field, ErrorMessage, configure } from "vee-validate";
import { useStore } from "vuex";

export default defineComponent({
    name: "step-2",
    components: {
        Field,
        ErrorMessage,
    },
    props: ['formData'],
    setup() {
        onMounted(() => {
            fetchRecomendations();
        });
        const store = useStore();
        const currentUser = store.getters.currentUser;
        const goto = (link) => {
            window.location.href = "/" + link;
        };
        const tasksList = ref();
        tasksList.value = [
            {
                banner: '',
                title: '',
                url: '',
                type: ''
            },
        ];

        const fetchRecomendations = async () => {
            const response = await fetch('getactiveTasks', {});
            const data = await response.json();
            tasksList.value = data;
        };
        return {
            tasksList,
            currentUser,
            goto
        }
    }
});
</script>
<style>
.task-add-box {
    position: relative;
    border-style: dashed;
    border-color: #e4e6ef;
    border-width: 1px
}
</style>
