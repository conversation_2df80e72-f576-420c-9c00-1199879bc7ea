<div class="modal fade" id="editAnzscoModal" tabindex="-1" role="dialog" aria-labelledby="editAnzscoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editAnzscoModalLabel">Edit</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body mt-4">
                <form id="editAnzscoForm" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')
                    <div class="form-group">
                        <label for="edit_anzsco_title">ANZSCO Title <strong class="text-danger">*</strong></label>
                        <input type="text" name="anzsco_title" id="edit_anzsco_title" class="form-control" required maxlength="255">
                        @error('anzsco_title')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="form-group">
                        <label for="edit_occupation_type">Occupation Type <strong class="text-danger">*</strong></label>
                        <input type="text" name="occupation_type" id="edit_occupation_type" class="form-control" required maxlength="255">
                        @error('occupation_type')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    <input type="hidden" name="id" id="edit_occupation_id">
                    <div class="form-group">
                        <label for="edit_anzsco_code">ANZSCO Code <strong class="text-danger">*</strong></label>
                        <input type="text" name="anzsco_code" id="edit_anzsco_code" class="form-control" required maxlength="255">
                        @error('anzsco_code')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="form-group">
                        <label for="edit_media_type">Media Type</label>
                        <select id="edit_media_type" name="media_type" class="form-control">
                            <option value="file">Upload File</option>
                            <option value="url">Wistia URL or Embed Code</option>
                        </select>
                    </div>
                    <div id="edit_media_file_input" class="form-group">
                        <label for="edit_media">Upload Media</label>
                        <input type="file" name="media" id="edit_media" class="form-control p-1" accept=".jpg,.jpeg,.png,.gif,.mp4,.mov,.avi">
                        <small class="form-text text-muted">Leave empty to keep existing media.</small>
                        @error('media')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    <div id="edit_media_url_input" class="form-group" style="display: none;">
                        <label for="edit_media_text">Paste Wistia URL or Embed Code</label>
                        <textarea name="media_text" id="edit_media_text" class="form-control p-1"></textarea>
                        @error('media_text')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="form-group current_media">
                        <label>Current Media</label>
                        <div id="current_media" class="mt-2"></div>
                    </div>
                    <div class="form-group">
                        <label for="edit_sub_profile_code">Sub Profile Code (Optional)</label>
                        <input type="text" name="sub_profile_code" id="edit_sub_profile_code" class="form-control" maxlength="255">
                        @error('sub_profile_code')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="form-group">
                        <label for="edit_anzsco_description">Description</label>
                        <textarea name="anzsco_description" id="edit_anzsco_description" class="form-control"></textarea>
                        @error('anzsco_description')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>        
                    <div class="d-flex justify-content-end mt-4">
                        <button type="submit" class="btn btn-primary mr-2">Save</button>
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@push('scripts')
<script>
$(document).ready(function() {
    $('#edit_media_type').on('change', function() {
        if (this.value === 'file') {
            $('#edit_media_file_input').show();
            $('#edit_media_url_input').hide();
            $('#edit_media_text').val(''); 
            $('.current_media').removeClass('d-none');
        } else {
            $('#edit_media_file_input').hide();
            $('#edit_media_url_input').show();
            $('#edit_media').val(''); 
        }
    });

    // Handle edit button click to populate modal
    $(document).on('click', '.edit-anzsco-occupation', function(e) {
        var id = $(this).data('id');
        var anzsco_code = $(this).data('anzsco_code');
        var anzsco_title = $(this).data('anzsco_title');
        var anzsco_description = $(this).data('anzsco_description');
        var sub_profile_code = $(this).data('sub_profile_code');
        var occupation_type = $(this).data('occupation_type');
        var media = $(this).data('media');
       console.log(media, id, anzsco_code, anzsco_title, anzsco_description, sub_profile_code, occupation_type);
       

      
        $('#edit_occupation_id').val(id);
        $('#edit_anzsco_code').val(anzsco_code);
        $('#edit_anzsco_title').val(anzsco_title);
        $('#edit_anzsco_description').val(anzsco_description);
        $('#edit_sub_profile_code').val(sub_profile_code);
        $('#edit_occupation_type').val(occupation_type);

        
        $('#edit_media').val('');
        $('#edit_media_text').val('');
        $('#current_media').empty();
        $('.current_media').removeClass('d-none');

       
        if (media) {
            if (media.match(/\.(jpg|jpeg|png|gif|mp4|mov|avi)$/i)) {
                $('#edit_media_type').val('file');
                $('#edit_media_file_input').show();
                $('#edit_media_url_input').hide();
                $('.current_media').removeClass('d-none');

                if (media.match(/\.(mp4|mov|avi)$/i)) {
                    $('#current_media').html(`
                        <p>Existing Media: <a href="${media}" target="_blank" class="text-primary">View Video</a></p>
                    `);
                } 
            } else if (media.match(/^https?:\/\/(www\.)?wistia\.com/)) {
                // Wistia URL
                $('#edit_media_type').val('url');
                $('#edit_media_file_input').hide();
                $('#edit_media_url_input').show();
                $('#edit_media_text').val(media);
                $('.current_media').removeClass('d-none');
                // $('#current_media').html(`<a href="${media}" target="_blank">Wistia Video Link</a>`);
                $('#current_media').html(`
                        <p>Existing Media: <a href="${media}" target="_blank" class="text-primary">View Video</a></p>
                    `);
            } 
            else if (media.match(/<iframe|<embed/)) {
                // Embedded code
                console.log("matched");
                $('#edit_media_type').val('url');
                $('#edit_media_file_input').hide();
                $('#edit_media_url_input').show();
                $('#edit_media_text').val(media);
                $('.current_media').addClass('d-none');
                $('#current_media').html(media); 
            } 
            else {
                // Treat as generic URL or text
                $('#edit_media_type').val('url');
                $('#edit_media_file_input').hide();
                $('#edit_media_url_input').show();
                $('#edit_media_text').val(media);
                $('.current_media').removeClass('d-none');
                $('#current_media').html(` <p>Existing Media: <a href="${media}" target="_blank" class="text-primary">View Video</a></p>`);
            }
        } else {
            // No media
            $('#edit_media_type').val('file');
            $('#edit_media_file_input').show();
            $('#edit_media_url_input').hide();
            $('.current_media').removeClass('d-none');
            $('#current_media').html('<p>No media available.</p>');
        }
    });
});
</script>
@endpush