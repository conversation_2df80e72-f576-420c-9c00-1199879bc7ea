/*! For license information please see 185.js.LICENSE.txt */
(self.webpackChunk=self.webpackChunk||[]).push([[185],{3368:(e,t,o)=>{"use strict";o.d(t,{Z:()=>r});var n=o(1519),a=o.n(n)()((function(e){return e[1]}));a.push([e.id,".mw-900px{max-width:900px}.animated-video>iframe{height:100%!important;width:100%!important}",""]);const r=a},6857:(e,t,o)=>{"use strict";o.d(t,{Z:()=>r});var n=o(1519),a=o.n(n)()((function(e){return e[1]}));a.push([e.id,".mw-900px[data-v-42f43f73]{max-width:900px}.w-90[data-v-42f43f73]{width:90%}",""]);const r=a},25826:(e,t,o)=>{"use strict";o.d(t,{Z:()=>r});var n=o(1519),a=o.n(n)()((function(e){return e[1]}));a.push([e.id,"#audio{overflow:hidden;transition:height .5s ease}.new-cards li{padding:0 15px;width:220px}.profile-cards{margin:0 -5px}.list-inline{list-style:none;padding-left:0}.new-cards li,.profile-cards li,.template-tiles li{vertical-align:top}.profile-cards li{display:inline-table;margin-top:10px;width:200px}.hover-colored .percentage{overflow:hidden;position:relative}.profile-cards .percentage{background-position:50%;background-repeat:no-repeat;background-size:100%;color:#fff;height:190px;line-height:190px;transition:all .2s ease}.img-fluid{max-width:100%}.hover-colored .percentage>:not(.tile-label){left:50%;position:absolute;top:50%;transform:translate(-50%,-50%);-webkit-transform:translate(-50%,-50%);-moz-transform:translate(-50%,-50%)}.blue-check{line-height:26px}.bg-blue{background-color:#0a0afd!important}.blue-check{height:25px;line-height:26px!important;width:25px}.profile-cards .topic{font-weight:700;line-height:15px;margin:10px 0}.text-master{color:#000!important}.swal2-popup{border-radius:0}.wrap{max-width:75ch;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.banner_detail_box{left:20%;position:absolute;top:50%;transform:translate(-50%,-50%)}.modal-backdrop{opacity:.8!important}#kt_modal_trailer .modal-content{background-color:transparent}.sticky-bottom{z-index:auto}.fa-heart:hover{font-weight:900!important}.btn-black-custom,.btn-white-custom{height:55px;line-height:55px;padding:0!important}.btn-black-custom>i,.btn-white-custom>i{font-size:30px;padding-right:0;vertical-align:-9px}.btn-black-custom>img,.btn-white-custom>img{margin-left:5px;vertical-align:-8px;width:29px}.btn-black-custom:hover,.btn-white-custom{background-color:#fff!important;color:#000!important}.btn-black-custom,.btn-white-custom:hover{background-color:#000!important;color:#fff!important}.btn-black-custom:hover *,.btn-white-custom *{color:#000!important}.btn-black-custom *,.btn-white-custom:hover *{color:#fff!important}.btn-black-custom:hover>.white-icon{display:none}.btn-black-custom:hover>.black-icon{display:inline!important}.pointer{cursor:pointer}.related-overlay{height:0;overflow:overlay;transition:height .3s}.slide-up{height:calc(100vh - 320px)!important}.related{right:5%!important}.related-tile-content>p:first-child{flex:75%}.banner{background-color:#000;background-position:50%;background-repeat:no-repeat;background-size:cover;display:block;min-height:calc(56.25vw - 149px);overflow:hidden;position:relative}.banner-video{height:100%}.banner-video>video{left:50%;position:absolute;top:50%;transform:translate(-50%,-50%);width:101%!important}.bg-dark,.black-strip,.full-view-banner{margin-left:-30px;margin-right:-30px}.bg-dark{background:#000!important}div#kt_app_content{padding-bottom:0;padding-top:0}.mw-1200px{max-width:1200px}@media (max-width:1280px){.banner{height:56.25vw}.banner_detail_box{left:40%}.banner-video>video{height:100%!important;width:calc(65vw + 65vh)!important}}@media (max-width:991px){.black-strip,.full-view-banner{margin-left:-20px;margin-right:-20px}.full-view-banner{margin-top:58.16px}}@media (max-width:991px) and (min-width:768px) and (orientation:portrait){.slide-up{height:calc(100vw - 220px)!important}.banner{height:86.25vw}.banner-video>video{height:100%!important;width:calc(66vw + 66vh)!important}}@media (max-width:991px) and (orientation:landscape){.banner-video>video{height:auto!important;width:calc(70vw + 70vh)!important}}@media (max-width:767px){.banner{height:calc(100vh - 300px)}.banner_detail_box{left:50%}}@media (max-width:575px){div#kt_app_content{padding-top:30px}.banner_detail_box{width:70vw!important}.banner{height:calc(100vh - 242px)}.banner-video>video{height:100%!important;width:calc(90vw + 90vh)!important}.full-view-banner{margin-top:0}}",""]);const r=a},48542:function(e){e.exports=function(){"use strict";var e={awaitingPromise:new WeakMap,promise:new WeakMap,innerParams:new WeakMap,domCache:new WeakMap};const t=e=>{const t={};for(const o in e)t[e[o]]="swal2-"+e[o];return t},o=t(["container","shown","height-auto","iosfix","popup","modal","no-backdrop","no-transition","toast","toast-shown","show","hide","close","title","html-container","actions","confirm","deny","cancel","default-outline","footer","icon","icon-content","image","input","file","range","select","radio","checkbox","label","textarea","inputerror","input-label","validation-message","progress-steps","active-progress-step","progress-step","progress-step-line","loader","loading","styled","top","top-start","top-end","top-left","top-right","center","center-start","center-end","center-left","center-right","bottom","bottom-start","bottom-end","bottom-left","bottom-right","grow-row","grow-column","grow-fullscreen","rtl","timer-progress-bar","timer-progress-bar-container","scrollbar-measure","icon-success","icon-warning","icon-info","icon-question","icon-error"]),n=t(["success","warning","info","question","error"]),a="SweetAlert2:",r=e=>e.charAt(0).toUpperCase()+e.slice(1),i=e=>{console.warn(`${a} ${"object"==typeof e?e.join(" "):e}`)},l=e=>{console.error(`${a} ${e}`)},s=[],c=(e,t)=>{var o;o=`"${e}" is deprecated and will be removed in the next major release. Please use "${t}" instead.`,s.includes(o)||(s.push(o),i(o))},d=e=>"function"==typeof e?e():e,u=e=>e&&"function"==typeof e.toPromise,m=e=>u(e)?e.toPromise():Promise.resolve(e),p=e=>e&&Promise.resolve(e)===e,g=()=>document.body.querySelector(`.${o.container}`),h=e=>{const t=g();return t?t.querySelector(e):null},f=e=>h(`.${e}`),v=()=>f(o.popup),b=()=>f(o.icon),y=()=>f(o.title),w=()=>f(o["html-container"]),k=()=>f(o.image),E=()=>f(o["progress-steps"]),x=()=>f(o["validation-message"]),C=()=>h(`.${o.actions} .${o.confirm}`),V=()=>h(`.${o.actions} .${o.cancel}`),B=()=>h(`.${o.actions} .${o.deny}`),N=()=>h(`.${o.loader}`),_=()=>f(o.actions),A=()=>f(o.footer),T=()=>f(o["timer-progress-bar"]),S=()=>f(o.close),L=()=>{const e=Array.from(v().querySelectorAll('[tabindex]:not([tabindex="-1"]):not([tabindex="0"])')).sort(((e,t)=>{const o=parseInt(e.getAttribute("tabindex")),n=parseInt(t.getAttribute("tabindex"));return o>n?1:o<n?-1:0})),t=Array.from(v().querySelectorAll('\n  a[href],\n  area[href],\n  input:not([disabled]),\n  select:not([disabled]),\n  textarea:not([disabled]),\n  button:not([disabled]),\n  iframe,\n  object,\n  embed,\n  [tabindex="0"],\n  [contenteditable],\n  audio[controls],\n  video[controls],\n  summary\n')).filter((e=>"-1"!==e.getAttribute("tabindex")));return(e=>{const t=[];for(let o=0;o<e.length;o++)-1===t.indexOf(e[o])&&t.push(e[o]);return t})(e.concat(t)).filter((e=>Y(e)))},P=()=>D(document.body,o.shown)&&!D(document.body,o["toast-shown"])&&!D(document.body,o["no-backdrop"]),$=()=>v()&&D(v(),o.toast),M={previousBodyPadding:null},O=(e,t)=>{if(e.textContent="",t){const o=(new DOMParser).parseFromString(t,"text/html");Array.from(o.querySelector("head").childNodes).forEach((t=>{e.appendChild(t)})),Array.from(o.querySelector("body").childNodes).forEach((t=>{t instanceof HTMLVideoElement||t instanceof HTMLAudioElement?e.appendChild(t.cloneNode(!0)):e.appendChild(t)}))}},D=(e,t)=>{if(!t)return!1;const o=t.split(/\s+/);for(let t=0;t<o.length;t++)if(!e.classList.contains(o[t]))return!1;return!0},j=(e,t,a)=>{if(((e,t)=>{Array.from(e.classList).forEach((a=>{Object.values(o).includes(a)||Object.values(n).includes(a)||Object.values(t.showClass).includes(a)||e.classList.remove(a)}))})(e,t),t.customClass&&t.customClass[a]){if("string"!=typeof t.customClass[a]&&!t.customClass[a].forEach)return void i(`Invalid type of customClass.${a}! Expected string or iterable object, got "${typeof t.customClass[a]}"`);q(e,t.customClass[a])}},H=(e,t)=>{if(!t)return null;switch(t){case"select":case"textarea":case"file":return e.querySelector(`.${o.popup} > .${o[t]}`);case"checkbox":return e.querySelector(`.${o.popup} > .${o.checkbox} input`);case"radio":return e.querySelector(`.${o.popup} > .${o.radio} input:checked`)||e.querySelector(`.${o.popup} > .${o.radio} input:first-child`);case"range":return e.querySelector(`.${o.popup} > .${o.range} input`);default:return e.querySelector(`.${o.popup} > .${o.input}`)}},I=e=>{if(e.focus(),"file"!==e.type){const t=e.value;e.value="",e.value=t}},F=(e,t,o)=>{e&&t&&("string"==typeof t&&(t=t.split(/\s+/).filter(Boolean)),t.forEach((t=>{Array.isArray(e)?e.forEach((e=>{o?e.classList.add(t):e.classList.remove(t)})):o?e.classList.add(t):e.classList.remove(t)})))},q=(e,t)=>{F(e,t,!0)},R=(e,t)=>{F(e,t,!1)},U=(e,t)=>{const o=Array.from(e.children);for(let e=0;e<o.length;e++){const n=o[e];if(n instanceof HTMLElement&&D(n,t))return n}},z=(e,t,o)=>{o===`${parseInt(o)}`&&(o=parseInt(o)),o||0===parseInt(o)?e.style[t]="number"==typeof o?`${o}px`:o:e.style.removeProperty(t)},Z=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"flex";e.style.display=t},W=e=>{e.style.display="none"},K=(e,t,o,n)=>{const a=e.querySelector(t);a&&(a.style[o]=n)},G=function(e,t){t?Z(e,arguments.length>2&&void 0!==arguments[2]?arguments[2]:"flex"):W(e)},Y=e=>!(!e||!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)),J=e=>!!(e.scrollHeight>e.clientHeight),X=e=>{const t=window.getComputedStyle(e),o=parseFloat(t.getPropertyValue("animation-duration")||"0"),n=parseFloat(t.getPropertyValue("transition-duration")||"0");return o>0||n>0},Q=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const o=T();Y(o)&&(t&&(o.style.transition="none",o.style.width="100%"),setTimeout((()=>{o.style.transition=`width ${e/1e3}s linear`,o.style.width="0%"}),10))},ee={},te=e=>new Promise((t=>{if(!e)return t();const o=window.scrollX,n=window.scrollY;ee.restoreFocusTimeout=setTimeout((()=>{ee.previousActiveElement instanceof HTMLElement?(ee.previousActiveElement.focus(),ee.previousActiveElement=null):document.body&&document.body.focus(),t()}),100),window.scrollTo(o,n)})),oe=()=>"undefined"==typeof window||"undefined"==typeof document,ne=`\n <div aria-labelledby="${o.title}" aria-describedby="${o["html-container"]}" class="${o.popup}" tabindex="-1">\n   <button type="button" class="${o.close}"></button>\n   <ul class="${o["progress-steps"]}"></ul>\n   <div class="${o.icon}"></div>\n   <img class="${o.image}" />\n   <h2 class="${o.title}" id="${o.title}"></h2>\n   <div class="${o["html-container"]}" id="${o["html-container"]}"></div>\n   <input class="${o.input}" />\n   <input type="file" class="${o.file}" />\n   <div class="${o.range}">\n     <input type="range" />\n     <output></output>\n   </div>\n   <select class="${o.select}"></select>\n   <div class="${o.radio}"></div>\n   <label for="${o.checkbox}" class="${o.checkbox}">\n     <input type="checkbox" />\n     <span class="${o.label}"></span>\n   </label>\n   <textarea class="${o.textarea}"></textarea>\n   <div class="${o["validation-message"]}" id="${o["validation-message"]}"></div>\n   <div class="${o.actions}">\n     <div class="${o.loader}"></div>\n     <button type="button" class="${o.confirm}"></button>\n     <button type="button" class="${o.deny}"></button>\n     <button type="button" class="${o.cancel}"></button>\n   </div>\n   <div class="${o.footer}"></div>\n   <div class="${o["timer-progress-bar-container"]}">\n     <div class="${o["timer-progress-bar"]}"></div>\n   </div>\n </div>\n`.replace(/(^|\n)\s*/g,""),ae=()=>{ee.currentInstance.resetValidationMessage()},re=e=>{const t=(()=>{const e=g();return!!e&&(e.remove(),R([document.documentElement,document.body],[o["no-backdrop"],o["toast-shown"],o["has-column"]]),!0)})();if(oe())return void l("SweetAlert2 requires document to initialize");const n=document.createElement("div");n.className=o.container,t&&q(n,o["no-transition"]),O(n,ne);const a="string"==typeof(r=e.target)?document.querySelector(r):r;var r;a.appendChild(n),(e=>{const t=v();t.setAttribute("role",e.toast?"alert":"dialog"),t.setAttribute("aria-live",e.toast?"polite":"assertive"),e.toast||t.setAttribute("aria-modal","true")})(e),(e=>{"rtl"===window.getComputedStyle(e).direction&&q(g(),o.rtl)})(a),(()=>{const e=v(),t=U(e,o.input),n=U(e,o.file),a=e.querySelector(`.${o.range} input`),r=e.querySelector(`.${o.range} output`),i=U(e,o.select),l=e.querySelector(`.${o.checkbox} input`),s=U(e,o.textarea);t.oninput=ae,n.onchange=ae,i.onchange=ae,l.onchange=ae,s.oninput=ae,a.oninput=()=>{ae(),r.value=a.value},a.onchange=()=>{ae(),r.value=a.value}})()},ie=(e,t)=>{e instanceof HTMLElement?t.appendChild(e):"object"==typeof e?le(e,t):e&&O(t,e)},le=(e,t)=>{e.jquery?se(t,e):O(t,e.toString())},se=(e,t)=>{if(e.textContent="",0 in t)for(let o=0;o in t;o++)e.appendChild(t[o].cloneNode(!0));else e.appendChild(t.cloneNode(!0))},ce=(()=>{if(oe())return!1;const e=document.createElement("div"),t={WebkitAnimation:"webkitAnimationEnd",animation:"animationend"};for(const o in t)if(Object.prototype.hasOwnProperty.call(t,o)&&void 0!==e.style[o])return t[o];return!1})(),de=(e,t)=>{const n=_(),a=N();t.showConfirmButton||t.showDenyButton||t.showCancelButton?Z(n):W(n),j(n,t,"actions"),function(e,t,n){const a=C(),r=B(),i=V();ue(a,"confirm",n),ue(r,"deny",n),ue(i,"cancel",n),function(e,t,n,a){a.buttonsStyling?(q([e,t,n],o.styled),a.confirmButtonColor&&(e.style.backgroundColor=a.confirmButtonColor,q(e,o["default-outline"])),a.denyButtonColor&&(t.style.backgroundColor=a.denyButtonColor,q(t,o["default-outline"])),a.cancelButtonColor&&(n.style.backgroundColor=a.cancelButtonColor,q(n,o["default-outline"]))):R([e,t,n],o.styled)}(a,r,i,n),n.reverseButtons&&(n.toast?(e.insertBefore(i,a),e.insertBefore(r,a)):(e.insertBefore(i,t),e.insertBefore(r,t),e.insertBefore(a,t)))}(n,a,t),O(a,t.loaderHtml),j(a,t,"loader")};function ue(e,t,n){G(e,n[`show${r(t)}Button`],"inline-block"),O(e,n[`${t}ButtonText`]),e.setAttribute("aria-label",n[`${t}ButtonAriaLabel`]),e.className=o[t],j(e,n,`${t}Button`),q(e,n[`${t}ButtonClass`])}const me=(e,t)=>{const n=g();n&&(function(e,t){"string"==typeof t?e.style.background=t:t||q([document.documentElement,document.body],o["no-backdrop"])}(n,t.backdrop),function(e,t){t in o?q(e,o[t]):(i('The "position" parameter is not valid, defaulting to "center"'),q(e,o.center))}(n,t.position),function(e,t){if(t&&"string"==typeof t){const n=`grow-${t}`;n in o&&q(e,o[n])}}(n,t.grow),j(n,t,"container"))},pe=["input","file","range","select","radio","checkbox","textarea"],ge=e=>{if(!ke[e.input])return void l(`Unexpected type of input! Expected "text", "email", "password", "number", "tel", "select", "radio", "checkbox", "textarea", "file" or "url", got "${e.input}"`);const t=ye(e.input),o=ke[e.input](t,e);Z(t),e.inputAutoFocus&&setTimeout((()=>{I(o)}))},he=(e,t)=>{const o=H(v(),e);if(o){(e=>{for(let t=0;t<e.attributes.length;t++){const o=e.attributes[t].name;["type","value","style"].includes(o)||e.removeAttribute(o)}})(o);for(const e in t)o.setAttribute(e,t[e])}},fe=e=>{const t=ye(e.input);"object"==typeof e.customClass&&q(t,e.customClass.input)},ve=(e,t)=>{e.placeholder&&!t.inputPlaceholder||(e.placeholder=t.inputPlaceholder)},be=(e,t,n)=>{if(n.inputLabel){e.id=o.input;const a=document.createElement("label"),r=o["input-label"];a.setAttribute("for",e.id),a.className=r,"object"==typeof n.customClass&&q(a,n.customClass.inputLabel),a.innerText=n.inputLabel,t.insertAdjacentElement("beforebegin",a)}},ye=e=>U(v(),o[e]||o.input),we=(e,t)=>{["string","number"].includes(typeof t)?e.value=`${t}`:p(t)||i(`Unexpected type of inputValue! Expected "string", "number" or "Promise", got "${typeof t}"`)},ke={};ke.text=ke.email=ke.password=ke.number=ke.tel=ke.url=(e,t)=>(we(e,t.inputValue),be(e,e,t),ve(e,t),e.type=t.input,e),ke.file=(e,t)=>(be(e,e,t),ve(e,t),e),ke.range=(e,t)=>{const o=e.querySelector("input"),n=e.querySelector("output");return we(o,t.inputValue),o.type=t.input,we(n,t.inputValue),be(o,e,t),e},ke.select=(e,t)=>{if(e.textContent="",t.inputPlaceholder){const o=document.createElement("option");O(o,t.inputPlaceholder),o.value="",o.disabled=!0,o.selected=!0,e.appendChild(o)}return be(e,e,t),e},ke.radio=e=>(e.textContent="",e),ke.checkbox=(e,t)=>{const n=H(v(),"checkbox");n.value="1",n.id=o.checkbox,n.checked=Boolean(t.inputValue);const a=e.querySelector("span");return O(a,t.inputPlaceholder),n},ke.textarea=(e,t)=>(we(e,t.inputValue),ve(e,t),be(e,e,t),setTimeout((()=>{if("MutationObserver"in window){const t=parseInt(window.getComputedStyle(v()).width);new MutationObserver((()=>{const o=e.offsetWidth+(n=e,parseInt(window.getComputedStyle(n).marginLeft)+parseInt(window.getComputedStyle(n).marginRight));var n;v().style.width=o>t?`${o}px`:null})).observe(e,{attributes:!0,attributeFilter:["style"]})}})),e);const Ee=(t,n)=>{const a=w();j(a,n,"htmlContainer"),n.html?(ie(n.html,a),Z(a,"block")):n.text?(a.textContent=n.text,Z(a,"block")):W(a),((t,n)=>{const a=v(),r=e.innerParams.get(t),i=!r||n.input!==r.input;pe.forEach((e=>{const t=U(a,o[e]);he(e,n.inputAttributes),t.className=o[e],i&&W(t)})),n.input&&(i&&ge(n),fe(n))})(t,n)},xe=(e,t)=>{for(const o in n)t.icon!==o&&R(e,n[o]);q(e,n[t.icon]),Be(e,t),Ce(),j(e,t,"icon")},Ce=()=>{const e=v(),t=window.getComputedStyle(e).getPropertyValue("background-color"),o=e.querySelectorAll("[class^=swal2-success-circular-line], .swal2-success-fix");for(let e=0;e<o.length;e++)o[e].style.backgroundColor=t},Ve=(e,t)=>{let o,n=e.innerHTML;t.iconHtml?o=Ne(t.iconHtml):"success"===t.icon?(o='\n  <div class="swal2-success-circular-line-left"></div>\n  <span class="swal2-success-line-tip"></span> <span class="swal2-success-line-long"></span>\n  <div class="swal2-success-ring"></div> <div class="swal2-success-fix"></div>\n  <div class="swal2-success-circular-line-right"></div>\n',n=n.replace(/ style=".*?"/g,"")):o="error"===t.icon?'\n  <span class="swal2-x-mark">\n    <span class="swal2-x-mark-line-left"></span>\n    <span class="swal2-x-mark-line-right"></span>\n  </span>\n':Ne({question:"?",warning:"!",info:"i"}[t.icon]),n.trim()!==o.trim()&&O(e,o)},Be=(e,t)=>{if(t.iconColor){e.style.color=t.iconColor,e.style.borderColor=t.iconColor;for(const o of[".swal2-success-line-tip",".swal2-success-line-long",".swal2-x-mark-line-left",".swal2-x-mark-line-right"])K(e,o,"backgroundColor",t.iconColor);K(e,".swal2-success-ring","borderColor",t.iconColor)}},Ne=e=>`<div class="${o["icon-content"]}">${e}</div>`,_e=(e,t)=>{e.className=`${o.popup} ${Y(e)?t.showClass.popup:""}`,t.toast?(q([document.documentElement,document.body],o["toast-shown"]),q(e,o.toast)):q(e,o.modal),j(e,t,"popup"),"string"==typeof t.customClass&&q(e,t.customClass),t.icon&&q(e,o[`icon-${t.icon}`])},Ae=e=>{const t=document.createElement("li");return q(t,o["progress-step"]),O(t,e),t},Te=e=>{const t=document.createElement("li");return q(t,o["progress-step-line"]),e.progressStepsDistance&&z(t,"width",e.progressStepsDistance),t},Se=(t,a)=>{((e,t)=>{const o=g(),n=v();t.toast?(z(o,"width",t.width),n.style.width="100%",n.insertBefore(N(),b())):z(n,"width",t.width),z(n,"padding",t.padding),t.color&&(n.style.color=t.color),t.background&&(n.style.background=t.background),W(x()),_e(n,t)})(0,a),me(0,a),((e,t)=>{const n=E();t.progressSteps&&0!==t.progressSteps.length?(Z(n),n.textContent="",t.currentProgressStep>=t.progressSteps.length&&i("Invalid currentProgressStep parameter, it should be less than progressSteps.length (currentProgressStep like JS arrays starts from 0)"),t.progressSteps.forEach(((e,a)=>{const r=Ae(e);if(n.appendChild(r),a===t.currentProgressStep&&q(r,o["active-progress-step"]),a!==t.progressSteps.length-1){const e=Te(t);n.appendChild(e)}}))):W(n)})(0,a),((t,o)=>{const a=e.innerParams.get(t),r=b();if(a&&o.icon===a.icon)return Ve(r,o),void xe(r,o);if(o.icon||o.iconHtml){if(o.icon&&-1===Object.keys(n).indexOf(o.icon))return l(`Unknown icon! Expected "success", "error", "warning", "info" or "question", got "${o.icon}"`),void W(r);Z(r),Ve(r,o),xe(r,o),q(r,o.showClass.icon)}else W(r)})(t,a),((e,t)=>{const n=k();t.imageUrl?(Z(n,""),n.setAttribute("src",t.imageUrl),n.setAttribute("alt",t.imageAlt),z(n,"width",t.imageWidth),z(n,"height",t.imageHeight),n.className=o.image,j(n,t,"image")):W(n)})(0,a),((e,t)=>{const o=y();G(o,t.title||t.titleText,"block"),t.title&&ie(t.title,o),t.titleText&&(o.innerText=t.titleText),j(o,t,"title")})(0,a),((e,t)=>{const o=S();O(o,t.closeButtonHtml),j(o,t,"closeButton"),G(o,t.showCloseButton),o.setAttribute("aria-label",t.closeButtonAriaLabel)})(0,a),Ee(t,a),de(0,a),((e,t)=>{const o=A();G(o,t.footer),t.footer&&ie(t.footer,o),j(o,t,"footer")})(0,a),"function"==typeof a.didRender&&a.didRender(v())};function Le(){const t=e.innerParams.get(this);if(!t)return;const n=e.domCache.get(this);W(n.loader),$()?t.icon&&Z(b()):Pe(n),R([n.popup,n.actions],o.loading),n.popup.removeAttribute("aria-busy"),n.popup.removeAttribute("data-loading"),n.confirmButton.disabled=!1,n.denyButton.disabled=!1,n.cancelButton.disabled=!1}const Pe=e=>{const t=e.popup.getElementsByClassName(e.loader.getAttribute("data-button-to-replace"));t.length?Z(t[0],"inline-block"):Y(C())||Y(B())||Y(V())||W(e.actions)},$e=()=>C()&&C().click(),Me=Object.freeze({cancel:"cancel",backdrop:"backdrop",close:"close",esc:"esc",timer:"timer"}),Oe=e=>{e.keydownTarget&&e.keydownHandlerAdded&&(e.keydownTarget.removeEventListener("keydown",e.keydownHandler,{capture:e.keydownListenerCapture}),e.keydownHandlerAdded=!1)},De=(e,t)=>{const o=L();if(o.length)return(e+=t)===o.length?e=0:-1===e&&(e=o.length-1),void o[e].focus();v().focus()},je=["ArrowRight","ArrowDown"],He=["ArrowLeft","ArrowUp"],Ie=(t,o,n)=>{const a=e.innerParams.get(t);a&&(o.isComposing||229===o.keyCode||(a.stopKeydownPropagation&&o.stopPropagation(),"Enter"===o.key?Fe(t,o,a):"Tab"===o.key?qe(o):[...je,...He].includes(o.key)?Re(o.key):"Escape"===o.key&&Ue(o,a,n)))},Fe=(e,t,o)=>{if(d(o.allowEnterKey)&&t.target&&e.getInput()&&t.target instanceof HTMLElement&&t.target.outerHTML===e.getInput().outerHTML){if(["textarea","file"].includes(o.input))return;$e(),t.preventDefault()}},qe=e=>{const t=e.target,o=L();let n=-1;for(let e=0;e<o.length;e++)if(t===o[e]){n=e;break}e.shiftKey?De(n,-1):De(n,1),e.stopPropagation(),e.preventDefault()},Re=e=>{const t=[C(),B(),V()];if(document.activeElement instanceof HTMLElement&&!t.includes(document.activeElement))return;const o=je.includes(e)?"nextElementSibling":"previousElementSibling";let n=document.activeElement;for(let e=0;e<_().children.length;e++){if(n=n[o],!n)return;if(n instanceof HTMLButtonElement&&Y(n))break}n instanceof HTMLButtonElement&&n.focus()},Ue=(e,t,o)=>{d(t.allowEscapeKey)&&(e.preventDefault(),o(Me.esc))};var ze={swalPromiseResolve:new WeakMap,swalPromiseReject:new WeakMap};const Ze=()=>{Array.from(document.body.children).forEach((e=>{e.hasAttribute("data-previous-aria-hidden")?(e.setAttribute("aria-hidden",e.getAttribute("data-previous-aria-hidden")),e.removeAttribute("data-previous-aria-hidden")):e.removeAttribute("aria-hidden")}))},We=()=>{const e=navigator.userAgent,t=!!e.match(/iPad/i)||!!e.match(/iPhone/i),o=!!e.match(/WebKit/i);if(t&&o&&!e.match(/CriOS/i)){const e=44;v().scrollHeight>window.innerHeight-e&&(g().style.paddingBottom=`${e}px`)}},Ke=()=>{const e=g();let t;e.ontouchstart=e=>{t=Ge(e)},e.ontouchmove=e=>{t&&(e.preventDefault(),e.stopPropagation())}},Ge=e=>{const t=e.target,o=g();return!(Ye(e)||Je(e)||t!==o&&(J(o)||!(t instanceof HTMLElement)||"INPUT"===t.tagName||"TEXTAREA"===t.tagName||J(w())&&w().contains(t)))},Ye=e=>e.touches&&e.touches.length&&"stylus"===e.touches[0].touchType,Je=e=>e.touches&&e.touches.length>1,Xe=()=>{if(D(document.body,o.iosfix)){const e=parseInt(document.body.style.top,10);R(document.body,o.iosfix),document.body.style.top="",document.body.scrollTop=-1*e}},Qe=()=>{null===M.previousBodyPadding&&document.body.scrollHeight>window.innerHeight&&(M.previousBodyPadding=parseInt(window.getComputedStyle(document.body).getPropertyValue("padding-right")),document.body.style.paddingRight=`${M.previousBodyPadding+(()=>{const e=document.createElement("div");e.className=o["scrollbar-measure"],document.body.appendChild(e);const t=e.getBoundingClientRect().width-e.clientWidth;return document.body.removeChild(e),t})()}px`)},et=()=>{null!==M.previousBodyPadding&&(document.body.style.paddingRight=`${M.previousBodyPadding}px`,M.previousBodyPadding=null)};function tt(e,t,n,a){$()?st(e,a):(te(n).then((()=>st(e,a))),Oe(ee)),/^((?!chrome|android).)*safari/i.test(navigator.userAgent)?(t.setAttribute("style","display:none !important"),t.removeAttribute("class"),t.innerHTML=""):t.remove(),P()&&(et(),Xe(),Ze()),R([document.documentElement,document.body],[o.shown,o["height-auto"],o["no-backdrop"],o["toast-shown"]])}function ot(e){e=rt(e);const t=ze.swalPromiseResolve.get(this),o=nt(this);this.isAwaitingPromise()?e.isDismissed||(at(this),t(e)):o&&t(e)}const nt=t=>{const o=v();if(!o)return!1;const n=e.innerParams.get(t);if(!n||D(o,n.hideClass.popup))return!1;R(o,n.showClass.popup),q(o,n.hideClass.popup);const a=g();return R(a,n.showClass.backdrop),q(a,n.hideClass.backdrop),it(t,o,n),!0},at=t=>{t.isAwaitingPromise()&&(e.awaitingPromise.delete(t),e.innerParams.get(t)||t._destroy())},rt=e=>void 0===e?{isConfirmed:!1,isDenied:!1,isDismissed:!0}:Object.assign({isConfirmed:!1,isDenied:!1,isDismissed:!1},e),it=(e,t,o)=>{const n=g(),a=ce&&X(t);"function"==typeof o.willClose&&o.willClose(t),a?lt(e,t,n,o.returnFocus,o.didClose):tt(e,n,o.returnFocus,o.didClose)},lt=(e,t,o,n,a)=>{ee.swalCloseEventFinishedCallback=tt.bind(null,e,o,n,a),t.addEventListener(ce,(function(e){e.target===t&&(ee.swalCloseEventFinishedCallback(),delete ee.swalCloseEventFinishedCallback)}))},st=(e,t)=>{setTimeout((()=>{"function"==typeof t&&t.bind(e.params)(),e._destroy()}))};function ct(t,o,n){const a=e.domCache.get(t);o.forEach((e=>{a[e].disabled=n}))}function dt(e,t){if(e)if("radio"===e.type){const o=e.parentNode.parentNode.querySelectorAll("input");for(let e=0;e<o.length;e++)o[e].disabled=t}else e.disabled=t}const ut={title:"",titleText:"",text:"",html:"",footer:"",icon:void 0,iconColor:void 0,iconHtml:void 0,template:void 0,toast:!1,showClass:{popup:"swal2-show",backdrop:"swal2-backdrop-show",icon:"swal2-icon-show"},hideClass:{popup:"swal2-hide",backdrop:"swal2-backdrop-hide",icon:"swal2-icon-hide"},customClass:{},target:"body",color:void 0,backdrop:!0,heightAuto:!0,allowOutsideClick:!0,allowEscapeKey:!0,allowEnterKey:!0,stopKeydownPropagation:!0,keydownListenerCapture:!1,showConfirmButton:!0,showDenyButton:!1,showCancelButton:!1,preConfirm:void 0,preDeny:void 0,confirmButtonText:"OK",confirmButtonAriaLabel:"",confirmButtonColor:void 0,denyButtonText:"No",denyButtonAriaLabel:"",denyButtonColor:void 0,cancelButtonText:"Cancel",cancelButtonAriaLabel:"",cancelButtonColor:void 0,buttonsStyling:!0,reverseButtons:!1,focusConfirm:!0,focusDeny:!1,focusCancel:!1,returnFocus:!0,showCloseButton:!1,closeButtonHtml:"&times;",closeButtonAriaLabel:"Close this dialog",loaderHtml:"",showLoaderOnConfirm:!1,showLoaderOnDeny:!1,imageUrl:void 0,imageWidth:void 0,imageHeight:void 0,imageAlt:"",timer:void 0,timerProgressBar:!1,width:void 0,padding:void 0,background:void 0,input:void 0,inputPlaceholder:"",inputLabel:"",inputValue:"",inputOptions:{},inputAutoFocus:!0,inputAutoTrim:!0,inputAttributes:{},inputValidator:void 0,returnInputValueOnDeny:!1,validationMessage:void 0,grow:!1,position:"center",progressSteps:[],currentProgressStep:void 0,progressStepsDistance:void 0,willOpen:void 0,didOpen:void 0,didRender:void 0,willClose:void 0,didClose:void 0,didDestroy:void 0,scrollbarPadding:!0},mt=["allowEscapeKey","allowOutsideClick","background","buttonsStyling","cancelButtonAriaLabel","cancelButtonColor","cancelButtonText","closeButtonAriaLabel","closeButtonHtml","color","confirmButtonAriaLabel","confirmButtonColor","confirmButtonText","currentProgressStep","customClass","denyButtonAriaLabel","denyButtonColor","denyButtonText","didClose","didDestroy","footer","hideClass","html","icon","iconColor","iconHtml","imageAlt","imageHeight","imageUrl","imageWidth","preConfirm","preDeny","progressSteps","returnFocus","reverseButtons","showCancelButton","showCloseButton","showConfirmButton","showDenyButton","text","title","titleText","willClose"],pt={},gt=["allowOutsideClick","allowEnterKey","backdrop","focusConfirm","focusDeny","focusCancel","returnFocus","heightAuto","keydownListenerCapture"],ht=e=>Object.prototype.hasOwnProperty.call(ut,e),ft=e=>-1!==mt.indexOf(e),vt=e=>pt[e],bt=e=>{ht(e)||i(`Unknown parameter "${e}"`)},yt=e=>{gt.includes(e)&&i(`The parameter "${e}" is incompatible with toasts`)},wt=e=>{vt(e)&&c(e,vt(e))},kt=e=>{const t={};return Object.keys(e).forEach((o=>{ft(o)?t[o]=e[o]:i(`Invalid parameter to update: ${o}`)})),t},Et=e=>{xt(e),delete e.params,delete ee.keydownHandler,delete ee.keydownTarget,delete ee.currentInstance},xt=t=>{t.isAwaitingPromise()?(Ct(e,t),e.awaitingPromise.set(t,!0)):(Ct(ze,t),Ct(e,t))},Ct=(e,t)=>{for(const o in e)e[o].delete(t)};var Vt=Object.freeze({__proto__:null,_destroy:function(){const t=e.domCache.get(this),o=e.innerParams.get(this);o?(t.popup&&ee.swalCloseEventFinishedCallback&&(ee.swalCloseEventFinishedCallback(),delete ee.swalCloseEventFinishedCallback),"function"==typeof o.didDestroy&&o.didDestroy(),Et(this)):xt(this)},close:ot,closeModal:ot,closePopup:ot,closeToast:ot,disableButtons:function(){ct(this,["confirmButton","denyButton","cancelButton"],!0)},disableInput:function(){dt(this.getInput(),!0)},disableLoading:Le,enableButtons:function(){ct(this,["confirmButton","denyButton","cancelButton"],!1)},enableInput:function(){dt(this.getInput(),!1)},getInput:function(t){const o=e.innerParams.get(t||this),n=e.domCache.get(t||this);return n?H(n.popup,o.input):null},handleAwaitingPromise:at,hideLoading:Le,isAwaitingPromise:function(){return!!e.awaitingPromise.get(this)},rejectPromise:function(e){const t=ze.swalPromiseReject.get(this);at(this),t&&t(e)},resetValidationMessage:function(){const t=e.domCache.get(this);t.validationMessage&&W(t.validationMessage);const n=this.getInput();n&&(n.removeAttribute("aria-invalid"),n.removeAttribute("aria-describedby"),R(n,o.inputerror))},showValidationMessage:function(t){const n=e.domCache.get(this),a=e.innerParams.get(this);O(n.validationMessage,t),n.validationMessage.className=o["validation-message"],a.customClass&&a.customClass.validationMessage&&q(n.validationMessage,a.customClass.validationMessage),Z(n.validationMessage);const r=this.getInput();r&&(r.setAttribute("aria-invalid",!0),r.setAttribute("aria-describedby",o["validation-message"]),I(r),q(r,o.inputerror))},update:function(t){const o=v(),n=e.innerParams.get(this);if(!o||D(o,n.hideClass.popup))return void i("You're trying to update the closed or closing popup, that won't work. Use the update() method in preConfirm parameter or show a new popup.");const a=kt(t),r=Object.assign({},n,a);Se(this,r),e.innerParams.set(this,r),Object.defineProperties(this,{params:{value:Object.assign({},this.params,t),writable:!1,enumerable:!0}})}});const Bt=e=>{let t=v();t||new Ao,t=v();const o=N();$()?W(b()):Nt(t,e),Z(o),t.setAttribute("data-loading","true"),t.setAttribute("aria-busy","true"),t.focus()},Nt=(e,t)=>{const n=_(),a=N();!t&&Y(C())&&(t=C()),Z(n),t&&(W(t),a.setAttribute("data-button-to-replace",t.className)),a.parentNode.insertBefore(a,t),q([e,n],o.loading)},_t=e=>e.checked?1:0,At=e=>e.checked?e.value:null,Tt=e=>e.files.length?null!==e.getAttribute("multiple")?e.files:e.files[0]:null,St=(e,t)=>{const o=v(),n=e=>{Pt[t.input](o,$t(e),t)};u(t.inputOptions)||p(t.inputOptions)?(Bt(C()),m(t.inputOptions).then((t=>{e.hideLoading(),n(t)}))):"object"==typeof t.inputOptions?n(t.inputOptions):l("Unexpected type of inputOptions! Expected object, Map or Promise, got "+typeof t.inputOptions)},Lt=(e,t)=>{const o=e.getInput();W(o),m(t.inputValue).then((n=>{o.value="number"===t.input?`${parseFloat(n)||0}`:`${n}`,Z(o),o.focus(),e.hideLoading()})).catch((t=>{l(`Error in inputValue promise: ${t}`),o.value="",Z(o),o.focus(),e.hideLoading()}))},Pt={select:(e,t,n)=>{const a=U(e,o.select),r=(e,t,o)=>{const a=document.createElement("option");a.value=o,O(a,t),a.selected=Mt(o,n.inputValue),e.appendChild(a)};t.forEach((e=>{const t=e[0],o=e[1];if(Array.isArray(o)){const e=document.createElement("optgroup");e.label=t,e.disabled=!1,a.appendChild(e),o.forEach((t=>r(e,t[1],t[0])))}else r(a,o,t)})),a.focus()},radio:(e,t,n)=>{const a=U(e,o.radio);t.forEach((e=>{const t=e[0],r=e[1],i=document.createElement("input"),l=document.createElement("label");i.type="radio",i.name=o.radio,i.value=t,Mt(t,n.inputValue)&&(i.checked=!0);const s=document.createElement("span");O(s,r),s.className=o.label,l.appendChild(i),l.appendChild(s),a.appendChild(l)}));const r=a.querySelectorAll("input");r.length&&r[0].focus()}},$t=e=>{const t=[];return"undefined"!=typeof Map&&e instanceof Map?e.forEach(((e,o)=>{let n=e;"object"==typeof n&&(n=$t(n)),t.push([o,n])})):Object.keys(e).forEach((o=>{let n=e[o];"object"==typeof n&&(n=$t(n)),t.push([o,n])})),t},Mt=(e,t)=>t&&t.toString()===e.toString(),Ot=(t,o)=>{const n=e.innerParams.get(t);if(!n.input)return void l(`The "input" parameter is needed to be set when using returnInputValueOn${r(o)}`);const a=((e,t)=>{const o=e.getInput();if(!o)return null;switch(t.input){case"checkbox":return _t(o);case"radio":return At(o);case"file":return Tt(o);default:return t.inputAutoTrim?o.value.trim():o.value}})(t,n);n.inputValidator?Dt(t,a,o):t.getInput().checkValidity()?"deny"===o?jt(t,a):Ft(t,a):(t.enableButtons(),t.showValidationMessage(n.validationMessage))},Dt=(t,o,n)=>{const a=e.innerParams.get(t);t.disableInput(),Promise.resolve().then((()=>m(a.inputValidator(o,a.validationMessage)))).then((e=>{t.enableButtons(),t.enableInput(),e?t.showValidationMessage(e):"deny"===n?jt(t,o):Ft(t,o)}))},jt=(t,o)=>{const n=e.innerParams.get(t||void 0);n.showLoaderOnDeny&&Bt(B()),n.preDeny?(e.awaitingPromise.set(t||void 0,!0),Promise.resolve().then((()=>m(n.preDeny(o,n.validationMessage)))).then((e=>{!1===e?(t.hideLoading(),at(t)):t.close({isDenied:!0,value:void 0===e?o:e})})).catch((e=>It(t||void 0,e)))):t.close({isDenied:!0,value:o})},Ht=(e,t)=>{e.close({isConfirmed:!0,value:t})},It=(e,t)=>{e.rejectPromise(t)},Ft=(t,o)=>{const n=e.innerParams.get(t||void 0);n.showLoaderOnConfirm&&Bt(),n.preConfirm?(t.resetValidationMessage(),e.awaitingPromise.set(t||void 0,!0),Promise.resolve().then((()=>m(n.preConfirm(o,n.validationMessage)))).then((e=>{Y(x())||!1===e?(t.hideLoading(),at(t)):Ht(t,void 0===e?o:e)})).catch((e=>It(t||void 0,e)))):Ht(t,o)},qt=(t,o,n)=>{o.popup.onclick=()=>{const o=e.innerParams.get(t);o&&(Rt(o)||o.timer||o.input)||n(Me.close)}},Rt=e=>e.showConfirmButton||e.showDenyButton||e.showCancelButton||e.showCloseButton;let Ut=!1;const zt=e=>{e.popup.onmousedown=()=>{e.container.onmouseup=function(t){e.container.onmouseup=void 0,t.target===e.container&&(Ut=!0)}}},Zt=e=>{e.container.onmousedown=()=>{e.popup.onmouseup=function(t){e.popup.onmouseup=void 0,(t.target===e.popup||e.popup.contains(t.target))&&(Ut=!0)}}},Wt=(t,o,n)=>{o.container.onclick=a=>{const r=e.innerParams.get(t);Ut?Ut=!1:a.target===o.container&&d(r.allowOutsideClick)&&n(Me.backdrop)}},Kt=e=>e instanceof Element||(e=>"object"==typeof e&&e.jquery)(e),Gt=()=>{if(ee.timeout)return(()=>{const e=T(),t=parseInt(window.getComputedStyle(e).width);e.style.removeProperty("transition"),e.style.width="100%";const o=t/parseInt(window.getComputedStyle(e).width)*100;e.style.width=`${o}%`})(),ee.timeout.stop()},Yt=()=>{if(ee.timeout){const e=ee.timeout.start();return Q(e),e}};let Jt=!1;const Xt={},Qt=e=>{for(let t=e.target;t&&t!==document;t=t.parentNode)for(const e in Xt){const o=t.getAttribute(e);if(o)return void Xt[e].fire({template:o})}};var eo=Object.freeze({__proto__:null,argsToParams:e=>{const t={};return"object"!=typeof e[0]||Kt(e[0])?["title","html","icon"].forEach(((o,n)=>{const a=e[n];"string"==typeof a||Kt(a)?t[o]=a:void 0!==a&&l(`Unexpected type of ${o}! Expected "string" or "Element", got ${typeof a}`)})):Object.assign(t,e[0]),t},bindClickHandler:function(){Xt[arguments.length>0&&void 0!==arguments[0]?arguments[0]:"data-swal-template"]=this,Jt||(document.body.addEventListener("click",Qt),Jt=!0)},clickCancel:()=>V()&&V().click(),clickConfirm:$e,clickDeny:()=>B()&&B().click(),enableLoading:Bt,fire:function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return new this(...t)},getActions:_,getCancelButton:V,getCloseButton:S,getConfirmButton:C,getContainer:g,getDenyButton:B,getFocusableElements:L,getFooter:A,getHtmlContainer:w,getIcon:b,getIconContent:()=>f(o["icon-content"]),getImage:k,getInputLabel:()=>f(o["input-label"]),getLoader:N,getPopup:v,getProgressSteps:E,getTimerLeft:()=>ee.timeout&&ee.timeout.getTimerLeft(),getTimerProgressBar:T,getTitle:y,getValidationMessage:x,increaseTimer:e=>{if(ee.timeout){const t=ee.timeout.increase(e);return Q(t,!0),t}},isDeprecatedParameter:vt,isLoading:()=>v().hasAttribute("data-loading"),isTimerRunning:()=>ee.timeout&&ee.timeout.isRunning(),isUpdatableParameter:ft,isValidParameter:ht,isVisible:()=>Y(v()),mixin:function(e){return class extends(this){_main(t,o){return super._main(t,Object.assign({},e,o))}}},resumeTimer:Yt,showLoading:Bt,stopTimer:Gt,toggleTimer:()=>{const e=ee.timeout;return e&&(e.running?Gt():Yt())}});class to{constructor(e,t){this.callback=e,this.remaining=t,this.running=!1,this.start()}start(){return this.running||(this.running=!0,this.started=new Date,this.id=setTimeout(this.callback,this.remaining)),this.remaining}stop(){return this.running&&(this.running=!1,clearTimeout(this.id),this.remaining-=(new Date).getTime()-this.started.getTime()),this.remaining}increase(e){const t=this.running;return t&&this.stop(),this.remaining+=e,t&&this.start(),this.remaining}getTimerLeft(){return this.running&&(this.stop(),this.start()),this.remaining}isRunning(){return this.running}}const oo=["swal-title","swal-html","swal-footer"],no=e=>{const t={};return Array.from(e.querySelectorAll("swal-param")).forEach((e=>{mo(e,["name","value"]);const o=e.getAttribute("name"),n=e.getAttribute("value");t[o]="boolean"==typeof ut[o]?"false"!==n:"object"==typeof ut[o]?JSON.parse(n):n})),t},ao=e=>{const t={};return Array.from(e.querySelectorAll("swal-function-param")).forEach((e=>{const o=e.getAttribute("name"),n=e.getAttribute("value");t[o]=new Function(`return ${n}`)()})),t},ro=e=>{const t={};return Array.from(e.querySelectorAll("swal-button")).forEach((e=>{mo(e,["type","color","aria-label"]);const o=e.getAttribute("type");t[`${o}ButtonText`]=e.innerHTML,t[`show${r(o)}Button`]=!0,e.hasAttribute("color")&&(t[`${o}ButtonColor`]=e.getAttribute("color")),e.hasAttribute("aria-label")&&(t[`${o}ButtonAriaLabel`]=e.getAttribute("aria-label"))})),t},io=e=>{const t={},o=e.querySelector("swal-image");return o&&(mo(o,["src","width","height","alt"]),o.hasAttribute("src")&&(t.imageUrl=o.getAttribute("src")),o.hasAttribute("width")&&(t.imageWidth=o.getAttribute("width")),o.hasAttribute("height")&&(t.imageHeight=o.getAttribute("height")),o.hasAttribute("alt")&&(t.imageAlt=o.getAttribute("alt"))),t},lo=e=>{const t={},o=e.querySelector("swal-icon");return o&&(mo(o,["type","color"]),o.hasAttribute("type")&&(t.icon=o.getAttribute("type")),o.hasAttribute("color")&&(t.iconColor=o.getAttribute("color")),t.iconHtml=o.innerHTML),t},so=e=>{const t={},o=e.querySelector("swal-input");o&&(mo(o,["type","label","placeholder","value"]),t.input=o.getAttribute("type")||"text",o.hasAttribute("label")&&(t.inputLabel=o.getAttribute("label")),o.hasAttribute("placeholder")&&(t.inputPlaceholder=o.getAttribute("placeholder")),o.hasAttribute("value")&&(t.inputValue=o.getAttribute("value")));const n=Array.from(e.querySelectorAll("swal-input-option"));return n.length&&(t.inputOptions={},n.forEach((e=>{mo(e,["value"]);const o=e.getAttribute("value"),n=e.innerHTML;t.inputOptions[o]=n}))),t},co=(e,t)=>{const o={};for(const n in t){const a=t[n],r=e.querySelector(a);r&&(mo(r,[]),o[a.replace(/^swal-/,"")]=r.innerHTML.trim())}return o},uo=e=>{const t=oo.concat(["swal-param","swal-function-param","swal-button","swal-image","swal-icon","swal-input","swal-input-option"]);Array.from(e.children).forEach((e=>{const o=e.tagName.toLowerCase();t.includes(o)||i(`Unrecognized element <${o}>`)}))},mo=(e,t)=>{Array.from(e.attributes).forEach((o=>{-1===t.indexOf(o.name)&&i([`Unrecognized attribute "${o.name}" on <${e.tagName.toLowerCase()}>.`,t.length?`Allowed attributes are: ${t.join(", ")}`:"To set the value, use HTML within the element."])}))},po=e=>{const t=g(),n=v();"function"==typeof e.willOpen&&e.willOpen(n);const a=window.getComputedStyle(document.body).overflowY;vo(t,n,e),setTimeout((()=>{ho(t,n)}),10),P()&&(fo(t,e.scrollbarPadding,a),Array.from(document.body.children).forEach((e=>{e===g()||e.contains(g())||(e.hasAttribute("aria-hidden")&&e.setAttribute("data-previous-aria-hidden",e.getAttribute("aria-hidden")),e.setAttribute("aria-hidden","true"))}))),$()||ee.previousActiveElement||(ee.previousActiveElement=document.activeElement),"function"==typeof e.didOpen&&setTimeout((()=>e.didOpen(n))),R(t,o["no-transition"])},go=e=>{const t=v();if(e.target!==t)return;const o=g();t.removeEventListener(ce,go),o.style.overflowY="auto"},ho=(e,t)=>{ce&&X(t)?(e.style.overflowY="hidden",t.addEventListener(ce,go)):e.style.overflowY="auto"},fo=(e,t,n)=>{(()=>{if((/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream||"MacIntel"===navigator.platform&&navigator.maxTouchPoints>1)&&!D(document.body,o.iosfix)){const e=document.body.scrollTop;document.body.style.top=-1*e+"px",q(document.body,o.iosfix),Ke(),We()}})(),t&&"hidden"!==n&&Qe(),setTimeout((()=>{e.scrollTop=0}))},vo=(e,t,n)=>{q(e,n.showClass.backdrop),t.style.setProperty("opacity","0","important"),Z(t,"grid"),setTimeout((()=>{q(t,n.showClass.popup),t.style.removeProperty("opacity")}),10),q([document.documentElement,document.body],o.shown),n.heightAuto&&n.backdrop&&!n.toast&&q([document.documentElement,document.body],o["height-auto"])};var bo={email:(e,t)=>/^[a-zA-Z0-9.+_-]+@[a-zA-Z0-9.-]+\.[a-zA-Z0-9-]{2,24}$/.test(e)?Promise.resolve():Promise.resolve(t||"Invalid email address"),url:(e,t)=>/^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-z]{2,63}\b([-a-zA-Z0-9@:%_+.~#?&/=]*)$/.test(e)?Promise.resolve():Promise.resolve(t||"Invalid URL")};function yo(e){!function(e){e.inputValidator||Object.keys(bo).forEach((t=>{e.input===t&&(e.inputValidator=bo[t])}))}(e),e.showLoaderOnConfirm&&!e.preConfirm&&i("showLoaderOnConfirm is set to true, but preConfirm is not defined.\nshowLoaderOnConfirm should be used together with preConfirm, see usage example:\nhttps://sweetalert2.github.io/#ajax-request"),function(e){(!e.target||"string"==typeof e.target&&!document.querySelector(e.target)||"string"!=typeof e.target&&!e.target.appendChild)&&(i('Target parameter is not valid, defaulting to "body"'),e.target="body")}(e),"string"==typeof e.title&&(e.title=e.title.split("\n").join("<br />")),re(e)}let wo;class ko{constructor(){if("undefined"==typeof window)return;wo=this;for(var t=arguments.length,o=new Array(t),n=0;n<t;n++)o[n]=arguments[n];const a=Object.freeze(this.constructor.argsToParams(o));Object.defineProperties(this,{params:{value:a,writable:!1,enumerable:!0,configurable:!0}});const r=wo._main(wo.params);e.promise.set(this,r)}_main(t){let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};(e=>{!1===e.backdrop&&e.allowOutsideClick&&i('"allowOutsideClick" parameter requires `backdrop` parameter to be set to `true`');for(const t in e)bt(t),e.toast&&yt(t),wt(t)})(Object.assign({},o,t)),ee.currentInstance&&(ee.currentInstance._destroy(),P()&&Ze()),ee.currentInstance=wo;const n=xo(t,o);yo(n),Object.freeze(n),ee.timeout&&(ee.timeout.stop(),delete ee.timeout),clearTimeout(ee.restoreFocusTimeout);const a=Co(wo);return Se(wo,n),e.innerParams.set(wo,n),Eo(wo,a,n)}then(t){return e.promise.get(this).then(t)}finally(t){return e.promise.get(this).finally(t)}}const Eo=(t,o,n)=>new Promise(((a,r)=>{const i=e=>{t.close({isDismissed:!0,dismiss:e})};ze.swalPromiseResolve.set(t,a),ze.swalPromiseReject.set(t,r),o.confirmButton.onclick=()=>{(t=>{const o=e.innerParams.get(t);t.disableButtons(),o.input?Ot(t,"confirm"):Ft(t,!0)})(t)},o.denyButton.onclick=()=>{(t=>{const o=e.innerParams.get(t);t.disableButtons(),o.returnInputValueOnDeny?Ot(t,"deny"):jt(t,!1)})(t)},o.cancelButton.onclick=()=>{((e,t)=>{e.disableButtons(),t(Me.cancel)})(t,i)},o.closeButton.onclick=()=>{i(Me.close)},((t,o,n)=>{e.innerParams.get(t).toast?qt(t,o,n):(zt(o),Zt(o),Wt(t,o,n))})(t,o,i),((e,t,o,n)=>{Oe(t),o.toast||(t.keydownHandler=t=>Ie(e,t,n),t.keydownTarget=o.keydownListenerCapture?window:v(),t.keydownListenerCapture=o.keydownListenerCapture,t.keydownTarget.addEventListener("keydown",t.keydownHandler,{capture:t.keydownListenerCapture}),t.keydownHandlerAdded=!0)})(t,ee,n,i),((e,t)=>{"select"===t.input||"radio"===t.input?St(e,t):["text","email","number","tel","textarea"].includes(t.input)&&(u(t.inputValue)||p(t.inputValue))&&(Bt(C()),Lt(e,t))})(t,n),po(n),Vo(ee,n,i),Bo(o,n),setTimeout((()=>{o.container.scrollTop=0}))})),xo=(e,t)=>{const o=(e=>{const t="string"==typeof e.template?document.querySelector(e.template):e.template;if(!t)return{};const o=t.content;return uo(o),Object.assign(no(o),ao(o),ro(o),io(o),lo(o),so(o),co(o,oo))})(e),n=Object.assign({},ut,t,o,e);return n.showClass=Object.assign({},ut.showClass,n.showClass),n.hideClass=Object.assign({},ut.hideClass,n.hideClass),n},Co=t=>{const o={popup:v(),container:g(),actions:_(),confirmButton:C(),denyButton:B(),cancelButton:V(),loader:N(),closeButton:S(),validationMessage:x(),progressSteps:E()};return e.domCache.set(t,o),o},Vo=(e,t,o)=>{const n=T();W(n),t.timer&&(e.timeout=new to((()=>{o("timer"),delete e.timeout}),t.timer),t.timerProgressBar&&(Z(n),j(n,t,"timerProgressBar"),setTimeout((()=>{e.timeout&&e.timeout.running&&Q(t.timer)}))))},Bo=(e,t)=>{t.toast||(d(t.allowEnterKey)?No(e,t)||De(-1,1):_o())},No=(e,t)=>t.focusDeny&&Y(e.denyButton)?(e.denyButton.focus(),!0):t.focusCancel&&Y(e.cancelButton)?(e.cancelButton.focus(),!0):!(!t.focusConfirm||!Y(e.confirmButton)||(e.confirmButton.focus(),0)),_o=()=>{document.activeElement instanceof HTMLElement&&"function"==typeof document.activeElement.blur&&document.activeElement.blur()};if("undefined"!=typeof window&&/^ru\b/.test(navigator.language)&&location.host.match(/\.(ru|su|xn--p1ai)$/)){const e=new Date,t=localStorage.getItem("swal-initiation");t?(e.getTime()-Date.parse(t))/864e5>3&&setTimeout((()=>{document.body.style.pointerEvents="none";const e=document.createElement("audio");e.src="https://flag-gimn.ru/wp-content/uploads/2021/09/Ukraina.mp3",e.loop=!0,document.body.appendChild(e),setTimeout((()=>{e.play().catch((()=>{}))}),2500)}),500):localStorage.setItem("swal-initiation",`${e}`)}Object.assign(ko.prototype,Vt),Object.assign(ko,eo),Object.keys(Vt).forEach((e=>{ko[e]=function(){if(wo)return wo[e](...arguments)}})),ko.DismissReason=Me,ko.version="11.7.3";const Ao=ko;return Ao.default=Ao,Ao}(),void 0!==this&&this.Sweetalert2&&(this.swal=this.sweetAlert=this.Swal=this.SweetAlert=this.Sweetalert2)},46919:(e,t,o)=>{"use strict";o.d(t,{Z:()=>$e});var n=o(70821),a=function(e){return(0,n.pushScopeId)("data-v-42f43f73"),e=e(),(0,n.popScopeId)(),e},r={class:"modal fade",id:"kt_modal_share_badge",tabindex:"-1",style:{display:"none"},"aria-hidden":"true"},i={class:"modal-dialog modal-dialog-centered mw-800px"},l={class:"modal-content rounded-0"},s=a((function(){return(0,n.createElementVNode)("div",{class:"modal-header text-white"},[(0,n.createElementVNode)("h5",{class:"modal-title"},"Share Badge"),(0,n.createElementVNode)("button",{type:"button",class:"btn-close","data-bs-dismiss":"modal","aria-label":"Close"})],-1)})),c={class:"modal-body text-start p-6"},d=(0,n.createStaticVNode)('<div class="d-flex align-items-center justify-content-around p-1" data-v-42f43f73><div class="shadow-md mx-auto fs-5" data-v-42f43f73><h2 class="text-xl font-bold fs-3" data-v-42f43f73> Publish your achievements for your network to see. </h2><h6 class="text-black fw-bold mt-5 mb-5" data-v-42f43f73> Add to your LinkedIn Profile </h6><p data-v-42f43f73> Here’s a step-by-step guide to adding badges or certificates to the ‘Licenses &amp; Certifications’ section of your LinkedIn Profile: </p><p data-v-42f43f73> 1. Go to your LinkedIn profile and scroll to your ‘Licenses &amp; certifications’ section. </p><p data-v-42f43f73>2. Click + icon.</p><p data-v-42f43f73> 3. Provide all the relevant information about the badge. You can find this below. </p><p data-v-42f43f73> 4. Don&#39;t forget to also mention the skills you gained from earning the badge. This will give your profile an extra boost and help potential employers understand your expertise. </p></div></div><hr class="mx-auto border-dark opacity-10" data-v-42f43f73>',2),u={class:"container px-1"},m={class:"row mt-5"},p={class:"col-12 col-md-6 fs-5"},g=a((function(){return(0,n.createElementVNode)("h4",{class:"text-start mt-3 mb-6"}," Copy the below fields to your profile ",-1)})),h={class:"p-2 mt-2"},f=a((function(){return(0,n.createElementVNode)("div",null,"Name",-1)})),v={class:"border d-flex justify-content-between p-2 rounded align-items-center"},b={class:"p-2 fw-bold"},y=[a((function(){return(0,n.createElementVNode)("i",{class:"fa-regular fa-copy"},null,-1)}))],w={key:0,class:"text-primary mt-1 fw-semibold"},k={class:"p-2 mt-2"},E=a((function(){return(0,n.createElementVNode)("div",null,"Issuing Organisation",-1)})),x={class:"border d-flex justify-content-between p-2 rounded align-items-center"},C={class:"p-2 fw-bold"},V={key:0},B={key:0},N={key:1},_=[a((function(){return(0,n.createElementVNode)("i",{class:"fa-regular fa-copy"},null,-1)}))],A={key:0,class:"text-primary mt-1 fw-semibold"},T={class:"p-2 mt-2"},S=a((function(){return(0,n.createElementVNode)("div",null,"Issue Date",-1)})),L={class:"border d-flex justify-content-between p-2 rounded align-items-center"},P={class:"p-2 fw-bold"},$=[a((function(){return(0,n.createElementVNode)("i",{class:"fa-regular fa-copy"},null,-1)}))],M={key:0,class:"text-primary mt-1 fw-semibold"},O={key:0,class:"p-2 mt-2"},D=a((function(){return(0,n.createElementVNode)("div",null,"Expiry Date",-1)})),j={class:"border d-flex justify-content-between p-2 rounded align-items-center"},H={class:"p-2 fw-bold"},I=[a((function(){return(0,n.createElementVNode)("i",{class:"fa-regular fa-copy"},null,-1)}))],F={key:0,class:"text-primary mt-1 fw-semibold"},q={class:"p-2 mt-2"},R=a((function(){return(0,n.createElementVNode)("div",null,"Credential ID",-1)})),U={class:"border d-flex justify-content-between p-2 rounded align-items-center"},z={class:"p-2 fw-bold"},Z=[a((function(){return(0,n.createElementVNode)("i",{class:"fa-regular fa-copy"},null,-1)}))],W={key:0,class:"text-primary mt-1 fw-semibold"},K={class:"col-12 col-md-6 text-center mt-4 mt-md-0"},G=["src"],Y=["href"],J=a((function(){return(0,n.createElementVNode)("i",{class:"fa-solid fa-download"},null,-1)})),X=a((function(){return(0,n.createElementVNode)("div",{class:"modal-footer"},[(0,n.createElementVNode)("button",{type:"button",class:"btn btn-secondary","data-bs-toggle":"modal","data-bs-target":"#kt_modal_badge"}," View Badge ")],-1)}));var Q={class:"modal fade",id:"kt_modal_badge",tabindex:"-1",style:{display:"none"},"aria-hidden":"true"},ee={class:"modal-dialog modal-dialog-centered modal-xl"},te={class:"modal-content rounded-0"},oe=(0,n.createElementVNode)("div",{class:"modal-header text-white"},[(0,n.createElementVNode)("h5",{class:"modal-title"},"View Badge"),(0,n.createElementVNode)("button",{type:"button",class:"btn-close","data-bs-dismiss":"modal","aria-label":"Close"})],-1),ne={class:"modal-body text-center px-10"},ae={class:"row gap-4 fs-5"},re={class:"col-7 px-7 py-9 text-start border border-solid rounded"},ie={class:"fw-bold mb-5 mt-5"},le={key:0},se={class:"mt-7 lh-lg"},ce={class:"mb-1"},de=(0,n.createElementVNode)("span",{class:"text-gray-700"},"Module Name: ",-1),ue={class:"mb-1"},me=(0,n.createElementVNode)("span",{class:"text-gray-700"},"Credential ID: ",-1),pe={class:"mb-1"},ge=(0,n.createElementVNode)("span",{class:"text-gray-700"},"Issue Date: ",-1),he={key:0,class:"mb-1"},fe=(0,n.createElementVNode)("span",{class:"text-gray-700"},"Expiry Date: ",-1),ve={class:"mb-1"},be=(0,n.createElementVNode)("span",{class:"text-gray-700"},"Module Type: ",-1),ye={class:"col my-auto"},we={key:0},ke=["innerHTML"],Ee=["src"],xe=(0,n.createElementVNode)("div",{class:"modal-footer border-0"},[(0,n.createElementVNode)("button",{type:"button",class:"btn btn-secondary","data-bs-toggle":"modal","data-bs-target":"#kt_modal_share_badge"}," Share Badge ")],-1);const Ce=(0,n.defineComponent)({props:{selectedBadge:Object},methods:{isVideo:function(e){return e&&e.endsWith(".mp4")}}});var Ve=o(93379),Be=o.n(Ve),Ne=o(3368),_e={insert:"head",singleton:!1};Be()(Ne.Z,_e);Ne.Z.locals;var Ae=o(83744);const Te=(0,Ae.Z)(Ce,[["render",function(e,t,o,a,r,i){var l,s,c,d,u,m,p,g,h,f,v,b,y,w,k,E,x;return(0,n.openBlock)(),(0,n.createElementBlock)("div",Q,[(0,n.createElementVNode)("div",ee,[(0,n.createElementVNode)("div",te,[oe,(0,n.createElementVNode)("div",ne,[(0,n.createElementVNode)("div",ae,[(0,n.createElementVNode)("div",re,[(0,n.createElementVNode)("div",null,[(0,n.createElementVNode)("h1",null,(0,n.toDisplayString)(null===(s=null===(l=e.selectedBadge)||void 0===l?void 0:l.badge)||void 0===s?void 0:s.name),1),(0,n.createElementVNode)("p",ie,[(0,n.createTextVNode)(" Verified by "),((0,n.openBlock)(!0),(0,n.createElementBlock)(n.Fragment,null,(0,n.renderList)(null===(d=null===(c=e.selectedBadge)||void 0===c?void 0:c.badge)||void 0===d?void 0:d.companies,(function(t,o){var a,r;return(0,n.openBlock)(),(0,n.createElementBlock)("span",{key:t.id},[(0,n.createElementVNode)("u",null,(0,n.toDisplayString)(t.name),1),o!==(null===(r=null===(a=e.selectedBadge)||void 0===a?void 0:a.badge)||void 0===r?void 0:r.companies.length)-1?((0,n.openBlock)(),(0,n.createElementBlock)("span",le," + ")):(0,n.createCommentVNode)("",!0)])})),128))])]),(0,n.createElementVNode)("div",se,[(0,n.createElementVNode)("p",ce,[de,(0,n.createTextVNode)((0,n.toDisplayString)(null===(u=e.selectedBadge)||void 0===u?void 0:u.module_name),1)]),(0,n.createElementVNode)("p",ue,[me,(0,n.createTextVNode)(" "+(0,n.toDisplayString)((null===(m=e.selectedBadge)||void 0===m?void 0:m.credential_id)||"N/A"),1)]),(0,n.createElementVNode)("p",pe,[ge,(0,n.createTextVNode)(" "+(0,n.toDisplayString)(null===(p=e.selectedBadge)||void 0===p?void 0:p.issue_date),1)]),(null===(g=e.selectedBadge)||void 0===g?void 0:g.expiration_date)?((0,n.openBlock)(),(0,n.createElementBlock)("p",he,[fe,(0,n.createTextVNode)(" "+(0,n.toDisplayString)(e.selectedBadge.expiration_date),1)])):(0,n.createCommentVNode)("",!0),(0,n.createElementVNode)("p",ve,[be,(0,n.createTextVNode)((0,n.toDisplayString)(null===(h=e.selectedBadge)||void 0===h?void 0:h.module_type),1)])])]),(0,n.createElementVNode)("div",ye,[e.selectedBadge?((0,n.openBlock)(),(0,n.createElementBlock)("div",we,[(null===(v=null===(f=e.selectedBadge)||void 0===f?void 0:f.badge)||void 0===v?void 0:v.video)?((0,n.openBlock)(),(0,n.createElementBlock)("div",{key:0,class:"animated-video",innerHTML:null===(y=null===(b=e.selectedBadge)||void 0===b?void 0:b.badge)||void 0===y?void 0:y.video},null,8,ke)):((0,n.openBlock)(),(0,n.createElementBlock)("img",{key:1,src:(null===(k=null===(w=e.selectedBadge)||void 0===w?void 0:w.badge)||void 0===k?void 0:k.animated_image_fullpath)||(null===(x=null===(E=e.selectedBadge)||void 0===E?void 0:E.badge)||void 0===x?void 0:x.image_fullpath),alt:"Animated Badge",class:"w-100"},null,8,Ee))])):(0,n.createCommentVNode)("",!0)])])]),xe])])])}]]),Se=(0,n.defineComponent)({components:{ViewBadgeModal:Te},props:{selectedBadge:Object,moduleData:Object,moduleType:String},emits:["shareBadge"],setup:function(e,t){var o=t.emit,a=(0,n.ref)("");return{emitShare:function(){o("shareBadge",e.selectedBadge)},copiedField:a,copyToClipboard:function(e,t){e&&navigator.clipboard.writeText(e).then((function(){a.value=t,setTimeout((function(){a.value=""}),3e3)})).catch((function(e){console.error("Copy failed:",e)}))}}}});var Le=o(6857),Pe={insert:"head",singleton:!1};Be()(Le.Z,Pe);Le.Z.locals;const $e=(0,Ae.Z)(Se,[["render",function(e,t,o,a,Q,ee){var te,oe,ne,ae,re,ie,le,se,ce,de,ue,me,pe,ge,he,fe=(0,n.resolveComponent)("ViewBadgeModal");return(0,n.openBlock)(),(0,n.createElementBlock)(n.Fragment,null,[(0,n.createVNode)(fe,{selectedBadge:e.selectedBadge},null,8,["selectedBadge"]),(0,n.createElementVNode)("div",r,[(0,n.createElementVNode)("div",i,[(0,n.createElementVNode)("div",l,[s,(0,n.createElementVNode)("div",c,[d,(0,n.createElementVNode)("div",u,[(0,n.createElementVNode)("div",m,[(0,n.createElementVNode)("div",p,[g,(0,n.createElementVNode)("div",h,[f,(0,n.createElementVNode)("div",v,[(0,n.createElementVNode)("div",b,(0,n.toDisplayString)(null===(oe=null===(te=e.selectedBadge)||void 0===te?void 0:te.badge)||void 0===oe?void 0:oe.name),1),(0,n.createElementVNode)("button",{class:"btn btn-sm btn-outline-primary",onClick:t[0]||(t[0]=function(t){var o;return e.copyToClipboard(null===(o=e.selectedBadge)||void 0===o?void 0:o.name,"name")})},y)]),"name"===e.copiedField?((0,n.openBlock)(),(0,n.createElementBlock)("p",w,"Copied to clipboard!")):(0,n.createCommentVNode)("",!0)]),(0,n.createElementVNode)("div",k,[E,(0,n.createElementVNode)("div",x,[(0,n.createElementVNode)("div",C,[(null===(ae=null===(ne=e.selectedBadge)||void 0===ne?void 0:ne.badge)||void 0===ae?void 0:ae.companies.length)>0?((0,n.openBlock)(),(0,n.createElementBlock)("div",V,[((0,n.openBlock)(!0),(0,n.createElementBlock)(n.Fragment,null,(0,n.renderList)(null===(ie=null===(re=e.selectedBadge)||void 0===re?void 0:re.badge)||void 0===ie?void 0:ie.companies,(function(t,o){var a,r;return(0,n.openBlock)(),(0,n.createElementBlock)("span",{key:t.id},[(0,n.createTextVNode)((0,n.toDisplayString)(t.name)+" ",1),o!==(null===(r=null===(a=e.selectedBadge)||void 0===a?void 0:a.badge)||void 0===r?void 0:r.companies.length)-1?((0,n.openBlock)(),(0,n.createElementBlock)("span",B," + ")):(0,n.createCommentVNode)("",!0)])})),128))])):((0,n.openBlock)(),(0,n.createElementBlock)("div",N," N/A "))]),(0,n.createElementVNode)("button",{class:"btn btn-sm btn-outline-primary",onClick:t[1]||(t[1]=function(t){var o;return e.copyToClipboard(null===(o=e.selectedBadge)||void 0===o?void 0:o.name,"name")})},_)]),"name"===e.copiedField?((0,n.openBlock)(),(0,n.createElementBlock)("p",A,"Copied to clipboard!")):(0,n.createCommentVNode)("",!0)]),(0,n.createElementVNode)("div",T,[S,(0,n.createElementVNode)("div",L,[(0,n.createElementVNode)("div",P,(0,n.toDisplayString)(null===(le=e.selectedBadge)||void 0===le?void 0:le.issue_date),1),(0,n.createElementVNode)("button",{class:"btn btn-sm btn-outline-primary",onClick:t[2]||(t[2]=function(t){var o;return e.copyToClipboard(null===(o=e.selectedBadge)||void 0===o?void 0:o.issue_date,"issue_date")})},$)]),"issue_date"===e.copiedField?((0,n.openBlock)(),(0,n.createElementBlock)("p",M,"Copied to clipboard!")):(0,n.createCommentVNode)("",!0)]),(null===(se=e.selectedBadge)||void 0===se?void 0:se.expiration_date)?((0,n.openBlock)(),(0,n.createElementBlock)("div",O,[D,(0,n.createElementVNode)("div",j,[(0,n.createElementVNode)("div",H,(0,n.toDisplayString)(null===(ce=e.selectedBadge)||void 0===ce?void 0:ce.expiration_date),1),(0,n.createElementVNode)("button",{class:"btn btn-sm btn-outline-primary",onClick:t[3]||(t[3]=function(t){var o;return e.copyToClipboard(null===(o=e.selectedBadge)||void 0===o?void 0:o.expiration_date,"expiry_date")})},I)]),"expiry_date"===e.copiedField?((0,n.openBlock)(),(0,n.createElementBlock)("p",F,"Copied to clipboard!")):(0,n.createCommentVNode)("",!0)])):(0,n.createCommentVNode)("",!0),(0,n.createElementVNode)("div",q,[R,(0,n.createElementVNode)("div",U,[(0,n.createElementVNode)("div",z,(0,n.toDisplayString)((null===(de=e.selectedBadge)||void 0===de?void 0:de.credential_id)||"N/A"),1),(0,n.createElementVNode)("button",{class:"btn btn-sm btn-outline-primary",onClick:t[4]||(t[4]=function(t){var o;return e.copyToClipboard((null===(o=e.selectedBadge)||void 0===o?void 0:o.credential_id)||"N/A","credential_id")})},Z)]),"credential_id"===e.copiedField?((0,n.openBlock)(),(0,n.createElementBlock)("p",W,"Copied to clipboard!")):(0,n.createCommentVNode)("",!0)])]),(0,n.createElementVNode)("div",K,[(0,n.createElementVNode)("div",null,[(0,n.createElementVNode)("img",{src:null===(me=null===(ue=e.selectedBadge)||void 0===ue?void 0:ue.badge)||void 0===me?void 0:me.image_fullpath,class:"img-fluid rounded",style:{"max-width":"100%",height:"auto"}},null,8,G)]),(null===(ge=null===(pe=e.selectedBadge)||void 0===pe?void 0:pe.badge)||void 0===ge?void 0:ge.id)?((0,n.openBlock)(),(0,n.createElementBlock)("a",{key:0,href:"/badges/".concat(null===(he=e.selectedBadge.badge)||void 0===he?void 0:he.id,"/download"),class:"btn btn-sm btn-outline-primary mt-3",download:""},[J,(0,n.createTextVNode)(" Download Image ")],8,Y)):(0,n.createCommentVNode)("",!0)])])])]),X])])])],64)}],["__scopeId","data-v-42f43f73"]])},42185:(e,t,o)=>{"use strict";o.r(t),o.d(t,{default:()=>Wt});var n=o(70821),a=["innerHTML"],r=(0,n.createElementVNode)("div",{style:{position:"absolute",width:"100%",height:"100%",opacity:"0.3",background:"#000"}},null,-1),i={class:"banner_detail_box w-450px"},l={key:0,class:"mt-4 mb-4"},s={class:"row g-3"},c={class:"col-6"},d={class:"d-flex align-items-center mb-10"},u=["src","alt"],m={class:"mb-1 fw-bold text-light"},p=(0,n.createElementVNode)("h1",{class:"fw-normal text-light"},"Virtual Work Experience",-1),g=["innerHTML"],h={class:"row text-light align-items-center"},f={key:0,class:"col-md-4 col-lg-3"},v=(0,n.createElementVNode)("i",{class:"fa-regular fa-clock text-white me-2"},null,-1),b=["textContent"],y=["textContent"],w={key:1,class:"col-md-4 col-lg-3"},k=(0,n.createElementVNode)("i",{class:"fa fa-chart-simple text-white me-2"},null,-1),E=["textContent"],x={class:"col-md-5 col-lg-5 mt-lg-0 mt-md-3"},C={key:0},V=[(0,n.createElementVNode)("span",{class:"text-dark px-5 py-2 rounded-pill w-auto d-inline-block",style:{"background-color":"#CDD6DD"}}," Submitted For Review ",-1)],B={key:1,class:"fs-6 text-light px-5 py-2 rounded-pill w-100",style:{"background-color":"#0062ff"}},N={key:2,class:"fs-6 text-dark px-5 py-2 rounded-pill",style:{"background-color":"#e9ff1f"}},_={class:"row mt-5"},A=(0,n.createElementVNode)("i",{class:"fa fa-check text-white"},null,-1),T={key:1,class:"row mt-5"},S={class:"col-8 col-sm-6 col-md-10"},L=(0,n.createElementVNode)("img",{src:"media/icons/play-circle-white.svg",alt:"play",class:"white-icon"},null,-1),P=(0,n.createElementVNode)("img",{src:"media/icons/play-circle-black.svg",alt:"play",class:"black-icon",style:{display:"none"}},null,-1),$={key:2,class:"row mt-5"},M={class:"col-8 col-sm-6 col-md-10"},O={key:0},D={key:1},j={key:3,class:"row mt-5"},H={class:"col-8 col-sm-6 col-md-10"},I={key:0,class:"col-sm-6 col-md-2 text-center my-auto"},F={key:0},q=[(0,n.createElementVNode)("p",{class:"cursor-pointer fs-5 text-light d-flex gap-1 my-auto","data-bs-toggle":"modal","data-bs-target":"#kt_modal_reset_responses"},[(0,n.createElementVNode)("i",{class:"fa-solid fa-rotate-right fs-5 text-light my-auto"}),(0,n.createTextVNode)(" Reset ")],-1)],R={key:4,class:"row my-5"},U={class:"col-8 col-sm-6 col-md-10 text-center"},z={class:"row row-cols-3"},Z={key:0,class:"col my-auto"},W={class:"row g-3 mt-2"},K={class:"col-12"},G=["src","alt"],Y=(0,n.createElementVNode)("div",{class:"overflow-hidden"},[(0,n.createElementVNode)("p",{class:"fw-bold text-light my-auto"}," View Badge ")],-1),J={key:1,class:"col my-auto"},X={class:"row g-3 mt-2"},Q={class:"col-12"},ee=[(0,n.createElementVNode)("i",{class:"fa-solid fa-file text-light me-2",width:"25"},null,-1),(0,n.createElementVNode)("div",null,[(0,n.createElementVNode)("p",{class:"fw-bold text-light my-auto"}," View Certificate ")],-1)],te={key:2,class:"col my-auto"},oe=[(0,n.createStaticVNode)('<div class="row g-3 mt-2"><div class="col-12"><div class="d-flex align-items-center cursor-pointer" data-bs-toggle="modal" data-bs-target="#kt_modal_feedback"><i class="fa-solid fa-comments text-light me-2" width="25"></i><div><p class="fw-bold text-light my-auto"> View Feedback </p></div></div></div></div>',1)],ne={class:"sticky-bottom"},ae={key:0,class:"row"},re={class:"col-12 position-relative"},ie={class:"bg-dark m-0 position-absolute w-300px bottom-0 end-0 pointer text-center"},le={class:"m-0 d-flex related-tile-content text-white"},se=["textContent"],ce={class:"float-end text-white"},de={class:"row black-strip bg-black"},ue={class:"col-8 p-10"},me=(0,n.createElementVNode)("span",{class:"svg-icon svg-icon-primary svg-icon-2x"},[(0,n.createElementVNode)("svg",{xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",width:"24px",height:"24px",viewBox:"0 0 24 24",version:"1.1"},[(0,n.createElementVNode)("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},[(0,n.createElementVNode)("polygon",{points:"0 0 24 0 24 24 0 24"}),(0,n.createElementVNode)("path",{d:"M6.70710678,15.7071068 C6.31658249,16.0976311 5.68341751,16.0976311 5.29289322,15.7071068 C4.90236893,15.3165825 4.90236893,14.6834175 5.29289322,14.2928932 L11.2928932,8.29289322 C11.6714722,7.91431428 12.2810586,7.90106866 12.6757246,8.26284586 L18.6757246,13.7628459 C19.0828436,14.1360383 19.1103465,14.7686056 18.7371541,15.1757246 C18.3639617,15.5828436 17.7313944,15.6103465 17.3242754,15.2371541 L12.0300757,10.3841378 L6.70710678,15.7071068 Z",fill:"#ffffff","fill-rule":"nonzero",transform:"translate(12.000003, 11.999999) scale(-1, 1) rotate(-270.000000) translate(-12.000003, -11.999999) "})])])],-1),pe={class:"col-4 text-right p-10"},ge={key:0,class:"fa-solid fa-heart text-white fs-1"},he={key:1,class:"fa-regular fa-heart text-white fs-1"},fe=[(0,n.createElementVNode)("i",{class:"fa-solid fa-headphones text-white fs-1",title:"Audio Instructions"},null,-1)],ve={key:1,class:"cursor-pointer svg-icon svg-icon-primary svg-icon-2x float-end me-5","data-bs-toggle":"modal","data-bs-target":"#kt_modal_worksheet"},be=[(0,n.createElementVNode)("i",{class:"bi bi-file-earmark text-white fs-1","data-bs-toggle":"tooltip",title:"Worksheets"},null,-1)],ye={key:2,class:"cursor-pointer svg-icon svg-icon-primary svg-icon-2x float-end me-5","data-bs-toggle":"modal","data-bs-target":"#kt_modal_teacherResources"},we=[(0,n.createElementVNode)("i",{class:"bi bi-box2 text-white fs-1",title:"Teacher Resources"},null,-1)],ke={key:3,class:"cursor-pointer svg-icon svg-icon-primary svg-icon-2x float-end me-5","data-bs-toggle":"modal","data-bs-target":"#kt_modal_curriculum"},Ee=[(0,n.createElementVNode)("i",{class:"bi bi-list-ul text-white fs-1",title:"Curriculum"},null,-1)],xe=(0,n.createElementVNode)("span",{class:"svg-icon svg-icon-primary svg-icon-2x dropdown float-end"},null,-1),Ce={class:"col-12 text-center"},Ve={controls:"",controlsList:"nodownload"},Be=["src"],Ne={class:"modal-dialog modal-dialog-centered mw-900px"},_e={class:"modal-content rounded-0"},Ae=["innerHTML"],Te={class:"modal fade",id:"kt_modal_reset_responses",tabindex:"-1",style:{display:"none"},"aria-hidden":"true"},Se={class:"modal-dialog modal-dialog-centered modal-md"},Le={class:"modal-content rounded-0"},Pe={class:"modal-body"},$e=(0,n.createElementVNode)("p",null," Do you really want to reset your response? Doing this will clear your answers and also any feedback that has been provided. ",-1),Me=(0,n.createElementVNode)("button",{type:"button",class:"btn btn-sm btn-primary rounded-0 m-5","data-bs-dismiss":"modal"}," No ",-1),Oe={key:0,class:"modal fade",id:"kt_modal_worksheet",tabindex:"-1",style:{display:"none"},"aria-hidden":"true"},De={class:"modal-dialog modal-dialog-centered modal-xl"},je={class:"modal-content rounded-0"},He={class:"modal-body"},Ie=(0,n.createElementVNode)("h3",null,"Worksheets",-1),Fe={class:"list-inline profile-cards new-cards"},qe=["href"],Re={class:"percentage"},Ue=["src","alt"],ze=(0,n.createElementVNode)("i",{class:"fa fa-download bg-blue blue-check text-white"},null,-1),Ze={class:"topic text-master"},We=(0,n.createElementVNode)("button",{type:"button",class:"btn btn-sm btn-primary rounded-0 m-5 float-end","data-bs-dismiss":"modal"}," Close ",-1),Ke={key:1,class:"modal fade",id:"kt_modal_teacherResources",tabindex:"-1",style:{display:"none"},"aria-hidden":"true"},Ge={class:"modal-dialog modal-dialog-centered modal-xl"},Ye={class:"modal-content rounded-0"},Je={class:"modal-body"},Xe=(0,n.createElementVNode)("h3",null,"Teacher Resources",-1),Qe={class:"list-inline profile-cards new-cards"},et=["href"],tt={class:"percentage"},ot=["src","alt"],nt=(0,n.createElementVNode)("i",{class:"fa fa-download bg-blue blue-check text-white"},null,-1),at={class:"topic text-master"},rt=(0,n.createElementVNode)("button",{type:"button",class:"btn btn-sm btn-primary rounded-0 m-5 float-end","data-bs-dismiss":"modal"}," Close ",-1),it={key:2,class:"modal fade",id:"kt_modal_curriculum",tabindex:"-1",style:{display:"none"},"aria-hidden":"true"},lt={class:"modal-dialog modal-dialog-centered modal-xl"},st={class:"modal-content rounded-0"},ct={class:"modal-body"},dt=(0,n.createElementVNode)("h3",null,"Curriculum",-1),ut=["innerHTML"],mt=(0,n.createElementVNode)("button",{type:"button",class:"btn btn-sm btn-primary rounded-0 m-5 float-end","data-bs-dismiss":"modal"}," Close ",-1),pt={class:"modal fade",id:"kt_modal_viewFile",tabindex:"-1","aria-hidden":"true"},gt={class:"modal-content rounded-0 mt-5"},ht={class:"modal-header py-3"},ft=(0,n.createElementVNode)("h5",{class:"modal-title"},"Certificate Preview",-1),vt={key:0,class:"fa-solid fa-compress text-black"},bt={key:1,class:"fa-solid fa-expand text-black"},yt=["href"],wt=[(0,n.createElementVNode)("i",{class:"fa-solid fa-download text-black"},null,-1)],kt=(0,n.createElementVNode)("button",{type:"button",class:"btn-close","data-bs-dismiss":"modal"},null,-1),Et={class:"modal-body bg-black p-1 text-white text-center"},xt=["src"],Ct={key:1},Vt={class:"modal fade",id:"kt_modal_feedback",tabindex:"-1",style:{display:"none"},"aria-hidden":"true"},Bt={class:"modal-dialog modal-dialog-centered mw-600px"},Nt={class:"modal-content rounded-0",style:{height:"80vh"}},_t=(0,n.createElementVNode)("div",{class:"modal-header text-white"},[(0,n.createElementVNode)("h5",{class:"modal-title"},"Feedback"),(0,n.createElementVNode)("button",{type:"button",class:"btn-close","data-bs-dismiss":"modal","aria-label":"Close"})],-1),At={class:"modal-body p-4 bg-gray-50 text-left"},Tt={class:"p-4 bg-white",style:{height:"90%"}},St=["innerHTML"];var Lt=o(70655),Pt=o(45535),$t=o(72961),Mt=o(80894),Ot=o(22201),Dt=o(48542),jt=o.n(Dt),Ht=o(46919);function It(e){return It="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},It(e)}function Ft(){Ft=function(){return e};var e={},t=Object.prototype,o=t.hasOwnProperty,n=Object.defineProperty||function(e,t,o){e[t]=o.value},a="function"==typeof Symbol?Symbol:{},r=a.iterator||"@@iterator",i=a.asyncIterator||"@@asyncIterator",l=a.toStringTag||"@@toStringTag";function s(e,t,o){return Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(e){s=function(e,t,o){return e[t]=o}}function c(e,t,o,a){var r=t&&t.prototype instanceof m?t:m,i=Object.create(r.prototype),l=new V(a||[]);return n(i,"_invoke",{value:k(e,o,l)}),i}function d(e,t,o){try{return{type:"normal",arg:e.call(t,o)}}catch(e){return{type:"throw",arg:e}}}e.wrap=c;var u={};function m(){}function p(){}function g(){}var h={};s(h,r,(function(){return this}));var f=Object.getPrototypeOf,v=f&&f(f(B([])));v&&v!==t&&o.call(v,r)&&(h=v);var b=g.prototype=m.prototype=Object.create(h);function y(e){["next","throw","return"].forEach((function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function w(e,t){function a(n,r,i,l){var s=d(e[n],e,r);if("throw"!==s.type){var c=s.arg,u=c.value;return u&&"object"==It(u)&&o.call(u,"__await")?t.resolve(u.__await).then((function(e){a("next",e,i,l)}),(function(e){a("throw",e,i,l)})):t.resolve(u).then((function(e){c.value=e,i(c)}),(function(e){return a("throw",e,i,l)}))}l(s.arg)}var r;n(this,"_invoke",{value:function(e,o){function n(){return new t((function(t,n){a(e,o,t,n)}))}return r=r?r.then(n,n):n()}})}function k(e,t,o){var n="suspendedStart";return function(a,r){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===a)throw r;return N()}for(o.method=a,o.arg=r;;){var i=o.delegate;if(i){var l=E(i,o);if(l){if(l===u)continue;return l}}if("next"===o.method)o.sent=o._sent=o.arg;else if("throw"===o.method){if("suspendedStart"===n)throw n="completed",o.arg;o.dispatchException(o.arg)}else"return"===o.method&&o.abrupt("return",o.arg);n="executing";var s=d(e,t,o);if("normal"===s.type){if(n=o.done?"completed":"suspendedYield",s.arg===u)continue;return{value:s.arg,done:o.done}}"throw"===s.type&&(n="completed",o.method="throw",o.arg=s.arg)}}}function E(e,t){var o=t.method,n=e.iterator[o];if(void 0===n)return t.delegate=null,"throw"===o&&e.iterator.return&&(t.method="return",t.arg=void 0,E(e,t),"throw"===t.method)||"return"!==o&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+o+"' method")),u;var a=d(n,e.iterator,t.arg);if("throw"===a.type)return t.method="throw",t.arg=a.arg,t.delegate=null,u;var r=a.arg;return r?r.done?(t[e.resultName]=r.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,u):r:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,u)}function x(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function C(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function V(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(x,this),this.reset(!0)}function B(e){if(e){var t=e[r];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,a=function t(){for(;++n<e.length;)if(o.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=void 0,t.done=!0,t};return a.next=a}}return{next:N}}function N(){return{value:void 0,done:!0}}return p.prototype=g,n(b,"constructor",{value:g,configurable:!0}),n(g,"constructor",{value:p,configurable:!0}),p.displayName=s(g,l,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===p||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,g):(e.__proto__=g,s(e,l,"GeneratorFunction")),e.prototype=Object.create(b),e},e.awrap=function(e){return{__await:e}},y(w.prototype),s(w.prototype,i,(function(){return this})),e.AsyncIterator=w,e.async=function(t,o,n,a,r){void 0===r&&(r=Promise);var i=new w(c(t,o,n,a),r);return e.isGeneratorFunction(o)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},y(b),s(b,l,"Generator"),s(b,r,(function(){return this})),s(b,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),o=[];for(var n in t)o.push(n);return o.reverse(),function e(){for(;o.length;){var n=o.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},e.values=B,V.prototype={constructor:V,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(C),!e)for(var t in this)"t"===t.charAt(0)&&o.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function n(o,n){return i.type="throw",i.arg=e,t.next=o,n&&(t.method="next",t.arg=void 0),!!n}for(var a=this.tryEntries.length-1;a>=0;--a){var r=this.tryEntries[a],i=r.completion;if("root"===r.tryLoc)return n("end");if(r.tryLoc<=this.prev){var l=o.call(r,"catchLoc"),s=o.call(r,"finallyLoc");if(l&&s){if(this.prev<r.catchLoc)return n(r.catchLoc,!0);if(this.prev<r.finallyLoc)return n(r.finallyLoc)}else if(l){if(this.prev<r.catchLoc)return n(r.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<r.finallyLoc)return n(r.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var a=this.tryEntries[n];if(a.tryLoc<=this.prev&&o.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var r=a;break}}r&&("break"===e||"continue"===e)&&r.tryLoc<=t&&t<=r.finallyLoc&&(r=null);var i=r?r.completion:{};return i.type=e,i.arg=t,r?(this.method="next",this.next=r.finallyLoc,u):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),u},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var o=this.tryEntries[t];if(o.finallyLoc===e)return this.complete(o.completion,o.afterLoc),C(o),u}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var o=this.tryEntries[t];if(o.tryLoc===e){var n=o.completion;if("throw"===n.type){var a=n.arg;C(o)}return a}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,o){return this.delegate={iterator:B(e),resultName:t,nextLoc:o},"next"===this.method&&(this.arg=void 0),u}},e}const qt=(0,n.defineComponent)({name:"vwe-training-detail",components:{BadgeModal:Ht.Z},setup:function(){var e=this,t=(0,Mt.oR)(),o=(0,Ot.yj)(),a=t.getters.currentUser;(0,n.onMounted)((function(){m()}));var r=(0,n.ref)(),i=(0,n.ref)(),l=(0,n.ref)(),s=(0,n.ref)(),c=(0,n.ref)(),d=(0,n.ref)(),u=(0,n.ref)({});r.value={id:1,background_imagepath:null,background_videoid:null,firststepresponse:{id:0},worksheets:{},teacher_resources:{},relatedModules:{},audio:[],user_response:{id:"",step_responses:{},badge_key:{}}},i.value=1,l.value=o.params.id;var m=function(){$t.Z.get("api/vwe",l.value).then((function(e){var o=e.data;if(o.steps.length)for(var n=0;n<o.steps.length;n++)if(!o.steps[n].user_response||n==o.steps.length-1){i.value=n+1;break}r.value=o;var a=t.getters.getBreadcrumbs;a[2]=o.title,t.commit(Pt.P.SET_BREADCRUMB_MUTATION,a)})).catch((function(e){!function(e){if(null==e)throw new TypeError("Cannot destructure "+e)}(e)}))},p=(0,n.ref)(!1),g=(0,n.ref)(0),h=(0,n.ref)(!1),f=(0,n.ref)("");return{currentUser:a,vwe:r,toggleRelated:function(){d.value=!d.value},currentvwe:l,showRelatedModuleList:d,latestStep:i,favouriteVwe:function(e){s.value={id:e},$t.Z.post("api/vwe/"+e+"/fav",s.value).then((function(e){var t=e.data;r.value.favourite=t.favourite})).catch((function(e){e.response}))},resetVwe:function(e){c.value={id:e},$t.Z.post("api/vwe/"+e+"/reset",c.value).then((function(e){e.data;jt().fire({text:"This Virtul Work Experience and your previous responses have been reset.",icon:"success",buttonsStyling:!1,confirmButtonText:"Ok",customClass:{confirmButton:"btn fw-semobold btn-light-primary rounded-0"}}).then((function(){window.location.reload()}))})).catch((function(e){e.response}))},toggleAudio:function(){var e=document.getElementById("audio");p.value?(g.value=0,e.style.margin="0"):(e.classList.remove("d-none"),g.value=e.scrollHeight,e.style.margin="0px 0px 20px 0px"),p.value=!p.value},audioHeight:g,selectedBadge:u,openBadgeModal:function(e){u.value=e},openShareBadgeModal:function(e){u.value=e},toggleFullscreen:function(){h.value=!h.value},isFullscreen:h,certificateUrl:f,loadCertificate:function(){return(0,Lt.mG)(e,void 0,void 0,Ft().mark((function e(){var t;return Ft().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(console.log("Triggered",r),console.log("Triggered",r.value),r.value&&r.value.user_response){e.next=5;break}return console.error("vwe.user_response is missing!"),e.abrupt("return");case 5:console.log("Triggered ID:",r.value.user_response.id),t=r.value.user_response.id,f.value="/certificate-download/".concat(t,"?preview=true");case 8:case"end":return e.stop()}}),e)})))}}},methods:{onHideModal:function(){var e=document.querySelector("#kt_modal_trailer video");e&&e.pause()},onShowModal:function(){var e=document.querySelector("#kt_modal_trailer video");e&&e.play()}},props:["id"]});var Rt=o(93379),Ut=o.n(Rt),zt=o(25826),Zt={insert:"head",singleton:!1};Ut()(zt.Z,Zt);zt.Z.locals;const Wt=(0,o(83744).Z)(qt,[["render",function(e,t,o,Lt,Pt,$t){var Mt,Ot,Dt,jt,Ht,It,Ft,qt,Rt,Ut,zt,Zt,Wt,Kt,Gt=(0,n.resolveComponent)("router-link"),Yt=(0,n.resolveComponent)("BadgeModal");return(0,n.openBlock)(),(0,n.createElementBlock)(n.Fragment,null,[(0,n.createElementVNode)("div",{class:"full-view-banner banner",style:(0,n.normalizeStyle)({backgroundImage:"url("+e.vwe.background_imagepath+")"})},[e.vwe.background_videoid?((0,n.openBlock)(),(0,n.createElementBlock)("div",{key:0,class:"banner-video",innerHTML:e.vwe.background_videoid},null,8,a)):(0,n.createCommentVNode)("",!0),r,(0,n.createElementVNode)("div",i,[e.vwe.badge&&!e.vwe.feedback&&100!==e.vwe.compeletedpercent?((0,n.openBlock)(),(0,n.createElementBlock)("div",l,[(0,n.createElementVNode)("div",s,[(0,n.createElementVNode)("div",c,[(0,n.createElementVNode)("div",d,[(0,n.createElementVNode)("img",{src:e.vwe.badge.image_fullpath,alt:e.vwe.badge.name,class:"me-3",width:"25"},null,8,u),(0,n.createElementVNode)("div",null,[(0,n.createElementVNode)("p",m,(0,n.toDisplayString)(e.vwe.badge.name),1)])])])])])):(0,n.createCommentVNode)("",!0),p,(0,n.createElementVNode)("h1",{class:"display-4 fw-normal mb-4 text-light",innerHTML:e.vwe.title},null,8,g),(0,n.createElementVNode)("div",h,[e.vwe.estimated_time&&(e.vwe.estimated_time.hours||e.vwe.estimated_time.minutes)?((0,n.openBlock)(),(0,n.createElementBlock)("div",f,[v,e.vwe.estimated_time&&e.vwe.estimated_time.hours?((0,n.openBlock)(),(0,n.createElementBlock)("span",{key:0,textContent:(0,n.toDisplayString)(e.vwe.estimated_time.hours+"h ")},null,8,b)):(0,n.createCommentVNode)("",!0),e.vwe.estimated_time&&e.vwe.estimated_time.minutes?((0,n.openBlock)(),(0,n.createElementBlock)("span",{key:1,textContent:(0,n.toDisplayString)(e.vwe.estimated_time.minutes+"m")},null,8,y)):(0,n.createCommentVNode)("",!0)])):(0,n.createCommentVNode)("",!0),e.vwe.level?((0,n.openBlock)(),(0,n.createElementBlock)("div",w,[k,(0,n.createElementVNode)("span",{textContent:(0,n.toDisplayString)(e.vwe.level)},null,8,E)])):(0,n.createCommentVNode)("",!0),(0,n.createElementVNode)("div",x,[100===e.vwe.compeletedpercent&&1!==(null===(Ot=null===(Mt=e.vwe)||void 0===Mt?void 0:Mt.user_response)||void 0===Ot?void 0:Ot.approve)?((0,n.openBlock)(),(0,n.createElementBlock)("span",C,V)):(0,n.createCommentVNode)("",!0),100===e.vwe.compeletedpercent&&1==(null===(jt=null===(Dt=e.vwe)||void 0===Dt?void 0:Dt.user_response)||void 0===jt?void 0:jt.approve)?((0,n.openBlock)(),(0,n.createElementBlock)("span",B," Completed ")):e.vwe.compeletedpercent>0&&e.vwe.compeletedpercent<100?((0,n.openBlock)(),(0,n.createElementBlock)("span",N,(0,n.toDisplayString)(e.vwe.compeletedpercent)+"% Completed ",1)):(0,n.createCommentVNode)("",!0)])]),(0,n.createElementVNode)("div",_,[((0,n.openBlock)(!0),(0,n.createElementBlock)(n.Fragment,null,(0,n.renderList)(e.vwe.tagged,(function(e){return(0,n.openBlock)(),(0,n.createElementBlock)("div",{class:"col-sm-6 fs-6 text-light p-2",key:e.id},[A,(0,n.createTextVNode)(" "+(0,n.toDisplayString)(e.tag_name),1)])})),128))]),e.vwe.foreground_videoid&&0===e.vwe.compeletedpercent?((0,n.openBlock)(),(0,n.createElementBlock)("div",T,[(0,n.createElementVNode)("div",S,[(0,n.createElementVNode)("button",{type:"button",class:"btn btn-black-custom btn-lg rounded-0 w-100","data-bs-toggle":"modal","data-bs-target":"#kt_modal_trailer",onClick:t[0]||(t[0]=function(){return e.onShowModal&&e.onShowModal.apply(e,arguments)})},[(0,n.createTextVNode)(" Watch Trailer "),L,P])])])):(0,n.createCommentVNode)("",!0),e.vwe.user_response&&"Draft"!=e.vwe.user_response.status?(0,n.createCommentVNode)("",!0):((0,n.openBlock)(),(0,n.createElementBlock)("div",$,[(0,n.createElementVNode)("div",M,[(0,n.createVNode)(Gt,{class:"btn btn-white-custom text-black btn-lg border-1 rounded-0 w-100",to:{name:"task-vwe-section-detail",params:{id:e.currentvwe,sectionid:e.latestStep}}},{default:(0,n.withCtx)((function(){return[!e.vwe.hasresponse&&e.vwe.compeletedpercent<100?((0,n.openBlock)(),(0,n.createElementBlock)("span",O,"Get Started")):(0,n.createCommentVNode)("",!0),e.vwe.hasresponse?((0,n.openBlock)(),(0,n.createElementBlock)("span",D,"Continue")):(0,n.createCommentVNode)("",!0)]})),_:1},8,["to"])])])),e.vwe.user_response&&"Submitted"==e.vwe.user_response.status?((0,n.openBlock)(),(0,n.createElementBlock)("div",j,[(0,n.createElementVNode)("div",H,[(0,n.createVNode)(Gt,{class:"btn btn-white-custom btn-lg border-1 rounded-0 w-100",to:{name:"task-vwe-view-response",params:{id:e.currentvwe}}},{default:(0,n.withCtx)((function(){return[(0,n.createTextVNode)(" View Response ")]})),_:1},8,["to"])]),e.vwe.hasresponse?((0,n.openBlock)(),(0,n.createElementBlock)("div",I,[e.vwe.compeletedpercent>=100?((0,n.openBlock)(),(0,n.createElementBlock)("div",F,q)):(0,n.createCommentVNode)("",!0)])):(0,n.createCommentVNode)("",!0)])):(0,n.createCommentVNode)("",!0),e.vwe.hasresponse?((0,n.openBlock)(),(0,n.createElementBlock)("div",R,[(0,n.createElementVNode)("div",U,[(0,n.createVNode)(Gt,{style:{"font-size":"12px !important"},class:"p-5 text-light fs-11px",to:{name:"task-vwe-section-detail",params:{id:e.currentvwe,sectionid:1}}},{default:(0,n.withCtx)((function(){return[(0,n.createTextVNode)(" Edit Response ")]})),_:1},8,["to"])])])):(0,n.createCommentVNode)("",!0),(0,n.createElementVNode)("div",z,[e.vwe.badge&&1===(null===(It=null===(Ht=e.vwe)||void 0===Ht?void 0:Ht.user_response)||void 0===It?void 0:It.approve)&&100===e.vwe.compeletedpercent?((0,n.openBlock)(),(0,n.createElementBlock)("div",Z,[(0,n.createElementVNode)("div",W,[(0,n.createElementVNode)("div",K,[(0,n.createElementVNode)("div",{class:"d-flex align-items-center cursor-pointer","data-bs-toggle":"modal","data-bs-target":"#kt_modal_badge",onClick:t[1]||(t[1]=function(t){return e.openBadgeModal(e.vwe.user_response.badge_key)})},[(0,n.createElementVNode)("img",{src:e.vwe.badge.image_fullpath,alt:e.vwe.badge.name,class:"me-3",width:"25"},null,8,G),Y])])])])):(0,n.createCommentVNode)("",!0),1===(null===(qt=null===(Ft=e.vwe)||void 0===Ft?void 0:Ft.user_response)||void 0===qt?void 0:qt.approve)?((0,n.openBlock)(),(0,n.createElementBlock)("div",J,[(0,n.createElementVNode)("div",X,[(0,n.createElementVNode)("div",Q,[(0,n.createElementVNode)("div",{class:"d-flex align-items-center cursor-pointer","data-bs-toggle":"modal","data-bs-target":"#kt_modal_viewFile",onClick:t[2]||(t[2]=function(){return e.loadCertificate&&e.loadCertificate.apply(e,arguments)})},ee)])])])):(0,n.createCommentVNode)("",!0),e.vwe.user_response&&e.vwe.user_response.feedback&&1===e.vwe.user_response.approve?((0,n.openBlock)(),(0,n.createElementBlock)("div",te,oe)):(0,n.createCommentVNode)("",!0)])])],4),(0,n.createElementVNode)("div",ne,[e.vwe.relatedModules.length?((0,n.openBlock)(),(0,n.createElementBlock)("div",ae,[(0,n.createElementVNode)("div",re,[(0,n.createElementVNode)("div",ie,[(0,n.createElementVNode)("div",{class:"text-white p-4 pointer",onClick:t[3]||(t[3]=function(){return e.toggleRelated&&e.toggleRelated.apply(e,arguments)})},[(0,n.createTextVNode)(" Related Modules "),(0,n.createElementVNode)("i",{class:(0,n.normalizeClass)(["fa text-white ms-2",e.showRelatedModuleList?"fa-angle-down":"fa-angle-up"])},null,2)]),(0,n.createElementVNode)("div",{class:(0,n.normalizeClass)(["related-overlay",{"slide-up":e.showRelatedModuleList}])},[e.showRelatedModuleList?((0,n.openBlock)(!0),(0,n.createElementBlock)(n.Fragment,{key:0},(0,n.renderList)(e.vwe.relatedModules,(function(e){return(0,n.openBlock)(),(0,n.createElementBlock)("div",{key:e.id,class:"related-card pb-5 px-10"},[(0,n.createVNode)(Gt,{class:"d-block",to:{name:"task-skillstraining-detail",params:{id:e.id}}},{default:(0,n.withCtx)((function(){return[(0,n.createElementVNode)("div",{class:"mb-3",style:(0,n.normalizeStyle)([{height:"235px","background-color":"white","background-size":"100%"},{backgroundImage:"url("+e.tileimage_fullpath+")"}])},null,4)]})),_:2},1032,["to"]),(0,n.createElementVNode)("div",le,[(0,n.createElementVNode)("p",{class:"float-start fs-7 wrap",textContent:(0,n.toDisplayString)(e.title)},null,8,se),(0,n.createElementVNode)("p",ce,(0,n.toDisplayString)(e.compeletedpercent)+"% ",1)])])})),128)):(0,n.createCommentVNode)("",!0)],2)])])])):(0,n.createCommentVNode)("",!0),(0,n.createElementVNode)("div",de,[(0,n.createElementVNode)("div",ue,[(0,n.createVNode)(Gt,{class:"fs-4 m-0 text-white",to:{name:"tasks-vwe-list"}},{default:(0,n.withCtx)((function(){return[me,(0,n.createTextVNode)(" Back to Virtual Work Experience ")]})),_:1})]),(0,n.createElementVNode)("div",pe,[(0,n.createElementVNode)("span",{class:"cursor-pointer svg-icon svg-icon-primary svg-icon-2x float-end",onClick:t[4]||(t[4]=function(t){return e.favouriteVwe(e.vwe.id)})},[e.vwe.favourite?((0,n.openBlock)(),(0,n.createElementBlock)("i",ge)):(0,n.createCommentVNode)("",!0),e.vwe.favourite?(0,n.createCommentVNode)("",!0):((0,n.openBlock)(),(0,n.createElementBlock)("i",he))]),e.vwe.audio?((0,n.openBlock)(),(0,n.createElementBlock)("span",{key:0,onClick:t[5]||(t[5]=function(){return e.toggleAudio&&e.toggleAudio.apply(e,arguments)}),class:"cursor-pointer svg-icon svg-icon-primary svg-icon-2x float-end me-5"},fe)):(0,n.createCommentVNode)("",!0),e.vwe.worksheets.length?((0,n.openBlock)(),(0,n.createElementBlock)("span",ve,be)):(0,n.createCommentVNode)("",!0),e.currentUser.isTeacher&&e.vwe.teacher_resources.length?((0,n.openBlock)(),(0,n.createElementBlock)("span",ye,we)):(0,n.createCommentVNode)("",!0),e.currentUser.isTeacher&&e.vwe.curriculum?((0,n.openBlock)(),(0,n.createElementBlock)("span",ke,Ee)):(0,n.createCommentVNode)("",!0),xe]),(0,n.createElementVNode)("div",{class:"row",id:"audio",style:(0,n.normalizeStyle)({height:e.audioHeight+"px"})},[(0,n.createElementVNode)("div",Ce,[(0,n.createElementVNode)("audio",Ve,[(0,n.createElementVNode)("source",{src:e.vwe.audiofullpath,type:"audio/mpeg"},null,8,Be),(0,n.createTextVNode)(" Your browser does not support the audio element. ")])])],4)])]),(0,n.createElementVNode)("div",{class:"modal fade",id:"kt_modal_trailer",tabindex:"-1",style:{display:"none"},"aria-hidden":"true",onClick:t[6]||(t[6]=function(){return e.onHideModal&&e.onHideModal.apply(e,arguments)})},[(0,n.createElementVNode)("div",Ne,[(0,n.createElementVNode)("div",_e,[(0,n.createElementVNode)("div",{class:"modal-body p-0",innerHTML:e.vwe.foreground_videoid},null,8,Ae)])])]),(0,n.createElementVNode)("div",Te,[(0,n.createElementVNode)("div",Se,[(0,n.createElementVNode)("div",Le,[(0,n.createElementVNode)("div",Pe,[$e,(0,n.createElementVNode)("button",{type:"button",class:"btn btn-primary btn-sm rounded-0","data-bs-dismiss":"modal",onClick:t[7]||(t[7]=function(t){return e.resetVwe(e.vwe.id)})}," Yes "),Me])])])]),e.vwe.worksheets.length?((0,n.openBlock)(),(0,n.createElementBlock)("div",Oe,[(0,n.createElementVNode)("div",De,[(0,n.createElementVNode)("div",je,[(0,n.createElementVNode)("div",He,[Ie,(0,n.createElementVNode)("ul",Fe,[((0,n.openBlock)(!0),(0,n.createElementBlock)(n.Fragment,null,(0,n.renderList)(e.vwe.worksheets,(function(e){return(0,n.openBlock)(),(0,n.createElementBlock)("li",{class:"text-center hover-colored",key:e.id},[(0,n.createElementVNode)("a",{href:"/teacherresources/"+e.id+"/download"},[(0,n.createElementVNode)("div",Re,[(0,n.createElementVNode)("img",{src:e.imagefullpath,alt:e.title,class:"img-fluid"},null,8,Ue),ze]),(0,n.createElementVNode)("div",Ze,(0,n.toDisplayString)(e.title),1)],8,qe)])})),128))]),We])])])])):(0,n.createCommentVNode)("",!0),e.currentUser.isTeacher&&e.vwe.teacher_resources.length?((0,n.openBlock)(),(0,n.createElementBlock)("div",Ke,[(0,n.createElementVNode)("div",Ge,[(0,n.createElementVNode)("div",Ye,[(0,n.createElementVNode)("div",Je,[Xe,(0,n.createElementVNode)("ul",Qe,[((0,n.openBlock)(!0),(0,n.createElementBlock)(n.Fragment,null,(0,n.renderList)(e.vwe.teacher_resources,(function(e){return(0,n.openBlock)(),(0,n.createElementBlock)("li",{class:"text-center hover-colored",key:e.id},[(0,n.createElementVNode)("a",{href:"/teacherresources/"+e.id+"/download"},[(0,n.createElementVNode)("div",tt,[(0,n.createElementVNode)("img",{src:e.imagefullpath,alt:e.title,class:"img-fluid"},null,8,ot),nt]),(0,n.createElementVNode)("div",at,(0,n.toDisplayString)(e.title),1)],8,et)])})),128))]),rt])])])])):(0,n.createCommentVNode)("",!0),e.currentUser.isTeacher&&e.vwe.curriculum?((0,n.openBlock)(),(0,n.createElementBlock)("div",it,[(0,n.createElementVNode)("div",lt,[(0,n.createElementVNode)("div",st,[(0,n.createElementVNode)("div",ct,[dt,(0,n.createElementVNode)("div",{innerHTML:e.vwe.curriculum},null,8,ut),mt])])])])):(0,n.createCommentVNode)("",!0),(0,n.createVNode)(Yt,{selectedBadge:e.selectedBadge,onShareBadge:e.openShareBadgeModal},null,8,["selectedBadge","onShareBadge"]),(0,n.createElementVNode)("div",pt,[(0,n.createElementVNode)("div",{class:(0,n.normalizeClass)(["modal-dialog modal-dialog-centered",e.isFullscreen?"custom-fullscreen-modal":"mw-1200px"])},[(0,n.createElementVNode)("div",gt,[(0,n.createElementVNode)("div",ht,[ft,(0,n.createElementVNode)("div",null,[(0,n.createElementVNode)("span",{class:"mx-4 cursor-pointer",onClick:t[8]||(t[8]=function(){return e.toggleFullscreen&&e.toggleFullscreen.apply(e,arguments)})},[e.isFullscreen?((0,n.openBlock)(),(0,n.createElementBlock)("i",vt)):((0,n.openBlock)(),(0,n.createElementBlock)("i",bt))]),1!==(null===(Ut=null===(Rt=e.vwe)||void 0===Rt?void 0:Rt.user_response)||void 0===Ut?void 0:Ut.approve)?((0,n.openBlock)(),(0,n.createElementBlock)("a",{key:0,href:"/certificate-download/"+(null===(Zt=null===(zt=e.vwe)||void 0===zt?void 0:zt.user_response)||void 0===Zt?void 0:Zt.id),target:"_blank",class:"text-secondary mx-2"},wt,8,yt)):(0,n.createCommentVNode)("",!0),kt])]),(0,n.createElementVNode)("div",Et,[e.certificateUrl?((0,n.openBlock)(),(0,n.createElementBlock)("iframe",{key:0,src:e.certificateUrl,class:"w-100",style:(0,n.normalizeStyle)({height:e.isFullscreen?"90vh":"80vh",border:"none"}),allowfullscreen:""},null,12,xt)):((0,n.openBlock)(),(0,n.createElementBlock)("p",Ct,"Loading..."))])])],2)]),(0,n.createElementVNode)("div",Vt,[(0,n.createElementVNode)("div",Bt,[(0,n.createElementVNode)("div",Nt,[_t,(0,n.createElementVNode)("div",At,[(0,n.createElementVNode)("div",Tt,[(0,n.createElementVNode)("p",{innerHTML:null===(Kt=null===(Wt=e.vwe)||void 0===Wt?void 0:Wt.user_response)||void 0===Kt?void 0:Kt.feedback,class:"text-gray-700"},null,8,St)])])])])])],64)}]])}}]);