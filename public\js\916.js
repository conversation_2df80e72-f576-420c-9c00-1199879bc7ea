/*! For license information please see 916.js.LICENSE.txt */
(self.webpackChunk=self.webpackChunk||[]).push([[916],{46702:(e,t)=>{var n,o,r;!function(a){if("undefined"!=typeof window){var i,l=0,c=!1,d=!1,s="message".length,u="[iFrameSizer]",m=u.length,p=null,f=window.requestAnimationFrame,v=Object.freeze({max:1,scroll:1,bodyScroll:1,documentElementScroll:1}),h={},g=null,w=Object.freeze({autoResize:!0,bodyBackground:null,bodyMargin:null,bodyMarginV1:8,bodyPadding:null,checkOrigin:!0,inPageLinks:!1,enablePublicMethods:!0,heightCalculationMethod:"bodyOffset",id:"iFrameResizer",interval:32,log:!1,maxHeight:1/0,maxWidth:1/0,minHeight:0,minWidth:0,mouseEvents:!0,resizeFrom:"parent",scrolling:!1,sizeHeight:!0,sizeWidth:!1,warningTimeout:5e3,tolerance:0,widthCalculationMethod:"scroll",onClose:function(){return!0},onClosed:function(){},onInit:function(){},onMessage:function(){_("onMessage function not defined")},onMouseEnter:function(){},onMouseLeave:function(){},onResized:function(){},onScroll:function(){return!0}}),b={};window.jQuery!==a&&((i=window.jQuery).fn?i.fn.iFrameResize||(i.fn.iFrameResize=function(e){return this.filter("iframe").each((function(t,n){P(n,e)})).end()}):B("","Unable to bind to jQuery, it is not fully loaded.")),o=[],(r="function"==typeof(n=U)?n.apply(t,o):n)===a||(e.exports=r),window.iFrameResize=window.iFrameResize||U()}function y(){return window.MutationObserver||window.WebKitMutationObserver||window.MozMutationObserver}function k(e,t,n){e.addEventListener(t,n,!1)}function x(e,t,n){e.removeEventListener(t,n,!1)}function E(e){return u+"["+function(e){var t="Host page: "+e;return window.top!==window.self&&(t=window.parentIFrame&&window.parentIFrame.getId?window.parentIFrame.getId()+": "+e:"Nested host page: "+e),t}(e)+"]"}function N(e){return h[e]?h[e].log:c}function V(e,t){C("log",e,t,N(e))}function B(e,t){C("info",e,t,N(e))}function _(e,t){C("warn",e,t,!0)}function C(e,t,n,o){!0===o&&"object"==typeof window.console&&console[e](E(t),n)}function L(e){function t(){r("Height"),r("Width"),R((function(){z(T),I(P),v("onResized",T)}),T,"init")}function n(e){return"border-box"!==e.boxSizing?0:(e.paddingTop?parseInt(e.paddingTop,10):0)+(e.paddingBottom?parseInt(e.paddingBottom,10):0)}function o(e){return"border-box"!==e.boxSizing?0:(e.borderTopWidth?parseInt(e.borderTopWidth,10):0)+(e.borderBottomWidth?parseInt(e.borderBottomWidth,10):0)}function r(e){var t=Number(h[P]["max"+e]),n=Number(h[P]["min"+e]),o=e.toLowerCase(),r=Number(T[o]);V(P,"Checking "+o+" is in range "+n+"-"+t),r<n&&(r=n,V(P,"Set "+o+" to min value")),r>t&&(r=t,V(P,"Set "+o+" to max value")),T[o]=""+r}function a(e){return L.slice(L.indexOf(":")+s+e)}function i(e,t){var n,o,r;n=function(){var n,o;D("Send Page Info","pageInfo:"+(n=document.body.getBoundingClientRect(),o=T.iframe.getBoundingClientRect(),JSON.stringify({iframeHeight:o.height,iframeWidth:o.width,clientHeight:Math.max(document.documentElement.clientHeight,window.innerHeight||0),clientWidth:Math.max(document.documentElement.clientWidth,window.innerWidth||0),offsetTop:parseInt(o.top-n.top,10),offsetLeft:parseInt(o.left-n.left,10),scrollTop:window.pageYOffset,scrollLeft:window.pageXOffset,documentHeight:document.documentElement.clientHeight,documentWidth:document.documentElement.clientWidth,windowHeight:window.innerHeight,windowWidth:window.innerWidth})),e,t)},o=32,b[r=t]||(b[r]=setTimeout((function(){b[r]=null,n()}),o))}function l(e){var t=e.getBoundingClientRect();return M(P),{x:Math.floor(Number(t.left)+Number(p.x)),y:Math.floor(Number(t.top)+Number(p.y))}}function c(e){var t=e?l(T.iframe):{x:0,y:0},n={x:Number(T.width)+t.x,y:Number(T.height)+t.y};V(P,"Reposition requested from iFrame (offset x:"+t.x+" y:"+t.y+")"),window.top===window.self?(p=n,d(),V(P,"--")):window.parentIFrame?window.parentIFrame["scrollTo"+(e?"Offset":"")](n.x,n.y):_(P,"Unable to scroll to requested position, window.parentIFrame not found")}function d(){!1===v("onScroll",p)?O():I(P)}function f(e){var t={};if(0===Number(T.width)&&0===Number(T.height)){var n=a(9).split(":");t={x:n[1],y:n[0]}}else t={x:T.width,y:T.height};v(e,{iframe:T.iframe,screenX:Number(t.x),screenY:Number(t.y),type:T.type})}function v(e,t){return S(P,e,t)}var g,w,y,E,N,C,L=e.data,T={},P=null;"[iFrameResizerChild]Ready"===L?function(){for(var e in h)D("iFrame requested init",H(e),h[e].iframe,e)}():u===(""+L).slice(0,m)&&L.slice(m).split(":")[0]in h?(y=L.slice(m).split(":"),E=y[1]?parseInt(y[1],10):0,N=h[y[0]]&&h[y[0]].iframe,C=getComputedStyle(N),T={iframe:N,id:y[0],height:E+n(C)+o(C),width:y[2],type:y[3]},P=T.id,h[P]&&(h[P].loaded=!0),(w=T.type in{true:1,false:1,undefined:1})&&V(P,"Ignoring init message from meta parent page"),!w&&function(e){var t=!0;return h[e]||(t=!1,_(T.type+" No settings for "+e+". Message was: "+L)),t}(P)&&(V(P,"Received: "+L),g=!0,null===T.iframe&&(_(P,"IFrame ("+T.id+") not found"),g=!1),g&&function(){var t,n=e.origin,o=h[P]&&h[P].checkOrigin;if(o&&""+n!="null"&&!(o.constructor===Array?function(){var e=0,t=!1;for(V(P,"Checking connection is from allowed list of origins: "+o);e<o.length;e++)if(o[e]===n){t=!0;break}return t}():(t=h[P]&&h[P].remoteHost,V(P,"Checking connection is from: "+t),n===t)))throw new Error("Unexpected message received from: "+n+" for "+T.iframe.id+". Message was: "+e.data+". This error can be disabled by setting the checkOrigin: false option or by providing of array of trusted domains.");return!0}()&&function(){switch(h[P]&&h[P].firstRun&&h[P]&&(h[P].firstRun=!1),T.type){case"close":F(T.iframe);break;case"message":s=a(6),V(P,"onMessage passed: {iframe: "+T.iframe.id+", message: "+s+"}"),v("onMessage",{iframe:T.iframe,message:JSON.parse(s)}),V(P,"--");break;case"mouseenter":f("onMouseEnter");break;case"mouseleave":f("onMouseLeave");break;case"autoResize":h[P].autoResize=JSON.parse(a(9));break;case"scrollTo":c(!1);break;case"scrollToOffset":c(!0);break;case"pageInfo":i(h[P]&&h[P].iframe,P),function(){function e(e,o){function r(){h[n]?i(h[n].iframe,n):t()}["scroll","resize"].forEach((function(t){V(n,e+t+" listener for sendPageInfo"),o(window,t,r)}))}function t(){e("Remove ",x)}var n=P;e("Add ",k),h[n]&&(h[n].stopPageInfo=t)}();break;case"pageInfoStop":h[P]&&h[P].stopPageInfo&&(h[P].stopPageInfo(),delete h[P].stopPageInfo);break;case"inPageLink":n=a(9).split("#")[1]||"",o=decodeURIComponent(n),(r=document.getElementById(o)||document.getElementsByName(o)[0])?(e=l(r),V(P,"Moving to in page link (#"+n+") at x: "+e.x+" y: "+e.y),p={x:e.x,y:e.y},d(),V(P,"--")):window.top===window.self?V(P,"In page link #"+n+" not found"):window.parentIFrame?window.parentIFrame.moveToAnchor(n):V(P,"In page link #"+n+" not found and window.parentIFrame not found");break;case"reset":j(T);break;case"init":t(),v("onInit",T.iframe);break;default:0===Number(T.width)&&0===Number(T.height)?_("Unsupported message received ("+T.type+"), this is likely due to the iframe containing a later version of iframe-resizer than the parent page"):t()}var e,n,o,r,s}())):B(P,"Ignored: "+L)}function S(e,t,n){var o=null,r=null;if(h[e]){if("function"!=typeof(o=h[e][t]))throw new TypeError(t+" on iFrame["+e+"] is not a function");r=o(n)}return r}function T(e){var t=e.id;delete h[t]}function F(e){var t=e.id;if(!1!==S(t,"onClose",t)){V(t,"Removing iFrame: "+t);try{e.parentNode&&e.parentNode.removeChild(e)}catch(e){_(e)}S(t,"onClosed",t),V(t,"--"),T(e)}else V(t,"Close iframe cancelled by onClose event")}function M(e){null===p&&V(e,"Get page position: "+(p={x:window.pageXOffset===a?document.documentElement.scrollLeft:window.pageXOffset,y:window.pageYOffset===a?document.documentElement.scrollTop:window.pageYOffset}).x+","+p.y)}function I(e){null!==p&&(window.scrollTo(p.x,p.y),V(e,"Set page position: "+p.x+","+p.y),O())}function O(){p=null}function j(e){V(e.id,"Size reset requested by "+("init"===e.type?"host page":"iFrame")),M(e.id),R((function(){z(e),D("reset","reset",e.iframe,e.id)}),e,"reset")}function z(e){function t(t){d||"0"!==e[t]||(d=!0,V(o,"Hidden iFrame detected, creating visibility listener"),function(){function e(){function e(e){function t(t){return"0px"===(h[e]&&h[e].iframe.style[t])}function n(e){return null!==e.offsetParent}h[e]&&n(h[e].iframe)&&(t("height")||t("width"))&&D("Visibility change","resize",h[e].iframe,e)}Object.keys(h).forEach((function(t){e(t)}))}function t(t){V("window","Mutation observed: "+t[0].target+" "+t[0].type),W(e,16)}function n(){var e=document.querySelector("body"),n={attributes:!0,attributeOldValue:!1,characterData:!0,characterDataOldValue:!1,childList:!0,subtree:!0};new o(t).observe(e,n)}var o=y();o&&n()}())}function n(n){!function(t){e.id?(e.iframe.style[t]=e[t]+"px",V(e.id,"IFrame ("+o+") "+t+" set to "+e[t]+"px")):V("undefined","messageData id not set")}(n),t(n)}var o=e.iframe.id;h[o]&&(h[o].sizeHeight&&n("height"),h[o].sizeWidth&&n("width"))}function R(e,t,n){n!==t.type&&f&&!window.jasmine?(V(t.id,"Requesting animation frame"),f(e)):e()}function D(e,t,n,o,r){var a,i=!1;o=o||n.id,h[o]&&(n&&"contentWindow"in n&&null!==n.contentWindow?(a=h[o]&&h[o].targetOrigin,V(o,"["+e+"] Sending msg to iframe["+o+"] ("+t+") targetOrigin: "+a),n.contentWindow.postMessage(u+t,a)):_(o,"["+e+"] IFrame("+o+") not found"),r&&h[o]&&h[o].warningTimeout&&(h[o].msgTimeout=setTimeout((function(){!h[o]||h[o].loaded||i||(i=!0,_(o,"IFrame has not responded within "+h[o].warningTimeout/1e3+" seconds. Check iFrameResizer.contentWindow.js has been loaded in iFrame. This message can be ignored if everything is working, or you can set the warningTimeout option to a higher value or zero to suppress this warning."))}),h[o].warningTimeout)))}function H(e){return e+":"+h[e].bodyMarginV1+":"+h[e].sizeWidth+":"+h[e].log+":"+h[e].interval+":"+h[e].enablePublicMethods+":"+h[e].autoResize+":"+h[e].bodyMargin+":"+h[e].heightCalculationMethod+":"+h[e].bodyBackground+":"+h[e].bodyPadding+":"+h[e].tolerance+":"+h[e].inPageLinks+":"+h[e].resizeFrom+":"+h[e].widthCalculationMethod+":"+h[e].mouseEvents}function P(e,t){function n(e){var t=e.split("Callback");if(2===t.length){var n="on"+t[0].charAt(0).toUpperCase()+t[0].slice(1);this[n]=this[e],delete this[e],_(i,"Deprecated: '"+e+"' has been renamed '"+n+"'. The old method will be removed in the next major version.")}}var o,r,i=function(n){if("string"!=typeof n)throw new TypeError("Invaild id for iFrame. Expected String");var o;return""===n&&(e.id=(o=t&&t.id||w.id+l++,null!==document.getElementById(o)&&(o+=l++),n=o),c=(t||{}).log,V(n,"Added missing iframe ID: "+n+" ("+e.src+")")),n}(e.id);i in h&&"iFrameResizer"in e?_(i,"Ignored iFrame, already setup."):(!function(t){var o;t=t||{},h[i]=Object.create(null),h[i].iframe=e,h[i].firstRun=!0,h[i].remoteHost=e.src&&e.src.split("/").slice(0,3).join("/"),function(e){if("object"!=typeof e)throw new TypeError("Options is not an object")}(t),Object.keys(t).forEach(n,t),function(e){for(var t in w)Object.prototype.hasOwnProperty.call(w,t)&&(h[i][t]=Object.prototype.hasOwnProperty.call(e,t)?e[t]:w[t])}(t),h[i]&&(h[i].targetOrigin=!0===h[i].checkOrigin?""===(o=h[i].remoteHost)||null!==o.match(/^(about:blank|javascript:|file:\/\/)/)?"*":o:"*")}(t),function(){switch(V(i,"IFrame scrolling "+(h[i]&&h[i].scrolling?"enabled":"disabled")+" for "+i),e.style.overflow=!1===(h[i]&&h[i].scrolling)?"hidden":"auto",h[i]&&h[i].scrolling){case"omit":break;case!0:e.scrolling="yes";break;case!1:e.scrolling="no";break;default:e.scrolling=h[i]?h[i].scrolling:"no"}}(),function(){function t(t){var n=h[i][t];1/0!==n&&0!==n&&(e.style[t]="number"==typeof n?n+"px":n,V(i,"Set "+t+" = "+e.style[t]))}function n(e){if(h[i]["min"+e]>h[i]["max"+e])throw new Error("Value for min"+e+" can not be greater than max"+e)}n("Height"),n("Width"),t("maxHeight"),t("minHeight"),t("maxWidth"),t("minWidth")}(),"number"!=typeof(h[i]&&h[i].bodyMargin)&&"0"!==(h[i]&&h[i].bodyMargin)||(h[i].bodyMarginV1=h[i].bodyMargin,h[i].bodyMargin=h[i].bodyMargin+"px"),o=H(i),(r=y())&&function(t){e.parentNode&&new t((function(t){t.forEach((function(t){Array.prototype.slice.call(t.removedNodes).forEach((function(t){t===e&&F(e)}))}))})).observe(e.parentNode,{childList:!0})}(r),k(e,"load",(function(){var t,n;D("iFrame.onload",o,e,a,!0),t=h[i]&&h[i].firstRun,n=h[i]&&h[i].heightCalculationMethod in v,!t&&n&&j({iframe:e,height:0,width:0,type:"init"})})),D("init",o,e,a,!0),h[i]&&(h[i].iframe.iFrameResizer={close:F.bind(null,h[i].iframe),removeListeners:T.bind(null,h[i].iframe),resize:D.bind(null,"Window resize","resize",h[i].iframe),moveToAnchor:function(e){D("Move to anchor","moveToAnchor:"+e,h[i].iframe,i)},sendMessage:function(e){D("Send Message","message:"+(e=JSON.stringify(e)),h[i].iframe,i)}}))}function W(e,t){null===g&&(g=setTimeout((function(){g=null,e()}),t))}function A(){"hidden"!==document.visibilityState&&(V("document","Trigger event: Visibility change"),W((function(){G("Tab Visible","resize")}),16))}function G(e,t){Object.keys(h).forEach((function(n){(function(e){return h[e]&&"parent"===h[e].resizeFrom&&h[e].autoResize&&!h[e].firstRun})(n)&&D(e,t,h[n].iframe,n)}))}function Z(){k(window,"message",L),k(window,"resize",(function(){var e;V("window","Trigger event: "+(e="resize")),W((function(){G("Window "+e,"resize")}),16)})),k(document,"visibilitychange",A),k(document,"-webkit-visibilitychange",A)}function U(){function e(e,n){n&&(!function(){if(!n.tagName)throw new TypeError("Object is not a valid DOM element");if("IFRAME"!==n.tagName.toUpperCase())throw new TypeError("Expected <IFRAME> tag, found <"+n.tagName+">")}(),P(n,e),t.push(n))}var t;return function(){var e,t=["moz","webkit","o","ms"];for(e=0;e<t.length&&!f;e+=1)f=window[t[e]+"RequestAnimationFrame"];f?f=f.bind(window):V("setup","RequestAnimationFrame not supported")}(),Z(),function(n,o){switch(t=[],function(e){e&&e.enablePublicMethods&&_("enablePublicMethods option has been removed, public methods are now always available in the iFrame")}(n),typeof o){case"undefined":case"string":Array.prototype.forEach.call(document.querySelectorAll(o||"iframe"),e.bind(a,n));break;case"object":e(n,o);break;default:throw new TypeError("Unexpected data type ("+typeof o+")")}return t}}}()},3368:(e,t,n)=>{"use strict";n.d(t,{Z:()=>a});var o=n(1519),r=n.n(o)()((function(e){return e[1]}));r.push([e.id,".mw-900px{max-width:900px}.animated-video>iframe{height:100%!important;width:100%!important}",""]);const a=r},6857:(e,t,n)=>{"use strict";n.d(t,{Z:()=>a});var o=n(1519),r=n.n(o)()((function(e){return e[1]}));r.push([e.id,".mw-900px[data-v-42f43f73]{max-width:900px}.w-90[data-v-42f43f73]{width:90%}",""]);const a=r},41921:(e,t,n)=>{"use strict";n.d(t,{Z:()=>a});var o=n(1519),r=n.n(o)()((function(e){return e[1]}));r.push([e.id,".app-container{background-color:#fff}.wrap{max-width:75ch;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.btn-white-custom{background:#fff;color:#000}.btn-black-custom:hover,.btn-white-custom{background-color:#fff!important;color:#000!important}.btn-black-custom,.btn-white-custom:hover,.btn.btn-white-custom:hover:not(.btn-active){background-color:#000!important;color:#fff!important}.module-sections{margin-left:-30px;margin-right:-30px;overflow:auto hidden;position:relative}.sticky-top{min-width:calc(100% - 140px);position:fixed}.module-section{border-bottom:1px solid;border-left:1px solid;border-top:1px solid;cursor:pointer;height:100px}.module-sections>.text-center:last-of-type>.module-section{border-right:1px solid}.app-content{padding:0}.banner_detail_box{left:20%;position:absolute;top:50%;transform:translate(-50%,-50%)}.modal-backdrop{opacity:.8!important}.section-content{margin-top:50px;padding-bottom:50px}.section-content iframe{width:100%!important}.section-content iframe.wistia_embed{height:100%!important}.section-content img{max-width:100%}.section-content p iframe,.section-content p img{margin-bottom:-1rem}.pointer{cursor:pointer}.overlay{overflow:overlay}.related{right:5%!important}.banner{background-color:#000;background-image:url(/images/vwe/home-parallax.jpg);background-position:50%;background-repeat:no-repeat;background-size:cover;display:block;min-height:calc(56.25vw - 149px);overflow:hidden;position:relative}.full-view-banner{margin-left:-30px;margin-right:-30px}.banner-video{height:100%}.banner-video>video{left:50%;position:absolute;top:50%;transform:translate(-50%,-50%);width:101%!important}.froala-response,.teacher-feedback{border-radius:10px;height:300px;overflow:auto;padding:20px}.froala-response{background-color:#fff;border:1px solid #bbb}.froala-response iframe{width:100%}.froala-response img{max-width:100%}div#kt_app_content{padding-bottom:0;padding-top:0}@media (max-width:1280px){.banner{height:56.25vw}.banner_detail_box{left:40%}.banner-video>video{height:100%!important;width:calc(65vw + 65vh)!important}}@media (min-width:992px){.sticky-top+.section-content{margin-top:100px}.module-sections{animation-duration:.2s;animation-fill-mode:forwards;animation-name:backtooriginal;z-index:100}.sticky-top{animation-duration:.2s;animation-fill-mode:forwards;animation-name:stick-top}@keyframes stick-top{0%{top:5px}to{top:0}}@keyframes backtooriginal{0%{top:-5px}to{top:0}}}@media (max-width:991px){.full-view-banner,.module-sections{margin-left:-20px;margin-right:-20px}.full-view-banner{margin-top:58.16px}.sticky-top{min-width:100%;top:119px}.module-section{height:100px}}@media (max-width:991px) and (min-width:768px) and (orientation:portrait){.banner{height:86.25vw}.banner-video>video{height:100%!important;width:calc(66vw + 66vh)!important}}@media (max-width:991px) and (orientation:landscape){.banner-video>video{height:auto!important;width:calc(70vw + 70vh)!important}}@media (max-width:767px){.banner{height:calc(100vh - 300px)}.banner_detail_box{left:50%}.sticky-top{margin-top:10px}}@media (max-width:575px){div#kt_app_content{padding-top:30px}.banner_detail_box{width:70vw!important}.full-view-banner{margin-top:0}.banner-video>video{height:100%!important;width:calc(90vw + 90vh)!important}}",""]);const a=r},96268:(e,t,n)=>{"use strict";n.d(t,{Z:()=>w});var o=n(70655),r=n(70821);function a(e){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a(e)}function i(){i=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,o=Object.defineProperty||function(e,t,n){e[t]=n.value},r="function"==typeof Symbol?Symbol:{},l=r.iterator||"@@iterator",c=r.asyncIterator||"@@asyncIterator",d=r.toStringTag||"@@toStringTag";function s(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(e){s=function(e,t,n){return e[t]=n}}function u(e,t,n,r){var a=t&&t.prototype instanceof f?t:f,i=Object.create(a.prototype),l=new _(r||[]);return o(i,"_invoke",{value:E(e,n,l)}),i}function m(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=u;var p={};function f(){}function v(){}function h(){}var g={};s(g,l,(function(){return this}));var w=Object.getPrototypeOf,b=w&&w(w(C([])));b&&b!==t&&n.call(b,l)&&(g=b);var y=h.prototype=f.prototype=Object.create(g);function k(e){["next","throw","return"].forEach((function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function x(e,t){function r(o,i,l,c){var d=m(e[o],e,i);if("throw"!==d.type){var s=d.arg,u=s.value;return u&&"object"==a(u)&&n.call(u,"__await")?t.resolve(u.__await).then((function(e){r("next",e,l,c)}),(function(e){r("throw",e,l,c)})):t.resolve(u).then((function(e){s.value=e,l(s)}),(function(e){return r("throw",e,l,c)}))}c(d.arg)}var i;o(this,"_invoke",{value:function(e,n){function o(){return new t((function(t,o){r(e,n,t,o)}))}return i=i?i.then(o,o):o()}})}function E(e,t,n){var o="suspendedStart";return function(r,a){if("executing"===o)throw new Error("Generator is already running");if("completed"===o){if("throw"===r)throw a;return L()}for(n.method=r,n.arg=a;;){var i=n.delegate;if(i){var l=N(i,n);if(l){if(l===p)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===o)throw o="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o="executing";var c=m(e,t,n);if("normal"===c.type){if(o=n.done?"completed":"suspendedYield",c.arg===p)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o="completed",n.method="throw",n.arg=c.arg)}}}function N(e,t){var n=t.method,o=e.iterator[n];if(void 0===o)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,N(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),p;var r=m(o,e.iterator,t.arg);if("throw"===r.type)return t.method="throw",t.arg=r.arg,t.delegate=null,p;var a=r.arg;return a?a.done?(t[e.resultName]=a.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,p):a:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,p)}function V(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function B(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function _(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(V,this),this.reset(!0)}function C(e){if(e){var t=e[l];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,r=function t(){for(;++o<e.length;)if(n.call(e,o))return t.value=e[o],t.done=!1,t;return t.value=void 0,t.done=!0,t};return r.next=r}}return{next:L}}function L(){return{value:void 0,done:!0}}return v.prototype=h,o(y,"constructor",{value:h,configurable:!0}),o(h,"constructor",{value:v,configurable:!0}),v.displayName=s(h,d,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===v||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,h):(e.__proto__=h,s(e,d,"GeneratorFunction")),e.prototype=Object.create(y),e},e.awrap=function(e){return{__await:e}},k(x.prototype),s(x.prototype,c,(function(){return this})),e.AsyncIterator=x,e.async=function(t,n,o,r,a){void 0===a&&(a=Promise);var i=new x(u(t,n,o,r),a);return e.isGeneratorFunction(n)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},k(y),s(y,d,"Generator"),s(y,l,(function(){return this})),s(y,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var o in t)n.push(o);return n.reverse(),function e(){for(;n.length;){var o=n.pop();if(o in t)return e.value=o,e.done=!1,e}return e.done=!0,e}},e.values=C,_.prototype={constructor:_,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(B),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function o(n,o){return i.type="throw",i.arg=e,t.next=n,o&&(t.method="next",t.arg=void 0),!!o}for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r],i=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var l=n.call(a,"catchLoc"),c=n.call(a,"finallyLoc");if(l&&c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(e,t){for(var o=this.tryEntries.length-1;o>=0;--o){var r=this.tryEntries[o];if(r.tryLoc<=this.prev&&n.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var a=r;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=e,i.arg=t,a?(this.method="next",this.next=a.finallyLoc,p):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),p},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),B(n),p}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var r=o.arg;B(n)}return r}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:C(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),p}},e}var l={class:"modal fade",id:"kt_modal_viewResponse",tabindex:"-1","aria-hidden":"true"},c={class:"modal-dialog modal-dialog-centered modal-xl"},d={class:"modal-content rounded-0 mt-5"},s={class:"modal-header py-3"},u=(0,r.createElementVNode)("h5",{class:"modal-title"},null,-1),m=[(0,r.createElementVNode)("i",{class:"fa-solid fa-expand text-black text-black"},null,-1)],p=["href"],f=[(0,r.createElementVNode)("i",{class:"fa-solid fa-download text-black"},null,-1)],v=(0,r.createElementVNode)("button",{type:"button",class:"btn-close","data-bs-dismiss":"modal"},null,-1),h={class:"modal-body bg-black p-0 text-white text-center"},g=["src"];const w=(0,r.defineComponent)({__name:"ResponseModal",props:{modalSrc:null,downloadUrl:null},setup:function(e){var t=this,n=function(){return(0,o.mG)(t,void 0,void 0,i().mark((function e(){var t;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=document.querySelector("#kt_modal_viewResponse iframe")){e.next=3;break}return e.abrupt("return");case 3:if(e.prev=3,document.fullscreenElement){e.next=9;break}return e.next=7,t.requestFullscreen();case 7:e.next=11;break;case 9:return e.next=11,document.exitFullscreen();case 11:e.next=16;break;case 13:e.prev=13,e.t0=e.catch(3),console.error("Error attempting to toggle fullscreen:",e.t0);case 16:case"end":return e.stop()}}),e,null,[[3,13]])})))};return function(t,o){return(0,r.openBlock)(),(0,r.createElementBlock)("div",l,[(0,r.createElementVNode)("div",c,[(0,r.createElementVNode)("div",d,[(0,r.createElementVNode)("div",s,[u,(0,r.createElementVNode)("div",null,[(0,r.createElementVNode)("span",{class:"mx-4 cursor-pointer",onClick:n},m),e.downloadUrl?((0,r.openBlock)(),(0,r.createElementBlock)("a",{key:0,href:e.downloadUrl,download:"",class:"text-secondary mx-2"},f,8,p)):(0,r.createCommentVNode)("",!0),v])]),(0,r.createElementVNode)("div",h,[(0,r.createElementVNode)("iframe",{class:"w-100",id:"previewFrame",style:{height:"80vh"},src:e.modalSrc,allowfullscreen:""},null,8,g)])])])])}}})},46919:(e,t,n)=>{"use strict";n.d(t,{Z:()=>Ie});var o=n(70821),r=function(e){return(0,o.pushScopeId)("data-v-42f43f73"),e=e(),(0,o.popScopeId)(),e},a={class:"modal fade",id:"kt_modal_share_badge",tabindex:"-1",style:{display:"none"},"aria-hidden":"true"},i={class:"modal-dialog modal-dialog-centered mw-800px"},l={class:"modal-content rounded-0"},c=r((function(){return(0,o.createElementVNode)("div",{class:"modal-header text-white"},[(0,o.createElementVNode)("h5",{class:"modal-title"},"Share Badge"),(0,o.createElementVNode)("button",{type:"button",class:"btn-close","data-bs-dismiss":"modal","aria-label":"Close"})],-1)})),d={class:"modal-body text-start p-6"},s=(0,o.createStaticVNode)('<div class="d-flex align-items-center justify-content-around p-1" data-v-42f43f73><div class="shadow-md mx-auto fs-5" data-v-42f43f73><h2 class="text-xl font-bold fs-3" data-v-42f43f73> Publish your achievements for your network to see. </h2><h6 class="text-black fw-bold mt-5 mb-5" data-v-42f43f73> Add to your LinkedIn Profile </h6><p data-v-42f43f73> Here’s a step-by-step guide to adding badges or certificates to the ‘Licenses &amp; Certifications’ section of your LinkedIn Profile: </p><p data-v-42f43f73> 1. Go to your LinkedIn profile and scroll to your ‘Licenses &amp; certifications’ section. </p><p data-v-42f43f73>2. Click + icon.</p><p data-v-42f43f73> 3. Provide all the relevant information about the badge. You can find this below. </p><p data-v-42f43f73> 4. Don&#39;t forget to also mention the skills you gained from earning the badge. This will give your profile an extra boost and help potential employers understand your expertise. </p></div></div><hr class="mx-auto border-dark opacity-10" data-v-42f43f73>',2),u={class:"container px-1"},m={class:"row mt-5"},p={class:"col-12 col-md-6 fs-5"},f=r((function(){return(0,o.createElementVNode)("h4",{class:"text-start mt-3 mb-6"}," Copy the below fields to your profile ",-1)})),v={class:"p-2 mt-2"},h=r((function(){return(0,o.createElementVNode)("div",null,"Name",-1)})),g={class:"border d-flex justify-content-between p-2 rounded align-items-center"},w={class:"p-2 fw-bold"},b=[r((function(){return(0,o.createElementVNode)("i",{class:"fa-regular fa-copy"},null,-1)}))],y={key:0,class:"text-primary mt-1 fw-semibold"},k={class:"p-2 mt-2"},x=r((function(){return(0,o.createElementVNode)("div",null,"Issuing Organisation",-1)})),E={class:"border d-flex justify-content-between p-2 rounded align-items-center"},N={class:"p-2 fw-bold"},V={key:0},B={key:0},_={key:1},C=[r((function(){return(0,o.createElementVNode)("i",{class:"fa-regular fa-copy"},null,-1)}))],L={key:0,class:"text-primary mt-1 fw-semibold"},S={class:"p-2 mt-2"},T=r((function(){return(0,o.createElementVNode)("div",null,"Issue Date",-1)})),F={class:"border d-flex justify-content-between p-2 rounded align-items-center"},M={class:"p-2 fw-bold"},I=[r((function(){return(0,o.createElementVNode)("i",{class:"fa-regular fa-copy"},null,-1)}))],O={key:0,class:"text-primary mt-1 fw-semibold"},j={key:0,class:"p-2 mt-2"},z=r((function(){return(0,o.createElementVNode)("div",null,"Expiry Date",-1)})),R={class:"border d-flex justify-content-between p-2 rounded align-items-center"},D={class:"p-2 fw-bold"},H=[r((function(){return(0,o.createElementVNode)("i",{class:"fa-regular fa-copy"},null,-1)}))],P={key:0,class:"text-primary mt-1 fw-semibold"},W={class:"p-2 mt-2"},A=r((function(){return(0,o.createElementVNode)("div",null,"Credential ID",-1)})),G={class:"border d-flex justify-content-between p-2 rounded align-items-center"},Z={class:"p-2 fw-bold"},U=[r((function(){return(0,o.createElementVNode)("i",{class:"fa-regular fa-copy"},null,-1)}))],q={key:0,class:"text-primary mt-1 fw-semibold"},Y={class:"col-12 col-md-6 text-center mt-4 mt-md-0"},J=["src"],X=["href"],Q=r((function(){return(0,o.createElementVNode)("i",{class:"fa-solid fa-download"},null,-1)})),K=r((function(){return(0,o.createElementVNode)("div",{class:"modal-footer"},[(0,o.createElementVNode)("button",{type:"button",class:"btn btn-secondary","data-bs-toggle":"modal","data-bs-target":"#kt_modal_badge"}," View Badge ")],-1)}));var $={class:"modal fade",id:"kt_modal_badge",tabindex:"-1",style:{display:"none"},"aria-hidden":"true"},ee={class:"modal-dialog modal-dialog-centered modal-xl"},te={class:"modal-content rounded-0"},ne=(0,o.createElementVNode)("div",{class:"modal-header text-white"},[(0,o.createElementVNode)("h5",{class:"modal-title"},"View Badge"),(0,o.createElementVNode)("button",{type:"button",class:"btn-close","data-bs-dismiss":"modal","aria-label":"Close"})],-1),oe={class:"modal-body text-center px-10"},re={class:"row gap-4 fs-5"},ae={class:"col-7 px-7 py-9 text-start border border-solid rounded"},ie={class:"fw-bold mb-5 mt-5"},le={key:0},ce={class:"mt-7 lh-lg"},de={class:"mb-1"},se=(0,o.createElementVNode)("span",{class:"text-gray-700"},"Module Name: ",-1),ue={class:"mb-1"},me=(0,o.createElementVNode)("span",{class:"text-gray-700"},"Credential ID: ",-1),pe={class:"mb-1"},fe=(0,o.createElementVNode)("span",{class:"text-gray-700"},"Issue Date: ",-1),ve={key:0,class:"mb-1"},he=(0,o.createElementVNode)("span",{class:"text-gray-700"},"Expiry Date: ",-1),ge={class:"mb-1"},we=(0,o.createElementVNode)("span",{class:"text-gray-700"},"Module Type: ",-1),be={class:"col my-auto"},ye={key:0},ke=["innerHTML"],xe=["src"],Ee=(0,o.createElementVNode)("div",{class:"modal-footer border-0"},[(0,o.createElementVNode)("button",{type:"button",class:"btn btn-secondary","data-bs-toggle":"modal","data-bs-target":"#kt_modal_share_badge"}," Share Badge ")],-1);const Ne=(0,o.defineComponent)({props:{selectedBadge:Object},methods:{isVideo:function(e){return e&&e.endsWith(".mp4")}}});var Ve=n(93379),Be=n.n(Ve),_e=n(3368),Ce={insert:"head",singleton:!1};Be()(_e.Z,Ce);_e.Z.locals;var Le=n(83744);const Se=(0,Le.Z)(Ne,[["render",function(e,t,n,r,a,i){var l,c,d,s,u,m,p,f,v,h,g,w,b,y,k,x,E;return(0,o.openBlock)(),(0,o.createElementBlock)("div",$,[(0,o.createElementVNode)("div",ee,[(0,o.createElementVNode)("div",te,[ne,(0,o.createElementVNode)("div",oe,[(0,o.createElementVNode)("div",re,[(0,o.createElementVNode)("div",ae,[(0,o.createElementVNode)("div",null,[(0,o.createElementVNode)("h1",null,(0,o.toDisplayString)(null===(c=null===(l=e.selectedBadge)||void 0===l?void 0:l.badge)||void 0===c?void 0:c.name),1),(0,o.createElementVNode)("p",ie,[(0,o.createTextVNode)(" Verified by "),((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(null===(s=null===(d=e.selectedBadge)||void 0===d?void 0:d.badge)||void 0===s?void 0:s.companies,(function(t,n){var r,a;return(0,o.openBlock)(),(0,o.createElementBlock)("span",{key:t.id},[(0,o.createElementVNode)("u",null,(0,o.toDisplayString)(t.name),1),n!==(null===(a=null===(r=e.selectedBadge)||void 0===r?void 0:r.badge)||void 0===a?void 0:a.companies.length)-1?((0,o.openBlock)(),(0,o.createElementBlock)("span",le," + ")):(0,o.createCommentVNode)("",!0)])})),128))])]),(0,o.createElementVNode)("div",ce,[(0,o.createElementVNode)("p",de,[se,(0,o.createTextVNode)((0,o.toDisplayString)(null===(u=e.selectedBadge)||void 0===u?void 0:u.module_name),1)]),(0,o.createElementVNode)("p",ue,[me,(0,o.createTextVNode)(" "+(0,o.toDisplayString)((null===(m=e.selectedBadge)||void 0===m?void 0:m.credential_id)||"N/A"),1)]),(0,o.createElementVNode)("p",pe,[fe,(0,o.createTextVNode)(" "+(0,o.toDisplayString)(null===(p=e.selectedBadge)||void 0===p?void 0:p.issue_date),1)]),(null===(f=e.selectedBadge)||void 0===f?void 0:f.expiration_date)?((0,o.openBlock)(),(0,o.createElementBlock)("p",ve,[he,(0,o.createTextVNode)(" "+(0,o.toDisplayString)(e.selectedBadge.expiration_date),1)])):(0,o.createCommentVNode)("",!0),(0,o.createElementVNode)("p",ge,[we,(0,o.createTextVNode)((0,o.toDisplayString)(null===(v=e.selectedBadge)||void 0===v?void 0:v.module_type),1)])])]),(0,o.createElementVNode)("div",be,[e.selectedBadge?((0,o.openBlock)(),(0,o.createElementBlock)("div",ye,[(null===(g=null===(h=e.selectedBadge)||void 0===h?void 0:h.badge)||void 0===g?void 0:g.video)?((0,o.openBlock)(),(0,o.createElementBlock)("div",{key:0,class:"animated-video",innerHTML:null===(b=null===(w=e.selectedBadge)||void 0===w?void 0:w.badge)||void 0===b?void 0:b.video},null,8,ke)):((0,o.openBlock)(),(0,o.createElementBlock)("img",{key:1,src:(null===(k=null===(y=e.selectedBadge)||void 0===y?void 0:y.badge)||void 0===k?void 0:k.animated_image_fullpath)||(null===(E=null===(x=e.selectedBadge)||void 0===x?void 0:x.badge)||void 0===E?void 0:E.image_fullpath),alt:"Animated Badge",class:"w-100"},null,8,xe))])):(0,o.createCommentVNode)("",!0)])])]),Ee])])])}]]),Te=(0,o.defineComponent)({components:{ViewBadgeModal:Se},props:{selectedBadge:Object,moduleData:Object,moduleType:String},emits:["shareBadge"],setup:function(e,t){var n=t.emit,r=(0,o.ref)("");return{emitShare:function(){n("shareBadge",e.selectedBadge)},copiedField:r,copyToClipboard:function(e,t){e&&navigator.clipboard.writeText(e).then((function(){r.value=t,setTimeout((function(){r.value=""}),3e3)})).catch((function(e){console.error("Copy failed:",e)}))}}}});var Fe=n(6857),Me={insert:"head",singleton:!1};Be()(Fe.Z,Me);Fe.Z.locals;const Ie=(0,Le.Z)(Te,[["render",function(e,t,n,r,$,ee){var te,ne,oe,re,ae,ie,le,ce,de,se,ue,me,pe,fe,ve,he=(0,o.resolveComponent)("ViewBadgeModal");return(0,o.openBlock)(),(0,o.createElementBlock)(o.Fragment,null,[(0,o.createVNode)(he,{selectedBadge:e.selectedBadge},null,8,["selectedBadge"]),(0,o.createElementVNode)("div",a,[(0,o.createElementVNode)("div",i,[(0,o.createElementVNode)("div",l,[c,(0,o.createElementVNode)("div",d,[s,(0,o.createElementVNode)("div",u,[(0,o.createElementVNode)("div",m,[(0,o.createElementVNode)("div",p,[f,(0,o.createElementVNode)("div",v,[h,(0,o.createElementVNode)("div",g,[(0,o.createElementVNode)("div",w,(0,o.toDisplayString)(null===(ne=null===(te=e.selectedBadge)||void 0===te?void 0:te.badge)||void 0===ne?void 0:ne.name),1),(0,o.createElementVNode)("button",{class:"btn btn-sm btn-outline-primary",onClick:t[0]||(t[0]=function(t){var n;return e.copyToClipboard(null===(n=e.selectedBadge)||void 0===n?void 0:n.name,"name")})},b)]),"name"===e.copiedField?((0,o.openBlock)(),(0,o.createElementBlock)("p",y,"Copied to clipboard!")):(0,o.createCommentVNode)("",!0)]),(0,o.createElementVNode)("div",k,[x,(0,o.createElementVNode)("div",E,[(0,o.createElementVNode)("div",N,[(null===(re=null===(oe=e.selectedBadge)||void 0===oe?void 0:oe.badge)||void 0===re?void 0:re.companies.length)>0?((0,o.openBlock)(),(0,o.createElementBlock)("div",V,[((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(null===(ie=null===(ae=e.selectedBadge)||void 0===ae?void 0:ae.badge)||void 0===ie?void 0:ie.companies,(function(t,n){var r,a;return(0,o.openBlock)(),(0,o.createElementBlock)("span",{key:t.id},[(0,o.createTextVNode)((0,o.toDisplayString)(t.name)+" ",1),n!==(null===(a=null===(r=e.selectedBadge)||void 0===r?void 0:r.badge)||void 0===a?void 0:a.companies.length)-1?((0,o.openBlock)(),(0,o.createElementBlock)("span",B," + ")):(0,o.createCommentVNode)("",!0)])})),128))])):((0,o.openBlock)(),(0,o.createElementBlock)("div",_," N/A "))]),(0,o.createElementVNode)("button",{class:"btn btn-sm btn-outline-primary",onClick:t[1]||(t[1]=function(t){var n;return e.copyToClipboard(null===(n=e.selectedBadge)||void 0===n?void 0:n.name,"name")})},C)]),"name"===e.copiedField?((0,o.openBlock)(),(0,o.createElementBlock)("p",L,"Copied to clipboard!")):(0,o.createCommentVNode)("",!0)]),(0,o.createElementVNode)("div",S,[T,(0,o.createElementVNode)("div",F,[(0,o.createElementVNode)("div",M,(0,o.toDisplayString)(null===(le=e.selectedBadge)||void 0===le?void 0:le.issue_date),1),(0,o.createElementVNode)("button",{class:"btn btn-sm btn-outline-primary",onClick:t[2]||(t[2]=function(t){var n;return e.copyToClipboard(null===(n=e.selectedBadge)||void 0===n?void 0:n.issue_date,"issue_date")})},I)]),"issue_date"===e.copiedField?((0,o.openBlock)(),(0,o.createElementBlock)("p",O,"Copied to clipboard!")):(0,o.createCommentVNode)("",!0)]),(null===(ce=e.selectedBadge)||void 0===ce?void 0:ce.expiration_date)?((0,o.openBlock)(),(0,o.createElementBlock)("div",j,[z,(0,o.createElementVNode)("div",R,[(0,o.createElementVNode)("div",D,(0,o.toDisplayString)(null===(de=e.selectedBadge)||void 0===de?void 0:de.expiration_date),1),(0,o.createElementVNode)("button",{class:"btn btn-sm btn-outline-primary",onClick:t[3]||(t[3]=function(t){var n;return e.copyToClipboard(null===(n=e.selectedBadge)||void 0===n?void 0:n.expiration_date,"expiry_date")})},H)]),"expiry_date"===e.copiedField?((0,o.openBlock)(),(0,o.createElementBlock)("p",P,"Copied to clipboard!")):(0,o.createCommentVNode)("",!0)])):(0,o.createCommentVNode)("",!0),(0,o.createElementVNode)("div",W,[A,(0,o.createElementVNode)("div",G,[(0,o.createElementVNode)("div",Z,(0,o.toDisplayString)((null===(se=e.selectedBadge)||void 0===se?void 0:se.credential_id)||"N/A"),1),(0,o.createElementVNode)("button",{class:"btn btn-sm btn-outline-primary",onClick:t[4]||(t[4]=function(t){var n;return e.copyToClipboard((null===(n=e.selectedBadge)||void 0===n?void 0:n.credential_id)||"N/A","credential_id")})},U)]),"credential_id"===e.copiedField?((0,o.openBlock)(),(0,o.createElementBlock)("p",q,"Copied to clipboard!")):(0,o.createCommentVNode)("",!0)])]),(0,o.createElementVNode)("div",Y,[(0,o.createElementVNode)("div",null,[(0,o.createElementVNode)("img",{src:null===(me=null===(ue=e.selectedBadge)||void 0===ue?void 0:ue.badge)||void 0===me?void 0:me.image_fullpath,class:"img-fluid rounded",style:{"max-width":"100%",height:"auto"}},null,8,J)]),(null===(fe=null===(pe=e.selectedBadge)||void 0===pe?void 0:pe.badge)||void 0===fe?void 0:fe.id)?((0,o.openBlock)(),(0,o.createElementBlock)("a",{key:0,href:"/badges/".concat(null===(ve=e.selectedBadge.badge)||void 0===ve?void 0:ve.id,"/download"),class:"btn btn-sm btn-outline-primary mt-3",download:""},[Q,(0,o.createTextVNode)(" Download Image ")],8,X)):(0,o.createCommentVNode)("",!0)])])])]),K])])])],64)}],["__scopeId","data-v-42f43f73"]])},89916:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>_t});var o=n(70821),r=["innerHTML"],a=(0,o.createElementVNode)("div",{style:{position:"absolute",width:"100%",height:"100%",opacity:".3",background:"#000"}},null,-1),i={class:"banner_detail_box w-450px"},l={key:0,class:"mt-4 mb-4"},c={class:"row g-3"},d={class:"col-6"},s={class:"d-flex align-items-center mb-10"},u=["src","alt"],m={class:"mb-1 fw-bold text-light fs-4"},p=(0,o.createElementVNode)("h1",{class:"fw-normal text-light"},"Virtual Work Experience",-1),f=["innerHTML"],v={class:"row text-light align-items-center"},h={key:0,class:"col-md-4 col-lg-3"},g=(0,o.createElementVNode)("i",{class:"fa-regular fa-clock text-white me-2"},null,-1),w=["textContent"],b=["textContent"],y={key:1,class:"col-md-4 col-lg-3"},k=(0,o.createElementVNode)("i",{class:"fa fa-chart-simple text-white me-2"},null,-1),x=["textContent"],E={class:"col-md-5 col-lg-5 mt-lg-0 mt-md-3"},N={key:0},V=[(0,o.createElementVNode)("span",{class:"text-dark px-5 py-2 rounded-pill w-auto d-inline-block",style:{"background-color":"#CDD6DD"}}," Submitted For Review ",-1)],B={key:1,class:"fs-6 text-light px-5 py-2 rounded-pill w-100",style:{"background-color":"#0062ff"}},_={key:2,class:"fs-6 text-dark px-5 py-2 rounded-pill",style:{"background-color":"#e9ff1f"}},C={class:"row mt-5"},L=(0,o.createElementVNode)("i",{class:"fa fa-check text-white"},null,-1),S={key:1,class:"row mt-5"},T=[(0,o.createElementVNode)("div",{class:"col-8 col-sm-6 col-md-12"},[(0,o.createElementVNode)("button",{type:"button",class:"btn btn-black-custom btn-lg rounded-0 w-100 p-md-5","data-bs-toggle":"modal","data-bs-target":"#kt_modal_trailer"}," Watch Trailer ")],-1)],F={key:2,class:"row mt-5"},M={class:"col-8 col-sm-6 col-md-10"},I={key:1,class:"btn btn-white-custom btn-lg border-1 rounded-0 w-100 p-md-5",style:{"font-size":"14px !important"},"data-bs-toggle":"modal","data-bs-target":"#kt_modal_viewResponse"},O={class:"row row-cols-3 mt-5"},j={key:0,class:"col my-auto"},z={class:"row g-3 mt-2"},R={class:"col-12"},D=["src","alt"],H=(0,o.createElementVNode)("div",{class:"overflow-hidden"},[(0,o.createElementVNode)("p",{class:"fw-bold text-light my-auto"}," View Badge ")],-1),P={key:1,class:"col my-auto"},W={class:"row g-3 mt-2"},A={class:"col-12"},G=[(0,o.createElementVNode)("i",{class:"fa-solid fa-file text-light me-2",width:"25"},null,-1),(0,o.createElementVNode)("div",null,[(0,o.createElementVNode)("p",{class:"fw-bold text-light my-auto"}," View Certificate ")],-1)],Z={key:2,class:"col my-auto"},U=[(0,o.createStaticVNode)('<div class="row g-3 mt-2"><div class="col-12"><div class="d-flex align-items-center cursor-pointer" data-bs-toggle="modal" data-bs-target="#kt_modal_feedback"><i class="fa-solid fa-comments text-light me-2" width="25"></i><div><p class="fw-bold text-light my-auto"> View Feedback </p></div></div></div></div>',1)],q={class:"module-section d-flex flex-column justify-content-center align-items-center py-5"},Y={class:"svg-icon svg-icon-primary svg-icon-2x"},J={key:0,xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",width:"24px",height:"24px",viewBox:"0 0 24 24",version:"1.1"},X=[(0,o.createElementVNode)("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},[(0,o.createElementVNode)("mask",{fill:"white"},[(0,o.createElementVNode)("use",{"xlink:href":"#path-1"})]),(0,o.createTextVNode)(),(0,o.createElementVNode)("g"),(0,o.createTextVNode)(),(0,o.createElementVNode)("path",{d:"M15.6274517,4.55882251 L14.4693753,6.2959371 C13.9280401,5.51296885 13.0239252,5 12,5 C10.3431458,5 9,6.34314575 9,8 L9,10 L14,10 L17,10 L18,10 C19.1045695,10 20,10.8954305 20,12 L20,18 C20,19.1045695 19.1045695,20 18,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,12 C4,10.8954305 4.8954305,10 6,10 L7,10 L7,8 C7,5.23857625 9.23857625,3 12,3 C13.4280904,3 14.7163444,3.59871093 15.6274517,4.55882251 Z",fill:"#ffffff"})],-1)],Q={key:1,xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",width:"24px",height:"24px",viewBox:"0 0 24 24",version:"1.1"},K=[(0,o.createElementVNode)("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},[(0,o.createElementVNode)("mask",{fill:"white"},[(0,o.createElementVNode)("use",{"xlink:href":"#path-1"})]),(0,o.createTextVNode)(),(0,o.createElementVNode)("g"),(0,o.createTextVNode)(),(0,o.createElementVNode)("path",{d:"M7,10 L7,8 C7,5.23857625 9.23857625,3 12,3 C14.7614237,3 17,5.23857625 17,8 L17,10 L18,10 C19.1045695,10 20,10.8954305 20,12 L20,18 C20,19.1045695 19.1045695,20 18,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,12 C4,10.8954305 4.8954305,10 6,10 L7,10 Z M12,5 C10.3431458,5 9,6.34314575 9,8 L9,10 L15,10 L15,8 C15,6.34314575 13.6568542,5 12,5 Z",fill:"#000000"})],-1)],$=["innerHTML"],ee={class:"m-0 text-white"},te=["textContent"],ne=["textContent"],oe={class:"module-section d-flex flex-column justify-content-center align-items-center py-5"},re={class:"svg-icon svg-icon-primary svg-icon-2x"},ae={key:0,xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",width:"24px",height:"24px",viewBox:"0 0 24 24",version:"1.1"},ie=[(0,o.createElementVNode)("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},[(0,o.createElementVNode)("mask",{fill:"white"},[(0,o.createElementVNode)("use",{"xlink:href":"#path-1"})]),(0,o.createTextVNode)(),(0,o.createElementVNode)("g"),(0,o.createTextVNode)(),(0,o.createElementVNode)("path",{d:"M15.6274517,4.55882251 L14.4693753,6.2959371 C13.9280401,5.51296885 13.0239252,5 12,5 C10.3431458,5 9,6.34314575 9,8 L9,10 L14,10 L17,10 L18,10 C19.1045695,10 20,10.8954305 20,12 L20,18 C20,19.1045695 19.1045695,20 18,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,12 C4,10.8954305 4.8954305,10 6,10 L7,10 L7,8 C7,5.23857625 9.23857625,3 12,3 C13.4280904,3 14.7163444,3.59871093 15.6274517,4.55882251 Z",fill:"#ffffff"})],-1)],le={key:1,xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",width:"24px",height:"24px",viewBox:"0 0 24 24",version:"1.1"},ce=[(0,o.createElementVNode)("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},[(0,o.createElementVNode)("mask",{fill:"white"},[(0,o.createElementVNode)("use",{"xlink:href":"#path-1"})]),(0,o.createTextVNode)(),(0,o.createElementVNode)("g"),(0,o.createTextVNode)(),(0,o.createElementVNode)("path",{d:"M7,10 L7,8 C7,5.23857625 9.23857625,3 12,3 C14.7614237,3 17,5.23857625 17,8 L17,10 L18,10 C19.1045695,10 20,10.8954305 20,12 L20,18 C20,19.1045695 19.1045695,20 18,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,12 C4,10.8954305 4.8954305,10 6,10 L7,10 Z M12,5 C10.3431458,5 9,6.34314575 9,8 L9,10 L15,10 L15,8 C15,6.34314575 13.6568542,5 12,5 Z",fill:"#000000"})],-1)],de={id:"vweSections",class:"section-content"},se={class:"d-flex justify-content-center"},ue={class:"container"},me={class:"row border rounded p-10 d-flex",style:{"min-height":"70vh"}},pe={class:"col-lg-3 col-md-8 overflow-auto",style:{"max-height":"70vh"}},fe={class:"nav nav-tabs nav-pills flex-row border-0 flex-md-column me-5 mb-3 mb-md-0 fs-6"},ve=["onClick"],he={class:"d-flex flex-column align-items-start"},ge={class:"fs-4 fw-bold"},we={class:"fs-7 text-left text-capitalize"},be={class:"nav-item w-100 me-0 mb-md-2"},ye=[(0,o.createElementVNode)("span",{class:"d-flex flex-column align-items-start"},[(0,o.createElementVNode)("span",{class:"fs-4 fw-bold"},"Final Step"),(0,o.createElementVNode)("span",{class:"fs-7"},"Submission")],-1)],ke={class:"col overflow-auto",style:{"max-height":"70vh"}},xe={key:0},Ee=["href"],Ne=(0,o.createElementVNode)("i",{class:"fa fa-eye my-auto"},null,-1),Ve={key:0},Be=(0,o.createElementVNode)("i",{class:"fa-regular fa-clock text-dark me-2"},null,-1),_e=["textContent"],Ce=["textContent"],Le=["innerHTML"],Se=["innerHTML"],Te={key:1},Fe=[(0,o.createElementVNode)("span",{class:"text-dark"},[(0,o.createElementVNode)("i",{class:"fa-regular fa-circle-xmark text-dark"}),(0,o.createElementVNode)("span",{class:""}," No was answer required on this section. ")],-1)],Me={key:0,class:"text-center mt-5"},Ie=(0,o.createElementVNode)("h4",null,"Students were asked to upload a document on their final step.",-1),Oe={class:"d-flex justify-content-center gap-10 pt-10"},je={key:0},ze=[(0,o.createElementVNode)("button",{class:"btn btn-secondary rounded",style:{"font-size":"14px !important"},"data-bs-toggle":"modal","data-bs-target":"#kt_modal_viewResponse"},[(0,o.createElementVNode)("i",{class:"fa fa-eye"}),(0,o.createTextVNode)(" View Response ")],-1)],Re={key:1,class:"d-flex gap-10"},De=["href"],He=(0,o.createElementVNode)("i",{class:"fa fa-download"},null,-1),Pe=["href"],We=(0,o.createElementVNode)("i",{class:"fa fa-download"},null,-1),Ae={class:"modal fade",id:"kt_modal_trailer",tabindex:"-1",style:{display:"none"},"aria-hidden":"true"},Ge={class:"modal-dialog modal-dialog-centered mw-900px"},Ze={class:"modal-content rounded-0"},Ue=["innerHTML"],qe={class:"modal fade",id:"kt_modal_viewFile",tabindex:"-1","aria-hidden":"true"},Ye={class:"modal-content rounded-0 mt-5"},Je={class:"modal-header py-3"},Xe=(0,o.createElementVNode)("h5",{class:"modal-title"},"Certificate Preview",-1),Qe={key:0,class:"fa-solid fa-compress text-black"},Ke={key:1,class:"fa-solid fa-expand text-black"},$e=["href"],et=[(0,o.createElementVNode)("i",{class:"fa-solid fa-download text-black"},null,-1)],tt=(0,o.createElementVNode)("button",{type:"button",class:"btn-close","data-bs-dismiss":"modal"},null,-1),nt={class:"modal-body bg-black p-1 text-white text-center"},ot=["src"],rt={key:1},at={class:"modal fade",id:"kt_modal_feedback",tabindex:"-1",style:{display:"none"},"aria-hidden":"true"},it={class:"modal-dialog modal-dialog-centered mw-600px"},lt={class:"modal-content rounded-0",style:{height:"80vh"}},ct=(0,o.createElementVNode)("div",{class:"modal-header text-white"},[(0,o.createElementVNode)("h5",{class:"modal-title"},"Feedback"),(0,o.createElementVNode)("button",{type:"button",class:"btn-close","data-bs-dismiss":"modal","aria-label":"Close"})],-1),dt={class:"modal-body p-4 bg-gray-50 text-left"},st={class:"p-4 bg-white",style:{height:"90%"}},ut=["innerHTML"];var mt=n(70655),pt=n(72961),ft=n(80894),vt=n(22201),ht=n(46702),gt=n.n(ht),wt=n(46919),bt=n(96268);function yt(e){return yt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},yt(e)}function kt(){kt=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,o=Object.defineProperty||function(e,t,n){e[t]=n.value},r="function"==typeof Symbol?Symbol:{},a=r.iterator||"@@iterator",i=r.asyncIterator||"@@asyncIterator",l=r.toStringTag||"@@toStringTag";function c(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,n){return e[t]=n}}function d(e,t,n,r){var a=t&&t.prototype instanceof m?t:m,i=Object.create(a.prototype),l=new V(r||[]);return o(i,"_invoke",{value:k(e,n,l)}),i}function s(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=d;var u={};function m(){}function p(){}function f(){}var v={};c(v,a,(function(){return this}));var h=Object.getPrototypeOf,g=h&&h(h(B([])));g&&g!==t&&n.call(g,a)&&(v=g);var w=f.prototype=m.prototype=Object.create(v);function b(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function y(e,t){function r(o,a,i,l){var c=s(e[o],e,a);if("throw"!==c.type){var d=c.arg,u=d.value;return u&&"object"==yt(u)&&n.call(u,"__await")?t.resolve(u.__await).then((function(e){r("next",e,i,l)}),(function(e){r("throw",e,i,l)})):t.resolve(u).then((function(e){d.value=e,i(d)}),(function(e){return r("throw",e,i,l)}))}l(c.arg)}var a;o(this,"_invoke",{value:function(e,n){function o(){return new t((function(t,o){r(e,n,t,o)}))}return a=a?a.then(o,o):o()}})}function k(e,t,n){var o="suspendedStart";return function(r,a){if("executing"===o)throw new Error("Generator is already running");if("completed"===o){if("throw"===r)throw a;return _()}for(n.method=r,n.arg=a;;){var i=n.delegate;if(i){var l=x(i,n);if(l){if(l===u)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===o)throw o="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o="executing";var c=s(e,t,n);if("normal"===c.type){if(o=n.done?"completed":"suspendedYield",c.arg===u)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o="completed",n.method="throw",n.arg=c.arg)}}}function x(e,t){var n=t.method,o=e.iterator[n];if(void 0===o)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,x(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),u;var r=s(o,e.iterator,t.arg);if("throw"===r.type)return t.method="throw",t.arg=r.arg,t.delegate=null,u;var a=r.arg;return a?a.done?(t[e.resultName]=a.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,u):a:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,u)}function E(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function N(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function V(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(E,this),this.reset(!0)}function B(e){if(e){var t=e[a];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,r=function t(){for(;++o<e.length;)if(n.call(e,o))return t.value=e[o],t.done=!1,t;return t.value=void 0,t.done=!0,t};return r.next=r}}return{next:_}}function _(){return{value:void 0,done:!0}}return p.prototype=f,o(w,"constructor",{value:f,configurable:!0}),o(f,"constructor",{value:p,configurable:!0}),p.displayName=c(f,l,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===p||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,c(e,l,"GeneratorFunction")),e.prototype=Object.create(w),e},e.awrap=function(e){return{__await:e}},b(y.prototype),c(y.prototype,i,(function(){return this})),e.AsyncIterator=y,e.async=function(t,n,o,r,a){void 0===a&&(a=Promise);var i=new y(d(t,n,o,r),a);return e.isGeneratorFunction(n)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},b(w),c(w,l,"Generator"),c(w,a,(function(){return this})),c(w,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var o in t)n.push(o);return n.reverse(),function e(){for(;n.length;){var o=n.pop();if(o in t)return e.value=o,e.done=!1,e}return e.done=!0,e}},e.values=B,V.prototype={constructor:V,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(N),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function o(n,o){return i.type="throw",i.arg=e,t.next=n,o&&(t.method="next",t.arg=void 0),!!o}for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r],i=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var l=n.call(a,"catchLoc"),c=n.call(a,"finallyLoc");if(l&&c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(e,t){for(var o=this.tryEntries.length-1;o>=0;--o){var r=this.tryEntries[o];if(r.tryLoc<=this.prev&&n.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var a=r;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=e,i.arg=t,a?(this.method="next",this.next=a.finallyLoc,u):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),u},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),N(n),u}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var r=o.arg;N(n)}return r}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:B(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),u}},e}const xt=(0,o.defineComponent)({name:"vwe-detail",components:{BadgeModal:wt.Z,ResponseModal:bt.Z},setup:function(){var e=this,t=(0,ft.oR)(),n=(0,vt.yj)(),r=(0,o.ref)(""),a=(0,o.ref)(""),i=(0,o.ref)(""),l=t.getters.currentUser;(0,o.onMounted)((function(){return(0,mt.mG)(e,void 0,void 0,kt().mark((function e(){return kt().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,g();case 2:gt()({heightCalculationMethod:"bodyScroll"},".section-content iframe");case 3:case"end":return e.stop()}}),e)})))})),(0,o.onMounted)((function(){var e=document.getElementById("kt_modal_viewResponse");e?e.addEventListener("show.bs.modal",(function(e){e.relatedTarget&&(r.value=c.value.student_response.view_response_file_path,a.value=c.value.student_response.id,i.value="workexperiencetemplates/responses/".concat(a.value,"/download"))})):console.warn("Modal element not found: #kt_modal_viewResponse")}));var c=(0,o.ref)(),d=(0,o.ref)(),s=n.params.student,u=((0,o.ref)(),(0,o.ref)(!1)),m=(0,o.ref)(null),p=(0,o.ref)({}),f=(0,o.ref)(""),v=(0,o.ref)(),h=0;c.value={id:1,background_imagepath:null,background_video:null,student_response:{step_responses:{}}},d.value=n.params.id;var g=function(){return(0,mt.mG)(e,void 0,void 0,kt().mark((function e(){var t,n,o,r;return kt().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,pt.Z.get("api/vwe/"+d.value+"/"+s);case 3:n=e.sent,o=n.data,c.value=o,c.value.user_response.step_responses.sort((function(e,t){return e.step.number-t.step.number})),r=document.getElementById("banner"),h=r.scrollHeight+120,""===(null===(t=o.user_response)||void 0===t?void 0:t.response_path)&&w(),e.next=15;break;case 12:e.prev=12,e.t0=e.catch(0),console.log(e.t0);case 15:case 16:case"end":return e.stop()}}),e,null,[[0,12]])})))},w=function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10,n=document.getElementById("vweSections");n?(n.scrollIntoView({behavior:"smooth",block:"start"}),console.log("Scrolled to #vweSections")):t>0?setTimeout((function(){return e(t-1)}),300):console.log("#vweSections not found after retries")};return{currentUser:l,vwe:c,currentVwe:d,config:{key:"hWA2C-7I2A4C3D5D2D2G3wxeklqcwvffrrhxhoqxpkC7bmnxE2F2G2D1B10B2B3E6F1F2==",height:300,attribution:!1,toolbarButtons:[""],events:{initialized:function(){console.log("initialized")}}},scrolled:u,handleScroll:function(){if((window.innerWidth||document.documentElement.clientWidth||document.body.clientWidth)>991){var e=document.getElementById("kt_app_toolbar");window.scrollY>h?(u.value=!0,e.style.display="none"):(u.value=!1,e.style.display="flex")}},viewResponse:function(){var e=document.querySelector(".banner");window.scrollBy({top:e.scrollHeight,left:0,behavior:"smooth"})},selectedStepId:m,openBadgeModal:function(e){p.value=e},openShareBadgeModal:function(e){p.value=e},selectedBadge:p,downloadUrl:i,modalSrc:r,loadCertificate:function(){return(0,mt.mG)(e,void 0,void 0,kt().mark((function e(){var t;return kt().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(c.value&&c.value.student_response){e.next=3;break}return console.error("vwe.student_response is missing!"),e.abrupt("return");case 3:t=c.value.student_response.id,f.value="/certificate-download/".concat(t,"?preview=true");case 5:case"end":return e.stop()}}),e)})))},certificateUrl:f,isFullscreen:v,toggleFullscreen:function(){v.value=!v.value},waitForSectionAndScroll:w}},props:["id"],created:function(){window.addEventListener("scroll",this.handleScroll)},destroyed:function(){window.removeEventListener("scroll",this.handleScroll)}});var Et=n(93379),Nt=n.n(Et),Vt=n(41921),Bt={insert:"head",singleton:!1};Nt()(Vt.Z,Bt);Vt.Z.locals;const _t=(0,n(83744).Z)(xt,[["render",function(e,t,n,mt,pt,ft){var vt,ht,gt,wt,bt,yt,kt,xt,Et=(0,o.resolveComponent)("router-link"),Nt=(0,o.resolveComponent)("BadgeModal"),Vt=(0,o.resolveComponent)("ResponseModal");return(0,o.openBlock)(),(0,o.createElementBlock)(o.Fragment,null,[(0,o.createElementVNode)("div",{id:"banner",class:"full-view-banner banner",style:(0,o.normalizeStyle)({backgroundImage:"url("+e.vwe.background_imagepath+")"})},[e.vwe.background_videoid?((0,o.openBlock)(),(0,o.createElementBlock)("div",{key:0,class:"banner-video",innerHTML:e.vwe.background_videoid},null,8,r)):(0,o.createCommentVNode)("",!0),a,(0,o.createElementVNode)("div",i,[e.vwe.badge&&1!==e.vwe.student_response.approve?((0,o.openBlock)(),(0,o.createElementBlock)("div",l,[(0,o.createElementVNode)("div",c,[(0,o.createElementVNode)("div",d,[(0,o.createElementVNode)("div",s,[(0,o.createElementVNode)("img",{src:e.vwe.badge.image_fullpath,alt:e.vwe.badge.name,class:"me-3",width:"25"},null,8,u),(0,o.createElementVNode)("div",null,[(0,o.createElementVNode)("p",m,(0,o.toDisplayString)(e.vwe.badge.name),1)])])])])])):(0,o.createCommentVNode)("",!0),p,(0,o.createElementVNode)("h1",{class:"display-4 fw-normal mb-4 text-light",innerHTML:e.vwe.title},null,8,f),(0,o.createElementVNode)("div",v,[e.vwe.estimated_time&&(e.vwe.estimated_time.hours||e.vwe.estimated_time.minutes)?((0,o.openBlock)(),(0,o.createElementBlock)("div",h,[g,e.vwe.estimated_time&&e.vwe.estimated_time.hours?((0,o.openBlock)(),(0,o.createElementBlock)("span",{key:0,textContent:(0,o.toDisplayString)(e.vwe.estimated_time.hours+"h ")},null,8,w)):(0,o.createCommentVNode)("",!0),e.vwe.estimated_time&&e.vwe.estimated_time.minutes?((0,o.openBlock)(),(0,o.createElementBlock)("span",{key:1,textContent:(0,o.toDisplayString)(e.vwe.estimated_time.minutes+"m")},null,8,b)):(0,o.createCommentVNode)("",!0)])):(0,o.createCommentVNode)("",!0),e.vwe.level?((0,o.openBlock)(),(0,o.createElementBlock)("div",y,[k,(0,o.createElementVNode)("span",{textContent:(0,o.toDisplayString)(e.vwe.level)},null,8,x)])):(0,o.createCommentVNode)("",!0),(0,o.createElementVNode)("div",E,[100===e.vwe.student_completed_percentage&&1!==(null===(ht=null===(vt=e.vwe)||void 0===vt?void 0:vt.student_response)||void 0===ht?void 0:ht.approve)?((0,o.openBlock)(),(0,o.createElementBlock)("span",N,V)):(0,o.createCommentVNode)("",!0),100===e.vwe.student_completed_percentage&&1===(null===(wt=null===(gt=e.vwe)||void 0===gt?void 0:gt.student_response)||void 0===wt?void 0:wt.approve)?((0,o.openBlock)(),(0,o.createElementBlock)("span",B," Completed ")):e.vwe.student_completed_percentage>0&&e.vwe.student_completed_percentage<100?((0,o.openBlock)(),(0,o.createElementBlock)("span",_,(0,o.toDisplayString)(e.vwe.student_completed_percentage)+"% Completed ",1)):(0,o.createCommentVNode)("",!0)])]),(0,o.createElementVNode)("div",C,[((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(e.vwe.tagged,(function(e){return(0,o.openBlock)(),(0,o.createElementBlock)("div",{class:"col-sm-6 fs-6 text-light p-2",key:e.id},[L,(0,o.createTextVNode)(" "+(0,o.toDisplayString)(e.tag_name),1)])})),128))]),e.vwe.foreground_video?((0,o.openBlock)(),(0,o.createElementBlock)("div",S,T)):(0,o.createCommentVNode)("",!0),e.vwe.student_response&&"Submitted"==e.vwe.student_response.status?((0,o.openBlock)(),(0,o.createElementBlock)("div",F,[(0,o.createElementVNode)("div",M,[""===e.vwe.student_response.response_path?((0,o.openBlock)(),(0,o.createElementBlock)("button",{key:0,class:"btn btn-white-custom btn-lg border-1 rounded-0 w-100 p-md-5",style:{"font-size":"14px !important"},onClick:t[0]||(t[0]=function(){return e.viewResponse&&e.viewResponse.apply(e,arguments)})}," View Response ")):((0,o.openBlock)(),(0,o.createElementBlock)("button",I," View Response "))])])):(0,o.createCommentVNode)("",!0),(0,o.createElementVNode)("div",O,[e.vwe.badge&&1===(null===(yt=null===(bt=e.vwe)||void 0===bt?void 0:bt.student_response)||void 0===yt?void 0:yt.approve)&&100===e.vwe.student_completed_percentage?((0,o.openBlock)(),(0,o.createElementBlock)("div",j,[(0,o.createElementVNode)("div",z,[(0,o.createElementVNode)("div",R,[(0,o.createElementVNode)("div",{class:"d-flex align-items-center cursor-pointer","data-bs-toggle":"modal","data-bs-target":"#kt_modal_badge",onClick:t[1]||(t[1]=function(t){return e.openBadgeModal(e.vwe.student_response.badge_key)})},[(0,o.createElementVNode)("img",{src:e.vwe.badge.image_fullpath,alt:e.vwe.badge.name,class:"me-3",width:"25"},null,8,D),H])])])])):(0,o.createCommentVNode)("",!0),1===(null===(xt=null===(kt=e.vwe)||void 0===kt?void 0:kt.student_response)||void 0===xt?void 0:xt.approve)?((0,o.openBlock)(),(0,o.createElementBlock)("div",P,[(0,o.createElementVNode)("div",W,[(0,o.createElementVNode)("div",A,[(0,o.createElementVNode)("div",{class:"d-flex align-items-center cursor-pointer","data-bs-toggle":"modal","data-bs-target":"#kt_modal_viewFile",onClick:t[2]||(t[2]=function(){return e.loadCertificate&&e.loadCertificate.apply(e,arguments)})},G)])])])):(0,o.createCommentVNode)("",!0),e.vwe.student_response&&e.vwe.student_response.feedback&&1===e.vwe.student_response.approve?((0,o.openBlock)(),(0,o.createElementBlock)("div",Z,U)):(0,o.createCommentVNode)("",!0)])])],4),(0,o.createElementVNode)("div",(0,o.mergeProps)({class:{row:e.vwe.student_response.step_responses.length<6,"sticky-top":e.scrolled}},(0,o.toHandlers)(e.handleScroll,!0),{class:"d-flex bg-black module-sections"}),[((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(e.vwe.student_response.step_responses,(function(t){return(0,o.openBlock)(),(0,o.createElementBlock)("div",{key:t.step.id,class:(0,o.normalizeClass)(["text-center p-0",[e.vwe.student_response.step_responses.length<6?"col":"col-6 col-sm-4 col-md-2","bg-black"]])},[(0,o.createElementVNode)("div",q,[(0,o.createElementVNode)("span",Y,[t?((0,o.openBlock)(),(0,o.createElementBlock)("svg",J,X)):((0,o.openBlock)(),(0,o.createElementBlock)("svg",Q,K))]),(0,o.createElementVNode)("p",{class:"m-0 px-5 text-white",innerHTML:t.step.title},null,8,$),(0,o.createElementVNode)("p",ee,[t.step.estimated_time&&t.step.estimated_time.hours?((0,o.openBlock)(),(0,o.createElementBlock)("span",{key:0,textContent:(0,o.toDisplayString)(t.step.estimated_time.hours+"h ")},null,8,te)):(0,o.createCommentVNode)("",!0),t.step.estimated_time&&t.step.estimated_time.minutes?((0,o.openBlock)(),(0,o.createElementBlock)("span",{key:1,textContent:(0,o.toDisplayString)(t.step.estimated_time.minutes+"m")},null,8,ne)):(0,o.createCommentVNode)("",!0),(0,o.createTextVNode)("   ")])])],2)})),128)),(0,o.createElementVNode)("div",{class:(0,o.normalizeClass)(["text-center p-0",[e.vwe.student_response.step_responses.length<6?"col":"col-6 col-sm-4 col-md-2 ","bg-black"]])},[(0,o.createElementVNode)("div",oe,[(0,o.createElementVNode)("span",re,[e.vwe.student_response?((0,o.openBlock)(),(0,o.createElementBlock)("svg",ae,ie)):((0,o.openBlock)(),(0,o.createElementBlock)("svg",le,ce))]),(0,o.createElementVNode)("p",{class:(0,o.normalizeClass)(["m-0",{"text-white":e.vwe.student_response}])}," Final Step ",2),(0,o.createElementVNode)("p",{class:(0,o.normalizeClass)(["m-0",{"text-white":e.vwe.student_response}])},"   ",2)])],2)],16),(0,o.createElementVNode)("div",de,[(0,o.createElementVNode)("div",se,[(0,o.createElementVNode)("div",ue,[(0,o.createElementVNode)("div",me,[(0,o.createElementVNode)("div",pe,[(0,o.createElementVNode)("ul",fe,[((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(e.vwe.student_response.step_responses,(function(t){return(0,o.openBlock)(),(0,o.createElementBlock)("li",{key:t.step.id,class:"nav-item w-100 me-0 mb-md-2"},[(0,o.createElementVNode)("a",{class:(0,o.normalizeClass)(["nav-link w-100 btn btn-flex btn-active-light-secondary btn-active-color-dark",{active:e.selectedStepId===t.step.id}]),onClick:function(n){return e.selectedStepId=t.step.id}},[(0,o.createElementVNode)("span",he,[(0,o.createElementVNode)("span",ge,"Section "+(0,o.toDisplayString)(t.step.number),1),(0,o.createElementVNode)("span",we,(0,o.toDisplayString)(t.step.title.toLowerCase()),1)])],10,ve)])})),128)),(0,o.createElementVNode)("li",be,[(0,o.createElementVNode)("a",{class:(0,o.normalizeClass)(["nav-link w-100 btn btn-flex btn-active-light-secondary btn-active-color-dark",{active:null===e.selectedStepId}]),onClick:t[3]||(t[3]=function(t){return e.selectedStepId=null})},ye,2)])])]),(0,o.createElementVNode)("div",ke,[((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(e.vwe.student_response.step_responses,(function(t){return(0,o.openBlock)(),(0,o.createElementBlock)(o.Fragment,{key:t.step.id},[e.selectedStepId===t.step.id?((0,o.openBlock)(),(0,o.createElementBlock)("div",xe,[(0,o.createVNode)(Et,{to:{name:"task-vwe-section-detail",params:{id:e.currentVwe,sectionid:t.step.number}},custom:""},{default:(0,o.withCtx)((function(e){var t=e.href;return[(0,o.createElementVNode)("a",{href:t,target:"_blank",rel:"noopener",class:"d-flex justify-content-end me-3 position-sticky top-0 bg-white p-2 gap-1"},[Ne,(0,o.createTextVNode)(" View Module ")],8,Ee)]})),_:2},1032,["to"]),t.step.estimated_time&&t.step.estimated_time.hours||t.step.estimated_time&&t.step.estimated_time.minutes?((0,o.openBlock)(),(0,o.createElementBlock)("div",Ve,[Be,t.step.estimated_time&&t.step.estimated_time.hours?((0,o.openBlock)(),(0,o.createElementBlock)("span",{key:0,textContent:(0,o.toDisplayString)(t.step.estimated_time.hours+"h ")},null,8,_e)):(0,o.createCommentVNode)("",!0),t.step.estimated_time&&t.step.estimated_time.minutes?((0,o.openBlock)(),(0,o.createElementBlock)("span",{key:1,textContent:(0,o.toDisplayString)(t.step.estimated_time.minutes+"m")},null,8,Ce)):(0,o.createCommentVNode)("",!0)])):(0,o.createCommentVNode)("",!0),(0,o.createElementVNode)("div",{class:"my-5",innerHTML:t.step.body},null,8,Le),t.step.response&&t.response?((0,o.openBlock)(),(0,o.createElementBlock)("div",{key:1,class:"froala-response mb-5",innerHTML:t.response},null,8,Se)):(0,o.createCommentVNode)("",!0)])):(0,o.createCommentVNode)("",!0),!e.selectedStepId===t.step.id?((0,o.openBlock)(),(0,o.createElementBlock)("div",Te,Fe)):(0,o.createCommentVNode)("",!0)],64)})),128)),null===e.selectedStepId?((0,o.openBlock)(),(0,o.createElementBlock)("div",Me,[Ie,(0,o.createElementVNode)("div",Oe,[e.vwe.student_response&&"Submitted"==e.vwe.student_response.status?((0,o.openBlock)(),(0,o.createElementBlock)("div",je,ze)):(0,o.createCommentVNode)("",!0),e.vwe.response&&e.vwe.student_response.response_path?((0,o.openBlock)(),(0,o.createElementBlock)("div",Re,[(0,o.createElementVNode)("a",{href:"/workexperiencetemplates/responses/"+e.vwe.student_response.id+"/download",class:"btn btn-secondary rounded"},[He,(0,o.createTextVNode)(" Download Response ")],8,De),"1"==e.vwe.student_response.approve?((0,o.openBlock)(),(0,o.createElementBlock)("a",{key:0,href:"/certificate-download/"+e.vwe.student_response.id,target:"_blank",class:"btn btn-secondary rounded"},[We,(0,o.createTextVNode)(" Download Certificate ")],8,Pe)):(0,o.createCommentVNode)("",!0)])):(0,o.createCommentVNode)("",!0)])])):(0,o.createCommentVNode)("",!0)])])])])]),(0,o.createElementVNode)("div",Ae,[(0,o.createElementVNode)("div",Ge,[(0,o.createElementVNode)("div",Ze,[(0,o.createElementVNode)("div",{class:"modal-body bg-black p-1",innerHTML:e.vwe.foreground_video},null,8,Ue)])])]),(0,o.createElementVNode)("div",qe,[(0,o.createElementVNode)("div",{class:(0,o.normalizeClass)(["modal-dialog modal-dialog-centered",e.isFullscreen?"custom-fullscreen-modal":"mw-1200px"])},[(0,o.createElementVNode)("div",Ye,[(0,o.createElementVNode)("div",Je,[Xe,(0,o.createElementVNode)("div",null,[(0,o.createElementVNode)("span",{class:"mx-4 cursor-pointer",onClick:t[4]||(t[4]=function(){return e.toggleFullscreen&&e.toggleFullscreen.apply(e,arguments)})},[e.isFullscreen?((0,o.openBlock)(),(0,o.createElementBlock)("i",Qe)):((0,o.openBlock)(),(0,o.createElementBlock)("i",Ke))]),"1"==e.vwe.student_response.approve?((0,o.openBlock)(),(0,o.createElementBlock)("a",{key:0,href:"/certificate-download/"+e.vwe.student_response.id,target:"_blank",class:"text-secondary mx-2"},et,8,$e)):(0,o.createCommentVNode)("",!0),tt])]),(0,o.createElementVNode)("div",nt,[e.certificateUrl?((0,o.openBlock)(),(0,o.createElementBlock)("iframe",{key:0,src:e.certificateUrl,class:"w-100",style:(0,o.normalizeStyle)({height:e.isFullscreen?"90vh":"80vh",border:"none"}),allowfullscreen:""},null,12,ot)):((0,o.openBlock)(),(0,o.createElementBlock)("p",rt,"Loading..."))])])],2)]),(0,o.createElementVNode)("div",at,[(0,o.createElementVNode)("div",it,[(0,o.createElementVNode)("div",lt,[ct,(0,o.createElementVNode)("div",dt,[(0,o.createElementVNode)("div",st,[(0,o.createElementVNode)("p",{innerHTML:e.vwe.student_response.feedback,class:"text-gray-700"},null,8,ut)])])])])]),(0,o.createVNode)(Nt,{selectedBadge:e.selectedBadge,onShareBadge:e.openShareBadgeModal},null,8,["selectedBadge","onShareBadge"]),(0,o.createVNode)(Vt,{modalSrc:e.modalSrc,downloadUrl:e.downloadUrl},null,8,["modalSrc","downloadUrl"])],64)}]])}}]);