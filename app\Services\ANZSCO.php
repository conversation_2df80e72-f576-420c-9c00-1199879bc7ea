<?php

namespace App\Services;

use App\AnzscoOccupation;
use App\CoreCompetencyAnchorValue;
use App\Lesson;
use App\Lessonstep;
use App\SkillstrainingTemplate;
use App\SpecialistCluster;
use App\SpecialistClusterFamily;
use App\SpecialistTask;
use App\Standard;
use App\Step;
use App\TechnologyTool;
use App\User;
use App\WewStep;
use App\WorkexperienceTemplate;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class ANZSCO {
    /**
     * Save similar anzsco fields data for modules
     *
     * @param \Illuminate\Database\Eloquent\Model $module
     * @param array $attachables
     * @return void
     */
    public function saveData ($module, $data)
    {
        $module->update(Arr::only($data, [
            'module_type_id'
        ]));

        $this->syncTagsToModule($module, $data['attachables'] ?? []);
    }

    /**
     * Attach/Sync ANZSCO tags to different modules
     *
     * @param \Illuminate\Database\Eloquent\Model $module
     * @param array $attachables
     * @return void
     */
    public function syncTagsToModule($module, $attachables = [])
    {
        $attachableTable = $module->getAnzscoAttachableTable();
        $attachableParentKey = $module->getAnzscoParentForeignKey();
        $now = now();

        // Get current attachments as a simple array of "type:id" strings
        $current = DB::table($attachableTable)
            ->where($attachableParentKey, $module->id)
            ->selectRaw("CONCAT(attachable_type, ':', attachable_id) as composite")
            ->pluck('composite')
            ->all();

        // Prepare new attachments and detect changes in a single pass
        $new = [];
        $changes = ['insert' => [], 'delete' => []];

        foreach ($attachables as $model => $ids) {
            foreach ($ids as $id) {
                $composite = "$model:$id";
                $new[] = $composite;

                if (!in_array($composite, $current)) {
                    $changes['insert'][] = [
                        $attachableParentKey => $module->id,
                        'attachable_type' => $model,
                        'attachable_id' => $id,
                        'created_at' => $now,
                        'updated_at' => $now,
                    ];
                }
            }
        }

        // Find items to delete (present in current but not in new)
        $changes['delete'] = array_diff($current, $new);

        // Execute changes in a transaction
        if (!empty($changes['delete']) || !empty($changes['insert'])) {
            DB::transaction(function () use ($attachableTable, $attachableParentKey, $module, $changes) {
                // Process deletions if needed
                if (!empty($changes['delete'])) {
                    $deleteQuery = DB::table($attachableTable)->where($attachableParentKey, $module->id);

                    $first = true;

                    foreach ($changes['delete'] as $composite) {
                        [$type, $id] = explode(':', $composite);
                        
                        if ($first) {
                            $deleteQuery->where(function ($q) use ($type, $id) {
                                $q->where('attachable_type', $type)
                                    ->where('attachable_id', $id);
                            });

                            $first = false;
                        } else {
                            $deleteQuery->orWhere(function ($q) use ($type, $id) {
                                $q->where('attachable_type', $type)
                                    ->where('attachable_id', $id);
                            });
                        }
                    }

                    $deleteQuery->delete();
                }

                // Process insertions if needed
                if (!empty($changes['insert'])) {
                    DB::table($attachableTable)->insert($changes['insert']);
                }
            });
        }
    }


    /**
     * Attach/Sync ANZSCO tags to different modules
     * 
     * WARNING: This perform deletion of existing tags and then insert new ones
     * 
     * @param \Illuminate\Database\Eloquent\Model $module
     * @param array $attachables
     * @return void
     */
    public function syncTagsToModuleComplete($module, $attachables = [])
    {
        $attachableTable = $module->getAnzscoAttachableTable();
        $attachableParentKey = $module->getAnzscoParentForeignKey();

        $insertArr = [];

        DB::table($attachableTable)->where($attachableParentKey, $module->id)->delete();

        if (count($attachables)) {
            foreach ($attachables as $model => $arr) {
                foreach ($arr as $id) {
                    array_push($insertArr, [
                        $attachableParentKey => $module->id,
                        'attachable_type' => $model,
                        'attachable_id' => $id,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            }
        }

        if (count($insertArr) > 0) {
            DB::table($attachableTable)->insert($insertArr);
        }
    }

    /**
     * Job Revommendation based on User Profiling (Videos watched)
     *
     * NOTE: This code was written using a Rscript file 
     * 
     * @param integer $userId
     * @return mixed
     */
    public function userProfilingJobRecommendation (int $userId)
    {
        $masterTableQuery = DB::table('occupation_specialist_tasks')
                            ->join('anzsco_occupations', 'occupation_specialist_tasks.anzsco_occupation_id', '=', 'anzsco_occupations.id')
                            ->leftJoin('specialist_tasks', 'occupation_specialist_tasks.specialist_task_id', '=', 'specialist_tasks.id')
                            ->leftJoin('specialist_clusters', 'specialist_tasks.specialist_cluster_id', '=', 'specialist_clusters.id')
                            ->leftJoin('specialist_cluster_families', 'specialist_tasks.specialist_cluster_family_id', '=', 'specialist_cluster_families.id')
                            ->leftJoin('skills_priorities', 'occupation_specialist_tasks.anzsco_occupation_id', '=', 'skills_priorities.id')
                            // Find Jobs with core competencies attached to them, don't recommend otherwise
                            ->whereExists(function ($query) {
                                $query->select(DB::raw(1))
                                    ->from('occupation_core_competencies')
                                    ->whereColumn('anzsco_occupations.id', 'occupation_core_competencies.anzsco_occupation_id');
                            });


        $profilingPostResponses = DB::table('profiling_post_responses as ppr')
                                    ->join('profiling_posts as pp', 'ppr.profiling_post_id', '=', 'pp.id')
                                    ->where('ppr.user_id', $userId)
                                    ->select([
                                        'ppr.user_id',
                                        'ppr.profiling_post_id',
                                        // 'pp.title',
                                        // 'ppr.like',
                                        // 'ppr.dislike',
                                        // 'ppr.timespent',
                                        // 'ppr.watched_elapsed_time',
                                        // 'ppr.duration',
                                        // 'scf.id as scf_id',
                                        // 'sc.id as sc_id',
                                        // 'st.id as st_id',
                                        // Handling `dislike`: If dislike != 0, set to -100, else 0
                                        DB::raw("CASE WHEN ppr.dislike = 0 THEN 0 ELSE -100 END AS dislike_score"),
                                        // Handling `watch_percent_video`
                                        DB::raw("CASE 
                                                    WHEN ppr.duration = 0 THEN 0 
                                                    ELSE COALESCE((ppr.watched_elapsed_time / ppr.duration) * 100, 0) 
                                                END AS watch_percent_video"),
                                        // Handling `keep_watching`
                                        DB::raw("CASE 
                                                    WHEN ppr.duration = 0 THEN 0 
                                                    ELSE COALESCE((ppr.timespent / ppr.duration) * 100, 0) 
                                                END AS keep_watching"),
                                        // Calculating the final `score`
                                        DB::raw("((ppr.like * 100) * 2) + 
                                                (CASE WHEN ppr.dislike = 0 THEN 0 ELSE -100 END) + 
                                                (CASE WHEN ppr.duration = 0 THEN 0 ELSE COALESCE((ppr.timespent / ppr.duration) * 100, 0) END) + 
                                                (CASE WHEN ppr.duration = 0 THEN 0 ELSE COALESCE((ppr.watched_elapsed_time / ppr.duration) * 100, 0) END) 
                                                AS score")
                                    ])
                                    ->orderByDesc('score')
                                    ->orderByDesc('ppr.timespent')
                                    ->limit(10)
                                    ->get();

        if ($profilingPostResponses->count() == 0) {
            return collect();
        }

        $recommendedJobs = collect();

        foreach ($profilingPostResponses as $ppr) {
            $masterJobs = (clone $masterTableQuery)
                        ->select(
                            'occupation_specialist_tasks.anzsco_occupation_id',
                            // 'occupation_specialist_tasks.specialist_task_id',
                            // 'occupation_specialist_tasks.time_spent_on_task',
                            // 'occupation_specialist_tasks.emerging_or_trending',
                            // 'occupation_specialist_tasks.time_spent_on_cluster',
                            // 'occupation_specialist_tasks.time_spent_on_family',
                            // 'anzsco_occupations.anzsco_code',
                            'anzsco_occupations.anzsco_title',
                            // 'anzsco_occupations.anzsco_description',
                            // 'specialist_tasks.name as SpecialistTask',
                            // 'specialist_clusters.name as SpecialistCluster',
                            // 'specialist_cluster_families.name as ClusterFamily',
                            'skills_priorities.national_shortage_rating'
                        )
                        ->where(function($query) use($ppr) {
                            return $query->whereIn('specialist_tasks.id', function(Builder $q) use($ppr) {
                                            return $q->select('attachable_id')
                                                    ->from('profiling_posts_attachables')
                                                    ->where('profiling_posts_attachables.attachable_type', SpecialistTask::class)
                                                    ->where('profiling_posts_attachables.profiling_post_id', $ppr->profiling_post_id);
                                        })
                                        ->whereIn('specialist_clusters.id', function(Builder $q) use($ppr) {
                                            return $q->select('attachable_id')
                                                    ->from('profiling_posts_attachables')
                                                    ->where('profiling_posts_attachables.attachable_type', SpecialistCluster::class)
                                                    ->where('profiling_posts_attachables.profiling_post_id', $ppr->profiling_post_id);
                                        })
                                        ->whereIn('specialist_cluster_families.id', function(Builder $q) use($ppr) {
                                            return $q->select('attachable_id')
                                                    ->from('profiling_posts_attachables')
                                                    ->where('profiling_posts_attachables.attachable_type', SpecialistClusterFamily::class)
                                                    ->where('profiling_posts_attachables.profiling_post_id', $ppr->profiling_post_id);
                                        });

                        })
                        ->whereNotNull('skills_priorities.national_shortage_rating') // Equivalent to `filter(!is.na(national_shortage_rating))`
                        ->orderByDesc('occupation_specialist_tasks.time_spent_on_family') // Sort by most time spent
                        ->distinct()
                        ->limit(3)
                        ->get();

            $recommendedJobs = $recommendedJobs->merge($masterJobs);
        }   
        
        // Add label
        $countRecommendedJobs = $recommendedJobs->count();

        if ($countRecommendedJobs == 0) {
            return collect();
        }

        $recommendedJobs = $recommendedJobs->unique();
            
        $recommendedJobs = $recommendedJobs->values()->map(function ($job, $index) use ($countRecommendedJobs) {
            // Determine position labels dynamically
            $job->position = match (true) {
                $index < ceil($countRecommendedJobs / 3) => 'high',
                $index < ceil(2 * $countRecommendedJobs / 3) => 'medium',
                default => 'low',
            };
    
            return $job;
        });

        // Filter for the similar ones
        $filteredJobs = collect();
        $processedTitles = [];

        foreach ($recommendedJobs as $job) {
            $isSimilar = false;

            foreach ($processedTitles as $existingJob) {
                if (levenshtein($existingJob->anzsco_title, $job->anzsco_title) < 3) {
                    $isSimilar = true;
                    break;
                }
            }

            if (!$isSimilar) {
                $filteredJobs->push($job);
                $processedTitles[] = $job;
            }
        }

        $recommendedJobs = $filteredJobs;

        // Add sub occupations/jobs
        // Filter Specialist cluster by recommended jobs in main query
        $specialistClusters = (clone $masterTableQuery)
                            ->whereIn('occupation_specialist_tasks.anzsco_occupation_id', $recommendedJobs->pluck('anzsco_occupation_id'))
                            ->select('occupation_specialist_tasks.anzsco_occupation_id', 'specialist_clusters.id as sc_id')
                            ->orderByDesc('occupation_specialist_tasks.time_spent_on_cluster')
                            ->distinct()
                            ->get()
                            ->groupBy('anzsco_occupation_id')
                            ->map(function ($clusters) {
                                return $clusters->pluck('sc_id')->take(5);
                            });

        $subOccupations = (clone $masterTableQuery)
                            ->whereIn('specialist_clusters.id', $specialistClusters->flatten()->unique())
                            ->whereNotIn('occupation_specialist_tasks.anzsco_occupation_id', $recommendedJobs->pluck('anzsco_occupation_id'))
                            ->selectRaw('
                                specialist_clusters.id as sc_id, 
                                anzsco_occupations.anzsco_title, 
                                occupation_specialist_tasks.anzsco_occupation_id, 
                                time_spent_on_cluster
                            ')
                            ->orderByDesc('time_spent_on_cluster')
                            ->distinct()
                            ->get()
                            ->groupBy('sc_id');

        $recommendedJobsWithSubs = $recommendedJobs->map(function ($job) use ($specialistClusters, $subOccupations) {
                                                        $clusters = $specialistClusters->get($job->anzsco_occupation_id, collect());

                                                        $subJobs = $clusters->map(function ($cluster) use ($subOccupations) {
                                                            return $subOccupations->get($cluster, collect())->first();
                                                        })
                                                        ->sortByDesc('time_spent_on_cluster')
                                                        ->values()
                                                        ->toArray();
                                                    
                                                        $job->sub_occupations = $subJobs;

                                                        return $job;
                                                    });

        return $recommendedJobsWithSubs;
    }

    /**
     * Core Competency Anchor Values Chart Data
     *
     * @param \App\Models\AnzscoOccupation $anzscoOccupation
     * @param mixed $user
     * @return array
     */
    public function getCoreCompetencyAnchorValuesChartData(AnzscoOccupation $anzscoOccupation, $user)
    {
        $CCAV = $anzscoOccupation->coreCompetencyAnchorValues;

        $data = [];
        
        foreach ($CCAV as $key => $item) {
            array_push($data, [
                'competency' => $item->coreCompetency?->name,
                'score_required' => $item->score,
                // 'pictureSettings' => [
                //     'src' => Storage::url($user->image). '?id=' . auth()->id()
                // ]
            ]);
        }

        return $data;
    }

    /**
     * Get Specialist tree chart data
     *
     * @param \App\Models\AnzscoOccupation $anzscoOccupation
     * @return mixed
     */
    public function getSpecialistTaskChartData(AnzscoOccupation $anzscoOccupation)
    {
        $specialistTasks = $anzscoOccupation->specialistTasks->groupBy('clusterFamily.name');
        
        $data = [];

        foreach ($specialistTasks as $key => $clusterFamily) {
            $data[$key] = $clusterFamily->pluck('pivot.time_spent_on_task','name')->toArray();
        }

        return $data;
    }

    /**
     * Get Skills Match data
     *
     * @param \App\Models\AnzscoOccupation $anzscoOccupation
     * @param mixed $user
     * @return array
     */
    public function userCoreCompetencyScoresComparision(AnzscoOccupation $anzscoOccupation, mixed $user)
    {
        $CCAV = $anzscoOccupation->coreCompetencyAnchorValues;

        $data = [];

        $userGainedCCAVScores = $this->getUserGainedCCAVScores($user->id);
        
        if($CCAV->count() > 0) {
            foreach ($CCAV as $key => $item) {
                array_push($data, [
                    'competency' => $item->coreCompetency?->name,
                    'score_required' => $item->score,
                    'score_gained' => $userGainedCCAVScores->firstWhere('core_competency_id', $item->core_competency_id)?->max_score ?: 0,
                    // 'pictureSettings' => [
                    //     'src' => Storage::url($user->image) . '?id=' . auth()->id()
                    // ]
                ]);
            }
        }

        return $data;
    }

    /**
     * Get Table HTML for particular Occupation's technology tools data 
     *
     * @param \App\Models\AnzscoOccupation $anzscoOccupation
     * @return mixed
     */
    public function getTechnologyToolsTableData(AnzscoOccupation $anzscoOccupation): mixed
    {
        return view('partials.anzsco.technology_tools_table',[
            'occupation' => $anzscoOccupation
        ])->render();
    }

    public function getPathsCCLaunchData()
    {
        return collect([
            ['core_competency_av_id_new' => 3,  'proficiency_level' => 'Basic',       'name' => 'Digital engagement'],
            ['core_competency_av_id_new' => 4,  'proficiency_level' => 'Intermediate', 'name' => 'Digital engagement'],
            ['core_competency_av_id_new' => 8,  'proficiency_level' => 'High',        'name' => 'Digital engagement'],
            ['core_competency_av_id_new' => 17, 'proficiency_level' => 'Intermediate', 'name' => 'Initiative and innovation'],
            ['core_competency_av_id_new' => 18, 'proficiency_level' => 'High',        'name' => 'Initiative and innovation'],
            ['core_competency_av_id_new' => 23, 'proficiency_level' => 'Basic',       'name' => 'Learning'],
            ['core_competency_av_id_new' => 26, 'proficiency_level' => 'Intermediate', 'name' => 'Learning'],
            ['core_competency_av_id_new' => 28, 'proficiency_level' => 'High',        'name' => 'Learning'],
            ['core_competency_av_id_new' => 33, 'proficiency_level' => 'Basic',       'name' => 'Numeracy'],
            ['core_competency_av_id_new' => 34, 'proficiency_level' => 'Intermediate', 'name' => 'Numeracy'],
            ['core_competency_av_id_new' => 38, 'proficiency_level' => 'High',        'name' => 'Numeracy'],
            ['core_competency_av_id_new' => 47, 'proficiency_level' => 'Intermediate', 'name' => 'Oral communication'],
            ['core_competency_av_id_new' => 48, 'proficiency_level' => 'High',        'name' => 'Oral communication'],
            ['core_competency_av_id_new' => 56, 'proficiency_level' => 'Intermediate', 'name' => 'Planning and organising'],
            ['core_competency_av_id_new' => 59, 'proficiency_level' => 'High',        'name' => 'Planning and organising'],
            ['core_competency_av_id_new' => 63, 'proficiency_level' => 'Basic',       'name' => 'Problem solving'],
            ['core_competency_av_id_new' => 65, 'proficiency_level' => 'Intermediate', 'name' => 'Problem solving'],
            ['core_competency_av_id_new' => 68, 'proficiency_level' => 'High',        'name' => 'Problem solving'],
            ['core_competency_av_id_new' => 75, 'proficiency_level' => 'Intermediate', 'name' => 'Reading'],
            ['core_competency_av_id_new' => 78, 'proficiency_level' => 'High',        'name' => 'Reading'],
            ['core_competency_av_id_new' => 83, 'proficiency_level' => 'Basic',       'name' => 'Teamwork'],
            ['core_competency_av_id_new' => 85, 'proficiency_level' => 'Intermediate', 'name' => 'Teamwork'],
            ['core_competency_av_id_new' => 88, 'proficiency_level' => 'High',        'name' => 'Teamwork'],
            ['core_competency_av_id_new' => 93, 'proficiency_level' => 'Basic',       'name' => 'Writing'],
            ['core_competency_av_id_new' => 95, 'proficiency_level' => 'Intermediate', 'name' => 'Writing'],
            ['core_competency_av_id_new' => 98, 'proficiency_level' => 'High',        'name' => 'Writing'],
        ]);
    }

    public function getModulesSuggestionsForSelectedJobs($userId)
    {
        // DB::enableQueryLog();
        // $startMemory = memory_get_usage(true);
        // $start = microtime(true);

        $ccLaunch = $this->getPathsCCLaunchData();
        
        // Fetch user-selected jobs
        $selectedJobs = AnzscoOccupation::query()
        ->with([
            'coreCompetencyAnchorValues.coreCompetency',
            'technologyTools:id',
            'specialistTasks' => function($query) {
                $query->select(
                    'specialist_tasks.id',
                    'specialist_tasks.specialist_cluster_id',
                    'specialist_tasks.specialist_cluster_family_id',
                )
                ->orderByDesc('occupation_specialist_tasks.time_spent_on_task');
            },
            // 'specialistTasks:id,specialist_cluster_id,specialist_cluster_family_id',
            'specialistTasks.cluster:id',
            'specialistTasks.clusterFamily:id',
        ])
        ->join('user_selected_occupations as uso', 'anzsco_occupations.id', '=', 'uso.anzsco_occupation_id')
        ->where('uso.user_id', $userId)
        ->select(
            'anzsco_occupations.anzsco_title',
            'anzsco_occupations.id',
            'uso.user_id',
        )
        ->distinct()
        ->get()
        ->each(function ($selectedJob) use ($ccLaunch) {
            // Core Competencies
            $selectedJob->coreCompetencyAnchorValues->transform(function ($ccav) use ($ccLaunch) {
                $launchMatch = $ccLaunch->first(fn($launchItem) =>
                    $launchItem['name'] == $ccav->coreCompetency?->name &&
                    $launchItem['proficiency_level'] == $ccav->proficiency_level
                );
    
                $ccav->core_competency_av_id_new = $launchMatch['core_competency_av_id_new'] ?? null;
    
                return $ccav;
            });

            // $selectedJob->setRelation('specialistTasks', $selectedJob->specialistTasks->take(5));
        });

        if ($selectedJobs->count() == 0) {
            return [
                'success' => false,
                'message' => 'Please select a job before proceeding !'
            ];
        }
    
        $userSelectedOccupationIds = $selectedJobs->pluck('id')->unique()->toArray();

        $ccavIdsGrouped = $selectedJobs->mapWithKeys(function ($job) {
            return [
                $job->id => $job->coreCompetencyAnchorValues->pluck('core_competency_av_id_new')->toArray()
            ];
        })->toArray();

        $techToolIdsGrouped = $selectedJobs->mapWithKeys(function ($job) {
            return [
                $job->id => $job->technologyTools->pluck('id')->toArray()
            ];
        })->toArray();

        $specialistTaskIdsGrouped = $selectedJobs->mapWithKeys(function ($job) {
            return [
                $job->id => $job->specialistTasks->take(5)->pluck('id')->toArray()
            ];
        })->toArray();

        $clusterIdsGrouped = $selectedJobs->mapWithKeys(function($job) {
            return [
                $job->id => $job->specialistTasks
                                ->sortByDesc('pivot.time_spent_on_cluster')
                                ->pluck('cluster.id')
                                ->filter()
                                ->unique()
                                ->take(3)
                                ->toArray()
            ];
        })->toArray();

        $clusterFamilyIdsGrouped = $selectedJobs->mapWithKeys(function($job) { 
            return [
                $job->id => $job->specialistTasks
                                ->sortByDesc('pivot.time_spent_on_family')
                                ->pluck('clusterFamily.id')
                                ->filter()
                                ->unique()
                                ->take(3)
                                ->toArray()
            ];
        })->toArray();
        
        $ccavIds = $selectedJobs->flatMap(function ($job) {
                                    return $job->coreCompetencyAnchorValues->pluck('core_competency_av_id_new');
                                })
                                ->filter()
                                ->unique()
                                ->values()
                                ->toArray();

                                

        $techToolIds = $selectedJobs->flatMap(function ($job) {
                                        return $job->technologyTools->pluck('id');
                                    })
                                    ->unique()
                                    ->values()
                                    ->toArray();

        $specialistTaskIds = $selectedJobs->flatMap(function ($job) {
                                        return $job->specialistTasks->take(5)->pluck('id');
                                    })
                                    ->unique()
                                    ->values()
                                    ->toArray();

        $clusterIds = $selectedJobs->flatMap(function($job) {
                                        return $job->specialistTasks
                                                    ->sortByDesc('pivot.time_spent_on_family')
                                                    ->pluck('cluster.id')
                                                    ->filter()
                                                    ->unique()
                                                    ->take(3);
                                    })
                                    ->unique()
                                    ->values()
                                    ->toArray();
        
        $clusterFamilyIds = $selectedJobs->flatMap(function($job) { 
                                        return $job->specialistTasks
                                                    ->sortByDesc('pivot.time_spent_on_family')
                                                    ->pluck('clusterFamily.id')
                                                    ->filter()
                                                    ->unique()
                                                    ->take(3);
                                    })
                                    ->unique()
                                    ->values()
                                    ->toArray();

        $commonModuleRelations = [
            'anzscoOccupations:id',
            'coreCompetencies:id',
            'coreCompetencyAnchorValues:id',
            'specialistTasks:id,specialist_cluster_id,specialist_cluster_family_id',
            'specialistClusterFamilies:id',
            'specialistClusters:id',
            'technologyTools:id',
            'userResponse',
            'userScormModuleResult'
        ];

        $filterConditions = function ($query) use ($userSelectedOccupationIds, $ccavIds, $techToolIds, $clusterFamilyIds, $clusterIds, $specialistTaskIds) {
            return $query->whereHas('coreCompetencyAnchorValues', function($qw) use($ccavIds) {
                        return $qw->whereIn('id', $ccavIds);
                    })
                    ->orWhereHas('technologyTools', function($qw) use($techToolIds) {
                        return $qw->whereIn('id', $techToolIds);
                    })
                    ->orWhereHas('anzscoOccupations', function($qw) use($userSelectedOccupationIds) {
                        return $qw->whereIn('id', $userSelectedOccupationIds);
                    })
                    ->orWhereHas('specialistClusterFamilies', function($qw) use($clusterFamilyIds) {
                        return $qw->whereIn('id', $clusterFamilyIds);
                    })
                    ->orWhereHas('specialistClusters', function($qw) use($clusterIds) {
                        return $qw->whereIn('id', $clusterIds);
                    })
                    ->orWhereHas('specialistTasks', function($qw) use($specialistTaskIds) {
                        return $qw->whereIn('id', $specialistTaskIds);
                    });
        };

        $lessons = Lesson::with($commonModuleRelations)
                            ->published()
                            // ->when(Auth::user()->isStudent() && Auth::user()->profile?->standard_id, function ($query) {
                            //     return $query->forStudents()->whereHas('years', function ($q) {
                            //         $q->where('standard_id', Auth::user()->profile->standard_id);
                            //     });
                            // })
                            // ->when(Auth::user()->isTeacher(), function ($query) {
                            //     return $query->whereHas('years', function ($q) {
                            //         $q->whereIn('standard_id', Standard::pluck('id'));
                            //     });
                            // })
                            ->where($filterConditions)
                            ->get();

        $skillsTrainingTemplates = SkillstrainingTemplate::with($commonModuleRelations)
                                                        ->published()
                                                        // ->when(Auth::user()->isStudent() && Auth::user()->profile?->standard_id, function ($query) {
                                                        //     return $query->forStudents()->whereHas('years', function ($q) {
                                                        //         $q->where('standard_id', Auth::user()->profile->standard_id);
                                                        //     });
                                                        // })
                                                        // ->when(Auth::user()->isTeacher(), function ($query) {
                                                        //     return $query->whereHas('years', function ($q) {
                                                        //         $q->whereIn('standard_id', Standard::pluck('id'));
                                                        //     });
                                                        // })
                                                        ->where($filterConditions)
                                                        ->get();

        $workExperienceTemplates = WorkexperienceTemplate::with($commonModuleRelations)
                                                        ->published()
                                                        ->where($filterConditions)
                                                        ->get();
        
        $selectedJobs->map(function($job) use(
                $lessons, 
                $skillsTrainingTemplates, 
                $workExperienceTemplates, 
                $ccavIdsGrouped, 
                $techToolIdsGrouped, 
                $specialistTaskIdsGrouped,
                $clusterIdsGrouped,
                $clusterFamilyIdsGrouped
            ) {
                $jobCcavIds = $ccavIdsGrouped[$job->id] ?? [];
                $jobTechToolIds = $techToolIdsGrouped[$job->id] ?? [];
                $jobSpecialistTaskIds = $specialistTaskIdsGrouped[$job->id] ?? [];
                $jobClusterIds = $clusterIdsGrouped[$job->id] ?? [];
                $jobClusterFamilyIds = $clusterFamilyIdsGrouped[$job->id] ?? [];
            
                $filterByCcav = fn($item) => $item->coreCompetencyAnchorValues->pluck('id')->intersect($jobCcavIds)->isNotEmpty();
                $filterByTechTools = fn($item) => $item->technologyTools->pluck('id')->intersect($jobTechToolIds)->isNotEmpty();
                // $filterByModuleCompletion = function ($item) {
                //     $scormCompleted = $item->userScormModuleResult && in_array($item->userScormModuleResult->lesson_status, ['completed', 'passed']);

                //     $responseSubmitted = $item->userResponse?->status === 'Submitted';

                //     return ($scormCompleted && $responseSubmitted) || (!$item->userScormModuleResult && $responseSubmitted);
                // };

                $filterBySpecialistTasks = fn($item) => $item->specialistTasks->pluck('id')->intersect($jobSpecialistTaskIds)->isNotEmpty();
                $filterByClusters = fn($item) => $item->specialistClusters->pluck('id')->intersect($jobClusterIds)->isNotEmpty();
                $filterByClusterFamilies = fn($item) => $item->specialistClusterFamilies->pluck('id')->intersect($jobClusterFamilyIds)->isNotEmpty();

                $job->can_continue_vwe = true; 
                $job->core_competency_modules = [
                    'lessons' => $lessons->filter($filterByCcav),
                    'skillsTrainingTemplates' => $skillsTrainingTemplates->filter($filterByCcav),
                    'workExperienceTemplates' => $workExperienceTemplates->filter($filterByCcav),
                ];

                // foreach ($job->core_competency_modules as $moduleType => $ccModules) {
                //     $totalCCModules = $ccModules->count(); 

                //     if ($totalCCModules === 0 || $totalCCModules !== $ccModules->filter($filterByModuleCompletion)->count()) {
                //         $job->can_continue_vwe = false; 
                //         break;
                //     }
                // }

                $job->technology_tool_modules = [
                    'lessons' => $lessons->filter($filterByTechTools),
                    'skillsTrainingTemplates' => $skillsTrainingTemplates->filter($filterByTechTools),
                    'workExperienceTemplates' => $workExperienceTemplates->filter($filterByTechTools),
                ];


                $job->primary_vwe_modules = [
                    'lessons' => $lessons->filter(function($item) use (
                        $filterBySpecialistTasks,
                        $filterByClusters,
                        $filterByClusterFamilies
                    ) {
                        return $filterBySpecialistTasks($item) || 
                            $filterByClusters($item) || 
                            $filterByClusterFamilies($item);
                    }),
                    
                    'skillsTrainingTemplates' => $skillsTrainingTemplates->filter(function($item) use (
                        $filterBySpecialistTasks,
                        $filterByClusters,
                        $filterByClusterFamilies
                    ) {
                        return $filterBySpecialistTasks($item) || 
                            $filterByClusters($item) || 
                            $filterByClusterFamilies($item);
                    }),
                    
                    'workExperienceTemplates' => $workExperienceTemplates->filter(function($item) use (
                        $filterBySpecialistTasks,
                        $filterByClusters,
                        $filterByClusterFamilies
                    ) {
                        return $filterBySpecialistTasks($item) || 
                            $filterByClusters($item) || 
                            $filterByClusterFamilies($item);
                    }),
                ];

                // COUNT 
                $job->core_competency_modules_counts = $this->getCountsOfRecommendedModules($job->core_competency_modules);
                $job->technology_tool_modules_counts = $this->getCountsOfRecommendedModules($job->technology_tool_modules);
                $job->primary_vwe_modules_counts = $this->getCountsOfRecommendedModules($job->primary_vwe_modules);
                
                $job->total_modules_count = $job->core_competency_modules_counts['total'] + 
                                            $job->technology_tool_modules_counts['total'] + 
                                            $job->primary_vwe_modules_counts['total'];

                return $job;
            });

        // $endTime = microtime(true) - $start;
        // $endMemory = memory_get_usage(true);;

        // dd(
        //     $lessons, 
        //     $selectedJobs->toArray(), 
        //     $endTime, 
        //     collect(DB::getQueryLog())->sum('time'),
        //     round(($endMemory - $startMemory) / 1048576, 2) . ' MB'
        // );

        // $results = [];
        
        // foreach ($selectedJobs as $job) {
        //     $recommendation = $paths->courseFinder(
        //         $job->anzsco_occupation_id, 
        //         $userId,
        //         $jobsCCMasterTable,
        //         $jobsTTMasterTable,
        //         $jobsClustersMasterTable,
        //         $ccLaunch
        //     );
            
        //     $results[$job->anzsco_occupation_id] = $recommendation;
        // }

        return [
            'success' => true,
            'data' => $selectedJobs,
        ];
    }

    public function getPathsModulesToDo ($userId, $filters = [])
    {
        $res = $this->getModulesSuggestionsForSelectedJobs($userId);

        if (!$res['success'] || empty($res['data'])) {
            return collect();
        }

        $selectedJobsWithModules = $res['data'];

        $modules = collect([
            'lessons' => collect(),
            'skillsTrainingTemplates' => collect(),
            'workExperienceTemplates' => collect(),
        ]);

        foreach ($selectedJobsWithModules as $selectedJob) {
            $groups = [
                $selectedJob->core_competency_modules,
                $selectedJob->technology_tool_modules,
                $selectedJob->primary_vwe_modules,
            ];

            foreach ($groups as $group) {
                foreach ($group as $type => $moduleCollection) {
                    if (
                        empty($filters['moduleType']) ||
                        empty($filters['moduleId'])
                    ) {
                        $matched = true;
                    } else {
                        $matched = collect($moduleCollection)->contains(fn($module) =>
                            $filters['moduleType'] === $type && $filters['moduleId'] == $module->id
                        );
                    }

                    if ($matched) {
                        $flatGroups = collect($groups);
                        $modules['lessons'] = $flatGroups->flatMap(function ($item) {
                            return $item['lessons']?->filter(function($m) {
                                return $m->userResponse?->status != 'Submitted';
                            }) ?? [];
                        });

                        $modules['skillsTrainingTemplates'] = $flatGroups->flatMap(function ($item) {
                            return $item['skillsTrainingTemplates']?->filter(function($m) {
                                return $m->userResponse?->status != 'Submitted';
                            }) ?? [];
                        });

                        $modules['workExperienceTemplates'] = $flatGroups->flatMap(function ($item) {
                            return $item['workExperienceTemplates']?->filter(function($m) {
                                return $m->userResponse?->status != 'Submitted';
                            }) ?? [];
                        });
                        break 2; // Exit both inner loops
                    }
                }
            }
        }

        $modules = $modules->map(fn($collection) => $collection->unique('id')->values());

        return $modules;
    }

    public function getCountsOfRecommendedModules($modules)
    {
        $counts = array_map(function ($collection) {
            return $collection->count();
        }, $modules);

        $counts['total'] = array_sum($counts);

        return $counts;
    }

    
    public function getUserGainedCCAVScores ($userId = null)
    {
        $userId = $userId ?: auth()->id();

        // Lessons
        $oneStepPerLesson = DB::table('lessonsteps as ls')
        ->select('ls.lesson_id', DB::raw('MIN(ls.id) as lesson_step_id'))
        ->where('ls.is_scorm', true)
        ->where('ls.is_scorm_scoring', true)
        ->groupBy('ls.lesson_id');

        $lessonQuery = DB::table('lessonables as l')
            ->join('lessons as les', 'les.id', '=', 'l.lesson_id')
            ->join('lessonresponses as lr', function ($join) use ($userId) {
                $join->on('lr.lesson_id', '=', 'les.id')
                    ->where('lr.status', 'Submitted')
                    ->where('lr.student_id', $userId);
            })
            ->join('core_competency_anchor_values as ccav', 'ccav.id', '=', 'l.attachable_id')
            ->leftJoinSub($oneStepPerLesson, 'scorm_step', function ($join) {
                $join->on('scorm_step.lesson_id', '=', 'les.id');
            })
            ->leftJoin('scorm_results as sr', function ($join) use ($userId) {
                $join->on('sr.resultable_id', '=', 'scorm_step.lesson_step_id')
                    ->where('sr.resultable_type', Lessonstep::class)
                    ->where('sr.user_id', $userId);
            })
            ->where(function($q) {
                $q->whereIn('sr.lesson_status', ['completed', 'passed'])
                ->orWhereNull('scorm_step.lesson_step_id');
            })
            ->where('l.attachable_type', CoreCompetencyAnchorValue::class)
            ->select('ccav.core_competency_id', 'ccav.score');

        // Work Experience
        $oneStepPerWE = DB::table('steps as we_s')
        ->select('we_s.template_id', DB::raw('MIN(we_s.id) as we_step_id'))
        ->where('we_s.is_scorm', true)
        ->where('we_s.is_scorm_scoring', true)
        ->groupBy('we_s.template_id');

        $workExpQuery = DB::table('workexperience_templateables as w')
            ->join('workexperience_templates as wet', 'wet.id', '=', 'w.workexperience_template_id')
            ->join('workexperience_responses as wr', function ($join) use ($userId) {
                $join->on('wr.template_id', '=', 'wet.id')
                    ->where('wr.status', 'Submitted')
                    ->where('wr.student_id', $userId);
            })
            ->join('core_competency_anchor_values as ccav', 'ccav.id', '=', 'w.attachable_id')
            ->leftJoinSub($oneStepPerWE, 'scorm_step', function ($join) {
                $join->on('scorm_step.template_id', '=', 'wet.id');
            })
            ->leftJoin('scorm_results as sr', function ($join) use ($userId) {
                $join->on('sr.resultable_id', '=', 'scorm_step.we_step_id')
                    ->where('sr.resultable_type', Step::class)
                    ->where('sr.user_id', $userId);
            })
            ->where(function($q) {
                $q->whereIn('sr.lesson_status', ['completed', 'passed'])
                ->orWhereNull('scorm_step.we_step_id');
            })
            ->where('w.attachable_type', CoreCompetencyAnchorValue::class)
            ->select('ccav.core_competency_id', 'ccav.score');

        // Skills Training
        $oneStepPerST = DB::table('wew_steps as st_s')
        ->select('st_s.stepable_id', DB::raw('MIN(st_s.id) as st_step_id'))
        ->where('st_s.is_scorm', true)
        ->where('st_s.is_scorm_scoring', true)
        ->where('st_s.stepable_type', SkillstrainingTemplate::class)
        ->groupBy('st_s.stepable_id');
        
        $skillsTrainingQuery = DB::table('skillstraining_templateables as s')
            ->join('skillstraining_templates as st', 'st.id', '=', 's.skillstraining_template_id')
            ->join('skillstraining_responses as str', function ($join) use ($userId) {
                $join->on('str.template_id', '=', 'st.id')
                    ->where('str.status', 'Submitted')
                    ->where('str.student_id', $userId);
            })
            ->join('core_competency_anchor_values as ccav', 'ccav.id', '=', 's.attachable_id')
            ->leftJoinSub($oneStepPerST, 'scorm_step', function ($join) {
                $join->on('scorm_step.stepable_id', '=', 'st.id');
            })
            ->leftJoin('scorm_results as sr', function ($join) use ($userId) {
                $join->on('sr.resultable_id', '=', 'scorm_step.st_step_id')
                    ->where('sr.resultable_type', WewStep::class)
                    ->where('sr.user_id', $userId);
            })
            ->where(function($q) {
                $q->whereIn('sr.lesson_status', ['completed', 'passed'])
                ->orWhereNull('scorm_step.st_step_id');
            })
            ->where('s.attachable_type', CoreCompetencyAnchorValue::class)
            ->select('ccav.core_competency_id', 'ccav.score');

        // Union and wrap
        $finalQuery = $lessonQuery
            ->unionAll($workExpQuery)
            ->unionAll($skillsTrainingQuery);

        $results = DB::table(DB::raw("({$finalQuery->toSql()}) as all_scores"))
                    ->mergeBindings($finalQuery)
                    ->select('core_competency_id', DB::raw('MAX(score) as max_score'))
                    ->groupBy('core_competency_id')
                    ->get();

        return $results;
    }

}
