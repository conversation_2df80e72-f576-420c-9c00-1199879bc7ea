/* Styles for paths.blade.php */
.path-custom-container {
    min-height: 100vh;
    /* Full viewport height */
    background-color: #f7f7f7;
    padding: 0px !important;
    /* Optional: adds spacing inside */
}

.success-alert {
    background-color: #d1fae5;
    color: #065f46;
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    transition: opacity 0.5s ease-out;
    font-weight: 500;
}

.fade-out {
    opacity: 0;
}

.tab-link {
    padding-bottom: 7px;
    border-bottom: 2px solid transparent;
    color: #6c757d;
    font-weight: 500;
    transition: all 0.3s ease;
}

.tab-active {
    border-bottom: 1px solid #000;
    color: #000 !important;
    /* font-weight: inherit; */
}

.skills-modules-link,a {
    text-decoration: none !important;
}


/* Job Details & Skills Match css start */

.my-path-body,
.my-path-body * {
    /* font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue",
        <PERSON><PERSON>, "Noto Sans", "Liberation Sans", sans-serif, "Apple Color Emoji",
        "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji" !important; */
    letter-spacing: normal;
    /* line-height: 1.5;
    font-weight: 300; */
}

.my-path-body .modal-title {
    font-size: 1.25rem;
}

.my-path-body .job-description {
    /* font-size: 1rem; */
    font-weight: 350;
    /* line-height: 1.5; */
    /* color: #212529; */
    /* text-align: left; */
}

.my-path-body .tab-content {

    font-weight: 300;
}

.my-path-body #skillsMatchChart {
    font-weight: 400;
}

.my-path-body .nav-tabs>li>a {
    /* all: unset;
    display: block; */
    font-family: inherit;
    font-weight: 350;
    letter-spacing: normal;
    text-transform: none;
    font-size: inherit;
    padding: 5px 17px;
    line-height: inherit;
    /* font-size: 1rem;
    color: inherit;
    text-transform: none;
    line-height: normal;
    padding: 0;
    margin: 0;
    border: none;
    background: transparent;
    position: static;  */
}

.my-path-body .nav-tabs>li>a:hover,
.my-path-body .nav-tabs>li>a:focus {
    background-color: #f5f5f5 !important;
    border-color: #ccc !important;
    color: #333 !important;
}

.my-path-body .nav-tabs>li>a.active:hover,
.my-path-body .nav-tabs>li>a.active:focus {
    background-color: #0d6efd !important;
    /* or your preferred color */
    color: #fff !important;
    border-color: #0d6efd !important;
}

.my-path-body .card-text {
    font-weight: 350;
}

.my-path-body .card-title {
    font-weight: 500;
}

.my-path-body .container {
    padding: 1rem;
    margin: 0 auto;
    width: 100%;
}

@media (min-width: 576px) {

    .my-path-body .container,
    .container-sm {
        max-width: 540px;
    }
}

@media (min-width: 768px) {

    .my-path-body .container,
    .container-md,
    .container-sm {
        max-width: 720px;
    }
}

@media (min-width: 992px) {
    .my-path-body .container {
        max-width: 960px;
    }
}

@media (min-width: 1200px) {
    .my-path-body .container {
        max-width: 1140px;
    }
}

/* Job Details & Skills Match css End */


/* Path title styling */
.path-title {
    font-size: 18px;
    font-weight: 600;
    line-height: 16px;

    /* font-size: 20px;
    font-weight: bold;
    color: #333;
    color: #333; */
    /* margin-bottom: 3px; */
}

/* Path description styling */
.path-description {
    font-size: 15px;
    color: #C8C8C8;
    /* margin-bottom: 15px; */
    /* text-align: center; */
}

/* Path list styling */
.path-list {
    list-style: none;
    padding: 0;
    margin: 0;
    width: 100%;
}

.path-list {
    border-top: 1px solid #f3f2f2;
}

.path-dropdown-button {
    background-color: #ffffff;
    /* color: #636c72; */
    color: #7e8285;
    font-weight: 550;
    border: 0px;
    /* border: 1px solid #ddd; */
    border-radius: 3px;
    padding: 8px 16px;
    font-size: 14px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    /* gap: 8px; */
    transition: all 0.2s ease;
}

.path-titles {
    color: #000;
    /* font-family: "Neue Haas Grotesk Text Pro"; */
    font-size: 16px;
    /* font-style: normal; */
    font-weight: 600;
    /* line-height: normal; */
}

.path-border-bottom {
    border-bottom: 1px solid #eceaea;
}

.overflow-hidden {
    overflow: hidden;
}

.path-card-boarder {
    border-radius: 10px;
}

.custom-pill-btn {
    display: inline-block;
    padding: 6px 20px;
    border: 1px solid #999;
    /* Light gray border */
    border-radius: 20px;
    /* Pill shape */
    font-size: 14px;
    color: #333;
    text-decoration: none;
    background-color: transparent;
    transition: all 0.2s ease;
    font-weight: 500;
}

.custom-pill-btn:hover,
.custom-pill-btn:focus {
    background-color: #f5f5f5;
    /* Light hover background */
    color: #000;
    text-decoration: none;
}

.d-none {
    display: none !important;
}

.mypaths-slider-nav .fa {
    font-size: 25px;
}

.slick-track {
    margin-left: unset;
    margin-right: unset;
}

.path-module-card:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
    transition: all 0.3s ease;
}

.path-card-img-h {
    width: 100%;
    aspect-ratio: 1 / 1;
    position: relative;
}

.path-title-font {
    font-family: Inter, Helvetica, "sans-serif";
    letter-spacing: normal;
}

.path-font-size-s {
    font-size: small;
    color: #a1a5b7;
    letter-spacing: normal
}

.path-content-p {
    padding-left: 15px;
    padding-right: 15px;
}

@media (min-width: 992px) {
    .path-content-p {
        padding-left: 30px;
        padding-right: 30px;
    }
}


.my-path-section {
    font-family: Inter, Helvetica, "sans-serif";
}

/* ============================================ */
/** Paths Info MODAL START */
#modalPathsIntro .modal-content {
    border-radius: 10px;
    /* background: linear-gradient(180deg, #CDD6DD 0%, rgba(255, 255, 255, 0.50) 47.35%, rgba(255, 255, 255, 0.37) 66.58%); */
    background: linear-gradient(180deg, rgba(205, 214, 221, 1) 0%, rgba(255, 255, 255, 1) 50%, rgba(255, 255, 255, 1) 100%);
    min-height: 555px;
    border: 1px solid #CDD6DD;
}

/* #modalPathsIntro .modal-dialog {
    max-width: 510px;
    width: 100%;
} */
#modalPathsIntro img {
    width: 100%;
}

#modalPathsIntro .modal-body {
    padding: 0;
}

#modalPathsIntro .modal-slider {
    width: 510px;
}

#modalPathsIntro .modal-info {
    padding: 20px;
}

#modalPathsIntro .modal-header {
    margin-bottom: 0;
    padding: 0;
    z-index: 3;
}

#modalPathsIntro h5 {
    font-size: 22px;
    font-weight: 500;
}

#modalPathsIntro .text-tip {
    color: #616161;
    font-size: 11px;
    font-weight: 400;
    padding: 0 27px;
    line-height: normal;
}

@media screen and (max-width: 600px) {
    #modalPathsIntro .modal-slider {
        width: 90vw;
    }
}

.how-paths-work-btn {
    border-radius: 5px;
    background: #DDD;
    color: #606060;
    text-align: center;
    font-weight: 600;
    padding: 13px 55px;
}

.modal-paths-btn-grey {
    border-radius: 5px;
    background: #DDD;
    color: #606060;
    text-align: center;
    font-weight: 600;
    padding: 13px 50px;
}

.modal-paths-btn-light-grey {
    border-radius: 5px;
    color: #606060;
    text-align: center;
    font-weight: 600;
    padding: 13px 50px;
    background: rgba(248, 246, 246, 0.81)
}

.buttons-grp-apart {
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
}

/** Paths Info Modal END */

.badge-module-status {
    border-radius: 20px;
    font-family: inherit;
    padding: 4px 13px;
    background: #0062FF;
    color: #fff;
    font-size: 12px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
}

.badge-module-status.badge-success {
    background: #31A400;
}

.badge-module-status.badge-failure {
    background: #FF4D00;
}

.badge-module-status.badge-incomplete {
    background: #E9FF1F;
    color: #000;
}

.badge-module-status.badge-secondary {
    background: #606060;
    color: #fff;
}

.badge-module-status.badge-not-started {
    background: #F8F8F8;
    color: #000;
}

.diabled-path-card img {
    filter: grayscale(100%);
}

.diabled-path-card {
    color: #606060;
    ;
}

.diabled-path-card .progress-bar-black {
    background: #bbbbbb;
}

/* .diabled-path-card .badge-module-status{
    background: #606060;
    color: #fff;
} */
.paths-job-info-btn {
    color: var(--button-text, #606060);
    text-align: center;
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    border: unset;
    border-radius: 5px;
    background: rgba(248, 246, 246, 0.81);
}

.badge-module-status.badge-in-progress {
    color: #2861F6;
    background: rgba(40, 97, 246, 0.13);
}

.badge-module-status.badge-faded-success {
    background: rgba(47, 192, 82, 0.13);
    color: #2FC052;
}

.badge-module-status.badge-faded-failure {
    background: #FEEFD4;
    color: #F6A000;
}

.breadcrumb-border-o {
    /* border: 1px solid #E9E9E9; */

    border: 0px solid #ffffff;
    border-radius: 1px;
    display: inline-block;
    height: 2px;
    width: 5px;
    background: #b5b5c3;
}
.card-item-level-s{
    font-size: 12px;
    font-weight: 500;
}