<?php

namespace App\Http\Controllers\Vue;

use App\Banner;
use App\Events\LessonResponseReset;
use App\Events\LessonResponseSubmitted;
use App\User;
use App\Lessonstep;
use App\Lesson;
use App\LessonCategory;
use App\Lessonresponse;
use App\TaskActivityResponse;
use App\Services\SCORM;
use App\Services\TimelineService;
use App\Standard;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Yajra\DataTables\Facades\DataTables;
use Auth;
use App\Services\UserAccessService;

class LessonsController extends Controller
{
    protected $scorm;

    public function __construct(
        SCORM $scorm
    ) {
        $this->scorm = $scorm;
    }

    public function list()
    {
        $difficulty = request('difficulty');
        $category = request('category');
        $skill = request('skill');

        $categories = LessonCategory::whereHas('lessons')
            ->with([
                'lessons.userResponse',
                'lessons.years',
                'lessons' => function ($lessons) use ($difficulty, $skill) {
                    $lessons->published()->select('lessons.id', 'lessons.tileimage', 'lessons.title', 'lessons.level', 'lessons.estimated_time')
                        ->when(Auth::user()->isStudent(), function ($query) {
                            return $query->forStudents()->whereHas('years', function ($q) {
                                $q->where('standard_id', Auth::user()->profile->standard_id);
                            });
                        })
                        ->when(Auth::user()->isTeacher(), function ($query) {
                            return $query->whereHas('years', function ($q) {
                                $q->whereIn('standard_id', Standard::pluck('id'));
                            });
                        })
                        ->when($difficulty, function ($query) use ($difficulty) {
                            return $query->where('level', $difficulty);
                        })
                        ->when($skill, function ($query) use ($skill) {
                            return $query->withAllTags([$skill]);
                        });
                }
            ])
            ->when($category, function ($query) use ($category) {
                return $query->whereId($category);
            })->orderBy('order')->orderBy('name')->get();
        return $categories->toArray();

        //     $tasks = Lesson::forStudents()
        //         ->select('id', 'tileimage', 'title', 'level', 'estimated_time')
        //         ->when(Auth::user()->isStudent(), function ($query) {
        //             return $query->whereHas('years', function ($q) {
        //                 $q->where('standard_id', Auth::user()->profile->standard_id);
        //             });
        //         })
        //         ->with('userResponse', 'years')
        //         ->when($difficulty, function ($query) use ($difficulty) {
        //             return $query->where('level', $difficulty);
        //         })
        //         ->when($category, function ($query) use ($category) {
        //             $query->whereHas('categories', function ($q) use ($category) {
        //                 return $q->where('lesson_category_id', $category);
        //             });
        //         })
        //         ->when($skill, function ($query) use ($skill) {
        //             return $query->withAllTags([$skill]);
        //         })
        //         ->get()->append('compeletedpercent');
        // }
        // if ($tasks) {
        //     foreach ($tasks as $key => $task) {
        //         if ($task->tileimage) {
        //             $tasks[$key]->tileimage = Storage::url($task->tileimage);
        //         }
        //     }
        // return $tasks->append('favourite')->append('hasresponse')->toArray();
    }

    public function detail($id, $student = null)
    {

        if ($student && !UserAccessService::currentUserCanAccess($student)) {
            abort(403, 'You do not have permission to perform this action.');
        }

        $lesson = Lesson::find($id)->append('hasresponse')->append('firststepresponse');

        if (!$lesson->hasresponse && $lesson->userResponse) {
            $lesson->userResponse()->delete();
        }
        if (@$lesson->userResponse()->with('activityResponses.activity')->first()->activityResponses) {
            foreach ($lesson->userResponse()->with('activityResponses.activity')->first()->activityResponses as $step) {
                if (!$step->activity) {
                    $step->delete();
                }
            }
        }

        $lesson =  Lesson::select('id', 'tileimage', 'title', 'level', 'estimated_time', 'badge_id', 'background_imagepath', 'background_video', 'foreground_video', 'response', 'curriculum')
            ->when(Auth::user()->isStudent(), function ($query) {
                return $query->forStudents()->whereHas('years', function ($q) {
                    $q->where('standard_id', Auth::user()->profile->standard_id);
                });
            })->with('worksheets', 'teacherResources', 'audio', 'tagged:id,tag_name,taggable_id', 'userResponse:id,status,lesson_id,student_id,filename,response_path,feedback,org_feedback', 'userResponse.activityResponses.activity', 'userResponse.badgeKey.badge.companies', 'steps.userResponse:lessonresponse_id,response,lessonstep_id', 'badge')->where('id', $id)->first()->append('favourite')->append('hasresponse')->append('firststepresponse');

        // ANZSCO and SCORM -- START
        $lesson->append([
            'anzsco_tag_names_grouped',
        ]);
        $lesson->load([
            'steps.scormTrackings' => fn($q) => $q->where('user_id', $student ?: auth()->id()),
            'steps.scormResult' => fn($q) => $q->where('user_id', $student ?: auth()->id()),
        ]);
        $lesson->steps->each->append('scorm_interactions');
        $lesson->scorm_scoring_step_result =  $this->scorm->scormScoringStepResult($lesson, $student ?: auth()->id());
        // ANZSCO and SCORM -- END

        $relatedlesson = $lesson->relatedlesson();
        $lesson = $lesson->toArray();
        $lesson['relatedlesson'] = $relatedlesson;
        if ($lesson && !empty($lesson['background_imagepath'])) {
            $lesson['background_imagepath'] = Storage::url($lesson['background_imagepath']);
        }
        // dd($lesson);
        if ($lesson['user_response'] && (($lesson['user_response']['feedback'] && Auth::user()->school_id) || ($lesson['user_response']['org_feedback'] && Auth::user()['organisation_id']))) {
            if ($lesson['user_response']['feedback'] && Auth::user()['school_id']) {
                $lesson['feedback'] = $lesson['user_response']['feedback'];
            } else {
                $lesson['feedback'] = $lesson['user_response']['org_feedback'];
            }
        }
        if ($lesson['relatedlesson'] && !empty($lesson['relatedlesson'])) {
            foreach ($lesson['relatedlesson'] as $k => $rl) {
                if (!empty($rl['background_imagepath'])) {
                    $lesson['relatedlesson'][$k]['background_imagepath'] = Storage::url($rl['background_imagepath']);
                }
            }
        }


        // dd($lesson);

        return $lesson;
    }

    public function searchLessons($value)
    {
        $searchedListData = [];

        $lessons = Lesson::published()->select('id', 'title', 'tileimage', 'level', 'publish')
            ->when((Auth::user()->isStudent() || session()->has("studentView")), function ($query) {
                return $query->whereHas('years', function ($q) {
                    $q->where('standard_id', Auth::user()->profile->standard_id);
                });
            })
            ->where('title', 'like', '%' . $value . '%')->take(10)->get();
        if (Auth::user()->hasLessonsAccess() && $lessons->count() > 0) {
            foreach ($lessons as $template) {
                if ($template->tileimage) {
                    $searchedListData['lessons'][] = [
                        'id' => $template->id,
                        'tile_img' => $template->tileimage_fullpath,
                        'title' => strlen($template->title) > 35 ? substr($template->title, 0, 35) . "..." : $template->title,
                        'url' =>  "/#/tasks/lessons/{$template->id}",
                        'level' => $template->level,
                    ];
                }
            }
        } else {
            $searchedListData['lessons'] = '';
        }

        return $searchedListData;
    }

    public function studentResponse($lesson, $student)
    {
        if (!UserAccessService::currentUserCanAccess($student)) {
            abort(403, 'You do not have permission to perform this action.');
        }

        $lesson =  Lesson::select('id', 'tileimage', 'title', 'level', 'estimated_time', 'badge_id', 'background_imagepath', 'background_video', 'foreground_video', 'response')->with('tagged:id,tag_name,taggable_id', 'badge')->where('id', $lesson)->first();

        if (@$lesson->userResponse($student)->with('activityResponses.activity')->first()->activityResponses) {
            foreach ($lesson->userResponse($student)->with('activityResponses.activity')->first()->activityResponses as $step) {
                if (!$step->activity) {
                    $step->delete();
                }
            }
        }

        // ANZSCO and SCORM -- START
        $lesson->append([
            'anzsco_tag_names_grouped'
        ]);
        $lesson->load([
            'steps.scormTrackings' => fn($q) => $q->where('user_id', $student),
            'steps.scormResult' => fn($q) => $q->where('user_id', $student),
        ]);
        $lesson->steps->each->append('scorm_interactions');
        $lesson->scorm_scoring_step_result =  $this->scorm->scormScoringStepResult($lesson, $student);
        // ANZSCO and SCORM -- END

        $lesson->student_response = $lesson->userResponse($student)->with('activityResponses.activity', 'badgeKey.badge.companies')->first()->toArray();


        if ($lesson->student_response['status'] != 'Submitted') {
            abort(403, 'You do not have permission to perform this action.');
        }

        if ($lesson && !empty($lesson['background_imagepath'])) {
            $lesson['background_imagepath'] = Storage::url($lesson['background_imagepath']);
        }

        // if ($lesson->activityResponses) {
        //     foreach ($lesson->activityResponses as $k => $rl) {
        //         if ($rl->userResponse($student)->first()) {
        //             $lesson->activityResponses[$k]->student_response = $rl->userResponse($student)->with('activity')->first()->toArray();
        //         }
        //     }
        // }

        $lesson->student_completed_percentage =  round(((($lesson->steps->pluck('student_response')->count() + 1) / ($lesson->steps->count() + 1)) * 100) / 5) * 5;

        $lesson->feedback = Auth::user()->isTeacher() ? $lesson->student_response['feedback'] : $lesson->student_response['org_feedback'];

        return $lesson->toArray();
    }

    public function lessonname($id)
    {
        return Lesson::where('id', $id)->value('title');
    }

    public function sectionwithlessonname($id, $sectionno)
    {
        $response = ["lesson" => "", "section" => ""];
        $lesson = Lesson::with('userResponse:status,lesson_id,student_id,filename', 'steps.userResponse')->where('id', $id)->first();
        if ($lesson) {
            $response['lesson'] = $lesson->title;
            foreach ($lesson->steps as $k => $step) {
                if ($k == ($sectionno - 1)) {
                    $response['section'] = $step->title;
                    break;
                }
            }
        }
        return $response;
    }

    public function banner()
    {
        return Banner::whereType('Lessons')->first();
    }

    public function  togglefav(Lesson $lesson)
    {
        Auth::user()->toggleFavorite($lesson);
        return $lesson->append('favourite')->only(['id', 'tileimage', 'title', 'level', 'estimated_time', 'favourite']);
    }

    public function  resetLesson(Lesson $lesson)
    {
        $stepids = $lesson->steps->pluck('id');

        $lessonresponse = Lessonresponse::where('lesson_id', $lesson->id)->where('student_id', Auth::user()->id)->where('standard_id', Auth::user()->profile->standard_id)->first();

        if ($stepids) {
            if ($lessonresponse) {
                TaskActivityResponse::where('lessonresponse_id', $lessonresponse->id)->whereIn('lessonstep_id', $stepids)->delete();
            }
        }

        $lessonresponse->delete();
        // ANZSCO and SCORM -- START
        $this->scorm->resetModuleScormTracking(auth()->user(), $lesson);
        // ANZSCO and SCORM -- END
        event(new LessonResponseReset($lesson, Auth::user()));
        return 'success';
    }
    // storeFinalResponse

    public function storeFinalResponse(Request $request, $id)
    {
        if (!Auth::user()->isStudent()) {
            return ['error' => "This serves a demo purpose. You can't submit the task. Thank you!"];
        }

        $lesson = Lesson::find($id);
        if ($lesson->response && $request->file('response')) {
            $ext = $request->file('response')->getClientOriginalExtension();
            $validator = ['ppt', 'pptx', 'doc', 'docx', 'xlsx', 'xls', 'pages', 'pdf'];

            if (!in_array($ext, $validator)) {
                return ['error' => 'Invalid file format . Valid files are ppt, pptx, doc, docx, xlsx, xls, pages, pdf '];
            }
        } else if ($lesson->response && $request->nofile != "false") {
            return ['error' => 'Please select a file to upload'];
        }
        try {
            if ($request->nofile == "false") {
                $path = Storage::cloud()->put('task_responses', $request->file('response'));
                $filename = $request->file('response')->getClientOriginalName();
            } else {
                $path = '';
                $filename = '';
            }
            if (($lesson->response && ($path || $request->nofile == "false")) || !$lesson->response) {
                $response = Lessonresponse::updateOrCreate(
                    [
                        'lesson_id' => $lesson->id,
                        'student_id' => Auth::id(),
                    ],
                    [
                        'standard_id' => Auth::user()->profile->standard_id,
                        'response_path' => $path,
                        'filename' => $filename,
                        'submitted_at' => now(),
                        'status' => 'Submitted'
                    ]
                );

                if ($response) {
                    event(new LessonResponseSubmitted($response));
                    return $response;
                }
                return ['error' => 'Unable to upload your response at the moment '];
            }
        } catch (\Exception $ex) {
            return ['error' => 'Unable to upload your response at the moment ' . $ex->getMessage()];
        }
    }

    public function storeResponse(Request $request, $id, $sectionid)
    {
        if (!Auth::user()->isStudent()) {
            return ['error' => "This serves a demo purpose. You can't submit the task. Thank you!"];
        }

        $section = Lessonstep::find($sectionid);
        $errors = [];
        if ($section) {
            if ($section->lesson_id == $id) {
                $response = $request->response;
                $user = Auth::user();

                $lessonresponseExisting = Lessonresponse::where(
                    [
                        'lesson_id' => $section->lesson_id,
                        'student_id' => $user->id,
                        'standard_id' => $user->profile->standard_id
                    ]
                )->first();

                if ($lessonresponseExisting) {
                    $lessonresponse = $lessonresponseExisting;
                } else {
                    $lessonresponse = Lessonresponse::create(
                        [
                            'lesson_id' => $section->lesson_id,
                            'student_id' => $user->id,
                            'standard_id' => $user->profile->standard_id,
                            'status' => 'Draft'
                        ]
                    );
                }

                event(new LessonResponseSubmitted($lessonresponse));

                $response = TaskActivityResponse::updateOrCreate([
                    'lessonresponse_id' => $lessonresponse->id,
                    'lessonstep_id' => $sectionid
                ], [
                    'response' => $response
                ]);

                if ($lessonresponseExisting) {
                    (new TimelineService())->log($lessonresponse, 'updated');
                } else {
                    (new TimelineService())->log($lessonresponse, 'created');
                }
            } else {
                $errors[] = "Invalid request";
            }
        } else {
            $errors[] = "Invalid request: Section does not exists ";
        }
        if (!empty($errors)) {
        }
        return $response->toArray();
    }

    public function  sectiondetail($id, $sectionid)
    {
        // $section=Lessonstep::find($sectionid);
        $lesson = Lesson::with(
            'userResponse:status,lesson_id,student_id,filename',
            'steps.userResponse',
            'badge',
            'steps.userScormResult' // SCORM data appended
        )
        ->where('id', $id)
        ->first();

        $lesson->toArray();
        if ($lesson && !empty($lesson['background_imagepath'])) {
            $lesson['background_imagepath'] = Storage::url($lesson['background_imagepath']);
        }
        if ($lesson['steps'] && !empty($lesson['steps'])) {
            foreach ($lesson['steps'] as $k => $rl) {
                if (!empty($rl['bg_image'])) {
                    $lesson['steps'][$k]['bg_image'] = Storage::url($rl['bg_image']);
                }
                if (!empty($rl['scorm_path'])) { // SCORM launch file url
                    $lesson['steps'][$k]['scorm_path'] = $this->scorm->scormUrl($rl['scorm_path']);
                }
            }
        }
        return $lesson;
    }

    public function serverSide(Request $request)
    {
        $lessons = Lesson::select('id', 'order', 'title', 'publish')->with('years');
        $datatable = Datatables::of($lessons)
            ->filter(function ($query) {
                if ($standard = request('standard')) {
                    $query->whereHas('years', function ($q) use ($standard) {
                        $q->where('standard_id', $standard);
                    });
                }
            }, true);
        return $datatable
            ->addColumn('years', function (Lesson $lesson) {
                $data = '';

                if ($lesson->years) {
                    foreach ($lesson->years as $year) {
                        $data .= '<span class="label">' . $year->title . '</span> ';
                    }
                }

                return $data;
            })
            ->addColumn('publish', function (Lesson $lesson) {
                if ($lesson->publish) {
                    $data = 'fa-check';
                } else {
                    $data = 'fa-times';
                }
                return '<i class="fa ' . $data . '"></i>';
            })
            ->addColumn('action', function (Lesson $lesson) {
                return '<button class="btn btn-link" data-toggle="tooltip" title="Copy URL!" data-text="' . route('tasks.show', $lesson->id) . '" onclick="copyPath(this)" onmouseleave="setDefaultTooltip(this)"><i class="fa fa-copy"></i></button><a class="1232" href="' . route('lessons.edit', $lesson->id) . '"><i class="fa fa-edit mr-2"></i></a><a href="' . route('lessons.destroy', $lesson->id) . '" data-method="delete" data-token="' . csrf_token() . '" data-confirm="Are you sure?"><i class="fa fa-trash-o text-danger"></i>
            </a>';
            })
            ->rawColumns(['years', 'publish', 'action'])
            ->make(true);
    }

    public function getFeedback($id)
    {
        $response = Lessonresponse::find($id);
        $data = $response->feedback ?? $response->org_feedback;
        return $data;
    }

    public function destroyResponse($id)
    {
        $lesson = Lessonresponse::findOrFail($id);
        if (!UserAccessService::currentUserCanAccess($lesson->student_id)) {
            abort(403, 'You do not have permission to perform this action.');
        }
        $lesson->delete();

        if (Auth::user()->isAdmin()) {
            return redirect('lessons/responses')->with('message', 'Response has been deleted successfully!');
        } elseif (Auth::user()->isTeacher() || Auth::user()->isStaff()) {
            return redirect('/student-tasks')->with('message', 'Response has been deleted successfully!');
        }
        return redirect()->route("tasks.show", $lesson->lesson_id)->with('message', 'Response has been deleted successfully!');
    }
}
