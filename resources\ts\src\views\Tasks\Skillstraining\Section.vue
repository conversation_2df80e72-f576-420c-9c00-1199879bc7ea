<template>
    <div id="banner" class="full-view-banner banner" :style="{ 'backgroundImage': 'url(' + backgroundFile() + ')', }">
        <div v-if="currentSection.bg_video" class="banner-video" v-html="currentSection.bg_video"></div>
        <div v-if="!currentSection.bg_video && !currentSection.bg_image && skillstraining.background_videoid" class="banner-video" v-html="skillstraining.background_videoid"></div>
        <div style="position:absolute;width:100%;height:100%;opacity:.3;background:#000;"></div>
        <div class="page-content">
            <div class="banner-textarea px-20 w-450px">
                <p class="fs-2x m-0 text-white">Section {{ currentSection.number }}</p>
                <p class="fs-3x m-0 text-white" v-html="currentSection.title"></p>
                <p class="m-0 text-white">
                    <span class="svg-icon svg-icon-primary svg-icon-2x" v-if="currentSection.estimated_time && (currentSection.estimated_time.hours != null || currentSection.estimated_time.minutes != null)"> <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1"> <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"> <rect x="0" y="0" width="24" height="24" /> <path d="M12,22 C7.02943725,22 3,17.9705627 3,13 C3,8.02943725 7.02943725,4 12,4 C16.9705627,4 21,8.02943725 21,13 C21,17.9705627 16.9705627,22 12,22 Z" fill="#ffffff" opacity="0.3" /> <path d="M11.9630156,7.5 L12.0475062,7.5 C12.3043819,7.5 12.5194647,7.69464724 12.5450248,7.95024814 L13,12.5 L16.2480695,14.3560397 C16.403857,14.4450611 16.5,14.6107328 16.5,14.7901613 L16.5,15 C16.5,15.2109164 16.3290185,15.3818979 16.1181021,15.3818979 C16.0841582,15.3818979 16.0503659,15.3773725 16.0176181,15.3684413 L11.3986612,14.1087258 C11.1672824,14.0456225 11.0132986,13.8271186 11.0316926,13.5879956 L11.4644883,7.96165175 C11.4845267,7.70115317 11.7017474,7.5 11.9630156,7.5 Z" fill="#ffffff" /> </g> </svg> </span>
                    <span style="vertical-align: middle"> <span v-if="currentSection.estimated_time && currentSection.estimated_time.hours" v-text="currentSection.estimated_time.hours + 'h '"></span> <span v-if="currentSection.estimated_time && currentSection.estimated_time.minutes" v-text="currentSection.estimated_time.minutes + 'm'"></span> </span>
                </p>
            </div>
        </div>
    </div>
    <div :class="{ row: skillstraining.steps.length < 6, 'sticky-top': scrolled }" v-on="handleScroll" class="d-flex module-sections">
        <template v-for="(step, index) in skillstraining.steps" :key="step.id">
            <div v-bind:class="[(skillstraining.steps.length < 6) ? 'col' : 'col-6 col-sm-4 col-md-2', (step.user_response || step.id == presentSectionid) ? 'bg-black' : '']" class="text-center p-0" @click="gotosection(step.id, step.user_response, index)">
                <div class="module-section d-flex flex-column justify-content-center align-items-center py-5" v-bind:class="[(!step.user_response && step.id != presentSectionid) ? 'bg-white' : '']">
                    <span class="svg-icon svg-icon-primary svg-icon-2x"> <svg v-if="step.user_response || step.id == presentSectionid" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1"> <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"> <mask fill="white"> <use xlink:href="#path-1" /> </mask> <g /> <path d="M15.6274517,4.55882251 L14.4693753,6.2959371 C13.9280401,5.51296885 13.0239252,5 12,5 C10.3431458,5 9,6.34314575 9,8 L9,10 L14,10 L17,10 L18,10 C19.1045695,10 20,10.8954305 20,12 L20,18 C20,19.1045695 19.1045695,20 18,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,12 C4,10.8954305 4.8954305,10 6,10 L7,10 L7,8 C7,5.23857625 9.23857625,3 12,3 C13.4280904,3 14.7163444,3.59871093 15.6274517,4.55882251 Z" fill="#ffffff" /> </g> </svg> <svg v-else xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1"> <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"> <mask fill="white"> <use xlink:href="#path-1" /> </mask> <g /> <path d="M7,10 L7,8 C7,5.23857625 9.23857625,3 12,3 C14.7614237,3 17,5.23857625 17,8 L17,10 L18,10 C19.1045695,10 20,10.8954305 20,12 L20,18 C20,19.1045695 19.1045695,20 18,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,12 C4,10.8954305 4.8954305,10 6,10 L7,10 Z M12,5 C10.3431458,5 9,6.34314575 9,8 L9,10 L15,10 L15,8 C15,6.34314575 13.6568542,5 12,5 Z" fill="#000000" /> </g> </svg> </span>
                    <p class="m-0 px-5" v-bind:class="[(step.user_response || step.id == presentSectionid) ? 'text-white' : '']" v-html="step.title"></p>
                    <p class="m-0" v-bind:class="[(step.user_response || step.id == presentSectionid) ? 'text-white' : '']">
                        <span v-if="step.estimated_time && step.estimated_time.hours" v-text="step.estimated_time.hours + 'h '"></span>
                        <span v-if="step.estimated_time && step.estimated_time.minutes" v-text="step.estimated_time.minutes + 'm'"></span> &nbsp;
                    </p>
                </div>
            </div>
        </template>
        <div v-bind:class="[(skillstraining.steps.length < 6) ? 'col' : 'col-6 col-sm-4 col-md-2', (skillstraining.user_response && skillstraining.user_response.status != 'Draft') ? 'bg-black' : '']" class="text-center p-0">
            <div class="module-section d-flex flex-column justify-content-center align-items-center py-5" v-bind:class="{ 'bg-white': !(skillstraining.user_response && skillstraining.user_response.status != 'Draft') }">
                <span class="svg-icon svg-icon-primary svg-icon-2x"> <svg v-if="(skillstraining.user_response && skillstraining.user_response.status != 'Draft')" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1"> <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"> <mask fill="white"> <use xlink:href="#path-1" /> </mask> <g /> <path d="M15.6274517,4.55882251 L14.4693753,6.2959371 C13.9280401,5.51296885 13.0239252,5 12,5 C10.3431458,5 9,6.34314575 9,8 L9,10 L14,10 L17,10 L18,10 C19.1045695,10 20,10.8954305 20,12 L20,18 C20,19.1045695 19.1045695,20 18,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,12 C4,10.8954305 4.8954305,10 6,10 L7,10 L7,8 C7,5.23857625 9.23857625,3 12,3 C13.4280904,3 14.7163444,3.59871093 15.6274517,4.55882251 Z" fill="#ffffff" /> </g> </svg> <svg v-else xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1"> <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"> <mask fill="white"> <use xlink:href="#path-1" /> </mask> <g /> <path d="M7,10 L7,8 C7,5.23857625 9.23857625,3 12,3 C14.7614237,3 17,5.23857625 17,8 L17,10 L18,10 C19.1045695,10 20,10.8954305 20,12 L20,18 C20,19.1045695 19.1045695,20 18,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,12 C4,10.8954305 4.8954305,10 6,10 L7,10 Z M12,5 C10.3431458,5 9,6.34314575 9,8 L9,10 L15,10 L15,8 C15,6.34314575 13.6568542,5 12,5 Z" fill="#000000" /> </g> </svg> </span>
                <p class="m-0" :class="{ 'text-white': (skillstraining.user_response && skillstraining.user_response.status != 'Draft') }"> Final Step </p>
                <p class="m-0" :class="{ 'text-white': (skillstraining.user_response && skillstraining.user_response.status != 'Draft') }"> &nbsp; </p>
            </div>
        </div>
    </div>
    <div class="section-content">
        <div class="" v-html="currentSection.body"></div>
        <Scorm
            ref="scormRef"
            :section="currentSection"
            :user="currentUser"
            :trackable-type="'wew_steps'"
            v-if="currentSection.is_scorm"
            @status-changed="handleScormStatusChange"
            @continue-next="nextSection"
        />
        <form v-if="currentSection.response" class="text-gray-700" id="skillstraining_section_resonpse">
            <div class="mt-4 pb-4 add-padding-to-text">
                <div id="app">
                    <froala :tag="'textarea'" :config="config" v-model="skillstrainingresponseform.response"></froala>
                    <p v-if="responseError.length" v-text="responseError" class="form-error mt-2 ms-2"></p>
                </div>
                <!-- <textarea name="activity_responses[{{ $value->id }}]" rows="5" class="form-control froalaeditor activity-response"></textarea> -->
            </div>
        </form>
    </div>
    <!-- <div class="my-10">
        <button class="btn btn-white rounded-0 w-150px" href="javascript:void()" @click="prevSection" v-if="!isFirstSection">
            Previous
        </button>
        <button class="btn btn-white float-end rounded-0 w-150px" href="javascript:void()" @click="nextSection" v-if="!isLastSection">
            Next
        </button>
        <button class="btn btn-white float-end rounded-0 w-150px" href="javascript:void()" @click="nextSection" v-if="isLastSection">
            Final Step
        </button>
    </div> -->
    <div class="footer-buttons bg-black clearfix">
        <div class="col-12">
            <button class="m-10 btn btn-black rounded-0 w-150px wrap" href="javascript:void()" @click="prevSection" v-if="!isFirstSection">
                {{ prevSectionDetail.title }}
            </button>
            <button class="m-10 btn btn-black float-end rounded-0 w-150px wrap" href="javascript:void()" @click="nextSection" v-if="!isLastSection">
                {{ nextSectionDetail.title }}
            </button>
            <button class="m-10 btn btn-black float-end rounded-0 w-150px wrap" href="javascript:void()" @click="nextSection" v-if="isLastSection"> Final Step </button>
        </div>
    </div>
</template>

<script lang="ts">
    import {
        defineComponent,
        ref,
        onMounted
    } from "vue";
    import {
        useStore
    } from "vuex";
    import Swal from "sweetalert2/dist/sweetalert2.min.js";
    import {
        Mutations,
        Actions
    } from "@/store/enums/StoreEnums";
    import ApiService from "@/core/services/ApiService";
    import VueFroala from "froala-wysiwyg-vue3";
    import iframeResize from 'iframe-resizer/js/iframeResizer';
    import Scorm from "@/components/modules/sections/Scorm.vue";

    import {
        useRouter,
        useRoute
    } from "vue-router";
    export default defineComponent({
        name: "skillstraining-section-detail",
        components: {
            VueFroala,
            Scorm,
        },
        setup(props) {
            const store = useStore();
            const router = useRouter();
            const currentUser = store.getters.currentUser;
            onMounted(async () => {
                await fetchSkillstrainingSectionDetail();
                checkPreviousScormCompletion();
                iframeResize({
                    heightCalculationMethod: 'bodyScroll'
                }, '.section-content iframe')
            });
            var str = (new URL(window.location.href)).hash;
            var n = str.lastIndexOf('/');
            const presentSectionid = ref(str.substring(n + 1));
            const skillstraining = ref();
            const currentSection = ref();
            const prevSectionDetail = ref();
            const prevSectionDetailIndex = ref();
            const nextSectionDetail = ref();
            const nextSectionDetailIndex = ref();
            const isLastSection = ref(false);
            const isFirstSection = ref(false);
            let sectionNavOffsetTop = 0;
            const scrolled = ref(false);
            currentSection.value = {};
            const skillstrainingresponseform = ref();
            const responseError = ref("");
            skillstrainingresponseform.value = {
                response: "",
            };
            nextSectionDetailIndex.value = 1;
            skillstraining.value = {
                id: "",
                steps: [],
            };
            nextSectionDetail.value = {
                title: "",
            };
            prevSectionDetail.value = {
                title: "",
            };
            const fetchSkillstrainingSectionDetail = async () => {
                try {
                    const { data } = await ApiService.get(`api/skillstraining/` + props.id + `/sections`, props.sectionid);
                    if (data.steps.length) {
                        for (let i = 0; i < data.steps.length; i++) {
                            if (!data.steps[i].user_response && i < (props.sectionid - 1) && !currentUser.isTeacher) {
                                skillstrainingresponseform.value.response = '';
                                router.push({
                                    name: "task-skillstraining-section-detail",
                                    params: {
                                        id: data.steps[i].stepable_id,
                                        sectionid: i + 1,
                                    },
                                }).then(() => {
                                    fetchSkillstrainingSectionDetail();
                                });

                                break;
                            }
                            if (i == (props.sectionid - 1)) {
                                if (i == data.steps.length - 1) {
                                    isLastSection.value = true;
                                } else {
                                    isLastSection.value = false;
                                    nextSectionDetail.value = data.steps[i + 1];
                                    nextSectionDetailIndex.value = i + 2;
                                }
                                isFirstSection.value = i == 0;

                                currentSection.value = data.steps[i];
                                var breadcrumbs = store.getters.getBreadcrumbs;
                                breadcrumbs[2] = data.title;
                                breadcrumbs[3] = currentSection.value.title;
                                store.commit(Mutations.SET_BREADCRUMB_MUTATION, breadcrumbs);
                                if (currentSection.value.user_response) {
                                    skillstrainingresponseform.value.response = currentSection.value.user_response.response;
                                }
                                if (i != 0) {
                                    prevSectionDetail.value = data.steps[i - 1];
                                    prevSectionDetailIndex.value = i;
                                }
                            }
                        }
                    }
                    skillstraining.value = data;
                    presentSectionid.value = currentSection.value.id;

                    var banner = document.getElementById('banner');
                    sectionNavOffsetTop = banner!.scrollHeight + 120;
                } catch (error: any) {
                    if (error.response) {
                        console.log(error.response.data);
                        console.log(error.response.status);
                        console.log(error.response.headers);
                    } else if (error.request) {
                        console.log(error.request);
                    } else {
                        console.log("Error", error.message);
                    }
                    console.log(error.config);
                };
            };
            const gotosection = (sectionid, cannavigate, index) => {
                if (cannavigate) {
                    router.push({
                        name: "task-skillstraining-section-detail",
                        params: {
                            id: skillstraining.value.id,
                            sectionid: (index + 1)
                        },
                    }).then(() => {
                        fetchSkillstrainingSectionDetail()
                    });
                }
            };
            const nextSection = () => {
                if (currentUser.isTeacher) {
                    if (isLastSection.value) {
                        skillstrainingresponseform.value.response = '';
                        router.push({
                            name: "task-skillstraining-section-final-step",
                            params: {
                                id: skillstraining.value.id
                            },
                        }).then(() => {
                            fetchSkillstrainingSectionDetail();
                        });
                    } else {
                        skillstrainingresponseform.value.response = '';
                        router.push({
                            name: "task-skillstraining-section-detail",
                            params: {
                                id: nextSectionDetail.value.stepable_id,
                                sectionid: nextSectionDetailIndex.value,
                            },
                        }).then(() => {
                            fetchSkillstrainingSectionDetail();
                        });
                    }
                } else {
                    if (!isScormCompleted(true)) {
                        return;
                    }
                    if (currentSection.value.response && !skillstrainingresponseform.value.response.length) {
                        responseError.value = "Please add a response";
                        return;
                    } else {
                        responseError.value = "";
                    }
                    ApiService.post(`api/skillstraining/` + props.id + `/sections/` + currentSection.value.id, skillstrainingresponseform.value)
                        .then(({
                            data
                        }) => {
                            responseError.value = data;
                            if (isLastSection.value) {
                                skillstrainingresponseform.value.response = '';
                                router.push({
                                    name: "task-skillstraining-section-final-step",
                                    params: {
                                        id: skillstraining.value.id
                                    },
                                }).then(() => {
                                    fetchSkillstrainingSectionDetail();
                                });
                            } else {
                                skillstrainingresponseform.value.response = '';
                                router.push({
                                    name: "task-skillstraining-section-detail",
                                    params: {
                                        id: nextSectionDetail.value.stepable_id,
                                        sectionid: nextSectionDetailIndex.value,
                                    },
                                }).then(() => {
                                    fetchSkillstrainingSectionDetail();
                                });
                            }
                        })
                        .catch((err) => {
                            console.error("catch error", err);
                        });
                }
            };
            const prevSection = () => {
                // fetch(`api/lessons`).then((response) => {
                //     console.log(response);
                // });
                skillstrainingresponseform.value.response = '';
                router.push({
                    name: "task-skillstraining-section-detail",
                    params: {
                        id: prevSectionDetail.value.stepable_id,
                        sectionid: prevSectionDetailIndex.value,
                    },
                }).then(() => {
                    fetchSkillstrainingSectionDetail();
                });
            };

            const handleScroll = () => {
                var windowWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
                if (windowWidth > 991) {
                    var element = document.getElementById('kt_app_toolbar') as HTMLElement;
                    if (window.scrollY > sectionNavOffsetTop) {
                        scrolled.value = true;
                        element.style.display = "none";
                    } else {
                        scrolled.value = false;
                        element.style.display = "flex";
                    }
                }
            }
            const backgroundFile = () => {
                return currentSection.value.bg_image ?? (!currentSection.value.bg_video ? skillstraining.value.background_imagepath : '');
            }

            // SCORM --- START
            const scormStatus = ref<string>("incomplete");
            const scormRef = ref();
            const handleScormStatusChange = (newStatus : string) => {
                scormStatus.value = newStatus;
            }
            const isScormCompleted = (showAlert: boolean = false) => {
                if (!scormRef.value || !currentSection.value?.is_scorm) {
                    return true;
                }

                return scormRef.value?.isScormCompleted(showAlert);
            }
            const checkPreviousScormCompletion = () => {
                if (
                    !isFirstSection.value && 
                    !currentUser.isTeacher && 
                    prevSectionDetail.value?.is_scorm
                ) {
                    if (!prevSectionDetail.value.user_scorm_result?.is_completed) { // Previous SCORM section is not completed
                        Swal.fire({
                            text: "(SCORM) : You must complete previously assigned content.",
                            icon: "warning",
                            buttonsStyling: false,
                            confirmButtonText: "OK",
                            customClass: {
                                confirmButton: "btn fw-semobold btn-primary rounded-0",
                            },
                        });

                        router.push({
                            name: "task-skillstraining-section-detail",
                            params: {
                                id: prevSectionDetail.value.stepable_id,
                                sectionid: prevSectionDetailIndex.value,
                            },
                        }).then(() => {
                            fetchSkillstrainingSectionDetail();
                        });
                    }

                    console.log("prevSectionDetail:", prevSectionDetail.value);
                }
            }
            // SCORM --- END

            return {
                currentUser,
                skillstraining,
                isFirstSection,
                isLastSection,
                currentSection,
                config: {
                    key: "hWA2C-7I2A4C3D5D2D2G3wxeklqcwvffrrhxhoqxpkC7bmnxE2F2G2D1B10B2B3E6F1F2==",
                    height: 300,
                    attribution: false,
                },
                nextSection,
                prevSection,
                skillstrainingresponseform,
                responseError,
                gotosection,
                nextSectionDetail,
                prevSectionDetail,
                presentSectionid,
                scrolled,
                nextSectionDetailIndex,
                handleScroll,
                backgroundFile,
                // SCORM START
                scormRef,
                handleScormStatusChange,
                // SCORM END
            };
        },
        props: ["id", "sectionid"],
        created() {
            window.addEventListener("scroll", this.handleScroll);
        },
        destroyed() {
            window.removeEventListener("scroll", this.handleScroll);
        }
    });
</script>

<style>
    .app-container {
        background-color: #fff;
    }

    .fr-box {
        z-index: 0;
    }

    .wrap {
        overflow: hidden;
        max-width: 55ch;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .btn-black {
        color: #fff;
        border: 1px solid !important;
        background: #000;
    }

    .btn-black:hover {
        color: #000;
        background: #fff;
    }


    /* .sticky-top+.section-content {
                                    margin-top: 100px;
                                } */

    .section-content {
        margin: 0px 0;
    }

    .section-content iframe {
        width: 100% !important;
    }

    .section-content iframe.wistia_embed {
        height: 100% !important;
    }

    .section-content img {
        max-width: 100%;
    }

    .section-content p img,
    .section-content p iframe {
        margin-bottom: -1rem;
    }

    .section-content img,
    .section-content iframe {
        margin-left: -31px;
        margin-right: -30px;
        max-width: calc(100% + 61px) !important;
        width: calc(100% + 61px) !important;
    }

    .btn-white {
        border: 1px solid #000 !important;
    }

    .btn-white:hover,
    .btn.btn-white:hover:not(.btn-active) {
        background-color: #000 !important;
        color: #fff !important;
    }

    /* Handle */
    ::-webkit-scrollbar-thumb {
        background: #000000 !important;
    }

    /* Handle on hover */
    ::-webkit-scrollbar-thumb:hover {
        background: #000000 !important;
    }

    .module-sections {
        overflow: auto hidden;
        margin-left: -30px;
        margin-right: -30px;
        position: relative;
    }

    .sticky-top {
        position: fixed;
        min-width: calc(100% - 140px);
    }

    .app-content {
        padding: 0px;
    }

    .full-page {
        margin-left: -20px;
        margin-right: -20px;
    }

    .banner {
        /* background-image: url("/images/vwe/home-parallax.jpg"); */
        background-color: #000;
        min-height: calc(56.25vw - 149px);
        display: block;
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center;
        position: relative;
        overflow: hidden;
    }

    .full-view-banner,
    .footer-buttons {
        margin-left: -30px;
        margin-right: -30px;
    }

    .banner-video {
        height: 100%;
    }

    .banner-video>video {
        /* height: 100%; */
        width: 101% !important;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .page-content {
        top: 40%;
        /* text-align: center; */
        position: absolute;
        width: 100%;
        padding: 0px 15px;
    }

    .module-section {
        border-top: 1px solid;
        border-bottom: 1px solid;
        border-left: 1px solid;
        cursor: pointer;
        height: 100px;
    }

    .module-sections>.text-center:last-of-type>.module-section {
        border-right: 1px solid;
    }

    @media (max-width: 1280px) {
        .banner {
            /* height: calc(56.25vw - 140px); */
            height: 56.25vw;
        }

        .banner-video>video {
            height: 100% !important;
            width: calc(65vw + 65vh) !important;
        }
    }

    @media (min-width: 992px) {
        .sticky-top+.section-content {
            margin-top: 100px;
        }

        .module-sections {
            animation-name: backtooriginal;
            animation-duration: 0.2s;
            animation-fill-mode: forwards;
            z-index: 100;
        }

        .sticky-top {
            animation-name: stick-top;
            animation-duration: 0.2s;
            animation-fill-mode: forwards;
        }

        @keyframes stick-top {
            from {
                top: 5px;
            }

            100% {
                top: 0px;
            }
        }

        @keyframes backtooriginal {
            from {
                top: -5px;
            }

            100% {
                top: 0px;
            }
        }
    }

    @media (max-width: 991px) {

        .full-view-banner,
        .module-sections,
        .footer-buttons {
            margin-left: -20px;
            margin-right: -20px;
        }

        .sticky-top {
            top: 119px;
            min-width: 100%;
        }

        .full-view-banner {
            margin-top: 58.16px;
        }

        .module-section {
            height: 100px;
        }
    }

    @media (max-width: 991px) and (min-width: 768px) and (orientation:portrait) {
        .banner {
            height: 86.25vw;
        }

        .banner-video>video {
            height: 100% !important;
            width: calc(66vw + 66vh) !important;
        }
    }

    @media (max-width: 991px) and (orientation:landscape) {
        .banner-video>video {
            height: auto !important;
            width: calc(70vw + 70vh) !important;
        }
    }

    @media (max-width: 767px) {
        .banner {
            height: calc(100vh - 300px);
        }

        .full-view-banner {
            margin-top: 0;
        }

        .banner-video>video {
            height: 100% !important;
            width: calc(90vw + 90vh) !important;
        }

        .sticky-top {
            margin-top: 10px;
        }
    }
</style>
