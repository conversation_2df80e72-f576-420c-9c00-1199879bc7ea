<template>
    <div class="my_path__modules">
        <h3>
            Let’s keep going!
        </h3>
        <h5>
            Let’s move onto these modules next.
        </h5>

        <div class="my_path__modules_carousel">
            <Carousel v-bind="carouselConfig" ref="carouselRef">
                <Slide v-for="slide in 10" :key="slide">
                    <div class="carousel__item">{{ slide }}</div>
                </Slide>
            </Carousel>

            <svg style="width: 70px;" viewBox="0 0 53 23" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle
                    cx="41.5"
                    cy="11.5"
                    r="11"
                    fill="white"
                    stroke="black"
                    @click="next"
                    style="cursor: pointer;"
                />
                <circle
                    cx="11.5"
                    cy="11.5"
                    r="11"
                    fill="white"
                    stroke="black"
                    @click="prev"
                    style="cursor: pointer;"
                />
                <path @click="prev" style="cursor: pointer;" fill-rule="evenodd" clip-rule="evenodd" d="M40.1943 15.8682C40.1622 15.8363 40.1368 15.7984 40.1195 15.7566C40.1022 15.7149 40.0933 15.6701 40.0933 15.6249C40.0933 15.5797 40.1022 15.5349 40.1195 15.4931C40.1368 15.4514 40.1622 15.4134 40.1943 15.3815L44.0766 11.4999L40.1943 7.61825C40.1623 7.58629 40.1369 7.54835 40.1196 7.50659C40.1023 7.46483 40.0934 7.42007 40.0934 7.37487C40.0934 7.32967 40.1023 7.28492 40.1196 7.24316C40.1369 7.2014 40.1623 7.16346 40.1943 7.1315C40.2262 7.09954 40.2642 7.07419 40.3059 7.05689C40.3477 7.03959 40.3924 7.03069 40.4376 7.03069C40.4828 7.03069 40.5276 7.03959 40.5693 7.05689C40.6111 7.07419 40.649 7.09954 40.681 7.1315L44.806 11.2565C44.838 11.2884 44.8634 11.3264 44.8807 11.3681C44.8981 11.4099 44.907 11.4547 44.907 11.4999C44.907 11.5451 44.8981 11.5899 44.8807 11.6316C44.8634 11.6734 44.838 11.7113 44.806 11.7432L40.681 15.8682C40.6491 15.9003 40.6111 15.9257 40.5694 15.943C40.5276 15.9603 40.4828 15.9692 40.4376 15.9692C40.3924 15.9692 40.3476 15.9603 40.3059 15.943C40.2641 15.9257 40.2262 15.9003 40.1943 15.8682Z" fill="black"/>
                <path @click="next" style="cursor: pointer;" fill-rule="evenodd" clip-rule="evenodd" d="M12.8057 7.13175C12.8378 7.16368 12.8632 7.20162 12.8805 7.24338C12.8978 7.28514 12.9067 7.32991 12.9067 7.37513C12.9067 7.42034 12.8978 7.46511 12.8805 7.50687C12.8632 7.54864 12.8378 7.58657 12.8057 7.6185L8.92344 11.5001L12.8057 15.3818C12.8377 15.4137 12.8631 15.4517 12.8804 15.4934C12.8977 15.5352 12.9066 15.5799 12.9066 15.6251C12.9066 15.6703 12.8977 15.7151 12.8804 15.7568C12.8631 15.7986 12.8377 15.8365 12.8057 15.8685C12.7738 15.9005 12.7358 15.9258 12.6941 15.9431C12.6523 15.9604 12.6076 15.9693 12.5624 15.9693C12.5172 15.9693 12.4724 15.9604 12.4307 15.9431C12.3889 15.9258 12.351 15.9005 12.319 15.8685L8.194 11.7435C8.16199 11.7116 8.13659 11.6736 8.11926 11.6319C8.10193 11.5901 8.09301 11.5453 8.09301 11.5001C8.09301 11.4549 8.10193 11.4101 8.11926 11.3684C8.13659 11.3266 8.16199 11.2887 8.194 11.2568L12.319 7.13175C12.3509 7.09974 12.3889 7.07434 12.4306 7.05701C12.4724 7.03968 12.5172 7.03076 12.5624 7.03076C12.6076 7.03076 12.6524 7.03968 12.6941 7.05701C12.7359 7.07434 12.7738 7.09974 12.8057 7.13175Z" fill="black"/>
            </svg>
        </div>
    </div>
</template>

<script setup>
import 'vue3-carousel/dist/carousel.css'
import { ref, defineExpose, onMounted } from 'vue';
import { Carousel, Slide, Pagination, Navigation } from 'vue3-carousel';
import ApiService from '@/core/services/ApiService';

const carouselConfig = {
  itemsToShow: 3,
  wrapAround: false,
};

const carouselRef = ref();

const next = () => carouselRef.value.next();
const prev = () => carouselRef.value.prev();

onMounted(() => {

});

const fetchMyPathsModules = async () => {
    try {
        const { data } = await ApiService.get(
            `/scorm-tracking/${props.trackableType}/${section.value?.id}`
        );

        if (data.success) {
            Swal.fire({
                title: "Done!",
                text: "Your response has been reset. Good luck on your next attempt!",
                confirmButtonText: "Let's go",
                icon: "success",
                customClass: {
                    confirmButton: 'btn fw-semibold btn-global-grey rounded'
                }
            });
                
            modalInstance.hide();

            router.push(props.redoSuccessRoute);
        } else {
            Swal.fire({
                title: "Error!",
                text: "Something went wrong, try again later.",
                icon: "error"
            });
        }

    } catch (error: any) {
        console.error("Error deleting SCORM tracking:", error);
        
        Swal.fire({
            title: "Error!",
            text: "Unexpected error occurred.",
            icon: "error"
        });
    }
};
</script>

<style scoped>
.my_path__modules{
    padding: 1rem 4rem;
}
</style>