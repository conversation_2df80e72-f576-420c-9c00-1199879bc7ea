html{
    scroll-behavior: smooth;
}
.profilling-progress-dial-parent{
    border-radius: 10px;
    padding: 7px;
    border: 1px dashed #fff;
}

.profilling-page{
    font-family: "Inter";
    font-optical-sizing: auto;
    /* font-weight: 500; */
    font-style: normal;
    font-variation-settings: "slnt" 0;
    background-color: #FFFFFF;
}
.profilling-top-navbar{
    background-color: #000000!important;
    height: 78px;
    padding-top: 20px
}
.profilling-top-navbar .navbar-brand {
    font-size: 40px;
    font-weight: 600;
}
.profiling-footer-next-step {
    background-color: #000000!important;
    height: 65px;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
}
.profiling-footer-next-step .nav-link{
    text-decoration: none;
    color: #fff;
    font-size: 14px;
}
.profilling-next-img{
    height: 20px;
    margin-left: 10px;
}
#user-profiling-next:hover {
    text-decoration: none;
}
.ns-right{
    border: 2px solid #fff;
    border-radius: 5px;
    padding: 10px;
}
.text-size-14{
    font-size: 14px;
}
.profilling-down-img{
    height: 35px;
}
.profilling-post{
    max-width: 320px;
    position: relative;
    margin-left: auto;
    margin-right: auto;
    overflow: hidden;
}

.profilling-post-modal{
    max-width: 400px;
    position: relative;
    margin-left: auto;
    margin-right: auto;
}

.profilling-post-video{
    width: 360px;
    height: 640px;
}

.video-js.vjs-9-16:not(.vjs-audio-only-mode) {
    /* border-radius: 10px; */
    overflow: hidden;
}
.vjs-control-bar
{
  position: relative;
  top: 15px;
}
.video-js .vjs-control-bar {
    background-color: transparent;
}
.video-bottom-section {
    color: white;
    position: absolute;
    bottom: 0;
    right: 20px;
    z-index: 200;
    width: 90%;
}
.video-bottom-section .video-like-btn{
    text-align: center;
    background: transparent;
    border: none;
    font-weight: bold;
    font-size: 2rem;
    text-align: right;
    color: white;
    position: absolute;
    bottom: 10px;
    right: 2px;
}

.video-top-section .video-like-btn{
    position: absolute;
    text-align: center;
    background: rgba(0, 0, 0, 0.6);
    border: none;
    /* font-weight: bold; */
    /* font-size: 2rem; */
    text-align: right;
    color: white;
    right: -12px;
    top: 8px;
}

.video-top-section .video-dislike-btn {
    position: absolute;
    text-align: center;
    background: rgba(0, 0, 0, 0.6);
    border: none;
    /* font-weight: bold; */
    /* font-size: 2rem; */
    text-align: right;
    color: white;
    right: -12px;
    top: 45px;
    margin-top: 5px;
}

.video-bottom-section .video-description{
    text-align: center;
    background: transparent;
    border: none;
    font-size: 12px;
    text-align: left;
    color: white;
    padding-left: 5px;
    padding-bottom: 10px;
}
.video-bottom-section .video-description h4{
    font-size: 18px;
}
.vjs-custom-video-close-btn{
    top: 10px;
}
.vjs-custom-video-like-btn{
    right: 20px;
    bottom: 50px;
}
.video-top-section{
    color: white;
    position: relative;
    top: 10px;
    right: 20px;
    z-index: 200;
}

.video-top-section .video-close-btn{
    position: absolute;
    text-align: center;
    background: transparent;
    border: none;
    /* font-weight: bold; */
    /* font-size: 2rem; */
    text-align: right;
    color: white;
    right: -20px;
    top: 8px;
}
.profilling-top-navbar-sub{
    display: none;
}
.profile-preview-col{
    width: 100%;
    border-top: 2px solid #fff;
}
.profile-preview img{
    width: 50px;
    height: 50px;
    border-radius: 50%;
}
.profile-preview h4{
    font-size: 22px!important;
    font-weight: 600!important;
    line-height: 1.8rem!important;
    top: 10px;
    position: relative;
    padding: 0%!important;
    margin-left: 10px;
}
.profile-preview .card{
    border-radius: 10px;
    top: 10%;
    width: 85%;
    margin-left: auto;
    margin-right: auto;
    height: auto;
}
.profilling-page .profile-preview-col{
    background-color: #000000;
}
.profilling-page .profile-preview{
    height: 82vh;
    overflow: auto;
}
.profile-preview-col .next-btn{
    background-color: #333333;
    width: 100%;
    text-align: center;
    color: #fff;
    border-color: #333333;
    height: 60px;
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 3rem
}

.profile-preview-col .next-btn:hover{
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
}
.jobs-chk-btn-group{
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
}

.job-sug-badge {
    border: 1px solid #F4F4F4;
    background: #F4F4F4;
    border-radius: 16px;
    font-size: 15px;
    font-weight: 500;
    color: #4E4E4E;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%; /* Equal height cards */
    margin-right: 10px;
}

.job-sug-badge .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.job-sug-badge-header{
    display:flex;
    justify-content:space-between;
}

.job-sug-badge .job-title {
    font-weight: 600;
    font-size: 16px;
}

.job-sug-badge .card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.5rem;
}

.job-sug-badge label{
    cursor: pointer;
}

.job-info-btn:hover {
    cursor: pointer;
}
/* input.custom-chk-btn:checked + .job-sug-badge > label > .mdi-add-circle{
    box-shadow: 0px 0px 3px inset;
    background: #eee;
    display: none;
}
input.custom-chk-btn:not(:checked) + .job-sug-badge > label > .mdi-check-circle{
    box-shadow: 0px 0px 3px inset;
    background: #eee;
    display: none;
} */
/* Hide add_circle when checked */
input.custom-chk-btn:checked + .job-sug-badge .mdi-add-circle {
    display: none;
}

/* Hide cancel when not checked */
input.custom-chk-btn:not(:checked) + .job-sug-badge .mdi-check-circle {
    display: none;
}

.suggestion-add-btn {
    border-radius: 12px;
    font-size: 0.875rem;
    font-weight: 500;
    border: 2px solid;
}
  
 
.job-sug-badge .mdi-check-circle{
    /* color: #d70000; */
}
.job-sug-badge .mdi-info{
    cursor: pointer;
}
input.jobs-chk-btn:not(:checked) + label:hover {
    /* box-shadow: 0px 1px 3px; */
    background-color: #d9d9d9;
}
/* input.jobs-chk-btn + label:active, */
input.jobs-chk-btn:checked + div.job-sug-badge {
    background: #68ADFF;
    color: #fff;
}


input.jobs-chk-btn-searchable + label {
    border: 1px solid #F4F4F4;
    background: #F4F4F4;
    cursor: pointer;
    border-radius: 20px;
    font-size: 15px;
    text-align: center;
    font-weight: 500;
    color: #4E4E4E;
    padding: 0.5rem 1.5rem .5rem 1.5rem;
}
input.jobs-chk-btn-searchable:not(:checked) + label:hover {
    /* box-shadow: 0px 1px 3px; */
    background-color: #d9d9d9;
}
input.jobs-chk-btn-searchable + label:active,
input.jobs-chk-btn-searchable:checked + label {
    background: #68ADFF!important;
    color: #fff;
}
.job-cluster h3{
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 1.5rem;
}
.preview-selected-job-label{
    border: 1px solid #68ADFF;
    background: #68ADFF;
    cursor: pointer;
    border-radius: 20px;
    font-size: 15px;
    text-align: left;
    font-weight: 500;
    padding: 0.5rem 1.5rem .5rem 1.5rem;
    color: #fff;
}
#more-jobs{
    display: flex;
    flex-wrap: wrap;
}
.profilling-page .form-search{
    background-color: #F4F4F680;
    padding: .3rem;
    border-radius: 5px;
}
.profilling-page .form-search input[type="text"]{
    background-color: #F4F4F680;
    border: unset;
    color: #000000;
    background-clip: text;
}
.profilling-page .form-search input[type="text"]:focus{
    border-color: #F4F4F680;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset, 0 0 8px #F4F4F680;
    outline: 0 none;
}
.profilling-page .form-search .btn{
    background-color: #F4F4F680;
}
.profilling-page .form-search input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
  color: #A5ABBF;
  font-size: 18px;
}

.profilling-page .form-search input:-moz-placeholder,
textarea:-moz-placeholder {
  color: #fff;
  font-size: 18px;
}
.video-top-section .account-name{
    position: absolute;
    left: 32px;
    top: 10px;
}
.red-like-btn svg{
    stroke: #FD1D1D;
    fill: #FD1D1D;
    animation: likeAnimation 0.1s;
}
.white-like-btn svg{
    stroke: #fff;
    fill: #fff;
    animation: likeAnimation 0.1s;
}
@keyframes likeAnimation {
    from {
        transform: scale(0);
    }
    to {
        transform: scale(1);
    }
}
.vjs-big-play-button {
    /* background-color: transparent !important; */
    /* width: 100% !important; */
    height: 100%;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('/public/custom/img/play2.png') !important;
    background-repeat: no-repeat !important;
    background-size: 46px !important;
    background-position: 50% calc(50% - 0px) !important;
    border: none !important;
    height: 46px!important;
    width: 46px!important;
    border-radius: 1.5rem!important;
    left: 55%!important;
}
.profiling-posts-page-links{
    margin-bottom: 10rem;
    margin-top: 1rem;
    position: relative;
}
@media (min-width: 1200px) {
    .profilling-posts .mt-xl-5{
        top: 55px;
    }
}
.job-cluster .section-link{
    text-decoration: none;
    color: inherit;
}

.job-clusters-parent{
    height: 90vh;
    overflow-y: scroll;
}
/* width */
.job-clusters-parent::-webkit-scrollbar {
    width: 8px;
}

/* Track */
.job-clusters-parent::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px grey;
    border-radius: 10px;
}

/* Handle */
.job-clusters-parent::-webkit-scrollbar-thumb {
    background: #494848;
    border-radius: 10px;
}

/* Handle on hover */
.job-clusters-parent::-webkit-scrollbar-thumb:hover {
    background: #494848;
}
.profiling-posts-page-links {
    margin-top: 8rem;
}
.job-search-results-parent{
    position: relative;
}
.job-search-results, .job-search-results-sub{
    position: absolute;
    z-index: 10;
    background: #4f4f4f;
    width: 100%;
    display: none;
    border-radius: 10px;
    color: #fff;
    padding: 12px;
    width: 100%;
    max-height: 350px;
    overflow-y: auto;
}

.search-wrapper {
    position: relative;
    width: 100%;
}

.job-search-control {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
}

.search-icon,
.clear-icon {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    color: #aaa;
    cursor: pointer;
}

.search-icon {
    left: 10px;
}

.clear-icon {
    right: 10px;
}

input.custom-chk-btn-searchable {
    display: none;
}
input.custom-chk-btn-searchable + label {
    border: 1px solid grey;
    background: ghoswhite;
    padding: 5px 8px;
    cursor: pointer;
    border-radius: 5px;
}
input.custom-chk-btn-searchable:not(:checked) + label:hover {
    /* box-shadow: 0px 1px 3px; */
}
input.custom-chk-btn-searchable + label:active,
input.custom-chk-btn-searchable:checked + label {
    box-shadow: 0px 0px 3px inset;
    background: #eee;
}
button.add-job-results{
    position: sticky;
    bottom: 0;
    background-color: #333333 !important;
    color: #fff;
    z-index: 11;
}
button.add-job-results:hover{
    background-color: #007bff;
    color: #fff;
}
.navbar-brand img{
    width: 180px;
}

/* vjs progress controll added April 4 2025 */

.video-js {
    position: relative;
    overflow: visible !important;
    height: 100%;
    width: 100%;
    max-width: 100%;
    margin: 0;
    object-fit: cover;
    background: none;
}

.video-js .vjs-control-bar {
    position: relative;
    height: auto;
    min-height: 30px;
}

.video-js .vjs-progress-control {
    /* top: 535px; */
    margin: -22px -10px 0 -8px;
}

.video-js .vjs-slider{
    background-color: transparent;
}

.vjs-play-progress {
    /* background: #fff !important; */
    border-radius: 7px;
}

.vjs-progress-holder {
    height: 7px !important;
    border-radius: 5px !important;
}

.vjs-play-progress::before {
    display: none !important;
}

.vjs-load-progress {
    background: transparent !important;
    border-radius: 0 !important;
    overflow: hidden;
}

.vjs-load-progress:before,
.vjs-load-progress:after {
    display: none !important;
}

/* Style the white loaded portion */
.vjs-load-progress div {
    background-color: transparent !important;
}

.vjs-loading-custom{
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0.85;
    text-align: left;
    box-sizing: border-box;
    background-clip: padding-box;
    width: 5em;
    height: 5em;
    border-radius: 50%;
    background-image: url("/public/custom/img/gifs/loading-profiling.gif");
    background-repeat: no-repeat;
    background-position: center;
    background-size: 400px 400px;
}
.skeleton-box {
    display: inline-block;
    height: 1em;
    position: relative;
    overflow: hidden;
    background-color: #DDDBDD;
}

@keyframes shimmer {
    100% {
        transform: translateX(100%);
    }
}

.skeleton-box::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    transform: translateX(-100%);
    background-image: linear-gradient(90deg, rgba(255, 255, 255, 0) 0, rgba(255, 255, 255, 0.2) 20%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0));
    -webkit-animation: shimmer 1s infinite;
    animation: shimmer 1s infinite;
    content: "";
}

.video-left-controls {
    display: flex;
    margin-left: -12px;
}

/**
* For Small Laptops
*/
@media only screen and (max-device-width: 1366px){
    .video-bottom-section .video-description h4{
        font-size: 16px;
    }
}



/**
* Mobile Screens
*/
@media only screen and (max-device-width: 480px){
    .profilling-top-navbar {
        height: auto;
    }
    .profilling-post{
        max-width: 100%;
    }
    .profilling-post-video{
        width: 100%;
        height: auto;

        aspect-ratio: 9/16;
    }
    .profiling-footer-next-step {
        height: auto;
    }
    .profilling-top-navbar-sub{
        display: block;
        height: auto;
        background-color: #000000;
        color: #fff;
        text-align: center;
    }
    .profilling-top-navbar-sub a{
        text-decoration: none;
        cursor: pointer;
        color: #fff;
    }
    .profilling-page .form-search{
        flex-flow: row;
        margin: 0rem 1rem 0rem 1rem;
    }
}


@media (max-width: 576px) {
    .jobs-chk-btn-group {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .jobs-chk-btn-group {
        grid-template-columns: 1fr;
    }
    @media (max-width: 640px) {
        .next-label {
            display: none;
        }
    }
}



