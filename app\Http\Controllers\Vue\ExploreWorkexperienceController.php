<?php

namespace App\Http\Controllers\Vue;

use Illuminate\Support\Facades\Validator;
use App\Mail\WorkexperienceResponseNotification;
use App\WorkexperienceResponse;
use App\WorkexperienceTemplate;
use App\VweStepResponse;
use App\Step;
use App\IndustryCategory;
use App\Menu;
use App\User;
use App\Banner;
use App\Events\WorkExperienceResponseReset;
use App\Events\WorkExperienceResponseSubmitted;
use App\Services\SCORM;
use App\Services\TimelineService;
use Auth;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use App\Services\UserAccessService;
use Mail;

class ExploreWorkexperienceController extends Controller
{
    protected $scorm;

    public function __construct(SCORM $scorm)
    {
        $this->scorm = $scorm;

        // $this->middleware('has_access_to_virtualworkexperience')->except('downloadPDF','studentResponse');
        $this->middleware(function ($request, $next) {

            if (Auth::user()->isStudent()/*  && Auth::user()->school()->exists() */) {
                return $next($request);
            }
            abort(403, 'You do not have permission to perform this action.');
        })->only('storeResponse', 'updateResponse', 'addTime');

        $this->middleware(function ($request, $next) {
            if (Auth::user()->school_id && Auth::user()->organisation_id) {
                if (Auth::user()->school->campuses()->exists()) {
                    $menu1 = Menu::whereYearId(Auth::user()->profile->standard_id)->whereParentMenu('Work Experience Week')->whereCampusId(Auth::user()->campuses->first()->id)->where(function ($query) {
                        $query->where('title', 'Work Experience')->orWhere('title', 'Work Experience Week');
                    })->exists();
                } else {
                    $menu1 = Menu::whereYearId(Auth::user()->profile->standard_id)->whereParentMenu('Work Experience Week')->whereSchoolId(Auth::user()->school_id)->where(function ($query) {
                        $query->where('title', 'Work Experience')->orWhere('title', 'Work Experience Week');
                    })->exists();
                }

                if (Auth::user()->organisation->campuses()->exists()) {
                    $menu2 = Menu::whereYearId(Auth::user()->profile->standard_id)->whereParentMenu('Work Experience Week')->whereCampusId(Auth::user()->orgCampuses->first()->id)->where(function ($query) {
                        $query->where('title', 'Work Experience')->orWhere('title', 'Work Experience Week');
                    })->exists();
                } else {
                    $menu2 = Menu::whereYearId(Auth::user()->profile->standard_id)->whereParentMenu('Work Experience Week')->whereSchoolId(Auth::user()->organisation_id)->where(function ($query) {
                        $query->where('title', 'Work Experience')->orWhere('title', 'Work Experience Week');
                    })->exists();
                }
                if ($menu1 && $menu2) {
                    abort(403, 'You do not have permission to perform this action.');
                }
            } elseif (Auth::user()->organisation_id && !Auth::user()->school_id) {
                if (Auth::user()->organisation->campuses()->exists()) {
                    $menu = Menu::whereYearId(Auth::user()->profile->standard_id)->whereParentMenu('Work Experience Week')->whereCampusId(Auth::user()->orgCampuses->first()->id)->where(function ($query) {
                        $query->where('title', 'Work Experience')->orWhere('title', 'Work Experience Week');
                    })->exists();
                } else {
                    $menu = Menu::whereYearId(Auth::user()->profile->standard_id)->whereParentMenu('Work Experience Week')->whereSchoolId(Auth::user()->organisation_id)->where(function ($query) {
                        $query->where('title', 'Work Experience')->orWhere('title', 'Work Experience Week');
                    })->exists();
                }
                if ($menu) {
                    abort(403, 'You do not have permission to perform this action.');
                }
            } elseif (!Auth::user()->organisation_id && Auth::user()->school_id) {
                if (Auth::user()->school->campuses()->exists()) {
                    $menu = Menu::whereYearId(Auth::user()->profile->standard_id)->whereParentMenu('Work Experience Week')->whereCampusId(Auth::user()->campuses->first()->id)->where(function ($query) {
                        $query->where('title', 'Work Experience')->orWhere('title', 'Work Experience Week');
                    })->exists();
                } else {
                    $menu = Menu::whereYearId(Auth::user()->profile->standard_id)->whereParentMenu('Work Experience Week')->whereSchoolId(Auth::user()->school_id)->where(function ($query) {
                        $query->where('title', 'Virtual Work Experience')->orWhere('title', 'Work Experience Week');
                    })->exists();
                }
                if ($menu) {
                    abort(403, 'You do not have permission to perform this action.');
                }
            }

            // if (Auth::user()->school()->exists()) {
            //     $menu = Menu::whereYearId(Auth::user()->profile->standard_id)->whereParentMenu('Work Experience Week')->whereSchoolId(Auth::user()->school_id)->where(function($query){
            //         $query->where('title', 'Work Experience')->orWhere('title', 'Work Experience Week');
            //     })->exists();
            //     if($menu) {
            //         abort(403, 'You do not have permission to perform this action.');
            //     }
            // }
            return $next($request);
        })->except('completedWorkexperience', 'show', 'addTime', 'downloadPDF');
    }

    public function list()
    {
        $difficulty = request('difficulty');
        $industry = request('industry');
        $skill = request('skill');
        $subject = request('subject');

        $categories = IndustryCategory::whereHas('vwetemplates')
            ->with([
                'vwetemplates.userResponse',
                'vwetemplates' => function ($templates) use ($difficulty, $skill, $subject) {
                    $templates->published()->select('workexperience_templates.id', 'workexperience_templates.tileimage', 'workexperience_templates.title', 'workexperience_templates.level', 'workexperience_templates.estimated_time')
                        ->when($subject, function ($query) use ($subject) {
                            return $query->whereHas('subjects', function ($q) use ($subject) {
                                $q->where('workexperience_subjects.id', $subject);
                            });
                        })
                        ->when($difficulty, function ($query) use ($difficulty) {
                            return $query->where('level', $difficulty);
                        })
                        ->when($skill, function ($query) use ($skill) {
                            return $query->withAllTags([$skill]);
                        });
                }
            ])
            ->when($industry, function ($query) use ($industry) {
                return $query->whereId($industry);
            })->orderBy('name')->get();

        return $categories->toArray();
    }

    public function detail($id, $student = null)
    {
        if ($student && !UserAccessService::currentUserCanAccess($student)) {
            abort(403, 'You do not have permission to perform this action.');
        }

        $workexperienceTemplate = WorkexperienceTemplate::find($id)->append('hasresponse')->append('firststepresponse');

        if (!$workexperienceTemplate->hasresponse && $workexperienceTemplate->userResponse) {
            $workexperienceTemplate->userResponse()->delete();
        }
        if (@$workexperienceTemplate->userResponse()->with('stepResponses.step')->first()->stepResponses) {
            foreach ($workexperienceTemplate->userResponse()->with('stepResponses.step')->first()->stepResponses as $step) {
                if (!$step->step) {
                    $step->delete();
                }
            }
        }

        $workexperienceTemplate =  WorkexperienceTemplate::select('id', 'tileimage', 'title', 'level', 'estimated_time', 'badge_id', 'background_imagepath', 'background_videoid', 'foreground_videoid', 'response', 'curriculum')
            ->with('worksheets', 'teacherResources', 'audio', 'tagged:id,tag_name,taggable_id', 'userResponse:id,status,template_id,student_id,filename,response_path,feedback,approve', 'userResponse.stepResponses.step', 'userResponse.badgeKey.badge.companies', 'steps.userResponse:workexperience_response_id,response,step_id','badge')->where('id', $id)->first()->append('audiofullpath')->append('favourite')->append('hasresponse')->append('firststepresponse');

        // ANZSCO and SCORM -- START
        $workexperienceTemplate->append([
            'anzsco_tag_names_grouped',
        ]);
        $workexperienceTemplate->load([
            'steps.scormTrackings' => fn($q) => $q->where('user_id', $student ?: auth()->id()),
            'steps.scormResult' => fn($q) => $q->where('user_id', $student ?: auth()->id()),
        ]);
        $workexperienceTemplate->steps->each->append('scorm_interactions');
        $workexperienceTemplate->scorm_scoring_step_result =  $workexperienceTemplate->userScormScoringStepResult($student ?: auth()->id());
        // ANZSCO and SCORM -- END

        $relatedModules = $workexperienceTemplate->skillstrainingTemplates()
            ->when(Auth::user()->isStudent(), function ($query) {
                return $query->forStudents()->whereHas('years', function ($q) {
                    $q->where('standard_id', Auth::user()->profile->standard_id);
                });
            })->get();
        $workexperienceTemplate = $workexperienceTemplate->toArray();
        $workexperienceTemplate['relatedModules'] = $relatedModules;
        if ($workexperienceTemplate && !empty($workexperienceTemplate['background_imagepath'])) {
            $workexperienceTemplate['background_imagepath'] = Storage::url($workexperienceTemplate['background_imagepath']);
        }
        if ($workexperienceTemplate['relatedModules'] && !empty($workexperienceTemplate['relatedModules'])) {
            foreach ($workexperienceTemplate['relatedModules'] as $k => $rl) {
                if (!empty($rl['background_imagepath'])) {
                    $workexperienceTemplate['relatedModules'][$k]['background_imagepath'] = Storage::url($rl['background_imagepath']);
                }
            }
        }

        // dd($workexperienceTemplate);

        return $workexperienceTemplate;
    }

    public function searchVwes($value)
    {
        $searchedListData = [];

        $we = WorkexperienceTemplate::published()->select('id', 'title', 'tileimage', 'level', 'publish')->where('title', 'like', '%' . $value . '%')->take(10)->get();
        if (Auth::user()->hasVweAccess() && $we->count() > 0) {
            foreach ($we as $template) {
                if ($template->tileimage) {
                    $searchedListData['vwes'][] = [
                        'id' => $template->id,
                        'tile_img' => $template->tileimage_fullpath,
                        'title' =>  strlen($template->title) > 35 ? substr($template->title, 0, 35) . "..." : $template->title,
                        'url' =>  "/#/tasks/vwe/{$template->id}",
                        'level' => $template->level,
                    ];
                }
            }
        } else {
            $searchedListData['vwes'] = '';
        }
        return $searchedListData;
    }

    public function studentResponse($id, $student)
    {
        if (!UserAccessService::currentUserCanAccess($student)) {
            abort(403, 'You do not have permission to perform this action.');
        }

        $workexperience =  WorkexperienceTemplate::select('id', 'tileimage', 'title', 'level', 'badge_id', 'estimated_time', 'background_imagepath', 'background_videoid', 'foreground_videoid', 'response')->with('tagged:id,tag_name,taggable_id','badge')->where('id', $id)->first();


        if (@$workexperience->userResponse($student)->with('stepResponses.step')->first()->stepResponses) {
            foreach ($workexperience->userResponse($student)->with('stepResponses.step')->first()->stepResponses as $step) {
                if (!$step->step) {
                    $step->delete();
                }
            }
        }

        // ANZSCO and SCORM -- START
        $workexperience->append([
            'anzsco_tag_names_grouped'
        ]);
        $workexperience->load([
            'steps.scormTrackings' => fn($q) => $q->where('user_id', $student),
            'steps.scormResult' => fn($q) => $q->where('user_id', $student),
        ]);
        $workexperience->steps->each->append('scorm_interactions');
        $workexperience->scorm_scoring_step_result =  $workexperience->userScormScoringStepResult($student);
        // ANZSCO and SCORM -- END

        $workexperience->student_response = $workexperience->userResponse($student)->with('stepResponses.step', 'badgeKey.badge.companies')->first()->toArray();

        if ($workexperience->student_response['status'] != 'Submitted') {
            abort(403, 'You do not have permission to perform this action.');
        }

        if ($workexperience && !empty($workexperience['background_imagepath'])) {
            $workexperience['background_imagepath'] = Storage::url($workexperience['background_imagepath']);
        }


        // if ($workexperience->steps) {
        //     foreach ($workexperience->steps as $k => $rl) {
        //         if ($rl->userResponse($student)->first()) {
        //             $workexperience->steps[$k]->student_response = $rl->userResponse($student)->with('step')->first()->toArray();
        //         }
        //     }
        // }

        $workexperience->student_completed_percentage =  round(((($workexperience->steps->pluck('student_response')->count() + 1) / ($workexperience->steps->count() + 1)) * 100) / 5) * 5;

        $workexperience->feedback = Auth::user()->isTeacher() ? $workexperience->student_response['feedback'] : $workexperience->student_response['org_feedback'];

        return $workexperience->toArray();
    }

    public function updateResponse(Request $request, $response)
    {
        $validator = Validator::make(
            $request->all(),
            [
                'response' => 'required|mimes:pptx,docx,pages,pdf',
            ],
            []
        );
        if ($validator->fails()) {
            return 'file-error';
        }
        $hour = (int)$request->hour;
        $minute = (int)$request->minute;
        if (!$hour && !$minute) {
            return 'time-error';
        }
        $time = $hour . ':' . $minute . ':0';
        try {

            $response = WorkexperienceResponse::where('student_id', Auth::id())->findOrFail($response);

            if ($response->response_path) {
                Storage::cloud()->delete($response->response_path);
            }

            $path = Storage::cloud()->put('responses', $request->file('response'));
            if ($path) {
                $response->response_path = $path;
                $response->filename =  $request->file('response')->getClientOriginalName();
                $response->time = $time;
                $response->save();
                if ($response) {
                    $type = "updated";
                    Mail::to(config('mail.contact.to'))->send(new WorkexperienceResponseNotification($type));
                    return $response;
                }
                return 'fail';
            }
        } catch (\Exception $ex) {
            report($ex);
            return 'fail';
        }
    }

    public function banner()
    {
        return Banner::whereType('Virtual Work Experience')->first();
    }

    public function  togglefav($id)
    {
        $workexperienceTemplate = WorkexperienceTemplate::find($id);
        if ($workexperienceTemplate) {
            Auth::user()->toggleFavorite($workexperienceTemplate);
        }
        return $workexperienceTemplate->append('favourite')->only(['id', 'tileimage', 'title', 'level', 'estimated_time', 'favourite']);
    }

    public function  resetVwe($id)
    {
        $workexperienceTemplate = WorkexperienceTemplate::find($id);
        $stepids = $workexperienceTemplate->steps->pluck('id');

        $workexperienceresponse = WorkexperienceResponse::where('template_id', $workexperienceTemplate->id)->where('student_id', Auth::user()->id)->first();

        if ($stepids) {
            if ($workexperienceresponse) {
                VweStepResponse::where('workexperience_response_id', $workexperienceresponse->id)->whereIn('step_id', $stepids)->delete();
            }
        }

        if ($workexperienceresponse) {
            $workexperienceresponse->delete();
            // ANZSCO and SCORM -- START
            $this->scorm->resetModuleScormTracking(auth()->user(), $workexperienceTemplate);
            // ANZSCO and SCORM -- END
            event(new WorkExperienceResponseReset($workexperienceTemplate,Auth::user()));
        }

        return 'success';
    }

    public function  sectiondetail($id, $sectionid)
    {
        $workexperience = WorkexperienceTemplate::with('userResponse:status,template_id,student_id,filename', 'steps.userResponse', 'steps.userScormResult')->where('id', $id)->first();

        $workexperience->toArray();
        if ($workexperience && !empty($workexperience['background_imagepath'])) {
            $workexperience['background_imagepath'] = Storage::url($workexperience['background_imagepath']);
        }
        if ($workexperience['steps'] && !empty($workexperience['steps'])) {
            foreach ($workexperience['steps'] as $k => $rl) {
                if (!empty($rl['bg_image'])) {
                    $workexperience['steps'][$k]['bg_image'] = Storage::url($rl['bg_image']);
                }
                if (!empty($rl['scorm_path'])) { // SCORM launch file url
                    $workexperience['steps'][$k]['scorm_path'] = $this->scorm->scormUrl($rl['scorm_path']);
                }
            }
        }

        return $workexperience;
    }

    public function storeResponse(Request $request, $id, $sectionid)
    {
        if (!Auth::user()->isStudent()) {
            return ['error' => "This serves a demo purpose. You can't submit the task. Thank you!"];
        }

        $section = Step::find($sectionid);
        $errors = [];
        if ($section) {
            if ($section->template_id == $id) {
                $response = $request->response;
                $user = Auth::user();
                $workexperienceresponseExisting = WorkexperienceResponse::where(
                    [
                        'template_id' => $section->template_id,
                        'student_id' => $user->id,
                    ],
                )->first();

                if ($workexperienceresponseExisting) {
                    $workexperienceresponse = $workexperienceresponseExisting;
                } else {
                    $workexperienceresponse = WorkexperienceResponse::create(
                        [
                            'template_id' => $section->template_id,
                            'student_id' => $user->id,
                            'status' => 'Draft'
                        ],
                    );
                }
                event(new WorkExperienceResponseSubmitted($workexperienceresponse));

                $response = VweStepResponse::updateOrCreate([
                    'workexperience_response_id' => $workexperienceresponse->id,
                    'step_id' => $sectionid
                ], [
                    'response' => $response
                ]);

                if ($workexperienceresponseExisting) {
                    (new TimelineService())->log($workexperienceresponse, 'updated');
                } else {
                    (new TimelineService())->log($workexperienceresponse, 'created');
                }
            } else {
                $errors[] = "Invalid request";
            }
        } else {
            $errors[] = "Invalid request: Section does not exists ";
        }
        if (!empty($errors)) {
        }

        return $response->toArray();
    }

    public function storeFinalResponse(Request $request, $id)
    {
        if (!Auth::user()->isStudent()) {
            return ['error' => "This serves a demo purpose. You can't submit the task. Thank you!"];
        }

        $workexperience = WorkexperienceTemplate::find($id);
        if ($workexperience->response && $request->file('response')) {
            $ext = $request->file('response')->getClientOriginalExtension();
            $validator = ['ppt', 'pptx', 'doc', 'docx', 'xlsx', 'xls', 'pages', 'pdf'];

            if (!in_array($ext, $validator)) {
                return ['error' => 'Invalid file format . Valid files are ppt, pptx, doc, docx, xlsx, xls, pages, pdf '];
            }
        } else if ($workexperience->response && $request->nofile != "false") {
            return ['error' => 'Please select a file to upload'];
        }

        $hour = (int)$request->hour;
        $minute = (int)$request->mins;

        $time = ($hour < 0 ? 0 : $hour) . ':' . $minute . ':0';

        try {
            if ($request->nofile == "false") {
                $path = Storage::cloud()->put('responses', $request->file('response'));
                $filename = $request->file('response')->getClientOriginalName();
            } else {
                $path = '';
                $filename = '';
            }

            if (($workexperience->response && ($path || $request->nofile == "false")) || !$workexperience->response) {
                $response = WorkexperienceResponse::updateOrCreate(
                    [
                        'template_id' => $workexperience->id,
                        'student_id' => Auth::id(),
                    ],
                    [
                        'response_path' => $path,
                        'time' => $time,
                        'filename' => $filename,
                        'submitted_at' => now(),
                        'status' => 'Submitted'
                    ]
                );

                if ($response) {
                    event(new WorkExperienceResponseSubmitted($response));
                    return $response;
                }
                return ['error' => 'Unable to upload your response at the moment '];
            }
        } catch (\Exception $ex) {
            return ['error' => 'Unable to upload your response at the moment ' . $ex->getMessage()];
        }
    }
}
