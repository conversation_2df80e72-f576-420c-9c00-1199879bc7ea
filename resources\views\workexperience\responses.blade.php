@extends('layouts.admin')
@section('breadcrumbs', Breadcrumbs::render('workexperience-responses'))
@push('stylesheets')
    <link href="{{ asset('assets/plugins/froala_editor/css/froala_editor.pkgd.min.css') }}" rel="stylesheet" type="text/css" />
@endpush
@section('content')
    <style>

    </style>
    @if (session()->has('message'))
        <div class="alert alert-success text-center">
            <a href="#" class="close" data-dismiss="alert" aria-label="close"></a>
            {{ session()->get('message') }}
        </div>
    @endif
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header separator">
                    <div class="card-title">Filter</div>
                </div>
                <div class="card-block">
                    <form method="GET">
                        <div class="row clearfix">
                            <div class="col-sm-3 col-md-3">
                                <div class="form-group form-group-default">
                                    <label>Student's name</label>
                                    <input type="text" name="name" value="{{ old('name') }}" class="form-control">
                                </div>
                            </div>
                            <div class="col-sm-6 col-md-3">
                                <div class="form-group form-group-default form-group-default-select2">
                                    <label>Task</label>
                                    <select class="full-width" name="template" data-init-plugin="select2" data-allowClear="true">
                                        <option value="">Any</option>
                                        @foreach ($templates as $template)
                                            <option value="{{ $template->id }}" @if (old('template') == $template->id) selected="selected" @endif>{{ $template->title }}</option>
                                        @endforeach
                                    </select>

                                </div>
                            </div>
                            <div class="col-sm-6 col-md-3">
                                <div class="form-group form-group-default form-group-default-select2">
                                    <label>Year</label>
                                    <select class="full-width" name="standard" data-init-plugin="select2" data-minimum-results-for-search="Infinity">
                                        <option value="">Any</option>
                                        @foreach ($standards as $standard)
                                            <option value="{{ $standard->id }}" @if (old('standard') == $standard->id) selected="selected" @endif>{{ $standard->title }}</option>
                                        @endforeach

                                    </select>
                                </div>
                            </div>
                            <div class="col-sm-6 col-md-3">
                                <div class="form-group form-group-default form-group-default-select2">
                                    <label>Feedback</label>
                                    <select class="full-width" name="feedback" data-init-plugin="select2" data-minimum-results-for-search="Infinity">
                                        <option value="">Any</option>
                                        <option value="yes" @if (old('feedback') == 'yes') selected="selected" @endif>Yes
                                        </option>
                                        <option value="no" @if (old('feedback') == 'no') selected="selected" @endif>No
                                        </option>
                                        @if (Auth::user()->isAdmin())
                                            <option value="pending" @if (old('feedback') == 'pending') selected="selected" @endif> Pending</option>
                                        @endif

                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row clearfix">
                            <div class="col-sm-6 col-md-3">
                                <div class="form-group form-group-default form-group-default-select2">
                                    <label>State</label>
                                    <select class="full-width" name="state" data-init-plugin="select2" data-minimum-results-for-search="Infinity">
                                        <option value="">Any</option>
                                        @foreach ($states as $state)
                                            <option value="{{ $state->id }}" @if (old('state') == $state->id) selected="selected" @endif>{{ $state->name }}</option>
                                        @endforeach

                                    </select>
                                </div>
                            </div>
                            <div class="col-sm-6 col-md-3">
                                <div class="form-group form-group-default form-group-default-select2">
                                    <label>School/Organisation</label>
                                    <select class="full-width" name="institute_type" data-init-plugin="select2" id="inst_type" data-minimum-results-for-search="Infinity">
                                        <option value="" selected>Any</option>
                                        <option value="School" @if ($selected_institute == 'School') selected="selected" @endif>School
                                        </option>
                                        <option value="Organisation" @if ($selected_institute == 'Organisation') selected="selected" @endif>Organisation
                                        </option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-sm-6 col-md-3" id="scl">
                                <div class="form-group form-group-default form-group-default-select2">
                                    <label>School</label>
                                    <select class="full-width" name="school" data-init-plugin="select2" id="school">
                                        <option value="" selected>Any</option>
                                        @foreach ($schools as $key => $school)
                                            <option value="{{ $school->id }}" @if (old('school') == $school->id) selected="selected" @endif>{{ $school->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-sm-6 col-md-3" id="org">
                                <div class="form-group form-group-default form-group-default-select2">
                                    <label>Organisation</label>
                                    <select class="full-width" name="organisation" data-init-plugin="select2" id="organisation">
                                        <option value="" selected>Any</option>
                                        @foreach ($orgs as $key => $org)
                                            <option value="{{ $org->id }}" @if (old('organisation') == $org->id) selected="selected" @endif>{{ $org->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-sm-12 col-md-12 text-right">
                                <button type="submit" class="btn btn-primary btn-custom-sm">Search</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="card card-default">
                <div class="card-header separator">
                    <div class="card-title">
                        Virtual Work Experience Responses
                    </div>
                </div>
                <div class="card-block">
                    <div class="my-2">
                        <a class="btn btn-primary" href="/we/export?fullname={{ old('name') }}&template={{ old('template') }}&year={{ old('standard') }}&feedback={{ old('feedback') }}&state={{ old('state') }}&institute_type={{ old('institute_type') }}&school={{ old('school') }}&organisation={{ old('organisation') }}">Export All <i class="fa fa-download"></i></a>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover dataTable no-footer">
                            <thead>
                                <tr>
                                    @if (Auth::user()->isAdmin())
                                        <th>Approve</th>
                                        <th>Feedback Provider</th>
                                    @endif
                                    <th>Student Name</th>
                                    <th>{{ $selected_institute }}</th>
                                    <th>Year</th>
                                    <th>Template</th>
                                    <th>Submitted On</th>
                                    <th>Feedback</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse ($responses as $key => $response)
                                    <tr>
                                        @if (Auth::user()->isAdmin())
                                            <td>
                                                @if ($response->feedback && $response->user->isMarker())
                                                    <i class="fa fa-check text-warning" data-toggle="tooltip" title="Pending feedback"></i>
                                                @else
                                                    @if ($response->approve)
                                                        <i class="fa fa-check text-success" data-toggle="tooltip" title="Approved"></i>
                                                    @else
                                                        <i class="fa fa-times text-danger" data-toggle="tooltip" title="No feedback"></i>
                                                    @endif
                                                @endif
                                            </td>
                                            <td>
                                                @if ($response->user_id)
                                                    {{ $response->user->name }}
                                                @else
                                                    <i class="fa fa-user text-danger" data-toggle="tooltip" title="No provider"></i>
                                                @endif
                                            </td>
                                        @endif
                                        <td>
                                            {{ $response->student->name }}
                                            <input type="hidden" name="student" id="student" value="{{ $response->student->name }}">
                                        </td>
                                        <td>
                                            @if ($selected_institute == 'School')
                                                {{ @$response->student->school->name ?? @$response->student->profile->school }}
                                            @else
                                                {{ @$response->student->organisation->name ?? @$response->student->profile->school }}
                                            @endif
                                        </td>
                                        <td>{{ @$response->student->profile->class->title == 'I’ve finished high school' ? 'Graduated ' . @$response->student->profile->graduate_year : @$response->student->profile->class->title }}
                                        </td>
                                        <td>{{ $response->template->title }}</td>
                                        <td>{{ $response->submitted_at }}</td>
                                        <td>
                                            @if ($response->feedback)
                                                <a href="{{ url('vwe-certificate/' . $response->id) }}"><i class="fa fa-eye"></i></a>
                                            @else
                                                <i class="fa fa-times text-danger"></i>
                                            @endif
                                        </td>
                                        <td>
                                            @if ($response->view_response_file_path != 'unsupported')
                                                <a href="#" class="m-r-10" data-toggle="modal" data-target="#responseFileModal" data-iframe="{{ $response->view_response_file_path }}" data-download="{{ route('workexperience.responsedownload', $response->id) }}"><i class="fa fa-file" data-toggle="tooltip" title="View Response File"></i></a>
                                            @else
                                                <a class="m-r-10" href="{{ route('workexperience.responsedownload', $response->id) }}" data-toggle="tooltip" title="Download user response"><i class="fa fa-cloud-download"></i></a>
                                            @endif
                                            @if (Auth::user()->isAdmin() || (Auth::user()->isMarker() && !$response->approve))
                                                <a class="m-r-10" data-toggle="modal" data-target="#feedbackModal" data-id="{{ $response->id }}"><i class="fa fa-commenting-o" data-toggle="tooltip" title="Add feedback"></i></a>
                                            @else
                                                <i class="fa fa-check text-success" data-toggle="tooltip" title="Feedback approved"></i>
                                            @endif
                                            @if (Auth::user()->isAdmin())
                                                <a class="m-r-10" data-toggle="modal" data-target="#messageModal" data-id="{{ $response->id }}"><i class="fa fa-envelope @if ($response->message) text-success @endif" data-toggle="tooltip" title="@if ($response->message) Message sent @else Add message @endif"></i></a>
                                                <a href="/workexperiencetemplates/responses/{{ $response->id }}" data-method="delete" data-token="{{ csrf_token() }}" data-confirm="Are you sure?"><i class="fa fa-trash-o text-danger" data-toggle="tooltip" title="Delete response"></i></a>
                                            @endif
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="9" class="text-center">Sorry! No result found.</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                        {{ $responses->appends(array_filter(['name' => old('name'), 'template' => old('template'), 'standard' => old('standard'), 'feedback' => old('feedback'), 'institute_type' => old('institute_type'), 'school' => old('school'), 'organisation' => old('organisation')]))->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
    @push('modals')
        <div class="modal fade slide-up" id="feedbackModal" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content-wrapper">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="exampleModalLabel">Add Feedback</h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <form class="form" method="POST" action="" id="formFeedback">
                            <input type="hidden" name="_method" value="PUT">
                            {{ csrf_field() }}
                            <div class="modal-body">
                                <input type="hidden" name="oldname" value="{{ old('name') }}">
                                <input type="hidden" name="oldschool" value="{{ old('school') }}">
                                <input type="hidden" name="oldtemplate" value="{{ old('template') }}">
                                <input type="hidden" name="oldstandard" value="{{ old('standard') }}">
                                <input type="hidden" name="oldfeedback" value="{{ old('feedback') }}">
                                <input type="hidden" name="oldorganisation" value="{{ old('organisation') }}">
                                <input type="hidden" name="oldinstitute_type" value="{{ $selected_institute }}">
                                <div class="row">
                                    <div class="col-12">
                                        <div class="form-group form-group-default">
                                            <label>Feedback</label>
                                            <textarea name="feedback" id="feedback" rows="7" class="form-control froala"></textarea>
                                        </div>
                                    </div>
                                </div>
                                @if (Auth::user()->isAdmin())
                                    <div class="row">
                                        <div class="check-primary no-margin">
                                            <input type="checkbox" value="1" id="approve" name="approve">
                                            <label for="" class="no-margin">Approve</label>
                                        </div>
                                    </div>
                                @endif
                            </div>
                            <div class="modal-footer">
                                <button type="submit" class="btn btn-primary">Submit</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal fade slide-up" id="messageModal" role="dialog" aria-labelledby="messageModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content-wrapper">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="messageModalLabel">Add Message</h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <form class="form" method="POST" action="" id="formMessage">
                            <input type="hidden" name="_method" value="PUT">
                            {{ csrf_field() }}
                            <div class="modal-body">
                                <input type="hidden" name="oldname" value="{{ old('name') }}">
                                <input type="hidden" name="oldschool" value="{{ old('school') }}">
                                <input type="hidden" name="oldtemplate" value="{{ old('template') }}">
                                <input type="hidden" name="oldstandard" value="{{ old('standard') }}">
                                <input type="hidden" name="oldfeedback" value="{{ old('feedback') }}">
                                <input type="hidden" name="oldorganisation" value="{{ old('organisation') }}">
                                <input type="hidden" name="oldinstitute_type" value="{{ $selected_institute }}">
                                <div class="row clearfix">
                                    <div class="col-md-12">
                                        <div class="form-group form-group-default form-group-default-select2">
                                            <label>Message Templates</label>
                                            <select class="full-width" name="message_list" id="message_list" data-init-plugin="select2" data-placeholder="Select...">
                                                <option value="" selected>Select...</option>
                                                <option value="download_error">Download error</option>
                                                <option value="private_document">Private document</option>
                                                <option value="incomplete_response">Incomplete response</option>
                                                <option value="custom_message">Custom message</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12">
                                        <div class="form-group form-group-default">
                                            <label>Message</label>
                                            <textarea name="message" id="message" rows="7" class="form-control"></textarea>
                                            <input type="hidden" name="message_student" id="message_student">
                                            <input type="hidden" name="message_template" id="message_template">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="submit" class="btn btn-primary">Send</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        @include('partials.preview-file')
    @endpush
    @push('scriptslib')
        <script type="text/javascript" src="{{ asset('assets/plugins/froala_editor/js/froala_editor.pkgd.min.js') }}"></script>
    @endpush
    @push('scripts')
        <script>
            if (jQuery('#inst_type').val() == 'School') {
                jQuery("#scl").show();
                jQuery("#org").hide();
                jQuery("#organisation").val('').trigger("change.select2");
            } else if (jQuery('#inst_type').val() == 'Organisation') {
                jQuery("#scl").hide();
                jQuery("#org").show();
                jQuery("#school").val('').trigger("change.select2");
            }

            jQuery('#inst_type').on('change', function() {
                if (this.value == 'School') {
                    jQuery("#scl").show();
                    jQuery("#org").hide();
                    jQuery("#organisation").val('').trigger("change.select2");
                } else {
                    jQuery("#org").show();
                    jQuery("#scl").hide();
                    jQuery("#school").val('').trigger("change.select2");
                }
            });
            jQuery(document).ready(function() {
                var $editor = new FroalaEditor('.froala', {
                    key: "{{ config('services.froala.key') }}",
                    imageDefaultWidth: 0,
                    videoDefaultWidth: 0,
                    heightMin: 200,
                    attribution: false,
                    placeholderText: 'Type your feedback here',
                    htmlRemoveTags: [],
                    quickInsertEnabled: false,
                    toolbarButtons: {
                        'moreText': {
                            'buttons': ['bold', 'italic', 'underline', 'strikeThrough', 'subscript', 'superscript']
                        },
                        'moreRich': {
                            'buttons': ['insertLink', 'insertTable', 'insertHR']
                        },
                        'moreMisc': {
                            'buttons': ['undo', 'redo', 'fullscreen', 'print', 'getPDF', 'spellChecker', 'selectAll', 'html', 'help'],
                            'align': 'right',
                            'buttonsVisible': 2
                        }
                    },
                });

                var $trigger;
                jQuery('#feedbackModal').on('show.bs.modal', function(e) {
                    $trigger = $(e.relatedTarget);
                    jQuery("#formFeedback").attr("action", "/workexperiencetemplates/feedback/" + $trigger.data('id'));
                    jQuery('#feedback').val('');
                    jQuery.ajax({
                        url: "/workexperiencetemplates/feedback/" + $trigger.data('id'),
                        success: function(data) {
                            jQuery.each(data, function(key, value) {
                                jQuery('#feedback').val(value);
                                // jQuery('#approve').val(key);
                                if (key == 1) {
                                    jQuery('#approve').prop("checked", true);
                                } else {
                                    jQuery('#approve').prop("checked", false);
                                }
                                $editor.html.set(value)
                            });
                        }
                    });
                });
                $('#feedbackModal').on('shown.bs.modal', function() {
                    $(document).off('focusin.modal');
                });
                $('#messageModal').on('shown.bs.modal', function() {
                    $(document).off('focusin.modal');
                });

                // jQuery('#formFeedback').validate({
                //     rules: {
                //         feedback: {
                //         required: true,
                //       }
                //     },
                // });


                jQuery('#messageModal').on('show.bs.modal', function(e) {
                    $trigger = $(e.relatedTarget);
                    jQuery("#formMessage").attr("action", "/workexperiencetemplates/message/" + $trigger.data('id'));
                    jQuery('#message').val('');
                    jQuery.ajax({
                        url: "/workexperiencetemplates/message/" + $trigger.data('id'),
                        success: function(data) {
                            // jQuery.each(data, function (key, value) {
                            jQuery('#message_student').val('Hi ' + data.student.name);
                            jQuery('#message_template').val(data.template.title);
                            if (data.message == null) {
                                jQuery('#message').val('Hi ' + data.student.name + ', ');
                                // jQuery('textarea[name="message"]').prop('disabled', true);
                            } else if (data.message != "") {
                                jQuery('#message').val(data.message);
                            }

                            // });
                        }
                    });
                });

                jQuery("#message_list").change(function() {
                    var val = jQuery(this).val();
                    var student = jQuery("#message_student").val();
                    var template = jQuery("#message_template").val();
                    if (val === "download_error") {
                        jQuery("#message").val(student + ', unfortunately we are having an issue downloading your file that you have uploaded for ' + template + '. Do you know what file type it was? You may like to try uploading it as a PDF? Could you please reupload your file in this new format so we can try to review it again. Thank you and sorry for the inconvience! - The Careers Department');
                    } else if (val === "private_document") {
                        jQuery("#message").val(student + ', unfortunately we are having trouble accessing the file you have uploaded for ' + template + ' as the link is not publicly accessible. Would you be able to resupply a public link for us to review? Thank you and sorry for the inconvience! - The Careers Department');
                    } else if (val === "incomplete_response") {
                        jQuery("#message").val(student + ', unfortunately we think you may have uploaded the wrong file or your task only partially complete. Could you please resubmit your task and then we will be able to review it for you then? Looking forward to receiving your work! - The Careers Department');
                    } else if (val === "custom_message") {
                        jQuery("#message").val(student + ', ');
                    }
                });

                jQuery('#messageModal').on('hidden.bs.modal', function(e) {
                    jQuery("#student").val('');
                    jQuery("#message").val('');
                    jQuery("#message_student").val('');
                    jQuery("#message_template").val('');
                    jQuery("#message_list").val([]).trigger("change.select2");
                });
            });

            jQuery(document).on('change', '.pick input', function() {
                $id = jQuery('.pick input input').val();
                var $url = '/response/' + jQuery(this).val() + '/approve';
                jQuery.ajax({
                    type: "get",
                    url: $url,
                    dataType: 'Json',
                });
            });
        </script>
    @endpush
@endsection
