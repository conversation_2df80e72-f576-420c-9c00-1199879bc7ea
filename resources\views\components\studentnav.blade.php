<link class="main-stylesheet" href="{{ asset('css/custom_mt.css') }}" rel="stylesheet" type="text/css" />
<script src="https://unpkg.com/@popperjs/core@2" type="text/javascript"></script>
<script src="{{ asset('js/kt_met/event-handler.js') }}" type="text/javascript"></script>
<script src="{{ asset('js/kt_met/util.js') }}" type="text/javascript"></script>
<script src="{{ asset('js/kt_met/menu.js') }}" type="text/javascript"></script>
<div id="kt_aside" class="app-aside flex-column" data-kt-drawer="true" data-kt-drawer-name="aside" data-kt-drawer-activate="{default: true, lg: false}" data-kt-drawer-overlay="true" data-kt-drawer-width="140px" data-kt-drawer-direction="start" data-kt-drawer-toggle="#kt_app_sidebar_mobile_toggle">
    <div class="aside-logo py-8 text-center" id="kt_app_sidebar_logo">
        <a href="#/" class="router-link-active">
            <img alt="Logo" src="/media/logos/default-small.svg" class="h-36px app-sidebar-logo-default">
        </a>
    </div>
    <div class="aside-menu flex-column-fluid">
        <div id="kt_app_sidebar_menu_wrapper" class="app-sidebar-wrapper hover-scroll-overlay-y my-5" data-kt-scroll="true" data-kt-scroll-activate="true" data-kt-scroll-height="auto" data-kt-scroll-dependencies="#kt_app_sidebar_logo, #kt_app_sidebar_footer" data-kt-scroll-wrappers="#kt_app_sidebar_menu" data-kt-scroll-offset="5px" data-kt-scroll-save-state="true" style="height: 359px;">
            <div id="#kt_app_sidebar_menu" class="menu menu-column menu-title-gray-700 menu-state-title-primary menu-state-icon-primary menu-state-bullet-primary menu-arrow-gray-500 fw-semibold" data-kt-menu="true">
                <div class="menu-item py-2">
                    <a aria-current="page" href="/#/dashboard" class="active router-link-exact-active menu-link menu-center flex-column">
                        <span class="menu-icon m-0">
                            <span class="svg-icon svg-icon-2">
                                <svg viewBox="0 0 22.172 20.531" width="19.5" height="19.5" xmlns="http://www.w3.org/2000/svg">
                                    <path xmlns="http://www.w3.org/2000/svg" d="M13.367,2.594l-.581.556L2.281,13.655l1.162,1.162,1.035-1.035v9.344h7.273V15.044h3.232v8.081h7.273V13.781l1.035,1.035,1.162-1.162L13.948,3.149Zm0,2.3,7.273,7.273v9.344H16.6V13.427H10.135v8.081H6.094V12.165Z" transform="translate(-2.281 -2.594)" fill="#707070">
                                    </path>
                                </svg>
                            </span>
                        </span>
                        <span class="menu-title">Home</span>
                    </a>
                </div>
                <div class="menu-item py-2">
                    <a class="menu-link menu-center flex-column" active-class="active" href="/profiles/edit">
                        <span class="menu-icon m-0">
                            <span class="svg-icon svg-icon-2">
                                <svg viewBox="0 0 18.176 19.994" width="19.5" height="19.5" xmlns="http://www.w3.org/2000/svg">
                                    <path xmlns="http://www.w3.org/2000/svg" d="M15.088,5a6.364,6.364,0,0,0-3.55,11.644A9.115,9.115,0,0,0,6,24.994H7.818a7.27,7.27,0,1,1,14.541,0h1.818a9.115,9.115,0,0,0-5.538-8.35A6.364,6.364,0,0,0,15.088,5Zm0,1.818a4.544,4.544,0,1,1-4.544,4.544A4.53,4.53,0,0,1,15.088,6.818Z" transform="translate(-6 -5)" fill="#707070">
                                    </path>
                                </svg>
                            </span>
                        </span>
                        <span class="menu-title">Profile</span>
                    </a>
                </div>
                <div class="menu-item py-2" data-kt-menu-trigger="click">
                    @if (Auth::user()->hasIndustriesAccess() || Auth::user()->hasEMagazineAccess())
                        <span class="menu-link menu-center flex-column">
                            <span class="menu-icon m-0">
                                <span class="svg-icon svg-icon-2">
                                    <svg viewBox="0 0 23.78 16.987" fill="currentColor" width="19.5" height="24" xmlns="http://www.w3.org/2000/svg"><g xmlns="http://www.w3.org/2000/svg" transform="translate(-95.976 -25.976)"><path d="M218.783,160.648q3.295-1.29,6.589-2.585c.282-.112.569-.219.846-.33.34-.136.433-.569.559-.884.51-1.288,1.016-2.575,1.526-3.863l1.21-3.061a.489.489,0,0,0-.6-.6l-6.6,2.566c-.292.112-.583.224-.875.34-.34.136-.433.569-.559.889-.5,1.273-.991,2.551-1.487,3.824-.4,1.035-.807,2.075-1.21,3.11a.486.486,0,1,0,.938.258q1.268-3.273,2.541-6.541c.122-.316.243-.627.364-.943l-.34.34q3.273-1.268,6.541-2.541l.943-.364-.6-.6-2.58,6.531c-.122.311-.248.627-.369.938l.34-.34q-3.266,1.283-6.531,2.561c-.306.122-.607.238-.914.36a.485.485,0,1,0,.267.933Z" transform="translate(-116.878 -117.975)"></path><path d="M117.041,37a9.81,9.81,0,1,1-.107-1.555A11.126,11.126,0,0,1,117.041,37a.486.486,0,0,0,.972,0,11.022,11.022,0,1,0-15.6,10.02,11.151,11.151,0,0,0,6.784.778,11.011,11.011,0,0,0,8.674-9.053A11.677,11.677,0,0,0,118.007,37a.484.484,0,0,0-.967,0Z"></path><path d="M340.884,270.572a.428.428,0,1,1-.428-.428.428.428,0,0,1,.428.428" transform="translate(-233.465 -233.575)"></path><path d="M329.68,259.858c0-.053.015-.029,0,0,0-.01.058-.087.02-.049s.019-.01.029-.015c.034-.019-.063,0,.01,0,.053,0,.029.015,0,0,.015.01.087.063.049.019s.01.015.015.029c.019.044,0-.087,0,.01,0,.053-.015.029,0,0-.015.02-.024.044-.039.063.044-.073.024,0-.015,0-.068,0,.073,0,0,0s-.063-.019-.015.01c-.02-.015-.044-.024-.063-.039.049.029.024.039,0-.015s.015.092.01-.01a.5.5,0,0,0,.486.486.491.491,0,0,0,.486-.486.914.914,0,1,0-.938.914.928.928,0,0,0,.938-.914.49.49,0,0,0-.486-.486.5.5,0,0,0-.486.486Z" transform="translate(-222.746 -222.861)"></path></g></svg>
                                </span>
                            </span>
                            <span class="menu-title">Explore</span>
                        </span>
                    @endif
                    <div class="menu-sub menu-sub-dropdown menu-sub-indention rounded-0" style="">
                        @if (Auth::user()->hasIndustriesAccess())
                            <div class="menu-item">
                                <a class="menu-link" active-class="active" href="/#/explore/industries">
                                    <span class="menu-title">Industries</span>
                                </a>
                            </div>
                        @endif
                        @if (Auth::user()->hasEMagazineAccess())
                            <div class="menu-item">
                                <a class="menu-link" active-class="active" href="/e-magazines/editions">
                                    <span class="menu-title">e-Magazine</span>
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
                <div class="menu-item py-2" data-kt-menu-trigger="click">
                    @if (Auth::user()->hasLessonsAccess() || Auth::user()->hasVweAccess() || Auth::user()->hasSkillsTrainingAccess() || Auth::user()->hasWhsAccess())
                        <span class="menu-link menu-center flex-column">
                            <span class="menu-icon m-0">
                                <span class="svg-icon svg-icon-2">
                                    <svg viewBox="0 0 23.78 16.987" width="19.5" height="19.5" xmlns="http://www.w3.org/2000/svg">
                                        <path xmlns="http://www.w3.org/2000/svg" d="M4.548,6V17.519L2.61,19.483A2.082,2.082,0,0,0,2,20.943a2.053,2.053,0,0,0,2.044,2.044H23.737a2.053,2.053,0,0,0,2.044-2.044,2.082,2.082,0,0,0-.61-1.46l-1.937-1.964V6Zm1.7,1.7H21.534v9.343H6.247ZM5.769,18.74H22.012l1.964,1.938a.4.4,0,0,1,.106.265.327.327,0,0,1-.345.345H4.044a.327.327,0,0,1-.345-.345.4.4,0,0,1,.106-.265Z" transform="translate(-2 -6)" fill="#707070">
                                        </path>
                                    </svg>
                                </span>
                            </span>
                            <span class="menu-title">Tasks</span>
                        </span>
                    @endif
                    <div class="menu-sub menu-sub-dropdown menu-sub-indention rounded-0" style="">
                         @if (Auth::user()->hasMyPathAccess())
                            <div class="menu-item">
                                <a class="menu-link" active-class="active" href="/my-path">
                                    <span class="menu-title">My Path </span>
                                </a>
                            </div>
                        @endif
                        @if (Auth::user()->hasLessonsAccess())
                            <div class="menu-item">
                                <a class="menu-link" active-class="active" href="/#/tasks/lessons">
                                    <span class="menu-title">Lessons</span>
                                </a>
                            </div>
                        @endif

                        @if (Auth::user()->hasVweAccess())
                            <div class="menu-item">
                                <a class="menu-link" active-class="active" href="/#/tasks/vwe">
                                    <span class="menu-title">Virtual Work Experience</span>
                                </a>
                            </div>
                        @endif
                        @if (Auth::user()->hasSkillsTrainingAccess())
                            <div class="menu-item">
                                <a class="menu-link" active-class="active" href="/#/tasks/skillstraining">
                                    <span class="menu-title">Skills Training</span>
                                </a>
                            </div>
                        @endif
                        @if (Auth::user()->hasWhsAccess())
                            <div class="menu-item">
                                <a class="menu-link" active-class="active" href="/wew/workhealthsafety">
                                    <span class="menu-title">Work, Health & Safety</span>
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
                <div class="menu-item py-2" data-kt-menu-trigger="click">
                    @if (Auth::user()->hasJobFinderAccess() || Auth::user()->hasScholarshipFinderAccess() || Auth::user()->hasCourseFinderAccess() || Auth::user()->hasEPortfolioAccess() || Auth::user()->hasSubjectSelectionsAccess())
                        <span class="menu-link menu-center flex-column">
                            <span class="menu-icon m-0">
                                <span class="svg-icon svg-icon-2">
                                    <svg viewBox="0 0 21.711 17.059" width="19.5" height="19.5" xmlns="http://www.w3.org/2000/svg">
                                        <path xmlns="http://www.w3.org/2000/svg" d="M10.529,5A1.562,1.562,0,0,0,8.978,6.551V8.1H2V22.059H23.711V8.1H16.732V6.551A1.562,1.562,0,0,0,15.181,5Zm0,1.551h4.652V8.1H10.529Zm-6.978,3.1H22.16v3.877h-3.1v-.775h-3.1v.775h-6.2v-.775h-3.1v.775h-3.1Zm0,5.428h3.1v.775h3.1V15.08h6.2v.775h3.1V15.08h3.1v5.428H3.551Z" transform="translate(-2 -5)" fill="#707070">
                                        </path>
                                    </svg>
                                </span>
                            </span>
                            <span class="menu-title">Tools</span>
                        </span>
                    @endif
                    <div class="menu-sub menu-sub-dropdown menu-sub-indention rounded-0" style="">
                        @if (Auth::user()->hasJobFinderAccess())
                            <div class="menu-item">
                                <a class="menu-link" active-class="active" target="_blank" href="https://au.indeed.com/ ">
                                    <span class="menu-title">Job Finder</span>
                                </a>
                            </div>
                        @endif
                        @if (Auth::user()->hasScholarshipFinderAccess())
                            <div class="menu-item">
                                <a class="menu-link" active-class="active" href="/scholarships">
                                    <span class="menu-title">Scholarship Finder</span>
                                </a>
                            </div>
                        @endif
                        @if (Auth::user()->hasResumeBuilderAccess())
                            <div class="menu-item">
                                <a class="menu-link" active-class="active" href="/cvs">
                                    <span class="menu-title">Resume Builder</span>
                                </a>
                            </div>
                        @endif
                        @if (Auth::user()->hasCourseFinderAccess())
                            <div class="menu-item">
                                <a class="menu-link" active-class="active" href="/coursefinder">
                                    <span class="menu-title">Course Finder</span>
                                </a>
                            </div>
                        @endif
                        @if (Auth::user()->hasEPortfolioAccess())
                            <div class="menu-item">
                                <a class="menu-link" active-class="active" href="/eportfolio">
                                    <span class="menu-title">ePortfolio</span>
                                </a>
                            </div>
                        @endif
                        @if (Auth::user()->hasSubjectSelectionsAccess())
                            <div class="menu-item">
                                <a class="menu-link" active-class="active" href="/subjects-selection">
                                    <span class="menu-title">Subject Selections</span>
                                </a>
                            </div>
                        @endif
                        @if (Auth::user()->hasCareerProfilingAccess())
                        <div class="menu-item">
                            <a class="menu-link" active-class="active" href="/profiler">
                                <span class="menu-title">Career Profiling</span>
                            </a>
                        </div>
                        @endif
                        @if (Auth::user()->hasVideoProfilingAccess())
                        <div class="menu-item">
                            <a class="menu-link" active-class="active" href="/career/profiling">
                                <span class="menu-title">Video Profiling</span>
                            </a>
                        </div>
                        @endif
                    </div>
                </div>
                @if (Auth::user()->hasHelpCentreAccess())
                    <div class="menu-item py-2" data-kt-menu-trigger="click">
                        <span class="menu-link menu-center flex-column">
                            <span class="menu-icon m-0">
                                <span class="svg-icon svg-icon-2">
                                    <svg preserveAspectRatio="xMidYMid meet" width="25" height="25" viewBox="0 0 512 512" version="1.0" xmlns="http://www.w3.org/2000/svg">
                                        <g xmlns="http://www.w3.org/2000/svg" transform="translate(0,512) scale(0.1,-0.1)" fill="#707070" stroke="none">
                                            <path d="M2415 5010 c-423 -28 -831 -177 -1170 -430 -475 -354 -784 -872 -869 -1461 -9 -59 -16 -132 -16 -163 l0 -56 -65 -40 c-116 -74 -196 -182 -230 -311 -22 -81 -21 -771 0 -854 23 -87 62 -152 136 -227 143 -145 359 -196 514 -123 62 30 138 107 168 173 22 47 22 53 22 607 l0 560 -28 56 c-44 91 -140 167 -231 184 l-38 8 7 78 c16 191 84 433 175 626 192 407 510 730 915 928 731 358 1597 227 2196 -333 335 -314 564 -777 605 -1226 l7 -73 -39 -7 c-91 -18 -187 -94 -231 -185 l-28 -56 0 -560 c0 -553 0 -561 22 -607 45 -97 175 -198 256 -198 l27 0 0 -137 c0 -156 -15 -233 -59 -321 -59 -116 -163 -203 -289 -242 -56 -17 -93 -20 -281 -20 l-217 0 -12 32 c-21 58 -97 134 -170 170 l-67 33 -280 0 -280 0 -66 -32 c-79 -39 -140 -102 -177 -181 -23 -49 -27 -70 -27 -152 1 -84 4 -102 29 -154 35 -72 97 -136 164 -170 77 -39 169 -49 407 -44 187 4 216 7 267 27 71 28 161 107 193 172 l24 47 253 4 c227 4 260 7 318 26 199 67 355 201 440 377 62 129 71 176 77 403 l6 203 41 21 c102 52 217 205 245 328 17 73 14 773 -4 839 -34 129 -114 237 -230 311 l-65 40 0 55 c0 88 -29 270 -66 415 -95 370 -288 711 -557 983 -330 334 -730 546 -1182 627 -91 17 -384 43 -420 38 -5 0 -59 -4 -120 -8z m-1805 -2337 c16 -9 33 -27 39 -40 16 -35 15 -983 -1 -1018 -13 -28 -55 -55 -86 -55 -87 0 -197 76 -238 165 -24 49 -24 54 -24 400 0 347 0 350 24 400 21 45 56 84 109 125 19 14 99 38 130 39 11 1 32 -6 47 -16z m4046 -8 c50 -21 115 -86 140 -140 24 -49 24 -54 24 -400 0 -346 0 -351 -24 -400 -41 -89 -151 -165 -238 -165 -31 0 -73 27 -86 55 -18 40 -16 991 2 1024 28 51 98 61 182 26z m-1268 -2095 c18 -11 40 -35 49 -55 22 -47 6 -105 -40 -137 -30 -22 -42 -23 -232 -26 -231 -4 -266 3 -303 58 -40 58 -13 143 53 170 11 5 115 9 230 9 193 1 213 -1 243 -19z"></path>
                                            <path d="M1656 3834 c-132 -32 -260 -131 -323 -250 -62 -116 -63 -130 -63 -760 0 -338 4 -593 10 -625 16 -84 76 -196 140 -260 84 -85 228 -149 332 -149 l38 0 0 -162 c0 -142 3 -169 21 -208 40 -89 111 -138 211 -147 106 -10 123 2 401 274 l249 243 367 0 c293 0 378 3 424 15 186 48 328 192 372 376 22 95 22 1183 0 1278 -43 183 -193 333 -376 376 -92 21 -1713 21 -1803 -1z m1769 -251 c65 -22 136 -93 158 -158 15 -43 17 -112 17 -605 0 -612 0 -609 -61 -683 -17 -20 -52 -49 -77 -65 l-47 -27 -425 -6 c-234 -3 -427 -6 -430 -7 -2 -1 -120 -118 -262 -259 l-258 -257 0 207 c0 302 -4 308 -210 317 -114 5 -131 9 -172 33 -26 15 -60 44 -77 64 -61 74 -61 71 -61 683 0 635 -2 619 80 701 82 82 30 77 956 78 741 1 825 -1 869 -16z"></path>
                                            <path d="M1888 2960 c-63 -34 -83 -73 -83 -160 0 -63 4 -80 23 -108 78 -109 257 -91 313 32 36 79 13 169 -56 222 -50 38 -141 44 -197 14z"></path>
                                            <path d="M2485 2966 c-16 -8 -42 -26 -57 -42 -108 -116 -26 -304 131 -304 159 0 241 188 133 304 -51 54 -140 72 -207 42z"></path>
                                            <path d="M3065 2966 c-58 -26 -105 -101 -105 -168 1 -49 33 -112 76 -144 33 -25 46 -29 104 -29 121 0 186 69 178 189 -4 74 -31 117 -88 146 -45 23 -120 26 -165 6z"></path>
                                        </g>
                                    </svg>
                                </span>
                            </span>
                            <span class="menu-title">Support</span>
                        </span>
                        <div class="menu-sub menu-sub-dropdown menu-sub-indention rounded-0" style="">
                            {{-- <div class="menu-item">
                                <a class="menu-link" active-class="active" href="/noticeboard">
                                    <span class="menu-title">Noticeboard</span>
                                </a>
                            </div> --}}
                            <div class="menu-item">
                                <a class="menu-link" active-class="active" target="_blank" href="https://help.thecareersdepartment.com/en/">
                                    <span class="menu-title">Help Centre</span>
                                </a>
                            </div>
                        </div>
                    </div>
                @endif

                {{-- <div class="menu-item py-2">
                    <a aria-current="page" href="{{ route('paths.index') }}" class="active router-link-exact-active menu-link menu-center flex-column">
                        <span class="menu-icon m-0">
                            <span class="svg-icon svg-icon-2">
                                <svg viewBox="0 0 22.172 20.531" width="19.5" height="19.5" xmlns="http://www.w3.org/2000/svg">
                                    <path xmlns="http://www.w3.org/2000/svg" d="M13.367,2.594l-.581.556L2.281,13.655l1.162,1.162,1.035-1.035v9.344h7.273V15.044h3.232v8.081h7.273V13.781l1.035,1.035,1.162-1.162L13.948,3.149Zm0,2.3,7.273,7.273v9.344H16.6V13.427H10.135v8.081H6.094V12.165Z" transform="translate(-2.281 -2.594)" fill="#707070">
                                    </path>
                                </svg>
                            </span>
                        </span>
                        <span class="menu-title">My Path</span>
                    </a>
                </div> --}}

            </div>
        </div>
    </div>
</div>
<script>
    jQuery(document).ready(function() {
        KTMenu.init();
    });
</script>
<style>
    .footer-icon:hover svg path,
    .aside-menu .menu>.menu-item:not(.here)>.menu-link:hover:not(.disabled):not(.active):not(.here) .menu-icon .svg-icon svg path,
    .menu-item .menu-link.active .menu-icon .svg-icon svg path {
        fill: #fff;
    }

    .footer-icon:hover svg path,
    .aside-menu .menu>.menu-item>.menu-link:hover .menu-title,
    .aside-menu .menu>.menu-item>.menu-link.active .menu-title {
        color: #fff !important;
    }
</style>
