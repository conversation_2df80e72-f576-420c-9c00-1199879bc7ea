"use strict";(self.webpackChunk=self.webpackChunk||[]).push([[816],{67194:(e,l,n)=>{n.d(l,{Z:()=>c});var o=n(1519),t=n.n(o)()((function(e){return e[1]}));t.push([e.id,".fullScreenItem{height:100%;width:100%;z-index:9999}.fullScreenItem,.togglefullscreen{left:0;position:fixed!important;top:0}.togglefullscreen{z-index:99999}",""]);const c=t},83816:(e,l,n)=>{n.r(l),n.d(l,{default:()=>h});var o=n(70821),t=(0,o.createElementVNode)("h1",null,"Slider",-1),c=(0,o.createElementVNode)("div",{class:"section"},[(0,o.createElementVNode)("div",{class:"slide"}," Slide 1 "),(0,o.createElementVNode)("div",{class:"slide"}," Slide 2 "),(0,o.createElementVNode)("div",{class:"slide"}," Slide 3 "),(0,o.createElementVNode)("div",{class:"slide"}," Slide 4 ")],-1),i={key:1,class:"fullScreenItem"},r=(0,o.createElementVNode)("div",{class:"section"},"Second section ...",-1),s=(0,o.createElementVNode)("div",{class:"section"},"Third section ...",-1),a=(0,o.createElementVNode)("div",{class:"section"},"Fourth section ...",-1),d=(0,o.createElementVNode)("div",{class:"section"},"Fifth section ...",-1);var u=n(81256);const f=(0,o.defineComponent)({name:"slide",components:{VueFullPage:u.Z},setup:function(){var e=(0,o.ref)(),l=((0,o.ref)(),(0,o.ref)(),(0,o.ref)()),n=(0,o.ref)(!1);e.value={licenseKey:"gplv3-license",scrollHorizontally:!0,loopTop:!0,loopBottom:!0,controlArrowsHTML:['<div class="fp-arrow"></div>','<div class="fp-arrow"></div>'],slidesNavigation:!0,sectionsColor:["#41b883","#ff5f45","#0798ec","#0748ec","#029a0c"]},l.value={licenseKey:"gplv3-license",scrollHorizontally:!0,slidesNavigation:!0,sectionsColor:["#41b883","#ff5f45","#0798ec","#0748ec","#029a0c"]};return{options:e,changeDirection:function(){n.value=!n.value},fullScreen:n,optionsFullScreen:l}}});var p=n(93379),v=n.n(p),m=n(67194),g={insert:"head",singleton:!1};v()(m.Z,g);m.Z.locals;const h=(0,n(83744).Z)(f,[["render",function(e,l,n,u,f,p){var v=(0,o.resolveComponent)("full-page");return(0,o.openBlock)(),(0,o.createElementBlock)("div",null,[t,(0,o.createElementVNode)("span",{onClick:l[0]||(l[0]=function(l){return e.changeDirection()})},"Change to horizontal"),e.fullScreen?(0,o.createCommentVNode)("",!0):((0,o.openBlock)(),(0,o.createBlock)(v,{key:0,ref:"fullpageSlider",options:e.options,id:"fullpage"},{default:(0,o.withCtx)((function(){return[c]})),_:1},8,["options"])),e.fullScreen?((0,o.openBlock)(),(0,o.createElementBlock)("div",i,[(0,o.createElementVNode)("span",{onClick:l[1]||(l[1]=function(l){return e.changeDirection()}),class:"togglefullscreen"},"Change to horizontal"),(0,o.createVNode)(v,{ref:"fullScreenSlider",options:e.optionsFullScreen,id:"fullScreen"},{default:(0,o.withCtx)((function(){return[r,s,a,d]})),_:1},8,["options"])])):(0,o.createCommentVNode)("",!0)])}]])}}]);