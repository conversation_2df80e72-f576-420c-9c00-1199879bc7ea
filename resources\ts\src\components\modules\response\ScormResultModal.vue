<template>
  <div
      class="modal fade show"
      id="scormResultModal"
      ref="scormResultModalRef"
      tabindex="-1"
      aria-labelledby="scormResultModalTitleId"
      aria-hidden="true"
      v-if="userResult.lesson_status"
  >
    <div class="modal-dialog modal-dialog-centered modal-dialog-scorm-result" role="document">
      <div class="modal-content">
        <div class="modal-body p-0">
          <div class="split-section">

            <!-- Top START -->
            <div class="top-half">
              <div class="d-flex justify-content-end p-4">
                <div class="close-scorm-result-modal-btn" data-bs-dismiss="modal">
                  <i class="fa fa-times" aria-hidden="true"></i>
                </div>
              </div>

              <div class="container mt-4">
                <div class="d-flex justify-content-center align-items-center flex-column text-center">
                  <div>
                      <h1 v-if="passed">
                        Congratulations!
                      </h1>
                      <h1 v-else>
                        Almost there!
                      </h1>
                    </div>

                    <div class="mt-3">
                      <h2>
                        <template v-if="passed">
                          You have successfully completed 
                          {{ module.title }}
                        </template>
                        <template v-else>
                          You completed 
                          {{ module.title }}
                          but did not quite meet the passing score.
                        </template>
                      </h2>
                    </div>
                  
                </div>
              </div>
            </div>
            <!-- Top END -->

            <!-- Bottom Section START -->
            <!-- <div class="bottom-half bg-white text-dark d-flex justify-content-center align-items-center">
              <h1>Bottom White Section</h1>
            </div> -->
            <!-- Bottom Section END -->

            <!-- Middle START -->
            <div class="middle-inline mx-auto">
              <div class="container">
                  <div class="row">

                    <!-- Left Middle box -->
                    <div class="col-md-6 d-flex justify-content-center align-items-center mb-2 mx-auto">
                      <div class="p-3 bg-white shadow-sm w-100 text-center rounded mid-box">

                        <div class="row result-info">
                          <div class="col-6 left">
                            <h2>Your Score</h2>
                          </div>
                          <div class="col-6 right">
                            <div :class="['badge', scormBadgeClass]">{{ userResult.score_raw ?? '-' }}/{{ userResult.score_max ?? 100 }}</div> 
                          </div>
                        </div>

                        <div class="row result-info">
                          <div class="col-6 left">
                            <h2>Outcome</h2>
                          </div>
                          <div class="col-6 right">
                            <div :class="['badge', scormBadgeClass]">{{ userResult.lesson_status == 'failed' ? 'Did Not Pass' : (ucwords(userResult.lesson_status) ?? '-')  }}</div> 
                          </div>
                        </div>

                        <div class="row result-info" v-if="userResult.lesson_status == 'failed' && redoSuccessRoute && canEdit">
                          <div class="col-12">
                            <button
                                type="button"
                                class="btn btn-primary w-100 rounded"
                                @click="resetScormResponse"
                            >Redo Assessment</button>
                          </div>
                        </div>

                        <div class="row result-info" v-if="!scormCompleted && redoSuccessRoute && canEdit">
                          <div class="col-12">
                            <button
                                type="button"
                                class="btn btn-primary w-100 rounded"
                                @click="goToScoringScormSection"
                            >Continue Assessment</button>
                          </div>
                        </div>

                      </div>

                    </div>

                    <!-- Right middle box -->
                    <div class="col-md-6 d-flex justify-content-center align-items-center mb-2" v-if="badgeData">
                      <div class="p-3 bg-white shadow-sm w-100 rounded mid-box">
                        <div class="row mt-2 badge-earnt-inner-body">
                          <div class="col-7 left">
                            <div>
                              <h2>Badge Earnt</h2>
                            </div>
                            
                            <div>
                              <h4>{{ badgeData.badge?.name }}</h4>
                            </div>

                            <div>
                              <span
                                  class="cursor-pointer"
                                  @click="emit('viewBagdeDetails')"
                                  data-bs-toggle="modal" 
                                  data-bs-target="#kt_modal_badge"
                              >
                                <i class="fa fa-eye"></i>&nbsp;View Details
                              </span>
                            </div>
                          </div>
                          <div class="col-5 right">
                            <img :src="badgeData.badge?.image_fullpath" :alt="badgeData.badge?.name">
                          </div>
                        </div>
                      </div>
                    </div>

                </div>
              </div>
            </div>
            <!-- Middle END -->

          </div>
          
          <!-- <MyPathModules /> -->
        </div>
        <!-- <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
            Close
          </button>
          <button type="button" class="btn btn-primary">Save</button>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import Modal from 'bootstrap/js/dist/modal';
import Swal from "sweetalert2/dist/sweetalert2.min.js";
import ApiService from "@/core/services/ApiService";
import { useRouter } from 'vue-router';
import { useStore } from 'vuex';
// import MyPathModules from './MyPathModules.vue';

const props = defineProps<{ 
    module: {
        title: string,
        scorm_scoring_step_result: any, 
        user_response?: any,
        student_response?: any,
        // core_competency_anchor_values: Array<{ [key: string]: any }>
    },
    trackableType: string,
    redoSuccessRoute: object,
}>();

const emit = defineEmits(['viewBagdeDetails']);

const store = useStore();
const currentUser = store.getters.currentUser;

const userResult = computed(() => {
  return props.module?.scorm_scoring_step_result?.user_scorm_result;
});

const section = computed(() => {
  return props.module?.scorm_scoring_step_result;
});

const passed = computed(() => {
  return userResult.value.lesson_status=='passed' || userResult.value.lesson_status=='completed';
});

const scormCompletionStatuses : Array<string> = [
    "passed", 
    "completed", 
    "failed"
];

const scormCompleted = computed(() => {
  return scormCompletionStatuses.includes(userResult.value.lesson_status);
});

// const ccavNames = computed(() => {
//   return props.module.core_competency_anchor_values?.map(item => `${item.core_competency.name}: Level ${item.score}`).join(', ');
// });

const canEdit = computed(() => {
  return currentUser.id == userResult.value?.user_id;
});

const userReponse = computed(() => {
  if (canEdit.value) {
    return props.module?.user_response;
  }

  return props.module?.student_response;
});

const badgeData = computed(() => {
  return userReponse.value?.badge_key;
});

const scormBadgeClass = computed(() => {
  if (scormCompleted.value) {
    if (userResult.value?.lesson_status == 'failed') {
      return 'badge-failure';
    } else if (userResult.value?.lesson_status == 'completed') {
      return 'badge-completed';
    } else {
      return 'badge-success';
    }
  } else {
    return 'badge-incomplete';
  }
});

const scormResultModalRef = ref();
let modalInstance = ref<Modal>(null);
const router = useRouter();

const ucwords = (str) => {
  if (str == null) return "";

  return String(str).replace(/\b\w/g, char => char.toUpperCase());
}

const goToScoringScormSection = () => {
  modalInstance.hide();
  router.push(props.redoSuccessRoute);
}; 

const resetScormResponse = async () => {
    const result = await Swal.fire({
        title: "Are you sure?",
        text: "This will reset your response in this section.",
        icon: "warning",
        buttonsStyling: false,
        confirmButtonText: "Ok",
        showCancelButton: true,
        customClass: {
            confirmButton: "btn fw-semibold btn-light-primary rounded-0",
            cancelButton: "btn fw-semibold btn-secondary rounded-0",
            container: 'swal2-top-modal'
        },
        didOpen: (popupEl) => {
          const container = popupEl.closest('.swal2-container') as HTMLElement;
          if (container) {
            container.style.zIndex = '9999';
          }
        }
    });

    if (result.isConfirmed) {
        try {
            const { data } = await ApiService.delete(
              `/scorm-tracking/${props.trackableType}/${section.value?.id}`
            );

            if (data.success) {
                Swal.fire({
                    title: "Done!",
                    text: "Your response has been reset. Good luck on your next attempt!",
                    confirmButtonText: "Let's go",
                    icon: "success",
                    customClass: {
                        confirmButton: 'btn fw-semibold btn-global-grey rounded'
                    }
                });
                  
                modalInstance.hide();

                router.push(props.redoSuccessRoute);
            } else {
                Swal.fire({
                    title: "Error!",
                    text: "Something went wrong, try again later.",
                    icon: "error"
                });
            }

        } catch (error: any) {
            console.error("Error deleting SCORM tracking:", error);
            
            Swal.fire({
                title: "Error!",
                text: "Unexpected error occurred.",
                icon: "error"
            });
        }
    }
    
};

const handleModalClose = () => {
  console.log('SCORM modal closed');

};

const openModal = () => {
  if (!modalInstance) {
    return;
  }

  closeAllOtherModals();

  modalInstance.show();
};

const closeAllOtherModals = () => {
  console.log("Closing all other modals");
  
  document.querySelectorAll<HTMLElement>('.modal').forEach(modalEl => {
    const isVisible = modalEl.classList.contains('show') || modalEl.style.display === 'block';

    if (isVisible) {
      const modalInstance = Modal?.getInstance(modalEl);

      if (modalInstance) {
        modalInstance.hide()
      } else {
        modalEl.classList.remove('show');
        modalEl.style.display = 'none';
        modalEl.setAttribute('aria-hidden', 'true');
      }
    }
  });
};

onMounted(() => {
  if (userResult.value?.lesson_status) {
    modalInstance = new Modal(scormResultModalRef.value);

    scormResultModalRef.value.addEventListener('hidden.bs.modal', handleModalClose);
  }

  if (localStorage.getItem('showScormResult')) {

    openModal();

    localStorage.removeItem('showScormResult');
  }
});

defineExpose({ openModal });
</script>

<style scoped>
.modal-backdrop {
  opacity: 0.8 !important;
}
.modal-dialog-scorm-result{
  max-width: 1188px;
}
.modal-dialog-scorm-result .split-section {
  height: 70vh;
  overflow: hidden;
  border-radius: 0.475rem;
}
.modal-dialog-scorm-result .top-half{
  height: 50%;
  background-color: #000;
  color: #FFF;
}
.modal-dialog-scorm-result .top-half h1,
.modal-dialog-scorm-result .top-half h2,
.modal-dialog-scorm-result .top-half h3{
  color: #FFF;
}
.modal-dialog-scorm-result .bottom-half {
  height: 50%;
}
.close-scorm-result-modal-btn{
  cursor: pointer;
}
.close-scorm-result-modal-btn .fa {
  font-size: 20px;
  color: #fff;
}

.modal-dialog-scorm-result .middle-inline {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 70%;
  z-index: 10;
  display: flex;
  justify-content: center;
  align-items: center;
}
.modal-dialog-scorm-result .middle-inline .mid-box{
  min-height: 215px;
}
.modal-dialog-scorm-result .middle-inline .badge{
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
}
.modal-dialog-scorm-result .badge-success{
  color: #31A400;
  border-radius: 10px;
  background: rgba(56, 186, 0, 0.21);
  display: flex;
  width: 120px;
  height: 45px;
  padding: 9px 38px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  flex-shrink: 0;
}
.modal-dialog-scorm-result .badge-failure{
  color: #F6A000;
  border-radius: 10px;
  background: #FEEFD4;
  border-radius: 10px;
  display: flex;
  width: 120px;
  height: 45px;
  padding: 9px 38px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  flex-shrink: 0;
}

.modal-dialog-scorm-result .badge-incomplete{
  color: #000;
  border-radius: 10px;
  background: #e9ff1f87;
  border-radius: 10px;
  display: flex;
  width: 120px;
  height: 45px;
  padding: 9px 38px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  flex-shrink: 0;
}

.modal-dialog-scorm-result .badge-completed{
  color: #0062FF;
  border-radius: 10px;
  background: #0062ff3d;
  border-radius: 10px;
  display: flex;
  width: 120px;
  height: 45px;
  padding: 9px 38px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  flex-shrink: 0;
}

.modal-dialog-scorm-result .middle-inline .result-info{
  margin-top: 1.5rem;
}
.modal-dialog-scorm-result .middle-inline .result-info .left {
  display: flex;
  justify-content: center;
  align-items: center;
}
.badge-earnt-inner-body{
  padding: 1.5rem;
}
.badge-earnt-inner-body{
  height: 100%;
}
.badge-earnt-inner-body .left{
  display: flex;
  justify-content: space-evenly;
  flex-direction: column;
}
.badge-earnt-inner-body .right{
  display: flex;
  align-items: center;
}
.badge-earnt-inner-body img{
  height: 150px;
}
</style>

<style>
.btn-global-grey{
    color: #606060 !important;
    font-weight: 600!important;
    background: #F8F6F6CF!important;
}
</style>