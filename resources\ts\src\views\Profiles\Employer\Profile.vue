<template>
    <div class="rounded-0 max-w-2xl mx-auto">
        <div class="card-header border-0 pt-6">
            <div class="card-title">
                <h1 class="text-2xl font-bold">Profile Settings</h1>
            </div>
        </div>
        <div>
            <div class="p-9">
                <div v-if="loading" class="text-center py-6">
                    <p class="text-gray-600">Loading...</p>
                </div>
                <div v-else-if="error" class="text-danger text-center py-6">
                    <p>{{ error }}</p>
                </div>
                <div v-else-if="employer" class="space-y-6">
                    <!-- Basic Information -->
                    <div class="card">
                        <div class="border border-white-300 card rounded">
                            <div class="d-flex items-center justify-content-between mt-10 mx-10">
                                <div>
                                    <p class="fs-4 fw-semibold mt-4">Basic Information</p>
                                </div>
                                <div>
                                    <button type="button"
                                        class="btn btn-light fw-semibold text-secondary-dark rounded w-full sm:w-auto"
                                        @click="toggleEditMode">
                                        {{ isEditMode ? 'Save Details' : 'Update Details' }}
                                    </button>
                                </div>
                            </div>
                            <hr class="border-gray-400 my-5">
                            <div class="p-3 mx-8">
                                <form @submit.prevent="updateEmployer">
                                    <div class="row mb-3">
                                        <label class="col-sm-4 col-form-label">First Name</label>
                                        <div class="col-sm-8">
                                            <p v-if="!isEditMode"
                                                class="form-control-plaintext text-right fw-bold cursor-pointer">{{
                                                    employer.first_name || 'N/A' }}</p>
                                            <input v-else type="text" class="form-control bg-light fw-bold"
                                                v-model="employer.first_name" />
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <label class="col-sm-4 col-form-label">Last Name</label>
                                        <div class="col-sm-8">
                                            <p v-if="!isEditMode"
                                                class="form-control-plaintext text-right fw-bold cursor-pointer">{{
                                                    employer.last_name || 'N/A' }}</p>
                                            <input v-else type="text" class="form-control bg-light fw-bold"
                                                v-model="employer.last_name" />
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <label class="col-sm-4 col-form-label">Position</label>
                                        <div class="col-sm-8">
                                            <p v-if="!isEditMode"
                                                class="form-control-plaintext text-right fw-bold cursor-pointer">{{
                                                    employer.profile.position || 'N/A' }}</p>
                                            <input v-else type="text" class="form-control bg-light fw-bold"
                                                v-model="employer.profile.position" />
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <label class="col-sm-4 col-form-label">Company</label>
                                        <div class="col-sm-8">
                                            <p v-if="!isEditMode"
                                                class="form-control-plaintext text-right fw-bold cursor-pointer">{{
                                                    employer.company_name || 'N/A' }}</p>
                                            <Field
                                                v-else
                                                as="select"
                                                name="company_id"
                                                v-model="employer.company_id"
                                                class="form-control bg-light fw-bold"
                                            >
                                                <option value="">Select Company</option>
                                                <option v-for="company in companieslist" :key="company.value" :value="company.value">
                                                    {{ company.label }}
                                                </option>
                                            </Field>
                                            <!-- <Multiselect v-else class="rounded form-control bg-light fw-bold"
                                                :searchable="false" placeholder="Select Company"
                                                noOptionsText="No companies available" :options="companieslist"
                                                v-model="employer.company_id" @select="handleCompanyChange"
                                                @clear="clearCompanySelection">
                                            </Multiselect> -->
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <label class="col-sm-4 col-form-label">Phone Number</label>
                                        <div class="col-sm-8">
                                            <p v-if="!isEditMode"
                                                class="form-control-plaintext text-right fw-bold cursor-pointer">{{
                                                    employer.profile.phone || 'N/A' }}</p>
                                            <input v-else type="text" class="form-control bg-light fw-bold"
                                                v-model="employer.profile.phone" />
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <label class="col-sm-4 col-form-label">Email</label>
                                        <div class="col-sm-8">
                                            <p v-if="!isEditMode"
                                                class="form-control-plaintext text-right fw-bold cursor-pointer">{{
                                                    employer.email || 'N/A' }}</p>
                                            <input v-else type="email" class="form-control bg-light fw-bold"
                                                v-model="employer.email" />
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <label class="col-sm-4 col-form-label">State</label>
                                        <div class="col-sm-8">
                                            <p v-if="!isEditMode"
                                                class="form-control-plaintext text-right fw-bold cursor-pointer">{{
                                                    employer.state || 'N/A' }}</p>
                                            <Field
                                                v-else
                                                as="select"
                                                name="state_id"
                                                v-model="employer.state_id"
                                                class="form-control bg-light fw-bold"
                                            >
                                                <option value="">Select State</option>
                                                <option v-for="state in stateslist" :key="state.value" :value="state.value">
                                                    {{ state.label }}
                                                </option>
                                            </Field>
                                            <!-- <Multiselect v-else class="rounded form-control bg-light fw-bold"
                                                :searchable="false" placeholder="Select State"
                                                noOptionsText="No states available" :options="stateslist"
                                                v-model="employer.state_id" @select="handleStateChange"
                                                @clear="clearStateSelection">
                                            </Multiselect> -->
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <label class="col-sm-4 col-form-label">Postcode</label>
                                        <div class="col-sm-8">
                                            <p v-if="!isEditMode"
                                                class="form-control-plaintext text-right fw-bold cursor-pointer">{{
                                                    employer.postcode || 'N/A' }}</p>
                                            <input v-else type="text" class="form-control bg-light fw-bold"
                                                v-model="employer.postcode" />
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    <!-- Change Password -->
                    <div class="border border-white-300 card rounded mt-20">
                        <div class="d-flex justify-content-between mt-10 mx-10">
                            <h2 class="fs-4 fw-semibold mt-4">Change Password</h2>
                            <div>
                                <button type="button"
                                    class="btn btn-light fw-semibold text-secondary-dark rounded w-full sm:w-auto"
                                    @click="openPasswordModal">
                                    Update Password
                                </button>
                            </div>
                        </div>
                        <hr class="border-gray-400 my-5">
                        <div class="grid grid-cols-1 gap-4">
                            <div class="mx-10 mb-10">
                                <label class="block text-sm font-medium text-gray-700">Current Password</label>
                                <div class="col-sm-4">
                                    <p class="mt-3 text-gray-900 border border-white-300 p-2 rounded">********</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Change Password Modal -->
                    <div v-if="showPasswordModal" class="modal fade show" tabindex="-1" aria-hidden="true"
                        style="display: block; background-color: rgba(0, 0, 0, 0.5);">
                        <div class="modal-dialog modal-dialog-centered modal-lg">
                            <div class="modal-content rounded">
                                <div class="modal-header">
                                    <h5 class="modal-title">Update Password</h5>
                                    <button type="button" class="btn-close" @click="closePasswordModal"></button>
                                </div>
                                <div class="modal-body">
                                    <p class="mb-4">Your password must be a minimum of 8 characters.</p>
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="currentPassword" class="form-label">Current Password</label>
                                            <input type="password" id="currentPassword" class="form-control bg-light"
                                                v-model="password.current" placeholder="Enter current password here"
                                                @blur="validateCurrentPassword" />
                                            <p v-if="passwordErrors.current" class="text-danger mt-2">{{
                                                passwordErrors.current }}</p>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="newPassword" class="form-label">New Password</label>
                                            <input type="password" id="newPassword" class="form-control bg-light"
                                                v-model="password.new" placeholder="Enter new password here"
                                                @blur="validateNewPassword" />
                                            <p v-if="passwordErrors.new" class="text-danger mt-2">{{ passwordErrors.new
                                            }}</p>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="confirmPassword" class="form-label">Re-Type Password</label>
                                            <input type="password" id="confirmPassword" class="form-control bg-light"
                                                v-model="password.confirm" placeholder="Re-type new password here"
                                                @blur="validatePasswordMatch" />
                                            <p v-if="passwordErrors.confirm" class="text-danger mt-2">{{
                                                passwordErrors.confirm }}</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary rounded"
                                        @click="savePassword">Save</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Delete Account -->
                    <div class="border border-white-300 card rounded mt-20">
                        <div class="mt-12 mb-2 mx-10">
                            <h2 class="fs-4 fw-semibold ">Delete Account</h2>

                        </div>
                        <hr class="border-gray-400 my-5">
                        <div
                            class="notice d-flex bg-light-warning rounded border-warning border border-dashed p-6 mx-10">
                            <!-- <i class="ki-outline ki-information fs-2tx text-warning me-4"></i> -->
                            <i class="fa-solid fa-circle-exclamation fs-2tx me-4" style="color: #EFBB27;"></i>
                            <div class="d-flex flex-stack flex-grow-1">
                                <div class="fw-semibold">
                                    <h4 class="text-gray-900 fw-bold">You Are Deactivating Your Account</h4>
                                    <div class="fs-6 text-gray-700">For extra security, this requires you to confirm
                                        your email or phone
                                        number when you reset your password.</div>
                                    <br>
                                    <!-- <a href="#">Learn more</a> -->
                                    <a class="fw-bold" href="/metronic8/demo6/account/billing.html"
                                        style="color: #3674FB;">Learn more</a>
                                </div>
                            </div>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-4 mt-10">
                            <div class="mx-10">
                                <input type="checkbox" id="confirmDeactivation"
                                    class="me-2 cursor-pointer form-check-input me-3" v-model="confirmDeactivation" />
                                <label for="confirmDeactivation" class="me-4">Confirm Account Deactivation</label>
                            </div>
                            <div>
                                <button type="button" class="btn btn-danger rounded mx-10"
                                    :disabled="!confirmDeactivation" @click="deactivateAccount">
                                    Deactivate Account
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-else class="text-center py-6">
                    <p class="text-gray-600">No employer data available.</p>
                </div>
            </div>
        </div>
    </div>

</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue';
import axios from 'axios';
import Swal from "sweetalert2/dist/sweetalert2.min.js";
import { Field } from "vee-validate";

export default defineComponent({
    name: 'EmployerProfile',
    components: {
     
        Field
    },
    setup() {
        const employer = ref({
            id: '',
            first_name: '',
            last_name: '',
            profile: {
                position: '',
                phone: ''
            },
            company_id: '', 
            company_name: '', 
            email: '',
            state_id: null,
            state: '', 
            postcode: '',
        });
        const loading = ref(true);
        const error = ref(null);
        const isEditMode = ref(false);
        const companieslist = ref<{ value: number; label: string }[]>([]);
        const stateslist = ref<{ value: number; label: string }[]>([]);
        const showPasswordModal = ref(false);
        const password = ref({
            current: '',
            new: '',
            confirm: ''
        });
        const passwordErrors = ref({
            current: '',
            new: '',
            confirm: ''
        });
        const isPasswordValid = ref({
            current: false,
            new: false,
            confirm: false
        });
        const confirmDeactivation = ref(false);

        const fetchCompanies = async () => {
            try {
                const response = await axios.get('/fetchAllCompanies');
                companieslist.value = response.data.map((item: any) => ({
                    value: item.id,
                    label: item.name
                }));
            } catch (error) {
                console.error(error);
            }
        };

        const fetchStates = async () => {
            try {
                const response = await axios.get('/fetchAllStates');
                stateslist.value = response.data.map((item: any) => ({
                    value: item.id,
                    label: item.name
                }));
            } catch (error) {
                console.error(error);
            }
        };

        const fetchEmployer = async () => {
            try {
                loading.value = true;
                await Promise.all([fetchCompanies(), fetchStates()]);
                const response = await axios.get('/getEmployerDetail');
                employer.value = {
                    ...response.data,
                    company_id: response.data.company_id,
                    company_name: response.data.company_name,
                    state_id: response.data.state_id,
                    state: response.data.state,
                    profile: response.data.profile || { position: '', phone: '' }
                };
                loading.value = false;
            } catch (err: any) {
                error.value = err.message || 'Failed to load employer data';
                loading.value = false;
            }
        };

        const toggleEditMode = () => {
            if (isEditMode.value) {
                updateEmployer();
            }
            isEditMode.value = !isEditMode.value;
        };

        const updateEmployer = async () => {
            try {
                const payload = {
                    id: employer.value.id,
                    first_name: employer.value.first_name,
                    last_name: employer.value.last_name,
                    profile: {
                        position: employer.value.profile.position,
                        phone: employer.value.profile.phone
                    },
                    company_id: employer.value.company_id,
                    email: employer.value.email,
                    state_id: employer.value.state_id,
                    postcode: employer.value.postcode,
                };
                await axios.post(`/employer/${employer.value.id}`, payload);
                Swal.fire({
                    icon: 'success',
                    title: 'Success',
                    text: 'Employer details updated successfully',
                }).finally(() => {
                    window.location.reload();
                });
            } catch (err) {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Failed to update employer details',
                });
            }
        };

        const handleCompanyChange = (selectedCompany: any) => {
            employer.value.company_id = selectedCompany;
        };

        const handleStateChange = (selectedState: any) => {
            employer.value.state_id = selectedState;
        };

        const clearCompanySelection = () => {
            employer.value.company_id = '';
        };

        const clearStateSelection = () => {
            employer.value.state_id = null;
        };

        const openPasswordModal = () => {
            showPasswordModal.value = true;
        };

        const closePasswordModal = () => {
            showPasswordModal.value = false;
        };

        const validateCurrentPassword = async () => {
            if (password.value.current === '') {
                passwordErrors.value.current = 'Current password is required';
                isPasswordValid.value.current = false; // Update the `current` property
                return;
            } else {
                passwordErrors.value.current = '';
            }

            try {
                const response = await axios.post(`/employer/${employer.value.id}/verify-password`, {
                    current_password: password.value.current
                });

                if (response.data.valid === false) {
                    passwordErrors.value.current = 'Current password is incorrect';
                    isPasswordValid.value.current = false; // Update the `current` property
                } else {
                    passwordErrors.value.current = '';
                    isPasswordValid.value.current = true; // Update the `current` property
                }
            } catch (error: any) {
                if (error.response && error.response.data && error.response.data.error) {
                    passwordErrors.value.current = error.response.data.error; // Display backend error
                } else {
                    passwordErrors.value.current = 'Error validating password';
                }
                isPasswordValid.value.current = false; // Update the `current` property
            }
        };

        const validateNewPassword = () => {
            if (password.value.new.length < 8) {
                passwordErrors.value.new = 'New password must be at least 8 characters long';
                isPasswordValid.value.new = false;
            } else {
                passwordErrors.value.new = '';
                isPasswordValid.value.new = true;
            }
        };

        const validatePasswordMatch = () => {
            if (password.value.new !== password.value.confirm) {
                passwordErrors.value.confirm = 'Passwords do not match';
                isPasswordValid.value.confirm = false; // Update the `confirm` property
            } else {
                passwordErrors.value.confirm = '';
                isPasswordValid.value.confirm = true; // Update the `confirm` property
            }
        };

        const savePassword = async () => {
            try {
                const payload = {
                    current_password: password.value.current,
                    new_password: password.value.new,
                    new_password_confirmation: password.value.confirm
                };

                const response = await axios.post(`/employer/${employer.value.id}/update-password`, payload);

                if (response.data.message) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Success',
                        text: response.data.message,
                    });
                    closePasswordModal();
                }
            } catch (error: any) {
                if (error.response && error.response.data && error.response.data.error) {
                    alert(error.response.data.error);
                } else {
                    alert('Failed to change password');
                }
                console.error(error);
            }
        };

        const deactivateAccount = async () => {
            try {
                const response = await axios.post('/account/deactivate');
                Swal.fire({
                    icon: 'success',
                    title: 'Success',
                    text: response.data.message,
                });
                // Optionally, redirect the user after deactivation
                window.location.href = '/logout';
            } catch (error: any) {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: error.response?.data?.error || 'An error occurred while deactivating the account.',
                });
            }
        };

        onMounted(() => {
            fetchEmployer();
        });

        return {
            employer,
            loading,
            error,
            isEditMode,
            companieslist,
            stateslist,
            toggleEditMode,
            handleCompanyChange,
            handleStateChange,
            clearCompanySelection,
            clearStateSelection,
            updateEmployer,
            showPasswordModal,
            password,
            openPasswordModal,
            closePasswordModal,
            savePassword,
            validateCurrentPassword,
            validateNewPassword,
            validatePasswordMatch,
            passwordErrors,
            isPasswordValid,
            confirmDeactivation,
            deactivateAccount
        };
    }
});
</script>

<style scoped>
.multiselect-wrapper {
    margin-left: -10px
}
</style>
