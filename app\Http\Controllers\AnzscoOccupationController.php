<?php

namespace App\Http\Controllers;

use App\AnzscoOccupation;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Auth;
use Exception;
use Illuminate\Support\Facades\Storage;
use Ya<PERSON>ra\DataTables\Facades\DataTables;
use Str;

class AnzscoOccupationController extends Controller
{
    /**
     * Get search result for ANZSCO Occupations/Jobs
     *
     * @param \Illuminate\Http\Request $request
     * @return mixed
     */
    public function search(Request $request): mixed
    {
        try{
            $search = $request->search;
            $data = null;

            if($search) {
                $data = AnzscoOccupation::query()
                        ->when($request->search, function($q) use($request) {
                            return $q->where(function($q) use ($request) {
                                return $q->where('anzsco_code', 'like', '%'. $request->search . '%')
                                        ->orWhereRaw('LOWER(anzsco_title) like ?', ['%'. strtolower($request->search) . '%']);
                            });
                        })
                        ->orderBy('anzsco_title')
                        ->when(!is_null($request->paginate) && !is_null($request->per_page), function(Builder $q) use($request) {
                            return $q->paginate($request->per_page, ['*'], 'page', $request->page);
                        }, function($q) {
                            return $q->get();
                        });
            }

            return response()->json([
                'success' => $data != null,
                'message' => $data != null ? 'Data Found' : 'Data Not Found',
                'data' => $data
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        if(!$request->ajax()) {
            return view('anzscooccupations.index');
        }

        $query = AnzscoOccupation::query()
        ->withCount('selectedUsers');

        return DataTables::of($query)
                        ->addIndexColumn()
                        ->addColumn('action', function ($row) {
                            return view('anzscooccupations.partials._td_action', compact('row'));
                        })
                        ->addColumn('selected_users_count', function ($row) {
                            return $row->selected_users_count;
                        })
                        ->rawColumns(['action'])
                        ->make(true);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('backend.anzsco-data.occupations.create_edit');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $data = $request->validate([
                'anzsco_code' => 'required|string|max:255',
                'anzsco_title' => 'required|string|max:255',
                'media' => 'nullable|file|mimes:jpg,jpeg,png,gif,mp4,mov,avi|max:153600',
                'media_text' => 'nullable|string',
                'anzsco_description' => 'nullable|max:255',
                'sub_profile_code' => 'nullable|string|max:255',
                'occupation_type' => 'required|string|max:255',
            ]);

            if ($request->hasFile('media')) {
                $file = $request->file('media');
                $fileName = Str::random(10) . '-' . uniqid(rand(1000, 9999)) . '.' . $file->getClientOriginalExtension();
                $filePath = "/anzsco-occupations";

                $file->storeAs($filePath, $fileName);
                $data['media'] = $filePath . '/' . $fileName;
            }elseif ($request->filled('media_text')) {
                $data['media'] = $request->input('media_text'); 
            }

            AnzscoOccupation::create($data);

            return response()->json([
                'success' => true,
                'message' => 'ANZSCO Occupation has been created successfully.'
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create ANZSCO Occupation: ' . $e->getMessage()
            ], 500);
        }
    }
    /**
     * Display the specified resource.
     */
    public function show(AnzscoOccupation $occupation)
    {
        return $this->edit($occupation);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(AnzscoOccupation $occupation)
    {
        return response()->json([
            'success' => true,
            'data' => $occupation
        ]);
        // return view('backend.anzsco-data.occupations.create_edit', [
        //     'data' => $occupation
        // ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, AnzscoOccupation $occupation)
    {
        try {
            $data = $request->validate([
                'anzsco_code' => 'required|string|max:255',
                'anzsco_title' => 'required|string|max:255',
                'media' => 'nullable|file|mimes:jpg,jpeg,png,gif,mp4,mov,avi|max:153600',
                'media_text' => 'nullable|string',
                'anzsco_description' => 'nullable|max:255',
                'sub_profile_code' => 'nullable|string|max:255',
                'occupation_type' => 'required|string|max:255',
            ]);

            if ($request->hasFile('media')) {
                if ($occupation->media && Storage::exists($occupation->media)) {
                    Storage::delete($occupation->media);
                }

                $file = $request->file('media');
                $fileName = Str::random(10) . '-' . uniqid(rand(1000, 9999)) . '.' . $file->getClientOriginalExtension();
                $filePath = "/anzsco-occupations";

                $file->storeAs($filePath, $fileName);
                $data['media'] = $filePath . '/' . $fileName;
            }elseif ($request->filled('media_text')) {
                $data['media'] = $request->input('media_text'); 
            }

            $occupation->update($data);

            return response()->json([
                'success' => true,
                'message' => 'ANZSCO Occupation has been updated successfully.'
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update ANZSCO Occupation: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(AnzscoOccupation $occupation)
    {
        try {
            if ($occupation->media && Storage::exists($occupation->media)) {
                Storage::delete($occupation->media);
            }
            $occupation->delete();

            return response()->json([
                'success' => true,
                'message' => 'ANZSCO Occupation has been deleted successfully.'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete ANZSCO Occupation: ' . $e->getMessage()
            ], 500);
        }
    }
}
