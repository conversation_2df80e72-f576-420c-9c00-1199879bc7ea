@extends('layouts.admin')
@section('breadcrumbs', Breadcrumbs::render('edit-company', $company->detail ? $company->detail->name : $company->name))
@section('content')
    @if (session()->has('message'))
        <div class="alert alert-success text-center">
            <a href="#" class="close" data-dismiss="alert" aria-label="close"></a> {{ session()->get('message') }}
        </div>
    @endif
    @push('stylesheets')
        <link media="screen" type="text/css" rel="stylesheet" href="{{ asset('assets/plugins/switchery/css/switchery.min.css') }}">
    @endpush
    @push('styles')
        <style>
            .custom-input:focus {
                box-shadow: none;
            }

            .bg-grey-lightest {
                background-color: #f1f1f1;
                padding: 15px 20px;
            }
        </style>
    @endpush
    <div class="row">
        <div class="col-lg-12">
            <div class="card card-default">
                <div class="card-header  separator">
                    <div class="card-title">Edit Company</div>
                </div>
                <div class="card-block">
                    <form method="POST" action="{{ route('companies.update', $company->id) }}" id="form-company" role="form" autocomplete="off" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group form-group-default required">
                                    <label>Company Name</label>
                                    <input type="text" class="form-control" name="company_name" value="{{ old('company_name', $company->detail ? $company->detail->name : $company->name) }}" required>
                                </div>
                            </div>
                            <div class="{{ $company->detail && $company->detail->logo ? 'col-md-4' : 'col-md-6' }}">
                                <div class="form-group form-group-default" :class="{{ $company->detail && $company->detail->logo ? 'required' : '' }}">
                                    <label>Logo</label>
                                    <p class="my-2">Recommended 1:1 aspect ratio logo</p>
                                    <input type="file" class="form-control" name="logo">
                                </div>
                            </div>
                            @if ($company->detail && $company->detail->logo)
                                <div class="col-md-2 mb-2">
                                    <img src="{{ Storage::url($company->detail->logo) }}" class="img-fluid" alt="" style="max-width: 100px;">
                                    <input type="hidden" name="current_logo" value="{{ $company->detail->logo }}
                                    ">
                                    
                                  
                                </div>
                            @endif
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group form-group-default form-group-default-select2 required">
                                    <label>States</label>
                                    <select class="full-width select2" data-init-plugin="select2" name="states[]" multiple>
                                        @foreach ($states as $state)
                                            <option value="{{ $state->id }}" {{ $company->states->contains($state->id) ? 'selected' : '' }}>{{ $state->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group form-group-default form-group-default-select2 required">
                                    <label>Associated Industries</label>
                                    <select class="full-width select2" data-init-plugin="select2" name="industries[]" multiple>
                                        @foreach ($industries as $industry)
                                            <option value="{{ $industry->id }}" {{ $company->industries->contains($industry->id) ? 'selected' : '' }}>{{ $industry->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group form-group-default form-group-default-select2">
                                    <label>Linked Content (Industry Content)</label>
                                    <select class="full-width select2" data-init-plugin="select2" name="industryunits[]" multiple>
                                        @foreach ($industryunits as $unit)
                                            <option value="{{ $unit->id }}" {{ $company->industryunits->contains($unit->id) ? 'selected' : '' }}>{{ $unit->title }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group form-group-default form-group-default-select2">
                                    <label>Linked VWE</label>
                                    <select class="full-width select2" data-init-plugin="select2" name="workexperience_templates[]" multiple>
                                        @foreach ($workexperienceTemplates as $template)
                                            <option value="{{ $template->id }}" {{ $company->workexperienceTemplates->contains($template->id) ? 'selected' : '' }}>{{ $template->title }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group form-group-default form-group-default-select2">
                                    <label>Linked Skills Trainings</label>
                                    <select class="full-width select2" data-init-plugin="select2" name="skillstraining_templates[]" multiple>
                                        @foreach ($skillstrainingTemplates as $template)
                                            <option value="{{ $template->id }}" {{ $company->skillstrainingTemplates->contains($template->id) ? 'selected' : '' }}>{{ $template->title }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group form-group-default form-group-default-select2">
                                    <label>Linked Lessons</label>
                                    <select class="full-width select2" data-init-plugin="select2" name="lessons[]" multiple>
                                        @foreach ($lessons as $lesson)
                                            <option value="{{ $lesson->id }}" {{ $company->lessons->contains($lesson->id) ? 'selected' : '' }}>{{ $lesson->title }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group form-group-default required">
                                    <label>Contact Person Name</label>
                                    <input type="text" class="form-control" name="contact_person" value="{{ old('contact_person', $company->detail ? $company->detail->contact_person : '') }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group form-group-default required">
                                    <label>Contact Person Position</label>
                                    <input type="text" class="form-control" name="position" value="{{ old('position', $company->detail ? $company->detail->position : '') }}">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group form-group-default required">
                                    <label>Contact Person Email</label>
                                    <input type="email" class="form-control" name="email" value="{{ old('email', $company->detail ? $company->detail->email : '') }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group form-group-default required">
                                    <label>Contact Person Phone</label>
                                    <input type="text" class="form-control" name="phone" value="{{ old('phone', $company->detail ? $company->detail->phone : '') }}">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group form-group-default required">
                                    <label>Subscription Start Date</label>
                                    <input type="date" class="form-control" name="subscription_start_date" value="{{ old('subscription_start_date', $company->detail && $company->detail->subscription_start_date ? $company->detail->subscription_start_date : '') }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group form-group-default required">
                                    <label>Subscription End Date</label>
                                    <input type="date" class="form-control" name="subscription_ending_on" value="{{ old('subscription_ending_on', $company->detail && $company->detail->subscription_ending_on ? $company->detail->subscription_ending_on : '') }}">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12 text-right">
                                <button class="btn btn-primary" type="submit">Update Company</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    @push('scripts')
        <script>
            jQuery(document).ready(function() {
                $('#form-company').validate({
                    rules: {
                        company_name: 'required',
                        logo: {
                            required: function(element) {
                                return !$('input[name="current_logo"]').val();
                            }
                        },
                        'states[]': 'required',
                        'industries[]': 'required',
                        contact_person: 'required',
                        position: 'required',
                        email: {
                            required: true,
                            email: true,
                        },
                        phone: 'required',
                        subscription_start_date: 'required',
                        subscription_ending_on: 'required',
                    }
                })
            });
        </script>
    @endpush
@endsection
