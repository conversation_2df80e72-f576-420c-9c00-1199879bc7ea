<?php

namespace App\Http\Controllers\Vue;

use App\Course;
use App\Employer;
use App\Profile;
use App\SkillstrainingTemplate;
use App\Student;
use App\WorkexperienceTemplate;
use App\Lesson;
use App\ActivityLog;
use App\Gameplan;
use App\GameplanQuestion;
use App\IndividualStudent;
use App\Plan;
use App\Services\UserAccessService;
use App\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class EmployersController extends Controller
{
    public function __construct()
    {
        $this->middleware('role:Employer');
    }


    public function getEmployerDetail()
    {
        $user = Auth::user();
        $employer = Employer::with('profile', 'company.detail')->find($user->id);
        $initials = $user->initials;

        $nameParts = explode(' ', $employer->name, 2);
        $firstName = $nameParts[0] ?? '';
        $lastName = $nameParts[1] ?? '';


        return response()->json([
            'id' => $employer->id,
            'first_name' => $firstName,
            'last_name' => $lastName,
            'email' => $employer->email,
            'state' => $employer->state ? $employer->state->name : null,
            'state_id' => $employer->state_id,
            'postcode' => $employer->postcode,
            'company_name' => $employer->company && $employer->company->detail ? $employer->company->detail->name : null,
            'company_logo' => $employer->company && $employer->company->detail ? $employer->company->detail->logo_full_path : null,
            'company_id' => $employer->company_id,
            'profile' => $employer->profile ? [
                'position' => $employer->profile->position ?? null,
                'phone' => $employer->profile->phone ?? null,
            ] : null,
            'initials' => $initials,
            'created_at' => $employer->created_at->toDateTimeString(),
            'updated_at' => $employer->updated_at->toDateTimeString(),
        ]);
    }

    public function update(Request $request)
    {
        // dd($request->all());
        $employer = Employer::find($request->id);
        // Log the incoming request and employer ID
        \Log::info('Employer Update Request', [
            'request_data' => $request->all(),
            'employer_id' => $employer->id
        ]);

        $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'profile.position' => 'required|string|max:255',
            'company_id' => 'required|exists:users,id',
            'profile.phone' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . $employer->id,
            'state_id' => 'required|exists:states,id',
            'postcode' => 'required|string|max:10',
            'password' => 'nullable|string|min:6',
        ]);

        try {
            $employer->update([
                'name' => $request->first_name . ' ' . $request->last_name,
                'email' => $request->email,
                'company_id' => $request->company_id,
                'state_id' => $request->state_id,
                'postcode' => $request->postcode,
            ]);

            \Log::info('Employer updated successfully', [
                'employer_id' => $employer->id,
                'updated_data' => [
                    'name' => $request->first_name . ' ' . $request->last_name,
                    'email' => $request->email,
                    'company_id' => $request->company_id,
                    'state_id' => $request->state_id,
                    'postcode' => $request->postcode,
                ]
            ]);

            if ($request->password) {
                $employer->update(['password' => bcrypt($request->password)]);
            }
        } catch (\Exception $e) {
            \Log::error('Error updating employer', [
                'employer_id' => $employer->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json(['error' => 'Error updating employer: ' . $e->getMessage()], 500);
        }

        try {
            if (!$employer->profile) {
                $profile = new Profile();
                $profile->user_id = $employer->id; // Ensure employer ID is set

                if (!$profile->user_id) {
                    \Log::error('Employer ID is null when creating profile', [
                        'employer' => $employer
                    ]);
                    return response()->json(['error' => 'Employer ID is null when creating profile'], 500);
                }

                $profile->save();

                \Log::info('Created new profile for employer', [
                    'employer_id' => $employer->id,
                    'profile_id' => $profile->id
                ]);
            }

            $employer->profile->update([
                'firstname' => $request->input('first_name'),
                'lastname' => $request->input('last_name'),
                'position' => $request->input('profile.position'),
                'phone' => $request->input('profile.phone'),
            ]);

            \Log::info('Employer profile updated successfully', [
                'employer_id' => $employer->id,
                'profile_id' => $employer->profile->id,
                'updated_data' => [
                    'position' => $request->input('profile.position'),
                    'phone' => $request->input('profile.phone'),
                ]
            ]);
        } catch (\Exception $e) {
            \Log::error('Error updating employer profile', [
                'employer_id' => $employer->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json(['error' => 'Error updating employer profile: ' . $e->getMessage()], 500);
        }

        return response()->json(['message' => 'Employer updated successfully']);
    }

    /**
     * Get students based on matching criteria with employer's company
     *
     * @return \Illuminate\Http\Response
     */
    public function getStudents()
    {
        // Get the authenticated employer
        $employer = Employer::findOrFail(Auth::id());

        // Get the employer's company with its relationships
        $company = $employer->company()->with([
            'industries',
            'industryunits',
            'workexperienceTemplates',
            'skillstrainingTemplates',
            'lessons'
        ])->first();

        if (!$company) {
            return response()->json(['message' => 'No company associated with this employer'], 404);
        }

        // Use the reusable query function
        $query = $this->getPipelineStudentsQuery($company, $employer);


        // Eager load only necessary relationships with constraints
        $query->with([
            'profile:id,user_id,firstname,lastname,standard_id,is_public', // Select only needed fields
            'profile.class:id,title', // Select only needed fields
            'latestGameplan.industries:id,name', // Select only needed fields from industries
            'latestGameplan.jobs:id,gameplan_id', // Select only needed fields from jobs
            'latestGameplan.companies:id,gameplan_id', // Select only needed fields from companies
            'state:id,name,code', // Include state information for location
            'followers' => function ($q) use ($employer) { // Eager load followers for the current employer
                $q->where('follow_by', $employer->id);
            },
            'lessonresponses' => function ($q) {
                $q->where('status', 'Submitted')->with('lesson:id,title');
            },
            'skillstrainingResponses' => function ($q) {
                $q->where('status', 'Submitted')->with('template:id,title');
            },
            'workexperienceResponses' => function ($q) {
                $q->where('status', 'Submitted')->with('template:id,title');
            },
            'favorites' => function ($q) {
                $q->where('favoriteable_type', 'App\\Industryunit')->with('favoriteable:id,title');
            },
            'latestGameplan.industries:id,name',
        ])->withCount('userSelectedOccupations');

        // Paginate results for better performance with large datasets
        $perPage = (int) request('per_page', 50); // Default to 50 per page, can be adjusted via request
        // $totalStudents = $query->count();
        $privateStudents = (clone $query)->whereHas('profile', function ($q) {  // need to clone query to avoid modifying original query
            $q->where('is_public', 0);
        })->count();
        $publicStudents = (clone $query)->whereHas('profile', function ($q) {  // need to clone query to avoid modifying original query
            $q->where('is_public', 1);
        })->count();
        $students = $query->paginate($perPage)->appends('initials');


        // Transform the data to include only necessary information
        $result = $students->through(function ($student) {
            return [
                'id' => $student->id,
                'name' => $student->name,
                'initials' => $student->initials,
                'email' => $student->email,
                'location' => $student->state ? $student->state->code : null,
                'year' => $student->profile && $student->profile->class ? $student->profile->class->title : null,
                'industriesCount' => @$student->latestGameplan->industries ? $student->latestGameplan->industries->count() : 0,
                'jobsCount' => ($student->latestGameplan?->jobs ? $student->latestGameplan->jobs->count() : 0) + $student->user_selected_occupations_count,
                'companiesCount' => $student->latestGameplan?->companies ? $student->latestGameplan->companies->count() : 0,
                'is_following' => $student->followers->isNotEmpty(), // Check if the eager-loaded collection is not empty
                'is_public' => $student->profile->is_public,
                'engaged_content' => $this->getEngagedContent($student),
            ];
        });

        // Return paginated response with metadata
        return response()->json([
            'result' => $result,
            'meta' => [
                'current_page' => $students->currentPage(),
                'from' => $students->firstItem(),
                'last_page' => $students->lastPage(),

                'per_page' => $students->perPage(),
                'to' => $students->lastItem(),
                'total' => $students->total(),
                'public_count' => $publicStudents,
                'private_count' => $privateStudents,
            ],
            'links' => [
                'first' => $students->url(1),
                'last' => $students->url($students->lastPage()),
                'prev' => $students->previousPageUrl(),
                'next' => $students->nextPageUrl(),
            ]
        ]);
    }
    protected function getEngagedContent($student)
    {
        $engagedContent = [];

        // Latest Submitted Lesson response
        $latestLessonResponse = $student->lessonresponses->where('status', 'Submitted')->sortByDesc('submitted_at')->first();
        if ($latestLessonResponse && $latestLessonResponse->lesson) {
            $engagedContent[] = $latestLessonResponse->lesson->title;
        }

        // Latest Submitted SkillsTraining response
        $latestSkillsTrainingResponse = $student->skillstrainingResponses->where('status', 'Submitted')->sortByDesc('submitted_at')->first();
        if ($latestSkillsTrainingResponse && $latestSkillsTrainingResponse->template) {
            $engagedContent[] = $latestSkillsTrainingResponse->template->title;
        }

        // Latest Submitted WorkExperience response
        $latestWorkExperienceResponse = $student->workexperienceResponses->where('status', 'Submitted')->sortByDesc('submitted_at')->first();
        if ($latestWorkExperienceResponse && $latestWorkExperienceResponse->template) {
            $engagedContent[] = $latestWorkExperienceResponse->template->title;
        }

        // Latest Favorited IndustryUnit
        $latestFavoriteIndustryUnit = User::find($student->id)->favorites->where('favoriteable_type', 'App\\Industryunit')->sortByDesc('created_at')->first();
        if ($latestFavoriteIndustryUnit && $latestFavoriteIndustryUnit->favoriteable) {
            $engagedContent[] = $latestFavoriteIndustryUnit->favoriteable->title;
        }

        // Latest Favorite IndustryCategory from Gameplan
        if ($student->latestGameplan && $student->latestGameplan->industries) {
            $latestGameplanIndustry = $student->latestGameplan->industries->first();
            if ($latestGameplanIndustry) {
                $engagedContent[] = $latestGameplanIndustry->name;
            }
        }

        return array_filter(array_values(array_unique($engagedContent)));
    }

    /**
     * Get student counts by state for map visualization
     *
     * @return \Illuminate\Http\Response
     */
    public function getStudentStateCounts()
    {
        // Get the authenticated employer
        $employer = Employer::findOrFail(Auth::id());

        // Get the employer's company with its relationships
        $company = $employer->company()->with([
            'industries',
            'industryunits',
            'workexperienceTemplates',
            'skillstrainingTemplates',
            'lessons'
        ])->first();

        if (!$company) {
            return response()->json(['message' => 'No company associated with this employer'], 404);
        }

        // Get IDs for filtering
        $companyIndustryIds = $company->industries->pluck('id')->toArray();
        $companyIndustryUnitIds = $company->industryunits->pluck('id')->toArray();
        $workexperienceTemplateIds = $company->workexperienceTemplates->pluck('id')->toArray();
        $skillstrainingTemplateIds = $company->skillstrainingTemplates->pluck('id')->toArray();
        $lessonIds = $company->lessons->pluck('id')->toArray();

        // Create a single optimized query that directly filters students
        $search = request('search');
        $completedModule = request('completed_module');
        $engagedContent = request('engaged_content');
        $industriesInGameplan = request('industries_in_gameplan');
        $companiesInGameplan = request('companies_in_gameplan');
        $query = Student::query();
        // Add search functionality
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'LIKE', "%{$search}%")
                    ->orWhere('email', 'LIKE', "%{$search}%");
            });
        }

        // Filter by state if requested
        $state = request('state');
        if ($state) {
            $query->whereHas('state', function ($q) use ($state) {
                $q->where('code', $state);
            });
        }

        // Filter by followed students if requested
        $isFollowing = request('is_following');
        if ($isFollowing) {
            $followedStudentIds = $employer->followedStudents()->pluck('following')->toArray();
            $query->whereIn('id', $followedStudentIds);
        }

        // Add OR conditions for each criteria
        $query->select('id', 'name', 'email', 'role_id', 'state_id', 'postcode')->where(function ($q) use ($workexperienceTemplateIds, $skillstrainingTemplateIds, $lessonIds, $companyIndustryIds, $companyIndustryUnitIds) {
            // Students who have submitted responses to the company's workexperienceTemplates
            if (!empty($workexperienceTemplateIds)) {
                $q->orWhereHas('workexperienceResponses', function ($subQ) use ($workexperienceTemplateIds) {
                    $subQ->whereIn('template_id', $workexperienceTemplateIds)
                        ->where('status', 'Submitted');
                });
            }

            // Students who have submitted responses to the company's skillstrainingTemplates
            if (!empty($skillstrainingTemplateIds)) {
                $q->orWhereHas('skillstrainingResponses', function ($subQ) use ($skillstrainingTemplateIds) {
                    $subQ->whereIn('template_id', $skillstrainingTemplateIds)
                        ->where('status', 'Submitted');
                });
            }

            // Students who have submitted responses to the company's lessons
            if (!empty($lessonIds)) {
                $q->orWhereHas('lessonresponses', function ($subQ) use ($lessonIds) {
                    $subQ->whereIn('lesson_id', $lessonIds)
                        ->where('status', 'Submitted');
                });
            }

            // Students whose gameplan industries match the company's industries
            if (!empty($companyIndustryIds)) {
                $q->orWhereHas('latestGameplan', function ($subQ) use ($companyIndustryIds) {
                    $subQ->whereHas('industries', function ($industryQ) use ($companyIndustryIds) {
                        $industryQ->whereIn('industry_categories.id', $companyIndustryIds);
                    });
                });
            }

            // Students who have favorited the same industryunits as the company
            if (!empty($companyIndustryUnitIds)) {
                $q->orWhereHas('favorites', function ($subQ) use ($companyIndustryUnitIds) {
                    $subQ->where('favoriteable_type', 'App\Industryunit')
                        ->whereIn('favoriteable_id', $companyIndustryUnitIds);
                });
            }
        })->where(function ($q) use ($completedModule, $engagedContent, $industriesInGameplan, $companiesInGameplan, $companyIndustryIds, $company) {
            $q->when($completedModule, function ($query) {
                $query->where(function ($subQuery) {
                    $subQuery->whereHas('workexperienceResponses', function ($q) {
                        $q->where('status', 'Submitted');
                    })->orWhereHas('skillstrainingResponses', function ($q) {
                        $q->where('status', 'Submitted');
                    })->orWhereHas('lessonresponses', function ($q) {
                        $q->where('status', 'Submitted');
                    });
                });
            })->when($engagedContent, function ($query) {
                $query->orWhereHas('favorites', function ($subQ) {
                    $subQ->where('favoriteable_type', 'App\Industryunit');
                });
            })->when($industriesInGameplan, function ($query) use ($companyIndustryIds) {
                $query->orWhereHas('latestGameplan', function ($subQ) use ($companyIndustryIds) {
                    $subQ->whereHas('industries', function ($industryQ) use ($companyIndustryIds) {
                        $industryQ->whereIn('industry_categories.id', $companyIndustryIds);
                    });
                });
            })->when($companiesInGameplan, function ($query) use ($company) {
                $query->orWhereHas('latestGameplan', function ($subQ) use ($company) {
                    $subQ->whereHas('companies', function ($companyQ) use ($company) {
                        $companyQ->where('company', 'LIKE', "%{$company->detail->name}%");
                    });
                });
            });
        });

        // Get state counts
        $stateCounts = $query->with('state:id,name,code')
            ->get()
            ->groupBy('state.code')
            ->map(function ($students, $stateCode) {
                return [
                    'state_code' => $stateCode,
                    'state_name' => $students->first()->state->name ?? 'Unknown',
                    'student_count' => $students->count()
                ];
            })
            ->values();

        return response()->json([
            'state_counts' => $stateCounts
        ]);
    }



    public function verifyCurrentPassword(Request $request, Employer $employer)
    {
        $request->validate([
            'current_password' => 'required',
        ]);

        // Check if the current password is correct
        if (!\Hash::check($request->current_password, $employer->password)) {
            return response()->json(['error' => 'Current password is incorrect'], 400);
        }

        return response()->json(['message' => 'Current password is correct']);
    }

    public function updatePassword(Request $request, Employer $employer)
    {
        $request->validate([
            'current_password' => 'required',
            'new_password' => 'required|string|min:8|confirmed',
        ]);

        // Check if the current password is correct
        if (!\Hash::check($request->current_password, $employer->password)) {
            return response()->json(['error' => 'Current password is incorrect'], 400);
        }

        // Update the password
        $employer->update([
            'password' => bcrypt($request->new_password),
        ]);

        return response()->json(['message' => 'Password updated successfully']);
    }


    public function deactivateAccount(Request $request)
    {
        $user = Auth::user();

        if (!$user) {
            return response()->json(['error' => 'User not authenticated'], 401);
        }

        $profile = $user->profile;

        if (!$profile) {
            return response()->json(['error' => 'Profile not found'], 404);
        }

        $profile->removed = 1; // Mark the account as removed
        $profile->save();

        return response()->json(['message' => 'Account deactivated successfully']);
    }

    /**
     * Toggle follow status for a student
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function toggleFollowStudent(Request $request)
    {
        $request->validate([
            'student_id' => 'required|integer|exists:users,id'
        ]);

        $employer = Employer::findOrFail(Auth::id());
        $studentId = $request->student_id;

        // Verify the student exists and is actually a student
        $student = Student::findOrFail($studentId);

        $isNowFollowing = $employer->toggleFollowStudent($studentId);

        return response()->json([
            'success' => true,
            'is_following' => $isNowFollowing,
            'message' => $isNowFollowing ? 'Student followed successfully' : 'Student unfollowed successfully'
        ]);
    }

    /**
     * Get follow status for multiple students
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function getFollowStatus(Request $request)
    {
        $request->validate([
            'student_ids' => 'required|array',
            'student_ids.*' => 'integer|exists:users,id'
        ]);

        $employer = Employer::findOrFail(Auth::id());
        $studentIds = $request->student_ids;

        $followedStudentIds = $employer->followedStudents()
            ->whereIn('following', $studentIds)
            ->pluck('following')
            ->toArray();

        $followStatus = [];
        foreach ($studentIds as $studentId) {
            $followStatus[$studentId] == in_array($studentId, $followedStudentIds);
        }

        return response()->json([
            'success' => true,
            'follow_status' => $followStatus
        ]);
    }

    /**
     * Get all modules (Lessons, Virtual Work Experience, Skills Training) linked to the employer's company
     */
    public function getCompanyModules(Request $request)
    {
        $employer = Employer::findOrFail(Auth::id());
        $company = $employer->company;
        if (!$company) {
            return response()->json(['message' => 'No company associated with this employer'], 404);
        }

        $courseType = rtrim($request->input('courseType', ''), '/');
        $difficulty = rtrim($request->input('difficulty', ''), '/');
        $search = rtrim($request->input('search', ''), '/');
        $perPage = (int) $request->input('per_page', 8);
        $page = (int) $request->input('page', 1);

        // Fetch all modules
        $lessons = $company->lessons()->with('badge.companies');
        $vwes = $company->workexperienceTemplates()->with('badge.companies');
        $skills = $company->skillstrainingTemplates()->with('badge.companies');

        // Apply filters/search to each
        if ($difficulty) {
            $lessons->where('level', $difficulty);
            $vwes->where('level', $difficulty);
            $skills->where('level', $difficulty);
        }
        if ($search) {
            $lessons->where(function ($q) use ($search) {
                $q->where('title', 'LIKE', "%$search%")
                    ->orWhere('level', 'LIKE', "%$search%");
            });
            $vwes->where(function ($q) use ($search) {
                $q->where('title', 'LIKE', "%$search%")
                    ->orWhere('level', 'LIKE', "%$search%");
            });
            $skills->where(function ($q) use ($search) {
                $q->where('title', 'LIKE', "%$search%")
                    ->orWhere('level', 'LIKE', "%$search%");
            });
        }

        // Get all results as arrays
        $lessons = $lessons->get()->map(function ($item) {
            $item->module_type = 'Lessons';
            return $item;
        });
        $vwes = $vwes->get()->map(function ($item) {
            $item->module_type = 'Virtual Work Experience';
            return $item;
        });
        $skills = $skills->get()->map(function ($item) {
            $item->module_type = 'Skills Training';
            return $item;
        });

        // Merge all
        $allModules = $lessons->concat($vwes)->concat($skills)->values();

        // Optionally filter by courseType after merging
        if ($courseType) {
            $allModules = $allModules->where('module_type', $courseType)->values();
        }

        // Sort by created_at desc (or any other field)
        $allModules = $allModules->sortByDesc('created_at')->values();

        // Manual pagination
        $total = $allModules->count();
        $sliced = $allModules->slice(($page - 1) * $perPage, $perPage)->values();
        $lastPage = (int) ceil($total / $perPage);
        $from = $total > 0 ? (($page - 1) * $perPage) + 1 : 0;
        $to = $total > 0 ? min($from + $perPage - 1, $total) : 0;

        return response()->json([
            'data' => $sliced,
            'meta' => [
                'total' => $total,
                'current_page' => $page,
                'per_page' => $perPage,
                'last_page' => $lastPage,
                'from' => $from,
                'to' => $to,
            ],
        ]);
    }


    public function certificatePreview(Request $request, $id, $type)
    {
        $template = null;
        $certificateTitle = null;
        $certificateImage = null;

        // Map frontend type to display type
        $displayType = $type;
        if ($type === 'skillstraining') {
            $template = SkillstrainingTemplate::find($id);
            $displayType = 'skills workshop';
        } elseif ($type === 'workexperience') {
            $template = WorkexperienceTemplate::find($id);
            $displayType = 'virtual work experience';
        }

        if ($template) {
            $certificateTitle = $template->certificate_title ?? $template->title ?? 'Certificate';
            $certificateImage = $template->certificate_image ?? null;
        }

        $templateTitle = $request->query('title', $certificateTitle ?? 'Demo Certificate Title');

        // if ($request->has('download')) {
        //     $pdf = \PDF::loadView('wew.pdf.demoCertificate', [
        //         'type' => $displayType,
        //         'template' => $template,
        //     ]);
        //     $filename = ($templateTitle ? str_replace(' ', '_', $templateTitle) : 'certificate') . '.pdf';
        //     return $pdf->download($filename);
        // }

        return view('wew.pdf.demoCertificate', [
            'type' => $displayType,
            'templateTitle' => $templateTitle,
            'certificateImage' => $certificateImage,
            'template' => $template,
        ]);
    }


    public function getCompletedModules(Request $request)
    {
        $courseId = $request->course_id;
        $courseType = $request->course_type;

        $relationMap = [
            'Lessons' => ['relation' => 'lessonresponses', 'foreign_key' => 'lesson_id'],
            'Virtual Work Experience' => ['relation' => 'workexperienceResponses', 'foreign_key' => 'template_id'],
            'Skills Training' => ['relation' => 'skillstrainingResponses', 'foreign_key' => 'template_id'],
        ];

        if (!isset($relationMap[$courseType])) {
            return response()->json(['error' => 'Invalid course type'], 400);
        }

        $relation = $relationMap[$courseType]['relation'];
        $foreignKey = $relationMap[$courseType]['foreign_key'];

        $courseTitle = match ($courseType) {
            'Lessons' => Lesson::find($courseId)?->title,
            'Virtual Work Experience' => WorkExperienceTemplate::find($courseId)?->title,
            'Skills Training' => SkillsTrainingTemplate::find($courseId)?->title,
            default => null,
        };

        // Build the base query
        $query = Student::whereIn('role_id', [3, 4])
            ->whereHas($relation, function ($query) use ($foreignKey, $courseId) {
                $query->where($foreignKey, $courseId)->where('status', 'Submitted');
            });

        if ($request->has('is_public')) {
            $query->whereHas('profile', function ($q) use ($request) {
                $q->where('is_public', $request->input('is_public'));
            });
        }
        // Pagination
        $perPage = (int) $request->input('per_page', 50);
        $page = (int) $request->input('page', 1);

        // Count public/private students
        $privateStudents = (clone $query)->whereHas('profile', function ($q) {
            $q->where('is_public', 0);
        })->count();
        $publicStudents = (clone $query)->whereHas('profile', function ($q) {
            $q->where('is_public', 1);
        })->count();

        $students = $query->paginate($perPage)->appends('initials');

        // Paginate the main query
        $studentsPaginator = $query->with([
            'profile.class',
            'latestGameplan.industries',
            'latestGameplan.companies',
            'latestGameplan.jobs',
            'userSelectedOccupations',
            'lessonresponses',
            'skillstrainingResponses',
            'workexperienceResponses',
        ])->paginate($perPage, ['*'], 'page', $page);

        // Map the paginated results as needed
        $students = $studentsPaginator->getCollection()->map(function ($student) use ($relation, $foreignKey, $courseId) {
            $gameplan = $student->latestGameplan;
            $jobCount = $gameplan?->jobs->unique('id')->count() ?? 0;
            $occupationCount = $student->userSelectedOccupations->unique('id')->count() ?? 0;
            $response = $student->$relation
                ->where($foreignKey, $courseId)
                ->where('status', 'Submitted')
                ->sortByDesc('id')
                ->first();
            return [
                'id' => $student->id,
                'name' => $student->name,
                'initials' => $student->initials,
                'is_public' => $student->profile?->is_public,
                'responsePath' => $response ? $response->view_response_file_path : null,
                'responseId' => $response ? $response->id : null,
                'profile' => $student->profile,
                'considering' => [
                    'industries' => $gameplan?->industries->unique('id')->count() ?? 0,
                    'companies'  => $gameplan?->companies->unique('id')->count() ?? 0,
                    'jobs'       => $jobCount + $occupationCount,
                ],
            ];
        })->values(); // Ensure it's a numerically indexed array

        return response()->json([
            'course_title' => $courseTitle,
            'students' => $students,
            'meta' => [
                'current_page' => $studentsPaginator->currentPage(),
                'from' => $studentsPaginator->firstItem(),
                'last_page' => $studentsPaginator->lastPage(),
                'per_page' => $studentsPaginator->perPage(),
                'to' => $studentsPaginator->lastItem(),
                'total' => $studentsPaginator->total(),
                'public_count' => $publicStudents,
                'private_count' => $privateStudents,
            ],
            'links' => [
                'first' => $studentsPaginator->url(1),
                'last' => $studentsPaginator->url($studentsPaginator->lastPage()),
                'prev' => $studentsPaginator->previousPageUrl(),
                'next' => $studentsPaginator->nextPageUrl(),
            ]
        ]);
    }

    /**
     * Get the base query for students matching company criteria
     * This is a reusable function for both getting students and counts
     *
     * @param \App\Company $company
     * @param \App\Employer $employer
     * @param bool $includeSelect Whether to include select statement (default: true)
     * @return \Illuminate\Database\Eloquent\Builder
     */
    private function getPipelineStudentsQuery($company, $employer, $includeSelect = true)
    {
        // Get IDs for filtering
        $companyIndustryIds = $company->industries->pluck('id')->toArray();
        $companyIndustryUnitIds = $company->industryunits->pluck('id')->toArray();
        $workexperienceTemplateIds = $company->workexperienceTemplates->pluck('id')->toArray();
        $skillstrainingTemplateIds = $company->skillstrainingTemplates->pluck('id')->toArray();
        $lessonIds = $company->lessons->pluck('id')->toArray();

        // Create a single optimized query that directly filters students
        $search = request('search');
        $completedModule = request('completed_module');
        $engagedContent = request('engaged_content');
        $industriesInGameplan = request('industries_in_gameplan');
        $companiesInGameplan = request('companies_in_gameplan');
        $query = Student::query();

        // Add search functionality
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'LIKE', "%{$search}%")
                    ->orWhere('email', 'LIKE', "%{$search}%");
            });
        }

        // Filter by state if requested
        $state = request('state');
        if ($state) {
            $query->whereHas('state', function ($q) use ($state) {
                $q->where('code', $state);
            });
        }

        // Filter by followed students if requested
        $isFollowing = request('is_following');
        if ($isFollowing) {
            $followedStudentIds = $employer->followedStudents()->pluck('following')->toArray();
            $query->whereIn('id', $followedStudentIds);
        }

        // Add select statement only if requested (to avoid SQL ambiguity in joins)
        if ($includeSelect) {
            $query->select('id', 'name', 'email', 'role_id', 'state_id', 'postcode');
        }

        // Add OR conditions for each criteria
        $query->where(function ($q) use ($workexperienceTemplateIds, $skillstrainingTemplateIds, $lessonIds, $companyIndustryIds, $companyIndustryUnitIds) {
            // Students who have submitted responses to the company's workexperienceTemplates
            if (!empty($workexperienceTemplateIds)) {
                $q->orWhereHas('workexperienceResponses', function ($subQ) use ($workexperienceTemplateIds) {
                    $subQ->whereIn('template_id', $workexperienceTemplateIds)
                        ->where('status', 'Submitted');
                });
            }

            // Students who have submitted responses to the company's skillstrainingTemplates
            if (!empty($skillstrainingTemplateIds)) {
                $q->orWhereHas('skillstrainingResponses', function ($subQ) use ($skillstrainingTemplateIds) {
                    $subQ->whereIn('template_id', $skillstrainingTemplateIds)
                        ->where('status', 'Submitted');
                });
            }

            // Students who have submitted responses to the company's lessons
            if (!empty($lessonIds)) {
                $q->orWhereHas('lessonresponses', function ($subQ) use ($lessonIds) {
                    $subQ->whereIn('lesson_id', $lessonIds)
                        ->where('status', 'Submitted');
                });
            }

            // Students whose gameplan industries match the company's industries
            if (!empty($companyIndustryIds)) {
                $q->orWhereHas('latestGameplan', function ($subQ) use ($companyIndustryIds) {
                    $subQ->whereHas('industries', function ($industryQ) use ($companyIndustryIds) {
                        $industryQ->whereIn('industry_categories.id', $companyIndustryIds);
                    });
                });
            }

            // Students who have favorited the same industryunits as the company
            if (!empty($companyIndustryUnitIds)) {
                $q->orWhereHas('favorites', function ($subQ) use ($companyIndustryUnitIds) {
                    $subQ->where('favoriteable_type', 'App\Industryunit')
                        ->whereIn('favoriteable_id', $companyIndustryUnitIds);
                });
            }
        })->where(function ($q) use ($completedModule, $engagedContent, $industriesInGameplan, $companiesInGameplan, $companyIndustryIds, $company) {
            $q->when($completedModule, function ($query) {
                $query->where(function ($subQuery) {
                    $subQuery->whereHas('workexperienceResponses', function ($q) {
                        $q->where('status', 'Submitted');
                    })->orWhereHas('skillstrainingResponses', function ($q) {
                        $q->where('status', 'Submitted');
                    })->orWhereHas('lessonresponses', function ($q) {
                        $q->where('status', 'Submitted');
                    });
                });
            })->when($engagedContent, function ($query) {
                $query->orWhereHas('favorites', function ($subQ) {
                    $subQ->where('favoriteable_type', 'App\Industryunit');
                });
            })->when($industriesInGameplan, function ($query) use ($companyIndustryIds) {
                $query->orWhereHas('latestGameplan', function ($subQ) use ($companyIndustryIds) {
                    $subQ->whereHas('industries', function ($industryQ) use ($companyIndustryIds) {
                        $industryQ->whereIn('industry_categories.id', $companyIndustryIds);
                    });
                });
            })->when($companiesInGameplan, function ($query) use ($company) {
                $query->orWhereHas('latestGameplan', function ($subQ) use ($company) {
                    $subQ->whereHas('companies', function ($companyQ) use ($company) {
                        $companyQ->where('company', 'LIKE', "%{$company->detail->name}%");
                    });
                });
            });
        });

        return $query;
    }

    /**
     * Get consolidated dashboard data for employer
     * Includes: company info, industry categories, modules, pipeline count, top locations
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getDashboardData()
    {
        $employer = Employer::findOrFail(Auth::id());

        // Get company with all necessary relationships
        $company = $employer->company()->with([
            'detail',
            'industries',
            'industryunits',
            'workexperienceTemplates',
            'skillstrainingTemplates',
            'lessons'
        ])->first();

        if (!$company) {
            return response()->json(['message' => 'No company associated with this employer'], 404);
        }

        // Company basic info
        $companyData = [
            'company_name' => $company->detail ? $company->detail->name : null,
            'company_logo' => $company->detail ? $company->detail->logo_full_path : null,
            'company_industry_categories' => $company->industries->pluck('name')->implode(', '),
        ];

        // Get company modules counts
        $modules = [
            'Virtual Work Experience' => $company->workexperienceTemplates->count(),
            'Skills Training' => $company->skillstrainingTemplates->count(),
            'Lessons' => $company->lessons->count()
        ];
        $companyData['company_modules'] = $modules;

        // Get pipeline count using the reusable query function
        $pipelineQuery = $this->getPipelineStudentsQuery($company, $employer);
        $companyData['pipeline_count'] = $pipelineQuery->count();

        // Get top 3 locations using the same query function (without select statement)
        $locationQuery = $this->getPipelineStudentsQuery($company, $employer, false);
        $topLocations = $locationQuery->join('states', 'users.state_id', '=', 'states.id')
                                     ->selectRaw('states.name as state_name, states.code as state_code, COUNT(users.id) as student_count')
                                     ->groupBy('states.id', 'states.name', 'states.code')
                                     ->orderByDesc('student_count')
                                     ->limit(3)
                                     ->get();
        $companyData['top_locations'] = $topLocations;

        return response()->json([
            'success' => true,
            'data' => $companyData
        ]);
    }



    /**
     * Get top 3 locations where pipeline is based
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTopLocations()
    {
        $employer = Employer::findOrFail(Auth::id());

        $company = $employer->company()->with([
            'industries',
            'industryunits',
            'workexperienceTemplates',
            'skillstrainingTemplates',
            'lessons'
        ])->first();

        if (!$company) {
            return response()->json(['message' => 'No company associated with this employer'], 404);
        }

        // Use the consolidated query function without select statement
        $query = $this->getPipelineStudentsQuery($company, $employer, false);

        // Get state counts
        $stateCounts = $query->join('states', 'users.state_id', '=', 'states.id')
                            ->selectRaw('states.name as state_name, states.code as state_code, COUNT(users.id) as student_count')
                            ->groupBy('states.id', 'states.name', 'states.code')
                            ->orderByDesc('student_count')
                            ->limit(3)
                            ->get();

        return response()->json([
            'success' => true,
            'top_locations' => $stateCounts
        ]);
    }

    /**
     * Get students count in pipeline
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPipelineCount()
    {
        $employer = Employer::findOrFail(Auth::id());

        $company = $employer->company()->with([
            'industries',
            'industryunits',
            'workexperienceTemplates',
            'skillstrainingTemplates',
            'lessons'
        ])->first();

        if (!$company) {
            return response()->json(['message' => 'No company associated with this employer'], 404);
        }

        // Use the reusable query function
        $query = $this->getPipelineStudentsQuery($company, $employer);
        $pipelineCount = $query->count();

        return response()->json([
            'success' => true,
            'pipeline_count' => $pipelineCount
        ]);
    }

    /**
     * Get timeline activities for pipeline students (last 5 activities per student)
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function pipelineTimeline(Request $request)
    {
        $employer = Employer::findOrFail(Auth::id());
        $company = $employer->company()->with([
            'industries',
            'industryunits',
            'workexperienceTemplates',
            'skillstrainingTemplates',
            'lessons'
        ])->first();
        if (!$company) {
            return response()->json(['message' => 'No company associated with this employer'], 404);
        }
        // Get IDs for filtering (same as getStudents)
        $companyIndustryIds = $company->industries->pluck('id')->toArray();
        $companyIndustryUnitIds = $company->industryunits->pluck('id')->toArray();
        $workexperienceTemplateIds = $company->workexperienceTemplates->pluck('id')->toArray();
        $skillstrainingTemplateIds = $company->skillstrainingTemplates->pluck('id')->toArray();
        $lessonIds = $company->lessons->pluck('id')->toArray();
        // Build pipeline students query (same as getStudents, but just get IDs)
        $query = Student::query();
        // Add search functionality
        // if ($search) {
        //     $query->where(function ($q) use ($search) {
        //         $q->where('name', 'LIKE', "%{$search}%")
        //             ->orWhere('email', 'LIKE', "%{$search}%");
        //     });
        // }

        // Filter by state if requested
        $state = request('state');
        if ($state) {
            $query->whereHas('state', function ($q) use ($state) {
                $q->where('code', $state);
            });
        }

        // Filter by followed students if requested
        $isFollowing = request('is_following');
        if ($isFollowing) {
            $followedStudentIds = $employer->followedStudents()->pluck('following')->toArray();
            $query->whereIn('id', $followedStudentIds);
        }

        // Add OR conditions for each criteria
        $query->select('id', 'name');
        $query->where(function ($q) use ($workexperienceTemplateIds, $skillstrainingTemplateIds, $lessonIds, $companyIndustryIds, $companyIndustryUnitIds) {
            if (!empty($workexperienceTemplateIds)) {
                $q->orWhereHas('workexperienceResponses', function ($subQ) use ($workexperienceTemplateIds) {
                    $subQ->whereIn('template_id', $workexperienceTemplateIds)
                        ->where('status', 'Submitted');
                });
            }
            if (!empty($skillstrainingTemplateIds)) {
                $q->orWhereHas('skillstrainingResponses', function ($subQ) use ($skillstrainingTemplateIds) {
                    $subQ->whereIn('template_id', $skillstrainingTemplateIds)
                        ->where('status', 'Submitted');
                });
            }
            if (!empty($lessonIds)) {
                $q->orWhereHas('lessonresponses', function ($subQ) use ($lessonIds) {
                    $subQ->whereIn('lesson_id', $lessonIds)
                        ->where('status', 'Submitted');
                });
            }
            if (!empty($companyIndustryIds)) {
                $q->orWhereHas('latestGameplan', function ($subQ) use ($companyIndustryIds) {
                    $subQ->whereHas('industries', function ($industryQ) use ($companyIndustryIds) {
                        $industryQ->whereIn('industry_categories.id', $companyIndustryIds);
                    });
                });
            }
            if (!empty($companyIndustryUnitIds)) {
                $q->orWhereHas('favorites', function ($subQ) use ($companyIndustryUnitIds) {
                    $subQ->where('favoriteable_type', 'App\\Industryunit')
                        ->whereIn('favoriteable_id', $companyIndustryUnitIds);
                });
            }
        });
        $students = $query->get();
        $studentIds = $students->pluck('id')->toArray();
        // Fetch last 5 activities for each student
        $activitiesByStudent = [];
        if (!empty($studentIds)) {
            $logs = ActivityLog::whereIn('causer_id', $studentIds)
                // ->where('log_name', 'timeline')
                ->where('causer_type', User::class)
                ->orderBy('created_at', 'desc')
                ->get()
                ->groupBy('causer_id');
            foreach ($students as $student) {
                $studentLogs = $logs->get($student->id, collect())->take(5)->map(function ($log) {
                    return [
                        'id' => $log->id,
                        'description' => $log->description,
                        'event' => $log->event,
                        'created_at' => $log->created_at,
                        'log_name' => $log->log_name,
                        'properties' => $log->properties,
                        'gameplan_id' => $log->properties['attributes']['data']['id'] ?? null,
                    ];
                });
                $activitiesByStudent[] = [
                    'student_id' => $student->id,
                    'student_name' => $student->name,
                    'activities' => $studentLogs,
                ];
            }
        }
        return response()->json(['timeline' => $activitiesByStudent]);
    }



    public function viewStudentGameplan($id, $gameplanId)
    {
        if (
            Auth::check() &&
            (
                Auth::user()->isStudent() ||
                Auth::user()->isParent() && UserAccessService::currentUserCanAccess($id) ||
                Auth::user()->isTeacher() && UserAccessService::currentUserCanAccess($id) ||
                Auth::user()->isEmployer() && UserAccessService::currentUserCanAccess($id)
            )
        ) {
            $breadcrumb = "gameplan";

            $bannerTitle = (Auth::user()->isParent() || Auth::user()->isTeacher() || Auth::user()->isEmployer())
                ? "Game plan"
                : "Your Game plan";

            $student = IndividualStudent::find($id) ?? Student::find($id);
            if (!$student) {
                abort(404, 'Student not found');
            }

            $name = $student->name;

            $gameplan = Gameplan::where('id', $gameplanId)
                ->where('user_id', $id)
                ->firstOrFail();

            $instituteDetail = Auth::user()->school?->detail;
            $instituteId = $instituteDetail?->school_id;
            $instituteType = $instituteDetail?->institute_type->value ?? null;

            $questions = GameplanQuestion::with('options')
                ->orderBy('sort_order')
                ->where(function ($query) use ($instituteType, $instituteId) {
                    $query->where('institute_type', $instituteType)
                        ->where(function ($subQuery) use ($instituteId) {
                            $subQuery->whereNull('institute_id')
                                ->orWhere('institute_id', $instituteId);
                        });
                })
                ->orWhereNull('institute_type')
                ->get();

            $currentPlan = Plan::where('user_id', $id)
                ->with(['tafes', 'colleges', 'universities', 'industries'])
                ->latest()
                ->first();

            return view('gameplans.student', compact(
                'gameplan',
                'questions',
                'currentPlan',
                'name',
                'breadcrumb',
                'bannerTitle',
                'id'
            ));
        }

        abort(403, 'You do not have permission to perform this action.');
    }
}
