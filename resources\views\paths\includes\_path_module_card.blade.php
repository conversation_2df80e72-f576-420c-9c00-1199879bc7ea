<div class="px-3">
    @php
        $disabled = $disabled ?? false;
        $completionPercentage = $module->compeletedpercent;
    @endphp
    @if (!$disabled)
        <a
            @if ($moduleType == 'workExperienceTemplates')
                href="/#/tasks/vwe/{{ $module->id }}"
            @elseif ($moduleType == 'lessons')
                href="/#/tasks/lessons/{{ $module->id }}"
            @elseif ($moduleType == 'skillsTrainingTemplates')
                href="/#/tasks/skillstraining/{{ $module->id }}"
            @else
                href="#"
            @endif
            style="text-decoration: none; color: inherit;"
        >
    @endif
        <div class="card mb-3 path-card-boarder path-module-card {{ $disabled ? 'diabled-path-card' : '' }}">
            <div class="row">
                <div class="col-md-5 pr-md-0">
                    <div class="rounded py-3 px-3 path-card-img-h">
                        <img
                            src="{{ $module->tileimage_fullpath }}" 
                            class="img-fluid rounded "
                            style="height: 100%; width: 100%; "
                        >
                    </div>
                </div>
                <div class="col-md-7 pl-md-0">
                    <div class="card-body d-flex flex-column h-75 my-3 px-3 px-md-0">
                        <p class="card-title">
                            {{ $module->title }}
                        </p>
                        <div class="mt-auto">
                            <div class="row d-flex align-items-end">
                                <span class="col-4 mr-3">
                                    @php
                                        $moduleCompletionStatus = $module->getModuleCompletionStatus($completionPercentage);
                                    @endphp
                                    <span class="badge rounded-pill text-bg-primary badge-module-status {{ $module->getModuleCompletionStatusClass($moduleCompletionStatus) }}">
                                        {{ ucwords(str_replace("_", " ", $moduleCompletionStatus)) }}
                                    </span>
                                </span>
                                @if (!empty($module->level))
                                    <span class="col small d-flex align-items-center">
                                        <svg width="23" height="20" viewBox="0 0 23 20" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                            <rect width="22.8842" height="19.5556" rx="5" fill="url(#pattern0_87_514)" />
                                            <defs>
                                                <pattern id="pattern0_87_514" patternContentUnits="objectBoundingBox" width="1" height="1">
                                                    <use xlink:href="#image0_87_514" transform="scale(0.0181818 0.0212766)" />
                                                </pattern>
                                                <image id="image0_87_514" width="55" height="47" preserveAspectRatio="none"
                                                    xlink:href="data:image/png;base64,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" />
                                            </defs>
                                        </svg>

                                        <span class="card-item-level-s"> {{ $module->level }}</span>
                                        
                                    </span>
                                @endif
                            </div>
                            
                            <div class="mt-3 pr-2">
                                <div
                                    class="progress mb-0"
                                    role="progressbar"
                                    aria-label="Completion"
                                    aria-valuenow="{{ $completionPercentage ?: 0 }}"
                                    aria-valuemin="0"
                                    aria-valuemax="100"
                                >
                                    <div
                                        class=" progress-bar-black"
                                        style="width: {{ $completionPercentage ?: 0 }}%"
                                    ></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @if (!$disabled)
    </a>
    @endif
</div>