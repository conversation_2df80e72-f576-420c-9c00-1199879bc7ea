/*! For license information please see 196.js.LICENSE.txt */
"use strict";(self.webpackChunk=self.webpackChunk||[]).push([[196],{34986:(e,t,n)=>{n.d(t,{Z:()=>a});var o=n(1519),r=n.n(o)()((function(e){return e[1]}));r.push([e.id,".wrap{max-width:55ch;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.banner_detail_box{left:20%;position:absolute;top:50%;transform:translate(-50%,-50%)}.response-upload-input>input{width:104px}.response-upload-input>label{background-color:#fff;border:1px solid #e4e6ef;border-left:none;color:#5e6278;flex-grow:1;font-size:1.1rem;font-weight:500;line-height:1.5;overflow:hidden;padding:.775rem 1rem;text-overflow:ellipsis;transition:border-color .15s ease-in-out,box-shadow .15s ease-in-out;white-space:nowrap}.response-upload-input>input:focus+label{border-color:#b5b5c3}.btn-white-custom{background:#fff;color:#000}.btn-white-custom:hover,.btn.btn-white-custom:hover:not(.btn-active){background-color:#000!important;color:#fff!important}.btn-white-custom:disabled{background-color:#fff;opacity:1}.pointer{cursor:pointer}.overlay{overflow:overlay}.related{right:5%!important}div#kt_app_content{padding-bottom:0;padding-top:0}.btn-white{border:1px solid #000!important}.btn-white:hover,.btn.btn-white:hover:not(.btn-active){background-color:#000!important;color:#fff!important}::-webkit-scrollbar-thumb,::-webkit-scrollbar-thumb:hover{background:#000!important}.module-sections{margin-left:-30px;margin-right:-30px;overflow:auto hidden}.app-content{padding:0}.full-page{margin-left:-20px;margin-right:-20px}.banner{background-color:#000;background-position:50%;background-repeat:no-repeat;background-size:cover;display:block;min-height:calc(45.25vw - 149px);overflow:hidden;position:relative}.full-view-banner{margin-left:-30px;margin-right:-30px}.banner-video{height:100%}.banner-video>video{left:50%;position:absolute;top:50%;transform:translate(-50%,-50%);width:101%!important}.page-content{padding:0 15px;position:absolute;top:40%;width:100%}.module-section{border-bottom:1px solid;border-left:1px solid;border-top:1px solid;cursor:pointer;height:100px}.module-sections>.text-center:last-of-type>.module-section{border-right:1px solid}@media (max-width:1280px){.banner{height:56.25vw}.banner_detail_box{left:40%}.banner-video>video{height:100%!important;width:calc(65vw + 65vh)!important}}@media (max-width:991px){.full-view-banner,.module-sections{margin-left:-20px;margin-right:-20px}.full-view-banner{margin-top:58.16px}.module-section{height:100px}}@media (max-width:991px) and (min-width:768px) and (orientation:portrait){.banner{height:86.25vw}.banner-video>video{height:100%!important;width:calc(66vw + 66vh)!important}}@media (max-width:991px) and (orientation:landscape){.banner-video>video{height:auto!important;width:calc(70vw + 70vh)!important}}@media (max-width:767px){.full-page{margin-left:0;margin-right:0}.banner{height:calc(100vh - 300px)}.banner_detail_box{left:50%}}@media (max-width:575px){div#kt_app_content{padding-top:30px}.banner_detail_box{width:70vw!important}.full-view-banner{margin-top:0}.banner-video>video{height:100%!important;width:calc(90vw + 90vh)!important}}",""]);const a=r},53196:(e,t,n)=>{n.r(t),n.d(t,{default:()=>X});var o=n(70821),r=["innerHTML"],a=(0,o.createElementVNode)("div",{style:{position:"absolute",width:"100%",height:"100%",opacity:".3",background:"#000"}},null,-1),i={class:"banner_detail_box w-450px"},l={key:0,class:"mt-4 mb-4"},s={class:"row g-3"},c={class:"d-flex align-items-center bg-light border border-secondary rounded shadow-sm p-3"},d=["src","alt"],u={class:"mb-1 fw-bold text-dark"},p=(0,o.createElementVNode)("h1",{class:"fw-normal text-light"},"Final Step",-1),m={class:"display-4 fw-normal text-light"},h={key:0},f={key:1,class:"row mt-5"},v={class:"col-12 fs-6 text-light d-flex response-upload-input"},g={for:"taskfiles"},w=["textContent"],b={class:"row mt-5"},y={class:"col-sm-12"},x=["disabled"],k={class:"svg-icon svg-icon-primary svg-icon-2x"},E={key:0,xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",width:"24px",height:"24px",viewBox:"0 0 24 24",version:"1.1"},L=[(0,o.createElementVNode)("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},[(0,o.createElementVNode)("mask",{fill:"white"},[(0,o.createElementVNode)("use",{"xlink:href":"#path-1"})]),(0,o.createElementVNode)("g"),(0,o.createElementVNode)("path",{d:"M15.6274517,4.55882251 L14.4693753,6.2959371 C13.9280401,5.51296885 13.0239252,5 12,5 C10.3431458,5 9,6.34314575 9,8 L9,10 L14,10 L17,10 L18,10 C19.1045695,10 20,10.8954305 20,12 L20,18 C20,19.1045695 19.1045695,20 18,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,12 C4,10.8954305 4.8954305,10 6,10 L7,10 L7,8 C7,5.23857625 9.23857625,3 12,3 C13.4280904,3 14.7163444,3.59871093 15.6274517,4.55882251 Z",fill:"#ffffff"})],-1)],N={key:1,xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",width:"24px",height:"24px",viewBox:"0 0 24 24",version:"1.1"},C=[(0,o.createElementVNode)("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},[(0,o.createElementVNode)("mask",{fill:"white"},[(0,o.createElementVNode)("use",{"xlink:href":"#path-1"})]),(0,o.createElementVNode)("g"),(0,o.createElementVNode)("path",{d:"M7,10 L7,8 C7,5.23857625 9.23857625,3 12,3 C14.7614237,3 17,5.23857625 17,8 L17,10 L18,10 C19.1045695,10 20,10.8954305 20,12 L20,18 C20,19.1045695 19.1045695,20 18,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,12 C4,10.8954305 4.8954305,10 6,10 L7,10 Z M12,5 C10.3431458,5 9,6.34314575 9,8 L9,10 L15,10 L15,8 C15,6.34314575 13.6568542,5 12,5 Z",fill:"#000000"})],-1)],V=["innerHTML"],_=["textContent"],B=["textContent"],S={class:"module-section d-flex flex-column justify-content-center align-items-center py-5"},j={class:"svg-icon svg-icon-primary svg-icon-2x"},O={key:0,xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",width:"24px",height:"24px",viewBox:"0 0 24 24",version:"1.1"},Z=[(0,o.createElementVNode)("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},[(0,o.createElementVNode)("mask",{fill:"white"},[(0,o.createElementVNode)("use",{"xlink:href":"#path-1"})]),(0,o.createElementVNode)("g"),(0,o.createElementVNode)("path",{d:"M15.6274517,4.55882251 L14.4693753,6.2959371 C13.9280401,5.51296885 13.0239252,5 12,5 C10.3431458,5 9,6.34314575 9,8 L9,10 L14,10 L17,10 L18,10 C19.1045695,10 20,10.8954305 20,12 L20,18 C20,19.1045695 19.1045695,20 18,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,12 C4,10.8954305 4.8954305,10 6,10 L7,10 L7,8 C7,5.23857625 9.23857625,3 12,3 C13.4280904,3 14.7163444,3.59871093 15.6274517,4.55882251 Z",fill:"#ffffff"})],-1)],F={key:1,xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",width:"24px",height:"24px",viewBox:"0 0 24 24",version:"1.1"},M=[(0,o.createElementVNode)("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},[(0,o.createElementVNode)("mask",{fill:"white"},[(0,o.createElementVNode)("use",{"xlink:href":"#path-1"})]),(0,o.createElementVNode)("g"),(0,o.createElementVNode)("path",{d:"M7,10 L7,8 C7,5.23857625 9.23857625,3 12,3 C14.7614237,3 17,5.23857625 17,8 L17,10 L18,10 C19.1045695,10 20,10.8954305 20,12 L20,18 C20,19.1045695 19.1045695,20 18,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,12 C4,10.8954305 4.8954305,10 6,10 L7,10 Z M12,5 C10.3431458,5 9,6.34314575 9,8 L9,10 L15,10 L15,8 C15,6.34314575 13.6568542,5 12,5 Z",fill:"#000000"})],-1)],T={key:0,class:"mt-4 mb-4"},z={class:"row g-3"},G={class:"d-flex align-items-center bg-light border border-secondary rounded shadow-sm p-3"},P=["src","alt"],R={class:"mb-1 fw-bold text-dark"};var D=n(70655),I=n(72961),U=n(80894),H=n(22201);function A(e){return A="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},A(e)}function Y(){Y=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,o=Object.defineProperty||function(e,t,n){e[t]=n.value},r="function"==typeof Symbol?Symbol:{},a=r.iterator||"@@iterator",i=r.asyncIterator||"@@asyncIterator",l=r.toStringTag||"@@toStringTag";function s(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(e){s=function(e,t,n){return e[t]=n}}function c(e,t,n,r){var a=t&&t.prototype instanceof p?t:p,i=Object.create(a.prototype),l=new N(r||[]);return o(i,"_invoke",{value:x(e,n,l)}),i}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=c;var u={};function p(){}function m(){}function h(){}var f={};s(f,a,(function(){return this}));var v=Object.getPrototypeOf,g=v&&v(v(C([])));g&&g!==t&&n.call(g,a)&&(f=g);var w=h.prototype=p.prototype=Object.create(f);function b(e){["next","throw","return"].forEach((function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function y(e,t){function r(o,a,i,l){var s=d(e[o],e,a);if("throw"!==s.type){var c=s.arg,u=c.value;return u&&"object"==A(u)&&n.call(u,"__await")?t.resolve(u.__await).then((function(e){r("next",e,i,l)}),(function(e){r("throw",e,i,l)})):t.resolve(u).then((function(e){c.value=e,i(c)}),(function(e){return r("throw",e,i,l)}))}l(s.arg)}var a;o(this,"_invoke",{value:function(e,n){function o(){return new t((function(t,o){r(e,n,t,o)}))}return a=a?a.then(o,o):o()}})}function x(e,t,n){var o="suspendedStart";return function(r,a){if("executing"===o)throw new Error("Generator is already running");if("completed"===o){if("throw"===r)throw a;return V()}for(n.method=r,n.arg=a;;){var i=n.delegate;if(i){var l=k(i,n);if(l){if(l===u)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===o)throw o="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o="executing";var s=d(e,t,n);if("normal"===s.type){if(o=n.done?"completed":"suspendedYield",s.arg===u)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o="completed",n.method="throw",n.arg=s.arg)}}}function k(e,t){var n=t.method,o=e.iterator[n];if(void 0===o)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,k(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),u;var r=d(o,e.iterator,t.arg);if("throw"===r.type)return t.method="throw",t.arg=r.arg,t.delegate=null,u;var a=r.arg;return a?a.done?(t[e.resultName]=a.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,u):a:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,u)}function E(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function L(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function N(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(E,this),this.reset(!0)}function C(e){if(e){var t=e[a];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,r=function t(){for(;++o<e.length;)if(n.call(e,o))return t.value=e[o],t.done=!1,t;return t.value=void 0,t.done=!0,t};return r.next=r}}return{next:V}}function V(){return{value:void 0,done:!0}}return m.prototype=h,o(w,"constructor",{value:h,configurable:!0}),o(h,"constructor",{value:m,configurable:!0}),m.displayName=s(h,l,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===m||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,h):(e.__proto__=h,s(e,l,"GeneratorFunction")),e.prototype=Object.create(w),e},e.awrap=function(e){return{__await:e}},b(y.prototype),s(y.prototype,i,(function(){return this})),e.AsyncIterator=y,e.async=function(t,n,o,r,a){void 0===a&&(a=Promise);var i=new y(c(t,n,o,r),a);return e.isGeneratorFunction(n)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},b(w),s(w,l,"Generator"),s(w,a,(function(){return this})),s(w,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var o in t)n.push(o);return n.reverse(),function e(){for(;n.length;){var o=n.pop();if(o in t)return e.value=o,e.done=!1,e}return e.done=!0,e}},e.values=C,N.prototype={constructor:N,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(L),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function o(n,o){return i.type="throw",i.arg=e,t.next=n,o&&(t.method="next",t.arg=void 0),!!o}for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r],i=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var l=n.call(a,"catchLoc"),s=n.call(a,"finallyLoc");if(l&&s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(e,t){for(var o=this.tryEntries.length-1;o>=0;--o){var r=this.tryEntries[o];if(r.tryLoc<=this.prev&&n.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var a=r;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=e,i.arg=t,a?(this.method="next",this.next=a.finallyLoc,u):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),u},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),L(n),u}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var r=o.arg;L(n)}return r}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:C(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),u}},e}const q=(0,o.defineComponent)({name:"lessons-final-step",components:{},setup:function(e){var t=(0,U.oR)(),n=(0,H.yj)(),r=(0,H.tv)(),a=t.getters.currentUser;(0,o.onMounted)((function(){v()}));var i=(0,o.ref)(),l=(0,o.ref)(),s=(0,o.ref)(),c=(0,o.ref)(),d=(0,o.ref)(),u=(0,o.ref)(),p=(0,o.ref)();l.value={id:1,background_imagepath:null,background_video:null,response:!1,steps:[]},s.value=0,c.value=n.params.id;var m=(0,o.ref)();var h=(0,o.ref)(),f=(0,o.ref)("");h.value={response:"",nofile:!l.value.response};var v=function(){I.Z.get("api/lessons",c.value).then((function(e){var t=e.data;if(t.steps.length)for(var n=0;n<t.steps.length;n++)t.steps[n].user_response||(s.value=t.steps[n].id);l.value=t})).catch((function(e){e.response}))};return{currentUser:a,lesson:l,toggleRelated:function(){p.value=!p.value},currentlesson:c,showRelatedLessonsList:p,latestStep:s,favouriteLesson:function(e){d.value={id:e},I.Z.post("api/lessons/"+e+"/fav",d.value).then((function(e){var t=e.data;l.value.favourite=t.favourite})).catch((function(e){e.response}))},getlatestStep:function(e){if(e.length)for(var t=0;t<e.length;t++)if(!e[t].user_response)return e[t].id},saveResponse:function(){var t;return(0,D.mG)(this,void 0,void 0,Y().mark((function n(){return Y().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(l.value.response&&i.value)try{h.value.response=i.value,h.value.nofile=!l.value.response,I.Z.upload("api/lessons/"+e.id+"/submit-task",h.value).then((function(e){var t=e.data;void 0!==t.error?f.value=t.error:(localStorage.setItem("showScormResult","1"),r.push({name:"task-lessons-view-response",params:{id:l.value.id}}).then((function(){})))})).catch((function(e){e.response}))}catch(e){console.error(e),null===(t=m.value)||void 0===t||t.reset(),i.value=null}else l.value.response&&!i.value?f.value="Please upload your task before submitting.":l.value.response||I.Z.upload("api/lessons/"+e.id+"/submit-task",h.value).then((function(e){var t=e.data;void 0!==t.error?f.value=t.error:(localStorage.setItem("showScormResult","1"),r.push({name:"task-lessons-view-response",params:{id:l.value.id}}).then((function(){})))})).catch((function(e){e.response}));case 1:case"end":return n.stop()}}),n)})))},onFileChanged:function(e){var t=e.target;t&&t.files&&(i.value=t.files[0],u.value=t.files[0].name)},responseError:f,fileName:u}},props:["id"]});var J=n(93379),K=n.n(J),Q=n(34986),W={insert:"head",singleton:!1};K()(Q.Z,W);Q.Z.locals;const X=(0,n(83744).Z)(q,[["render",function(e,t,n,D,I,U){var H,A,Y;return(0,o.openBlock)(),(0,o.createElementBlock)(o.Fragment,null,[(0,o.createElementVNode)("div",{class:"full-view-banner banner",style:(0,o.normalizeStyle)({backgroundImage:"url("+e.lesson.background_imagepath+")"})},[e.lesson.background_video?((0,o.openBlock)(),(0,o.createElementBlock)("div",{key:0,class:"banner-video",innerHTML:e.lesson.background_video},null,8,r)):(0,o.createCommentVNode)("",!0),a,(0,o.createElementVNode)("div",i,[(null===(H=e.lesson.badges)||void 0===H?void 0:H.length)&&!e.lesson.feedback&&100!==e.lesson.compeletedpercent?((0,o.openBlock)(),(0,o.createElementBlock)("div",l,[(0,o.createElementVNode)("div",s,[((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(e.lesson.badges,(function(e){return(0,o.openBlock)(),(0,o.createElementBlock)("div",{key:e.id,class:"col-6"},[(0,o.createElementVNode)("div",c,[(0,o.createElementVNode)("img",{src:e.image_fullpath,alt:e.name,class:"me-3",width:"60",height:"60"},null,8,d),(0,o.createElementVNode)("div",null,[(0,o.createElementVNode)("p",u,(0,o.toDisplayString)(e.name),1)])])])})),128))])])):(0,o.createCommentVNode)("",!0),p,(0,o.createElementVNode)("h1",m,[e.lesson.response?((0,o.openBlock)(),(0,o.createElementBlock)("span",h,"Upload & ")):(0,o.createCommentVNode)("",!0),(0,o.createTextVNode)("Submit")]),e.lesson.response?((0,o.openBlock)(),(0,o.createElementBlock)("div",f,[(0,o.createElementVNode)("div",v,[(0,o.createElementVNode)("input",{type:"file",id:"taskfiles",class:"form-control rounded-0",onChange:t[0]||(t[0]=function(t){return e.onFileChanged(t)})},null,32),(0,o.createElementVNode)("label",g,(0,o.toDisplayString)(null!==(A=e.fileName)&&void 0!==A?A:"Upload your task"),1)]),e.responseError.length?((0,o.openBlock)(),(0,o.createElementBlock)("p",{key:0,textContent:(0,o.toDisplayString)(e.responseError),class:"form-error mt-2 ms-2"},null,8,w)):(0,o.createCommentVNode)("",!0)])):(0,o.createCommentVNode)("",!0),(0,o.createElementVNode)("div",b,[(0,o.createElementVNode)("div",y,[(0,o.createElementVNode)("button",{disabled:!e.currentUser.isStudent,onClick:t[1]||(t[1]=function(t){return e.saveResponse()}),class:"btn btn-white-custom btn-lg rounded-0 w-100 p-md-5"}," Submit Task ",8,x)])])])],4),(0,o.createElementVNode)("div",{class:(0,o.normalizeClass)([{row:e.lesson.steps.length<6},"d-flex module-sections"])},[((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(e.lesson.steps,(function(t){return(0,o.openBlock)(),(0,o.createElementBlock)("div",{key:t.id,class:(0,o.normalizeClass)([[e.lesson.steps.length<6?"col":"col-6 col-sm-4 col-md-2",t.user_response?"bg-black":""],"text-center p-0"])},[(0,o.createElementVNode)("div",{class:(0,o.normalizeClass)(["module-section d-flex flex-column justify-content-center align-items-center py-5",{"bg-white":!t.user_response}])},[(0,o.createElementVNode)("span",k,[t.user_response?((0,o.openBlock)(),(0,o.createElementBlock)("svg",E,L)):((0,o.openBlock)(),(0,o.createElementBlock)("svg",N,C))]),(0,o.createElementVNode)("p",{class:(0,o.normalizeClass)(["m-0 px-5",{"text-white":t.user_response}]),innerHTML:t.title},null,10,V),(0,o.createElementVNode)("p",{class:(0,o.normalizeClass)(["m-0",{"text-white":t.user_response}])},[t.estimated_time&&t.estimated_time.hours?((0,o.openBlock)(),(0,o.createElementBlock)("span",{key:0,textContent:(0,o.toDisplayString)(t.estimated_time.hours+"h ")},null,8,_)):(0,o.createCommentVNode)("",!0),t.estimated_time&&t.estimated_time.minutes?((0,o.openBlock)(),(0,o.createElementBlock)("span",{key:1,textContent:(0,o.toDisplayString)(t.estimated_time.minutes+"m")},null,8,B)):(0,o.createCommentVNode)("",!0)],2)],2)],2)})),128)),(0,o.createElementVNode)("div",{class:(0,o.normalizeClass)(["text-center p-0",[e.lesson.steps.length<6?"col":"col-6 col-sm-4 col-md-2",e.lesson.user_response?"bg-black":""]])},[(0,o.createElementVNode)("div",S,[(0,o.createElementVNode)("span",j,[e.lesson.user_response?((0,o.openBlock)(),(0,o.createElementBlock)("svg",O,Z)):((0,o.openBlock)(),(0,o.createElementBlock)("svg",F,M))]),(0,o.createElementVNode)("p",{class:(0,o.normalizeClass)(["m-0",{"text-white":e.lesson.user_response}])},"Submit",2)])],2),(null===(Y=e.lesson.badges)||void 0===Y?void 0:Y.length)&&e.lesson.feedback&&100==e.lesson.compeletedpercent?((0,o.openBlock)(),(0,o.createElementBlock)("div",T,[(0,o.createElementVNode)("div",z,[((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(e.lesson.badges,(function(e){return(0,o.openBlock)(),(0,o.createElementBlock)("div",{key:e.id,class:"col-6"},[(0,o.createElementVNode)("div",G,[(0,o.createElementVNode)("img",{src:e.image_fullpath,alt:e.name,class:"me-3",width:"60",height:"60"},null,8,P),(0,o.createElementVNode)("div",null,[(0,o.createElementVNode)("p",R,(0,o.toDisplayString)(e.name),1)])])])})),128))])])):(0,o.createCommentVNode)("",!0)],2)],64)}]])}}]);