<div class="modal fade" id="addAnzscoModal" tabindex="-1" role="dialog" aria-labelledby="addAnzscoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addAnzscoModalLabel">Add</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body mt-4">
                <form id="addAnzscoForm" enctype="multipart/form-data">
                    @csrf
                    <div class="form-group">
                        <label for="anzsco_title">ANZSCO Title <strong class="text-danger">*</strong></label>
                        <input type="text" name="anzsco_title" id="anzsco_title" class="form-control" value="{{ old('anzsco_title') }}" required maxlength="255" style="border-radius: 7px">
                        <span class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label for="occupation_type">Occupation Type <strong class="text-danger">*</strong></label>
                        <input type="text" name="occupation_type" id="occupation_type" class="form-control" value="{{ old('occupation_type') }}" required maxlength="255" style="border-radius: 7px">
                        <span class="text-danger"></span>
                       
                    </div>
                    <div class="form-group">
                        <label for="anzsco_code">ANZSCO Code <strong class="text-danger">*</strong></label>
                        <input type="text" name="anzsco_code" id="anzsco_code" class="form-control" value="{{ old('anzsco_code') }}"  maxlength="255" style="border-radius: 7px" required>
                        <span class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label for="media_type">Media Type</label>
                        <select id="media_type" class="form-control" style="border-radius: 7px;">
                            <option value="file" selected>Upload File</option>
                            <option value="url">Wistia URL or Embed Code</option>
                        </select>
                    </div>
                    
                    <div id="media_file_input" class="form-group">
                        <label for="media">Upload Media</label>
                        <input type="file" name="media" id="media" class="form-control p-1" accept=".jpg,.jpeg,.png,.gif,.mp4,.mov,.avi" style="border-radius: 7px;">
                    </div>
                    
                    <div id="media_url_input" class="form-group" style="display: none;">
                        <label for="media_text">Paste Wistia URL or Embed Code</label>
                        <textarea name="media_text" id="media_text" class="form-control p-1" style="border-radius: 7px;"></textarea>
                    </div>                    
                    <div class="form-group">
                        <label for="sub_profile_code">Sub Profile Code (Optional)</label>
                        <input type="text" name="sub_profile_code" id="sub_profile_code" class="form-control" value="{{ old('sub_profile_code') }}" maxlength="255" style="border-radius: 7px">
                        <span class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label for="anzsco_description">Description</label>
                        <textarea name="anzsco_description" id="anzsco_description" class="form-control"  style="border-radius: 7px">{{ old('anzsco_description') }}</textarea>
                        <span class="text-danger"></span>
                   
                    </div>
                    <div class="d-flex justify-content-end mt-4">
                        <button type="submit" class="btn btn-primary mr-2">Save</button>
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    $('#media_type').on('change', function() {
        if ($(this).val() === 'file') {
            $('#media_file_input').show();
            $('#media_url_input').hide();
        } else {
            $('#media_file_input').hide();
            $('#media_url_input').show();
        }
    });
});
</script>
@endpush
