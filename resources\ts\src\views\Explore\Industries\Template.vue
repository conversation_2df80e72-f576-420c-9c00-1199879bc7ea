<template>
    <div class="full-page-banner banner" v-bind:style="{ 'backgroundImage': 'url(' + template.banner_fullpath + ')' }">
        <div v-if="template.video" ref="videoContainer" :class="{ 'wistia-overlay': !wistiaPlayed }" class="banner-video template-video" v-html="template.video[0].vimeopath"> </div>
        <div v-else-if="template.banner_video" class="banner-video" v-html="template.banner_video"></div>
        <div v-show="showBannerDetail" :style="{ opacity: wistiaPlayed ? '0' : '1.0' }" class="banner_detail_box w-450px">
            <!-- <h1 class="fw-normal text-light text-capitalize" v-html="template.type"></h1> -->
            <h1 class="display-4 fw-normal mb-4 text-light" v-html="template.title"></h1>
            <div class="row text-light">
                <div class="col-sm-4 d-flex align-items-center" :class="{ 'col-md-6': template.type === 'phoneafriend', 'col-md-4': template.type !== 'phoneafriend' }" v-if="template.type">
                    <i :class="['content-type-icon', 'text-light', 'las', template.icon_class]"></i>
                    <!-- <i class="fa-regular fa-clock text-dark me-2"></i> -->
                    <span class="ps-1 text-capitalize" v-text="template.template.name"></span>
                </div>
                <div class="col-sm-4 d-flex align-items-center" :class="{ 'col-md-6': template.type === 'phoneafriend', 'col-md-4': template.type !== 'phoneafriend' }" v-if="template.estimated_time && (template.estimated_time.hours || template.estimated_time.minutes)">
                    <i class="fa-regular fa-clock text-white me-2 align-middle"></i>
                    <span v-if="template.estimated_time && template.estimated_time.hours" v-text="template.estimated_time.hours + 'h '"></span>
                    <span class="ms-2" v-if="template.estimated_time && template.estimated_time.minutes" v-text="template.estimated_time.minutes + 'm'"></span>
                </div>
            </div>
            <!-- <div class="row mt-5" v-if="template.foreground_video">
                <div class="col-8 col-sm-6 col-md-12">
                    <button type="button" class="btn btn-black-custom btn-lg rounded-0 w-100 p-md-5" data-bs-toggle="modal" data-bs-target="#kt_modal_courses">
                        Watch Trailer
                    </button>
                </div>
            </div> -->
            <div class="row mt-5" v-if="template.phoneafriend">
                <div class="col-8 col-sm-6 col-md-12">
                    <button class="btn btn-white-custom text-black btn-lg border-1 rounded-0 w-100 p-md-5" @click="toggleAudio">
                        <span>Listen</span>
                    </button>
                </div>
            </div>
            <div class="row mt-5" v-if="template.virtualtour">
                <div class="col-8 col-sm-6 col-md-12">
                    <button class="btn btn-white-custom text-black btn-lg border-1 rounded-0 w-100 p-md-5" @click="watchTour">
                        <span>Watch</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div :class="{ 'sticky-bottom': template.type != 'video' }">
        <div class="row black-strip bg-black">
            <div class="col-md-8 p-10">
                <router-link class="m-0 text-white text-uppercase" :to="{ name: 'explore-industry-detail', params: { id: template.industry.id } }">
                    <span class="svg-icon svg-icon-primary svg-icon-2x"> <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                            <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                <polygon points="0 0 24 0 24 24 0 24" />
                                <path d="M6.70710678,15.7071068 C6.31658249,16.0976311 5.68341751,16.0976311 5.29289322,15.7071068 C4.90236893,15.3165825 4.90236893,14.6834175 5.29289322,14.2928932 L11.2928932,8.29289322 C11.6714722,7.91431428 12.2810586,7.90106866 12.6757246,8.26284586 L18.6757246,13.7628459 C19.0828436,14.1360383 19.1103465,14.7686056 18.7371541,15.1757246 C18.3639617,15.5828436 17.7313944,15.6103465 17.3242754,15.2371541 L12.0300757,10.3841378 L6.70710678,15.7071068 Z" fill="#ffffff" fill-rule="nonzero" transform="translate(12.000003, 11.999999) scale(-1, 1) rotate(-270.000000) translate(-12.000003, -11.999999) " />
                            </g>
                        </svg> </span>
                    Back to {{ template.industry.name }}
                </router-link>
            </div>
            <div v-if="template.type" class="col-md-4 text-end p-10 d-flex align-items-center justify-content-end">
                <div class="text-white me-5" style="text-transform: none;" v-if="(template.courses && template.courses.length > 0) || (template.jobsmap && template.jobsmap.length > 0)">See related</div>

                <div class="cursor-pointer svg-icon svg-icon-primary svg-icon-2x me-5" data-bs-toggle="modal" data-bs-target="#kt_modal_vwe" v-if="(currentUser.hasWhsAccess && template.workexperienceTemplates && template.workexperienceTemplates.length > 0) || (currentUser.hasSkillsTrainingAccess && template.skillstrainingTemplates && template.skillstrainingTemplates.length > 0)">
                    <!-- <img src="/media/industry/templateicons/course.png" width="24">  -->
                    <span class="text-white text-uppercase">Tasks</span>
                </div>

                <!-- <div class="cursor-pointer svg-icon svg-icon-primary svg-icon-2x me-5" data-bs-toggle="modal" data-bs-target="#kt_modal_job" @click="modalOpenJobs" v-if="currentUser.hasJobFinderAccess">
                    <span class="text-white text-uppercase">Jobs</span>
                </div> -->

                <div class="cursor-pointer svg-icon svg-icon-primary svg-icon-2x me-5" data-bs-toggle="modal" data-bs-target="#kt_modal_course" @click="modalOpenCourses" v-if="currentUser.hasCourseFinderAccess && template.courses">
                    <!-- <img src="/media/industry/templateicons/course.png" width="24">  -->
                    <span class="text-white text-uppercase">Courses</span>
                </div>

                <div v-if="template.phoneafriend" @click="toggleAudio" class="cursor-pointer svg-icon svg-icon-primary svg-icon-2x float-end me-5"> <i class="fa-solid fa-headphones text-white fs-1"></i>
                </div>

                <div class="cursor-pointer svg-icon svg-icon-primary svg-icon-2x" @click="favouriteTemplate(template.id)"> <i id="templateHeart" :class="'fa-' + (template.favUnits.includes(template.id) ? 'solid' : 'regular') + ' fa-heart text-white fs-1'"></i>
                </div>
            </div>

            <div class="row" id="audio" v-bind:style="{ 'height': audioHeight + 'px' }" v-if="template.type === 'phoneafriend'">
                <div class="col-12 text-center" v-html="template.phoneafriend.filepath">

                </div>
            </div>
        </div>
    </div>
    <!-- <div class="modal fade" id="kt_modal_courses" tabindex="-1" style="display: none" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered mw-900px">
            <div class="modal-content rounded-0">
                <div class="modal-body p-1" v-html="template.description"></div>
            </div>
        </div>
    </div> -->
    <template v-if="template.type">
        <CoursesPopup :templateId="template.id" />
        <JobsPopup :template="template" :where="currentUser.address" />
        <TasksPopup :template="template" />
    </template>
    <div id="virtualTour" v-if="template.type === 'virtualtour'" v-html="template.virtualtour[0].tourlink"></div>
    <div class="section-content section-block">
        <div class="" v-html="template.description"></div>
        <Checklist :template="template" v-if="template.checklist" />
        <FactsFaqs :template="template" v-if="template.factandfaqImages" />
        <ScrapBook :template="template" v-if="template.scrapbook" />
    </div>
    <CareerTimeline :template="template" v-if="template.careertimeline" />
    <JobsMap :template="template" v-if="template.jobsmap" />
    <!-- <div class="gallery">
            <PodcastGallery :slides="template.relatedUnits" />
    </div> -->
    <div class="industries" v-if="template.relatedUnits">
        <RealatedTemplatesCarousel :slides="template.relatedUnits" :favUnits="template.favUnits" />
    </div>
</template>
<script lang="ts">
import { defineComponent, ref, onMounted, onUnmounted } from "vue";
import RealatedTemplatesCarousel from "@/components/industries/extra/RealatedTemplatesCarousel.vue";
import CoursesPopup from "@/components/industries/extra/CoursesPopup.vue";
import JobsPopup from "@/components/industries/extra/JobsPopup.vue";
import TasksPopup from "@/components/industries/extra/TasksPopup.vue";
import Checklist from "@/components/industries/extra/Checklist.vue";
import FactsFaqs from "@/components/industries/extra/FactsFaqs.vue";
import JobsMap from "@/components/industries/extra/JobsMap.vue";
import ScrapBook from "@/components/industries/extra/ScrapBook.vue";
import CareerTimeline from "@/components/industries/extra/CareerTimeline.vue";
import PodcastGallery from "@/components/dashboards/extra/PodcastGallery.vue";
import { Mutations } from "@/store/enums/StoreEnums";
import ApiService from "@/core/services/ApiService";
import { useStore } from "vuex";
import { useRouter, useRoute } from "vue-router";
import Swal from "sweetalert2/dist/sweetalert2.min.js";
import iframeResize from 'iframe-resizer/js/iframeResizer';
import axios from "axios";


declare global {
    interface Window {
        _wq: any; // Replace 'any' with the appropriate type if possible
    }
}

export default defineComponent({
    name: "industry-template",
    components: {
        RealatedTemplatesCarousel,
        CoursesPopup,
        JobsPopup,
        Checklist,
        CareerTimeline,
        PodcastGallery,
        FactsFaqs,
        JobsMap,
        TasksPopup,
        ScrapBook
    },
    setup() {
        const store = useStore();
        const route = useRoute();
        const currentUser = store.getters.currentUser;
        let timeoutId: any;
        onMounted(async () => {
            await fetchTemplateDetail();
            iframeResize({
                heightCalculationMethod: 'bodyScroll'
            }, '.section-content iframe')

            window._wq = window._wq || [];
            window._wq.push({
                id: "_all", onReady: function (video) {
                    video.bind('play', () => onWistiaPlay(video));
                    video.bind('pause', () => onWistiaPause(video));

                }
            });
        });

        onUnmounted(() => {
            if (timeoutId) {
                clearTimeout(timeoutId);
            }
        });

        const template = ref();
        const currenttemplateindustry = ref();
        const currenttemplateunit = ref();
        const favTemplate = ref();
        template.value = {
            id: 1,
            background_imagepath: null,
            background_video: null,
            audio: [],
            industry: {
                id: 0,
                name: '',
            },
            favUnits: [],
            relatedUnits: {},
            // phoneafriend: null,
            // galleryImages: null,
            // studentChecklistData: null,
            // careertimeline: null,
            // factandfaqImages: null,
            // factandfaq: null,
        };
        currenttemplateindustry.value = route.params.id;
        currenttemplateunit.value = route.params.unit;

        const fetchTemplateDetail = async () => {
            try {
                const { data } = await ApiService.get(`api/explore/${currenttemplateindustry.value}/template/${currenttemplateunit.value}`);
                template.value = data;

                if (!template.value.user_template_progress || template.value.user_template_progress.status !== "completed") {
                    setTimeout(() => saveProgress('completed'), 10000);
                }

                if (template.value.type == 'phoneafriend') {
                    const tempElement = document.createElement("div");
                    tempElement.innerHTML = data.phoneafriend.filepath; // Assuming this contains your embedded code


                    const scriptTags = tempElement.querySelectorAll("script");
                    scriptTags.forEach((scriptTag) => {
                        const newScriptElement = document.createElement("script");
                        newScriptElement.src = scriptTag.src;
                        newScriptElement.defer = scriptTag.defer;
                        document.head.appendChild(newScriptElement);
                    });
                } else if (template.value.type == 'jobsmap') {
                    data.jobsmap.forEach((job) => {
                        if (job.code) {
                            const tempElement = document.createElement("div");
                            tempElement.innerHTML = job.code;

                            const scriptTags = tempElement.querySelectorAll("script");

                            scriptTags.forEach((scriptTag) => {
                                const newScriptElement = document.createElement("script");
                                newScriptElement.src = scriptTag.src;
                                newScriptElement.async = scriptTag.async;
                                newScriptElement.charset = scriptTag.charset;
                                newScriptElement.defer = scriptTag.defer;

                                document.head.appendChild(newScriptElement);
                            });
                        }
                    });
                } else if (template.value.type == 'checklist') {
                    try {
                        // Loop through checklist items and set the 'checked' property
                        if (template.value.studentChecklistData.length > 0) {
                            template.value.studentChecklistData.forEach((list) => {
                                const checkboxId = 'checklist' + list;
                                setTimeout(() => {
                                    const checkbox = document.getElementById(checkboxId) as HTMLInputElement;
                                    if (checkbox) {
                                        checkbox.checked = true; // Set the checkbox as checked
                                    }
                                }, 10);
                            });
                        }
                    } catch (error) {
                        console.error('Error unserializing data:', error);
                    }
                } else if (template.value.type == 'video' || template.value.type == 'virtualtour') {
                    // Create a script element
                    const vimeoScript = document.createElement('script');
                    // Set the src attribute to your external script URL
                    vimeoScript.src = 'https://player.vimeo.com/api/player.js';
                    // Set the type attribute to 'text/javascript'
                    vimeoScript.type = 'text/javascript';
                    // Append the script element to the document's head
                    document.head.appendChild(vimeoScript);
                }
                var breadcrumbs = store.getters.getBreadcrumbs;
                breadcrumbs[2] = data.industry.name;
                breadcrumbs[3] = data.title;
                store.commit(Mutations.SET_BREADCRUMB_MUTATION, breadcrumbs);

            } catch (error) {
                console.log(error);
            };
        };
        const wistiaPlayed = ref(false);
        // Function to run when the video is played
        function onWistiaPlay(video) {
            wistiaPlayed.value = true;
        }

        // Function to run when the video is paused
        function onWistiaPause(video) {
            wistiaPlayed.value = false;
        }

        const favouriteTemplate = (id) => {
            const icon = document.getElementById('templateHeart') as HTMLInputElement;
            if (icon.classList.contains('fa-regular')) {
                icon.classList.add('fa-solid');
                icon.classList.remove('fa-regular');
            } else if (icon.classList.contains('fa-solid')) {
                icon.classList.add('fa-regular');
                icon.classList.remove('fa-solid');
            }
            ApiService.post(`api/units/` + id + `/fav`, id)
                .then(({
                    data
                }) => {

                })
                .catch(({
                    response
                }) => { });
        };

        const showAudio = ref(false);
        const showBannerDetail = ref(true);
        const audioHeight = ref(0);
        const toggleAudio = () => {
            const audio = document.getElementById('audio') as HTMLInputElement;
            if (showAudio.value) {
                audioHeight.value = 0;
                audio.style.margin = '0';
            } else {
                audio.classList.remove('d-none');
                audioHeight.value = audio.scrollHeight;
                audio.style.margin = '0px 0px 20px 0px'
            }
            showAudio.value = !showAudio.value
        }

        const modalOpenCourses = () => {
            setTimeout(() => {
                const search = document.getElementById('searchCourse') as HTMLElement;
                search.click();
            }, 40);
        }

        const modalOpenJobs = () => {
            setTimeout(() => {
                const search = document.getElementById('searchJob') as HTMLElement;
                search.click();
            }, 40);
        }


        const watchTour = () => {
            const element = document.getElementsByClassName('sticky-bottom')[0];

            if (element) {
                // Scroll to the element smoothly
                element.scrollIntoView({ behavior: 'smooth' });
            }
        }

        const saveProgress = async (status) => {
            try {
                await axios.post('/storeTemplateProgress', {
                    template_id: route.params.unit,
                    status: status,
                });
            } catch (error) {
                console.error('Error saving progress:', error);
            }
        }

        return {
            currentUser,
            template,
            currenttemplateindustry,
            currenttemplateunit,
            favouriteTemplate,
            toggleAudio,
            watchTour,
            audioHeight,
            showBannerDetail,
            wistiaPlayed,
            modalOpenCourses,
            modalOpenJobs,
        };
    },
    props: ["id"],
});
</script>

<style>
#audio {
    overflow: hidden;
    transition: height 0.5s ease;
}

/* Worksheet Css Starts */
.new-cards li {
    padding: 0 15px;
    width: 220px;
}

.profile-cards {
    margin: 0 -5px;
}

.list-inline {
    padding-left: 0;
    list-style: none;
}

.new-cards li,
.profile-cards li,
.template-tiles li {
    vertical-align: top;
}

.profile-cards li {
    width: 200px;
    display: inline-table;
    margin-top: 10px;
}

.list-inline>li {
    /* display: inline-block; */
    /* padding-right: 5px; */
    /* padding-left: 5px; */
}

.hover-colored .percentage {
    overflow: hidden;
    position: relative;
}

.profile-cards .percentage {
    background-position: center;
    height: 190px;
    line-height: 190px;
    background-size: 100%;
    background-repeat: no-repeat;
    color: #fff;
    transition: all 0.2s ease;
}

.img-fluid {
    max-width: 100%;
}

.hover-colored .percentage> :not(.tile-label) {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
}

.blue-check {
    line-height: 26px;
    width: 25px;
    height: 25px;
}

.bg-blue {
    background-color: #0a0afd !important;
}

.blue-check {
    line-height: 26px !important;
    width: 25px;
    height: 25px;
}

.profile-cards .topic {
    font-weight: 700;
    margin: 10px 0;
    line-height: 15px;
}

.text-master {
    color: #000 !important;
}

/* Worksheet Css Ends */

.swal2-popup {
    border-radius: 0px;
}

.wrap {
    overflow: hidden;
    max-width: 75ch;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.banner_detail_box {
    position: absolute;
    top: 50%;
    left: 20%;
    transform: translate(-50%, -50%);
    transition: opacity 0.3s;
}

.modal-backdrop {
    opacity: 0.8 !important;
}

.sticky-bottom {
    z-index: auto;
}

.fa-heart:hover {
    font-weight: 900 !important;
}

.btn-black-custom:hover,
.btn-white-custom {
    background-color: #fff !important;
    color: #000 !important;
}

.btn-black-custom,
.btn-white-custom:hover {
    background-color: #000 !important;
    color: #fff !important;
}

.pointer {
    cursor: pointer;
}

.related-overlay {
    overflow: overlay;
    height: 0px;
    transition: height 0.3s;
}

.slide-up {
    height: calc(100vh - 320px) !important;
}

.related {
    right: 5% !important;
}

.related-tile-content>p:first-child {
    flex: 75%;
}

.banner {
    background-color: #000;
    /* background-image: url("/images/vwe/home-parallax.jpg"); */
    display: block;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    position: relative;
    overflow: hidden;
    min-height: calc(56.25vw - 149px);
    /*min-height: calc(56.25vw - 332px);*/
}

.banner-video {
    height: 100%;
}

.banner-video>video {
    /* height: 100%; */
    width: 101% !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.wistia-overlay {
    background: linear-gradient(90deg, rgb(0, 0, 0) 0%, rgb(181, 181, 181) 97%);
}

.wistia-overlay iframe {
    opacity: 0.6;
    transition: opacity 0.3s;
}

.full-page-banner,
.black-strip {
    margin-left: -30px;
    margin-right: -30px;
}

.bg-dark {
    background: #000 !important;
    margin-left: -30px;
    margin-right: -30px;
}

div#kt_app_content {
    padding-top: 0px;
    padding-bottom: 0px;
}

#virtualTour iframe {
    width: calc(100% + 60px);
    min-height: 45.5vw;
    margin: 0 -30px;
}

/***** Heart checkbox css starts ******/

.like-heart>i {
    font-size: 14px;
    padding: 8px;
    position: relative;
    margin: 0;
    cursor: pointer;
    background-color: #eee;
    color: #9d9d9d;
    display: block;
    border-radius: 50%;
    transition: all 0.4s;
    text-align: center;
}

.like-heart>i:hover,
.like-heart>i:focus,
.like-heart.liked>i {
    background-color: #000;
    color: #fff;
}

/***** Heart checkbox css starts ******/

.section-block {
    background-color: #fff;
    padding: 20px;
    width: 90%;
    margin: 20px auto;
}

.content-type-icon {
    font-size: 26px !important;
    vertical-align: middle !important;
}

@media (max-width: 1280px) {
    .banner {
        height: 56.25vw;
    }

    .banner_detail_box {
        left: 40%;
    }

    .banner-video>video {
        height: 100% !important;
        width: calc(65vw + 65vh) !important;
    }
}

@media (max-width: 991px) {

    .full-page-banner,
    .black-strip {
        margin-left: -20px;
        margin-right: -20px;
    }

    .full-page-banner {
        margin-top: 58.16px;
    }
}

@media (max-width: 991px) and (min-width: 768px) and (orientation: portrait) {
    .slide-up {
        height: calc(100vw - 220px) !important;
    }

    .banner {
        height: 86.25vw;
    }

    .banner-video>video {
        height: 100% !important;
        width: calc(66vw + 66vh) !important;
    }
}

@media (max-width: 991px) and (orientation: landscape) {
    .banner-video>video {
        height: auto !important;
        width: calc(70vw + 70vh) !important;
    }
}

@media (max-width: 767px) {
    /* .full-page-banner {
                                                                                                                                                                                                margin-left: -30px;
                                                                                                                                                                                                margin-right: -30px;
                                                                                                                                                                                            } */

    .banner {
        height: calc(100vh - 300px);
    }

    .banner_detail_box {
        left: 50%;
    }
}

@media (max-width: 575px) {
    div#kt_app_content {
        padding-top: 30px;
    }

    .banner_detail_box {
        width: 70vw !important;
    }

    .banner {
        height: calc(100vh - 242px);
    }

    .banner-video>video {
        height: 100% !important;
        width: calc(90vw + 90vh) !important;
    }

    .full-page-banner {
        margin-top: 0;
    }
}
</style>