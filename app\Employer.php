<?php

namespace App;

use App\Traits\HasIntercomUserInfo;
use App\Traits\UserSessionStatsTrait;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Cache;
use Overtrue\LaravelFavorite\Traits\Favoriter;

class Employer extends Authenticatable
{
    use Favoriter, UserSessionStatsTrait, HasIntercomUserInfo;

    protected $table = "users";
    protected $guarded = [];

    protected $hidden = [
        'password',
        'remember_token',
        'stripe_id',
        'card_brand',
        'card_last_four',
        'trial_ends_at',
    ];

    /**
     * The relationships that should always be loaded.
     *
     * @var array
     */
    protected $with = ['profile'];

    protected static function booted()
    {
        static::deleting(function ($employer) {
            if ($employer->profile) {
                $employer->profile->delete();
            }
        });

        static::addGlobalScope('employer', function (Builder $builder) {
            $builder->where('role_id', Role::whereName('Employer')->value('id'));
        });
    }

    public function profile()
    {
        return $this->hasOne(Profile::class, 'user_id');
    }

    public function role()
    {
        return $this->hasOne(Role::class);
    }

    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id');
    }

    public function state()
    {
        return $this->belongsTo(State::class);
    }

    /**
     * Students that this employer is following (only public profiles)
     */
    public function followedStudents()
    {
        return $this->belongsToMany(Student::class, 'user_follows', 'follow_by', 'following')
                    ->whereHas('profile', function ($query) {
                        $query->where('is_public', true);
                    })
                    ->withTimestamps();
    }

    /**
     * Check if this employer is following a specific student (only if public profile)
     */
    public function isFollowing($studentId)
    {
        return $this->followedStudents()
            ->where('users.id', $studentId)
            ->exists();
    }

    /**
     * Follow a student (only if their profile is public)
     */
    public function followStudent($studentId)
    {
        $student = Student::where('id', $studentId)
            ->whereHas('profile', function ($query) {
                $query->where('is_public', true);
            })->first();

        if ($student && !$this->isFollowing($studentId)) {
            $this->followedStudents()->attach($studentId);
            return true;
        }
        return false;
    }

    /**
     * Unfollow a student (only if their profile is public)
     */
    public function unfollowStudent($studentId)
    {
        if ($this->isFollowing($studentId)) {
            $this->followedStudents()->detach($studentId);
            return true;
        }
        return false;
    }

    /**
     * Toggle follow status for a student (only if their profile is public)
     */
    public function toggleFollowStudent($studentId)
    {
        if ($this->isFollowing($studentId)) {
            $this->unfollowStudent($studentId);
            return false; // Now unfollowing
        } else {
            return $this->followStudent($studentId); // Only follows if public
        }
    }

    /**
     * Get students in the employer's pipeline based on company criteria
     * This relationship includes students who match any of the following:
     * - Have submitted responses to company's modules (VWE, Skills Training, Lessons)
     * - Have gameplan industries matching company's industries
     * - Have favorited company's industry units
     */
    public function pipelineStudents()
    {
        $company = $this->company()->with([
            'industries',
            'industryunits',
            'workexperienceTemplates',
            'skillstrainingTemplates',
            'lessons'
        ])->first();

        if (!$company) {
            return Student::whereRaw('1 = 0'); // Return empty query if no company
        }

        // Get IDs for filtering
        $companyIndustryIds = $company->industries->pluck('id')->toArray();
        $companyIndustryUnitIds = $company->industryunits->pluck('id')->toArray();
        $workexperienceTemplateIds = $company->workexperienceTemplates->pluck('id')->toArray();
        $skillstrainingTemplateIds = $company->skillstrainingTemplates->pluck('id')->toArray();
        $lessonIds = $company->lessons->pluck('id')->toArray();

        $query = Student::query();

        // Apply request-based filters if available
        $search = request('search');
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'LIKE', "%{$search}%")
                    ->orWhere('email', 'LIKE', "%{$search}%");
            });
        }

        // Filter by state if requested
        $state = request('state');
        if ($state) {
            $query->whereHas('state', function ($q) use ($state) {
                $q->where('code', $state);
            });
        }

        // Filter by followed students if requested
        $isFollowing = request('is_following');
        if ($isFollowing) {
            $followedStudentIds = $this->followedStudents()->pluck('following')->toArray();
            $query->whereIn('id', $followedStudentIds);
        }

        // Add OR conditions for each criteria
        $query->where(function ($q) use ($workexperienceTemplateIds, $skillstrainingTemplateIds, $lessonIds, $companyIndustryIds, $companyIndustryUnitIds) {
            // Students who have submitted responses to the company's workexperienceTemplates
            if (!empty($workexperienceTemplateIds)) {
                $q->orWhereHas('workexperienceResponses', function ($subQ) use ($workexperienceTemplateIds) {
                    $subQ->whereIn('template_id', $workexperienceTemplateIds)
                        ->where('status', 'Submitted');
                });
            }

            // Students who have submitted responses to the company's skillstrainingTemplates
            if (!empty($skillstrainingTemplateIds)) {
                $q->orWhereHas('skillstrainingResponses', function ($subQ) use ($skillstrainingTemplateIds) {
                    $subQ->whereIn('template_id', $skillstrainingTemplateIds)
                        ->where('status', 'Submitted');
                });
            }

            // Students who have submitted responses to the company's lessons
            if (!empty($lessonIds)) {
                $q->orWhereHas('lessonresponses', function ($subQ) use ($lessonIds) {
                    $subQ->whereIn('lesson_id', $lessonIds)
                        ->where('status', 'Submitted');
                });
            }

            // Students whose gameplan industries match the company's industries
            if (!empty($companyIndustryIds)) {
                $q->orWhereHas('latestGameplan', function ($subQ) use ($companyIndustryIds) {
                    $subQ->whereHas('industries', function ($industryQ) use ($companyIndustryIds) {
                        $industryQ->whereIn('industry_categories.id', $companyIndustryIds);
                    });
                });
            }

            // Students who have favorited the same industryunits as the company
            if (!empty($companyIndustryUnitIds)) {
                $q->orWhereHas('favorites', function ($subQ) use ($companyIndustryUnitIds) {
                    $subQ->where('favoriteable_type', 'App\Industryunit')
                        ->whereIn('favoriteable_id', $companyIndustryUnitIds);
                });
            }
        });

        // Apply additional filters based on request parameters
        $completedModule = request('completed_module');
        $engagedContent = request('engaged_content');
        $industriesInGameplan = request('industries_in_gameplan');
        $companiesInGameplan = request('companies_in_gameplan');

        $query->where(function ($q) use ($completedModule, $engagedContent, $industriesInGameplan, $companiesInGameplan, $companyIndustryIds, $company) {
            $q->when($completedModule, function ($query) {
                $query->where(function ($subQuery) {
                    $subQuery->whereHas('workexperienceResponses', function ($q) {
                        $q->where('status', 'Submitted');
                    })->orWhereHas('skillstrainingResponses', function ($q) {
                        $q->where('status', 'Submitted');
                    })->orWhereHas('lessonresponses', function ($q) {
                        $q->where('status', 'Submitted');
                    });
                });
            })->when($engagedContent, function ($query) {
                $query->orWhereHas('favorites', function ($subQ) {
                    $subQ->where('favoriteable_type', 'App\Industryunit');
                });
            })->when($industriesInGameplan, function ($query) use ($companyIndustryIds) {
                $query->orWhereHas('latestGameplan', function ($subQ) use ($companyIndustryIds) {
                    $subQ->whereHas('industries', function ($industryQ) use ($companyIndustryIds) {
                        $industryQ->whereIn('industry_categories.id', $companyIndustryIds);
                    });
                });
            })->when($companiesInGameplan, function ($query) use ($company) {
                $query->orWhereHas('latestGameplan', function ($subQ) use ($company) {
                    $subQ->whereHas('companies', function ($companyQ) use ($company) {
                        $companyQ->where('company', 'LIKE', "%{$company->detail->name}%");
                    });
                });
            });
        });

        return $query;
    }
}
