<?php
/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
 */

use App\Employer;
use App\Http\Controllers\AnzscoOccupationController;
use App\Http\Controllers\Auth\RegisterationController;
use App\Http\Controllers\BannersController;
use App\Http\Controllers\BadgesController;
use App\Http\Controllers\CompaniesController;
use App\Http\Controllers\EmployersController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\ExperienceCategoriesController;
use App\Http\Controllers\GameplanQuestionController;
use App\Http\Controllers\PrimarySchoolsController;
use App\Http\Controllers\ProfilingPostController;
use App\Http\Controllers\SpecialistTaskController;
use App\Http\Controllers\UserProfilingController;
use App\Http\Controllers\Vue\StudentsController;
use App\Http\Controllers\Vue\PlansController;
use App\Http\Middleware\CourseFinderAccess;
use App\Http\Middleware\EMagazineAccess;
use App\Http\Middleware\EPortfolioAccess;
use App\Http\Middleware\GamePlanAccess;
use App\Http\Middleware\IndustriesAccess;
use App\Http\Middleware\JobFinderAccess;
use App\Http\Middleware\LessonsAccess;
use App\Http\Middleware\NoticeboardAccess;
use App\Http\Middleware\ProfilingAccess;
use App\Http\Middleware\ResumeBuilderAccess;
use App\Http\Middleware\ScholarshipFinderAccess;
use App\Http\Middleware\SkillsTrainingAccess;
use App\Http\Middleware\SubjectSelectionsAccess;
use App\Http\Middleware\VweAccess;
use App\Http\Middleware\WhsAccess;
use Illuminate\Http\Request;
use App\Http\Controllers\Vue\GameplansController;
use App\Http\Middleware\CareerProfilingAccess;
use App\Lessonresponse;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Spatie\PdfToImage\Pdf;
use App\Http\Controllers\SchoolDetailsController;


require_once __DIR__ . '/auth.php';
require_once __DIR__ . '/intercom.php';
require_once __DIR__ . '/lms.php';

Route::middleware(['cache.headers:private,max-age=300;etag'])->group(function () {

    Route::get('/loaderio-8dfaef7f8d6e2c13ba407e884c79c518', "SystemtuneController@loader");
    Route::get('/tct', "SystemtuneController@tct");

    Route::get("/bm", "WebinarController@sync");

    Route::get('/', 'HomeController@landing')->name('landing');
    // Route::view('school', "frontend.school");
    Route::get('school', "ClosuresController@school");
    Route::get('parent', "ClosuresController@parent");
    // Route::view('parent', "frontend.parent");

    Route::view('about', 'frontend.about');
    Route::view('terms', 'frontend.terms');
    Route::view('privacy', 'frontend.privacy');
    Route::view('faq', 'frontend.faq');
    Route::get('complaint', 'ClosuresController@complaint');
    Route::post('complaint', 'HomeController@complaintMail');
    Route::view('editorialguidelines', 'frontend.editorial');
    // Route::view('rules', 'frontend.rules');
    Route::view('tour', 'tour.tour');

    Route::view('blockquote', 'blockquote');

    Route::post('registerschool', 'HomeController@schoolRegister')->name('registerschool');

    Route::get("coursefindersite", "ClosuresController@coursefindersite")->name('coursefindersite');

    Route::post('/', 'HomeController@store')->name('contact_store');
    // Student

    Route::get('tour/{slug}', 'SchoolvisitTemplatesController@tour');
    Route::post('stripecast/webhooks', 'WebhookController@handle');


    Route::get('westpacscholarship/nomination', 'ScholarshipNominationController@index');
    Route::post('westpacscholarship/nomination', 'ScholarshipNominationController@store');

    Route::get('interview/nomination', 'InterviewNominationController@index');
    Route::post('interview/nomination', 'InterviewNominationController@store');

    Route::view('westpacscholarship/faqs', 'standalone.westpacfaqs');
    Route::view('westpacscholarship/apply', 'standalone.westpacapply');
    Route::get('scholarship/download-info-pack', 'ScholarshipNominationController@downloadInfoPack');

    Route::get('/checkLoginStatus', 'Auth\LoginController@checkLoginStatus');
    Route::get('/logout', 'Auth\LoginController@logout');
    // Route::view('virtual-work-experience', 'standalone.vwe');
    Route::permanentRedirect('virtual-work-experience', '/');
    Route::post('froala/upload_video', 'ClosuresController@uploadVideo');
    Route::post('froala/upload_file', 'ClosuresController@uploadFile');

    Route::get('subjects-selection', function (Request $request) {
        // Backward compatibility for old links
        // in generated recommendation pdfs.
        if ($id = $request->query('pdf_subject_id')) {
            return redirect('/subjects-selection/student/subject-details/' . $id);
        }

        return view('subjectsselection.index');
    })->middleware(SubjectSelectionsAccess::class);

    Route::get('/subjects-selection/{any}', function ($any = null) {
        return view('subjectsselection.index');
    })->where('any', '.*');

    Route::middleware(['auth'])->group(function () {
        // Flush Cache
        Route::get('/flush-cache', 'ClosuresController@flushCache');
        Route::redirect('/dashboard', '/#/dashboard');

        Route::post('students/getdata', 'StudentsController@getAdvanceFilterData');
        Route::get('students/data', [StudentsController::class, 'getAdvanceFilterData']);

        Route::post('/sessionexpire', 'Auth\LoginController@sessionExpire')->name('session-expire');
        Route::get('teacherresources/{id}/download', 'TeacherResourcesController@download')->name('teacherresources.download');


        /* Teachers routes Start */
        // Route::model('teacher', App\Teacher::class);
        Route::group(['middleware' =>  ['role:Teacher,Staff,Employer']], function () {
            Route::group(['middleware' => ['campus_must_be_selected']], function () {
                Route::group(['middleware' =>  ['secondary_teacher']], function () {
                    Route::post('teacherresources/filter', 'TeacherResourcesController@filter')->name('teacherresources.filter');
                    Route::model('teacherresource', App\TeacherResource::class);
                    Route::resource('teacherresources', 'TeacherResourcesController')->only('index');

                    Route::group(['middleware' =>  ['has_full_access']], function () {
                        // Route::get('teachers/import', 'TeachersController@import');
                        Route::get('teachers/{id}/invite', 'TeachersController@reSendInvitation');
                        Route::get('students/{id}/invite', 'StudentsController@reSendInvitation');
                        Route::get('teachers/downloadsample/{campus?}', 'TeachersController@downloadsample')->name("teachers.downloadsample");
                        Route::get('students/downloadsample/{campus?}', 'StudentsController@downloadsample')->name("students.downloadsample");

                        Route::post('teachers/import', 'TeachersController@importTeachers')->name("teachers.processimport");
                        Route::post('students/import', 'StudentsController@importStudents')->name("students.processimport");
                        Route::post('teacherStudentAdd', 'StudentsController@teacherStudentAdd')->name("teacherStudentAdd");
                        // Route::get('teachers/getdata', 'TeachersController@getAdvanceFilterData');
                        // Route::resource('teachers', 'TeachersController');

                        Route::get('staff/getdata', 'StaffController@getAdvanceFilterData');
                        Route::resource('staff', 'StaffController');
                        Route::get('staff/{id}/invite', 'StaffController@reSendInvitation');
                        Route::get("/getOrganisationCampuses", "ClosuresController@getOrganisationCampuses")->name('getOrganisationCampuses');

                        Route::get('wew/manage', 'WewController@index')->name('wew.manage');
                        Route::post('wew/toggle', 'WewController@toggle')->name('wew.toggle');
                        Route::post('menu/toggle', 'WewController@menutoggle')->name('menu.toggle');

                        Route::get('tasks-manage', 'TasksController@manage')->middleware(LessonsAccess::class);
                        Route::put('tasks/{id}/assignto', 'TasksController@assignTo');
                        Route::post('tasks/toggle', 'TasksController@toggle')->name('tasks.toggle');
                    });

                    Route::group(['middleware' =>  ['has_manager_access']], function () {
                        Route::post('students/bulkAction', 'StudentsController@bulkAction');
                        Route::resource('students', 'StudentsController');
                        Route::get('student/export', 'StudentsController@export')->name('students.export');
                        Route::get('teacher/export', 'TeachersController@export')->name('teachers.export');
                        Route::get('whs/export', 'WorkhealthsafetyTemplateController@export')->name('whs.export');
                        Route::get('we/export', 'WorkexperienceTemplateController@export')->name('we.export');
                        Route::get('st/export', 'SkillstrainingTemplateController@export')->name('st.export');
                        Route::get('ri/export', 'ResumesinterviewsTemplateController@export')->name('ri.export');
                        Route::get('wr/export', 'WorkreadyTemplateController@export')->name('wr.export');
                        Route::get('students/{id}/profiling', 'ProfilerController@showProfilerResult')->name('student.profiling');
                        Route::get('students/{id}/gameplan', 'GameplansController@studentGameplan')->name('student.gameplan');
                        Route::get('/gameplans/{studentId}/{gameplanId}', 'Vue\EmployersController@viewStudentGameplan')->name('employer.view-student-gameplan');
                        Route::get('students/{id}/timeline', 'StudentsController@timeline')->name('students.timeline');
                        Route::get('students/{id}/notes', 'StudentsController@notes')->name('students.notes');
                        Route::post('students/{id}/note', 'StudentsController@notesStore')->name('notes.store');
                        Route::get('notes/{id}', 'StudentsController@notesShow')->name('notes.show');
                        Route::post('notes/{id}/edit', 'StudentsController@notesUpdate')->name('notes.update');
                        Route::delete('notes/{id}', 'StudentsController@notesDestroy')->name('notes.destroy');
                        Route::get('students/{id}/cvs', 'CvsController@studentCvs')->name('students.cvs');
                        Route::get('reports', 'ReportsController@index')->name('reports.index');
                        Route::post('reports', 'ReportsController@filter')->name('reports.filter');
                        Route::get('reports/whichreportdata/{option}', 'ReportsController@whichReportData');
                        Route::get('reports/relatedoptiondata/{option}', 'ReportsController@relatedOptionData');
                        Route::get('reports/export/{qus}', 'ReportsController@export')->name('reports.export');
                        Route::get('getoptionreport', 'ReportsController@optionReport');
                        Route::get('/students/{id}/remove', 'StudentsController@remove');

                        Route::get('manage-tasks/filter', 'TasksController@manageTaskTab')->name('manage-tasks.filter');
                        Route::get('student-tasks', 'TasksController@responses')->name('tasks.student');

                        Route::get('wew/filter', 'WewController@filter')->name('wew.filter');
                        Route::get('tasks/feedback/{id}', 'TasksController@getFeedback');
                        Route::put('tasks/feedback/{id}', 'TasksController@storeFeedback');
                        Route::get('tasks/{taskId}/students/{studentId}', 'TasksController@studentResponse')->name('tasks.studentResponse');

                        Route::view('wew/responses', 'workexperienceweek.responses')->name('wew.responses');

                        Route::get('wew/responses/workexperience', 'WorkexperienceTemplateController@taskresponses')->name('workexperience.responses');
                        // Route::get('vwe-certificate/{id}', 'ExploreWorkexperienceController@downloadPDF');
                        // Route::get('workexperiencetemplates/feedback/{id}', 'WorkexperienceTemplateController@getFeedback');


                        Route::get('wew/responses/workhealthsafety', 'WorkhealthsafetyTemplateController@responses')->name('workhealthsafety.responses');
                        Route::get('workhealthsafety/responses/feedback/{id}', 'WorkhealthsafetyTemplateController@getFeedback');
                        Route::put('workhealthsafety/responses/feedback/{id}', 'WorkhealthsafetyTemplateController@storeFeedback');
                        Route::post('workhealthsafety/templates/toggle', 'WorkhealthsafetyTemplateController@toggle')->name('workhealthsafety.templates.toggle');

                        Route::get('wew/responses/skillstraining', 'SkillstrainingTemplateController@responses')->name('skillstraining.responses');
                        Route::get('skillstraining/responses/feedback/{id}', 'SkillstrainingTemplateController@getFeedback');
                        Route::put('skillstraining/responses/feedback/{id}', 'SkillstrainingTemplateController@storeFeedback');

                        Route::get('skillstraining/{id}/students/{studentId}', 'SkillstrainingTemplateController@studentResponse')->name('skillstraining.studentResponse');
                        Route::post('skillstraining/templates/toggle', 'SkillstrainingTemplateController@toggle')->name('skillstraining.templates.toggle');

                        Route::get('wew/responses/resumesinterviews', 'ResumesinterviewsTemplateController@responses')->name('resumesinterviews.responses');
                        Route::get('resumesinterviews/responses/feedback/{id}', 'ResumesinterviewsTemplateController@getFeedback');
                        Route::put('resumesinterviews/responses/feedback/{id}', 'ResumesinterviewsTemplateController@storeFeedback');
                        Route::post('resumesinterviews/templates/toggle', 'ResumesinterviewsTemplateController@toggle')->name('resumesinterviews.templates.toggle');

                        Route::get('wew/responses/workready', 'WorkreadyTemplateController@responses')->name('workready.responses');
                        Route::get('workready/responses/feedback/{id}', 'WorkreadyTemplateController@getFeedback');
                        Route::put('workready/responses/feedback/{id}', 'WorkreadyTemplateController@storeFeedback');
                        Route::post('workready/templates/toggle', 'WorkreadyTemplateController@toggle')->name('workready.templates.toggle');

                        Route::middleware([NoticeboardAccess::class])->group(function () {
                            Route::get('notice/{id}/{post}', 'NoticeboardController@post')->name('notice.post');
                            Route::resource('notices', 'NoticeboardController');
                        });

                        Route::get('licences', 'LicenceController@index')->name('licences.index');
                        Route::post('licences/{id}/unassign', 'LicenceController@unassign')->name('licences.unassign');
                        Route::post('licences/{id}/assign', 'LicenceController@assign')->name('licences.assign');
                        Route::get('licence-assign/search', 'LicenceController@searchStudent');
                    });


                    // Route::get("joinsession/{webinar}", "WebinarController@join")->name("sessions.join");
                });

                Route::group(['middleware' =>  ['has_full_access']], function () {
                    Route::get('teachers/getdata', 'TeachersController@getAdvanceFilterData');
                    Route::resource('teachers', 'TeachersController');
                });


                Route::get('viewtype/{type}', 'DashboardsController@viewChange')->name("teacher.viewtype");
                Route::get('schoolSection/{scho}', 'DashboardsController@schoolSection')->name("teacher.schoolSection");
                /* teacher dashboard calls */
                // getTeacherPopularIndustries
                Route::get("/dashboard/getPopularIndustries", "DashboardsController@getTeacherPopularIndustries")->name('getPopularIndustries');
            });
        });


        Route::group(['middleware' => ['role:Parent,Teacher,Staff']], function () {
            Route::group(['middleware' => ['ensure_parent_subscription_is_active']], function () {
                Route::get('/profiler/resultpdf/{id}', 'ProfilerController@downloadProfilerResult');
                Route::get('tasks/responses/{id}/download', 'TasksController@downloadResponse')->name('tasks.responsedownload');
            });

            /* Routes related to vue */
            Route::prefix('api')->group(function () {
                Route::get('/lessons/{id}/{student}', 'Vue\LessonsController@studentResponse')->middleware(LessonsAccess::class);
                Route::get('/skillstraining/{id}/{student}', 'Vue\WewSkillstrainingController@studentResponse')->middleware(SkillsTrainingAccess::class);
                Route::get('/vwe/{id}/{student}', 'Vue\ExploreWorkexperienceController@studentResponse')->middleware(VweAccess::class);
            });

            /* Routes related to vue ends */
        });


        Route::group(['middleware' =>  ['primary_teacher']], function () {

            Route::controller(PrimarySchoolsController::class)->group(function () {
                Route::get('activities-lessons', 'activitiesLessons')->name('activitiesLessons');
                Route::get('activities-lessons/filter', 'activitiesLessonsFilter')->name('activitiesLessonsFilter');
            });
        });
        Route::group(['middleware' =>  ['role:Compass Teacher']], function () {
            Route::get('compass-primary', [PrimarySchoolsController::class, 'landing'])->name('compass.landing');
        });


        Route::group(['middleware' => ['ensure_parent_subscription_is_active', 'plan_must_be_selected']], function () {
            Route::get('/profiles/edit/{id?}', 'ProfilesController@edit')->name('profile-edit');
            Route::put('/profiles/edit', 'ProfilesController@update');
        });


        Route::group(['middleware' => ['plan_must_be_selected']], function () {
            /* Routes related to vue */
            Route::prefix('api')->group(function () {

                Route::get('getBanner/{type}', "BannersController@getBanner");

                // For Lessons
                Route::middleware([LessonsAccess::class])->group(function () {
                    Route::post('/lessons', 'Vue\LessonsController@list');
                    Route::get('searchLessons/{value}', "Vue\LessonsController@searchLessons");
                    Route::get('/lessonname/{id}', 'Vue\LessonsController@lessonname');
                    Route::get('/sectionwithlessonname/{id}/{sectionnumber}', 'Vue\LessonsController@sectionwithlessonname');
                    Route::get('/lessons/{id}', 'Vue\LessonsController@detail');
                    Route::post('/lessons/{lesson}/fav', 'Vue\LessonsController@togglefav');
                    Route::post('/lessons/{lesson}/reset', 'Vue\LessonsController@resetLesson');
                    Route::get('/lessons/{lesson}/sections/{lessonstep}', 'Vue\LessonsController@sectiondetail');
                    Route::post('/lessons/{lesson}/sections/{lessonstep}', 'Vue\LessonsController@storeResponse');
                    Route::post('/lessons/{id}/submit-task', 'Vue\LessonsController@storeFinalResponse');
                });

                // For Skills Training
                Route::middleware([SkillsTrainingAccess::class])->group(function () {
                    Route::post('/skillstraining', 'Vue\WewSkillstrainingController@list');
                    Route::get('searchSkillsTrainings/{value}', "Vue\WewSkillstrainingController@searchSkillsTrainings");
                    Route::post('/skillstraining/{skillstraining}/fav', 'Vue\WewSkillstrainingController@togglefav');
                    Route::get('/skillstraining/{id}', 'Vue\WewSkillstrainingController@detail');
                    Route::post('/skillstraining/{skillstraining}/reset', 'Vue\WewSkillstrainingController@resetSkillstraining');
                    Route::get('/skillstraining/{skillstraining}/sections/{skillstrainingstep}', 'Vue\WewSkillstrainingController@sectiondetail');
                    Route::post('/skillstraining/{skillstraining}/sections/{skillstrainingstep}', 'Vue\WewSkillstrainingController@storeResponse');
                    Route::post('/skillstraining/{id}/submit-task', 'Vue\WewSkillstrainingController@storeFinalResponse');
                });

                // For Virtual Work Experience
                Route::middleware([VweAccess::class])->group(function () {
                    Route::post('/vwe', 'Vue\ExploreWorkexperienceController@list');
                    Route::get('searchVwes/{value}', "Vue\ExploreWorkexperienceController@searchVwes");
                    Route::post('/vwe/{vwe}/fav', 'Vue\ExploreWorkexperienceController@togglefav');
                    Route::get('/vwe/{id}', 'Vue\ExploreWorkexperienceController@detail');
                    Route::post('/vwe/{vwe}/reset', 'Vue\ExploreWorkexperienceController@resetVwe');
                    Route::get('/vwe/{vwe}/sections/{skillstrainingstep}', 'Vue\ExploreWorkexperienceController@sectiondetail');
                    Route::post('/vwe/{vwe}/sections/{skillstrainingstep}', 'Vue\ExploreWorkexperienceController@storeResponse');
                    Route::post('/vwe/{id}/submit-task', 'Vue\ExploreWorkexperienceController@storeFinalResponse');
                });

                // For Explore Industries
                Route::middleware([IndustriesAccess::class])->group(function () {
                    Route::post('/exploreIndustries', 'Vue\ExploreIndustriesController@exploreIndustries');
                    Route::get('/exploreIndustries/{id}', 'Vue\ExploreIndustriesController@detail');
                    Route::get('/explore/{industry}/template/{unit}', 'Vue\ExploreIndustriesController@showTemplate');
                    Route::get('/industryContentTemplates', 'Vue\ExploreIndustriesController@industryContentTemplates');
                    Route::post('/industries/{industry}/fav', 'Vue\ExploreIndustriesController@toggleindustryfav');
                    Route::post('/units/{unit}/fav', 'Vue\ExploreIndustriesController@togglefav');
                    // Route::get('searchIndustryTemplatesResults/{value}', "Vue\ExploreIndustriesController@searchIndustryTemplatesResults");
                    Route::get('searchIndustries/{value}', "Vue\ExploreIndustriesController@searchIndustries");
                    // lessons/57/sections/1 api/lessons/58/sections/168
                });

                // For Scholarship finder
                Route::middleware([ScholarshipFinderAccess::class])->group(function () {
                    Route::post('filterScholarships', 'Vue\ScholarshipfinderController@filter');
                    Route::post('scholarships/{scholarship}/fav', 'Vue\ScholarshipfinderController@toggleFav');
                    Route::get('selectedScholarships', 'Vue\ScholarshipfinderController@getSelectedScholarships');
                    Route::get('scholarship-fieldsofstudy', 'Vue\ScholarshipfinderController@fieldsOfStudy');
                    Route::get('scholarship-providers', 'Vue\ScholarshipfinderController@scholarshipProvider');
                    Route::get('scholarship-targetgroups', 'Vue\ScholarshipfinderController@targetGroups');
                });

                // For Course Finder
                Route::middleware([CourseFinderAccess::class])->group(function () {

                    Route::post('/filteredCourses', 'Vue\CoursesController@filteredCourses');
                    Route::post('/favCourse', "Vue\CoursesController@favCourse");
                    // Route::post("/selectedCourses","Vue\CoursesController@SelectedCourses");
                    Route::get("/selectedCourses", "Vue\CoursesController@SelectedCourses");
                });

                // For Job Finder
                Route::middleware([JobFinderAccess::class])->group(function () {

                    Route::post('/jobfinder', 'Vue\JobsfinderController@search');
                });
                Route::get('searchChild/{value}', "Vue\ClosuresController@searchChild");
                Route::get('parentInvitees', "Vue\ClosuresController@parentInvitees");
            });

            /* Routes related to vue ends */

            Route::group(['middleware' => ['ensure_parent_subscription_is_active']], function () {
                Route::group(['middleware' => ['campus_must_be_selected']], function () {
                    Route::get('home', 'DashboardsController@index')->name('home');
                    Route::get('/profiles/{user}/notifications', 'UserNotificationsController@index');
                    Route::delete('/profiles/{user}/notifications/{notification}', 'UserNotificationsController@destroy');
                    Route::resource('unitsfeedback', 'UnitsFeedbackController')->only('store');

                    Route::get('timeline', 'StudentsController@timeline')->name('student.timeline');
                    // Route::get('timeline', 'DashboardsController@timeline');
                    // Route::get('explorethingstoknow', 'ThingstoknowTemplateController@thingstoknow')->name('explorethingstoknow.index');;
                    // Route::get('explorethingstoknow/{template}', 'ThingstoknowTemplateController@showThingstoknow')->name('explorethingstoknow.show');

                    Route::prefix('profiler')->middleware([ProfilingAccess::class])->group(function () {
                        Route::get('start', 'ProfilerController@profilingStart');
                        Route::get('result/{id?}', 'ProfilerController@showProfilerResult')->name('profiler.result');
                        Route::get('resultpdf', 'ProfilerController@downloadProfilerResult');
                        Route::post('resultpdf/email', 'ProfilerController@emailProfilerResult');
                        Route::get('/', 'ProfilerController@studentProfiling');
                        Route::delete('/reset/{id}', 'ProfilerController@resetStudentProfiler');
                    });

                    Route::get('/getCourses', "ClosuresController@getCourses");


                    Route::get('/getRelatedLevels', 'ExploreIndustriesController@getRelatedLevels');

                    Route::get('/getFilteredCourses', 'ExploreIndustriesController@getFilteredCourses');

                    Route::get('/getGameplanCourses', 'GameplansController@getGameplanCourses');

                    Route::get('/favCourse', "ClosuresController@favCourse");

                    // Route::resource('exploreindustries', 'ExploreIndustriesController');
                    // Route::model('template', App\Template::class);
                    Route::middleware([IndustriesAccess::class])->group(function () {
                        Route::get('exploreindustries', 'ExploreIndustriesController@index')->name('exploreindustries.index');
                        Route::get('exploreindustries/{industry}', 'ExploreIndustriesController@view')->name('exploreindustries.view');
                        Route::get('exploreindustries/{industry}/unit/{unit}', 'ExploreIndustriesController@show')->name('exploreindustries.show');

                        Route::get('industryunit/toggle-bookmark', 'ExploreIndustriesController@toggleBookmark')->name('industryunit.toggle-bookmark');
                    });
                    // Route::get('exploreindustries/{id}/list/{template}', 'ExploreIndustriesController@list')->name('exploreindustries.lists');
                    // Route::get('exploreindustries/{id}/subcategories/{subcategory}', 'ExploreIndustriesController@subcategory')->name('exploreindustries.subcategories');
                    // Route::get('exploresubjects', 'ExploreIndustriesController@subjects');
                    Route::get('plan/single-plan', 'PlansController@singlePlan');
                    Route::get('unit/single-progress', 'StudentTemplateProgressController@singleProgress');
                    Route::get('profiling/single-result', 'ProfilerController@singleResult');
                    Route::get('lessonresponse/single-response', 'LessonsController@singleResponse');
                    Route::get('work-experience-response/single-response', 'WorkexperienceTemplateController@singleResponse');
                    Route::get('skills-training-response/single-response', 'SkillstrainingTemplateController@singleResponse');
                    Route::get('cvs-response/cv-timeline-block', 'CvsController@timelineBlock');
                    Route::get('activity-logs/get-timeline-content/{id}', 'ActivityLogController@getTimelineContent');



                    Route::resource('gameplan', 'GameplansController')->only('index', 'show', 'store');
                    Route::get('gameplan/{id}', 'GameplansController@index');

                    Route::get('studentSubjects/{id?}', 'GameplansController@studentSubjects')->name('studentSubjects');
                    Route::post('studentSubjects/store', 'GameplansController@subjectsStore')->name('studentSubjects.store');
                    Route::get('studentCourses/{id?}', 'GameplansController@studentCourses')->name('studentCourses');
                    Route::post('studentCourses/store', 'GameplansController@coursesStore')->name('studentCourses.store');
                    Route::get('studentScholarships/{id?}', 'GameplansController@studentScholarships')->name('studentScholarships');
                    Route::get('studentContent/{id?}', 'GameplansController@studentContent')->name('studentContent');

                    // Route::view('banner', 'partials.studenttopbannertabs');
                    Route::post('storeTemplateProgress', 'StudentTemplateProgressController@store')->name('storeTemplateProgress');
                    Route::post('storeTemplateData', 'StudentTemplateProgressController@storeTemplateData')->name('storeTemplateData');
                    Route::middleware([EMagazineAccess::class])->group(function () {
                        Route::get('e-magazines/editions', 'MagazineEditionsController@view');
                        Route::get('e-magazines/{id}', 'MagazinesController@view');
                    });


                    /*
                    |--------------------------------------------------------------------------
                    | Profiling Routes
                    |--------------------------------------------------------------------------
                    */
                    Route::middleware(['admin'])->group(function () {
                        Route::group(['prefix' => 'profiling-posts', 'as' => 'profiling-posts.'], function () {
                            Route::get('/', [ProfilingPostController::class, 'index'])->name('index');
                            Route::get('/datatable-list', [ProfilingPostController::class, 'datatableList'])->name('datatable.list');
                            Route::get('/create', [ProfilingPostController::class, 'create'])->name('create');
                            Route::post('/store', [ProfilingPostController::class, 'store'])->name('store');
                            Route::get('/edit/{profilingPost}', [ProfilingPostController::class, 'edit'])->name('edit');
                            Route::put('/update/{profilingPost}', [ProfilingPostController::class, 'update'])->name('update');
                            Route::delete('/delete/{profilingPost}', [ProfilingPostController::class, 'destroy'])->name('destroy');

                            Route::group(['prefix' => 'anzsco-data', 'as' => 'anzsco-data.'], function () {
                                Route::resource('occupations', AnzscoOccupationController::class);
                            });
                        });
                    });

                    Route::middleware([CareerProfilingAccess::class])->group(function () {
                        Route::get('career/profiling', 'UserProfilingController@index')->name('user.profiling.index');
                        Route::get('video/profiling', 'UserProfilingController@view');
                    });

                    // Route::get('/', [UserProfilingController::class, 'index'])->name('index');
                    Route::get('/job-suggestions', [UserProfilingController::class, 'jobSuggestions'])->name('user.profiling.job.suggestions');

                    Route::post('/update-single-response/{profilingPost}', [UserProfilingController::class, 'updateSingleResponse'])->name('user.profiling.update.single.response');

                    Route::post('/update-multiple-response', [UserProfilingController::class, 'updateMultipleResponse'])->name('user.profiling.update.multiple.response');

                    Route::post('/store-user-selected-occupations', [UserProfilingController::class, 'storeUserSelectedOccupations'])->name('user.profiling.store.userSelectedOccupations');

                    Route::post('/can-job-suggest', [UserProfilingController::class, 'canJobSuggest'])->name('user.profiling.canJobSuggest');

                    Route::get('/progress', [UserProfilingController::class, 'progress'])->name('user.profiling.progress');

                    Route::get('/job-suggestion-search', [UserProfilingController::class, 'jobSuggestionSearch'])->name('user.profiling.jobSuggestionSearch');

                    Route::get('/chunk', [UserProfilingController::class, 'loadChunk'])->name('user.profiling.chunk');

                    Route::get('/user-job-info/{anzscoOccupation}', [StudentsController::class, 'userJobInfo'])->name('userJobInfo');

                    Route::get('studentpdf', array('as' => 'studentpdf', 'uses' => 'GameplansController@parentDownloadPDF'));
                    Route::get('nonstudentpdf', array('as' => 'nonstudentpdf', 'uses' => 'GameplansController@nonStudentPDF'));

                    // Route::view('wew/overview', 'workexperienceweek.overview')->name('wew.overview');
                    // Route::view('wew/futureofwork', 'workexperienceweek.futureofwork')->name('wew.futureofwork');

                    Route::get('studentWorkexperiences/{id?}', 'ExploreWorkexperienceController@completedWorkexperience')->name('completedWorkexperience.index');
                    Route::resource('user-experiences', UserExperiencesController::class)->except(['index', 'create']);
                    Route::get('/getBadge/{badgeKey}', 'ExploreWorkexperienceController@getBadge');
                    Route::get('exploreworkexperience/{id}', 'ExploreWorkexperienceController@show')->name('exploreworkexperience.show');
                    Route::get('getWorkexperienceTemplates', 'ExploreWorkexperienceController@getTemplates')->name('getWorkexperienceTemplates');
                    Route::post('exploreworkexperience/{template}/response', 'ExploreWorkexperienceController@storeResponse')->name('exploreworkexperience.storeresponse');
                    Route::put('exploreworkexperience/{response}/updateresponse', 'ExploreWorkexperienceController@updateResponse')->name('exploreworkexperience.updateresponse');
                    Route::post('exploreworkexperience/{response}/addtime', 'ExploreWorkexperienceController@addTime')->name('exploreworkexperience.addtime');
                    Route::delete('workexperiencetemplates/responses/{id}', 'WorkexperienceTemplateController@destroyResponse');
                    Route::get('workexperiencetemplates/responses/{id}/download', 'WorkexperienceTemplateController@downloadResponses')->name('workexperience.responsedownload');
                    Route::get('workexperiencetemplates/responses/{id}/viewResponse', 'ExploreWorkexperienceController@getFileUrl');
                    Route::middleware([WhsAccess::class])->group(function () {
                        Route::get('wew/workhealthsafety/quiz', 'WewWorkhealthsafetyController@quiz')->name('wew.workhealthsafety.quiz');
                        Route::get('wew/workhealthsafety/quiz-start', 'WewWorkhealthsafetyController@quizStart')->name('wew.workhealthsafety.quiz-start');
                        Route::post('wew/workhealthsafety/quiz-store', 'WewWorkhealthsafetyController@quizStore')->name('wew.workhealthsafety.quiz-store');
                        Route::get('wew/workhealthsafety', 'WewWorkhealthsafetyController@index')->name('wew.workhealthsafety.index');
                        Route::get('wew/workhealthsafety/{id}', 'WewWorkhealthsafetyController@show')->name('wew.workhealthsafety.show');

                        Route::post('wew/workhealthsafety/{template}/response', 'WewWorkhealthsafetyController@storeResponse')->name('wew.workhealthsafety.storeresponse');
                        Route::get('workhealthsafety-certificate/{id?}', 'WewWorkhealthsafetyController@downloadPDF')->name('wew.workhealthsafety.certificate');
                        Route::delete('workhealthsafety/responses/{id}', 'WorkhealthsafetyTemplateController@destroyResponse')->name('workhealthsafety.responsedelete');
                        Route::get('workhealthsafety/responses/{id}/download', 'WorkhealthsafetyTemplateController@downloadResponses')->name('workhealthsafety.responsedownload');
                    });

                    Route::middleware([SkillsTrainingAccess::class])->group(function () {

                        Route::get('wew/skillstraining/{id}', 'WewSkillstrainingController@show')->name('wew.skillstraining.show');
                        Route::post('wew/skillstraining/{id}/activity-response', 'WewSkillstrainingController@storeActivityResponse')->name('wew.skillstraining.activity.storeresponse');
                        Route::post('wew/skillstraining/{template}/response', 'WewSkillstrainingController@storeResponse')->name('wew.skillstraining.storeresponse');
                        Route::get('skillstraining-certificate/{id}', 'WewSkillstrainingController@downloadPDF')->name('wew.skillstraining.certificate');
                        Route::delete('skillstraining/responses/{id}', 'SkillstrainingTemplateController@destroyResponse')->name('skillstraining.responsedelete');
                        Route::get('skillstraining/responses/{id}/download', 'SkillstrainingTemplateController@downloadResponses')->name('skillstraining.responsedownload');
                        Route::get('studentSkillstrainings/{id?}', 'WewSkillstrainingController@completedSkillsTraining')->name('completedskillstrainings.index');
                    });

                    Route::get('wew/resumesinterviews', 'WewResumesinterviewsController@index')->name('wew.resumesinterviews.index');
                    Route::get('wew/resumesinterviews/{id}', 'WewResumesinterviewsController@show')->name('wew.resumesinterviews.show');
                    Route::post('wew/resumesinterviews/{template}/response', 'WewResumesinterviewsController@storeResponse')->name('wew.resumesinterviews.storeresponse');
                    Route::get('resumesinterviews-certificate/{id}', 'WewResumesinterviewsController@downloadPDF')->name('wew.resumesinterviews.certificate');
                    Route::delete('resumesinterviews/responses/{id}', 'ResumesinterviewsTemplateController@destroyResponse')->name('resumesinterviews.responsedelete');
                    Route::get('resumesinterviews/responses/{id}/download', 'ResumesinterviewsTemplateController@downloadResponses')->name('resumesinterviews.responsedownload');

                    Route::get('wew/workready', 'WewWorkreadyController@index')->name('wew.workready.index');
                    Route::get('wew/workready/{id}', 'WewWorkreadyController@show')->name('wew.workready.show');
                    Route::post('wew/workready/{template}/response', 'WewWorkreadyController@storeResponse')->name('wew.workready.storeresponse');
                    Route::get('workready-certificate/{id}', 'WewWorkreadyController@downloadPDF')->name('wew.workready.certificate');
                    Route::delete('workready/responses/{id}', 'WorkreadyTemplateController@destroyResponse')->name('workready.responsedelete');
                    Route::get('workready/responses/{id}/download', 'WorkreadyTemplateController@downloadResponses')->name('workready.responsedownload');

                    Route::redirect('jobfinder', '/#/tools/jobfinder');
                    // Route::view('jobfinder', 'jobfinder.index')->middleware(JobFinderAccess::class);
                    Route::post('jobfinder', 'JobsfinderController@search');

                    Route::post('parentinvitees', 'ProfilesController@storeParentInvitees')->name('parentinvitees.store');
                    Route::delete('parentinvitees/{id}', 'ProfilesController@destroyParentInvitees')->name('parentinvitees.destroy');

                    Route::group(['middleware' => ['role:Student,Individual Student']], function () {
                        Route::resource('nominees', 'NomineesController');
                    });

                    Route::middleware([ResumeBuilderAccess::class])->group(function () {

                        Route::get('cvs/selecttemplate/{id?}', 'CvsController@selectTemplate')->name('cvs.selecttemplate');
                        Route::post('cvs/storetemplate/{id?}', 'CvsController@storeTemplate')->name('cvs.storetemplate');
                        Route::get('cvs/{id}/copy', 'CvsController@copy')->name('cvs.copy');
                        Route::post('cvs/{id}/pdf/email', 'CvsController@pdfEmail')->name('cvs.pdf.email');
                        Route::resource('cvs', 'CvsController')->except(['create', 'store']);
                        Route::post('getSection', 'CvsController@getSection');
                        Route::get('deleteSection/{id}', 'CvsController@deleteSection');
                        Route::get('studentResumes/{id?}', 'CvsController@studentProfileCvs');
                    });
                    //Eportfolio Section
                    // Route::post('getEportfolioSection', [EportfolioController::class, 'getSection']);
                    // Route::get('eportfolio/start', [EportfolioController::class, 'start'])->name('eportfolio.start');
                    // Route::resource('eportfolio', EportfolioController::class)->except(['create', 'store']);

                    Route::group(['middleware' => ['premium_parent']], function () {
                        Route::view('eportfolio', 'eportfolio.index')->middleware(EPortfolioAccess::class);
                        Route::view('eportfolio/home', 'eportfolio.home')->middleware(EPortfolioAccess::class);
                        Route::get('tasks', 'TasksController@index')->name('tasks.index')->middleware(LessonsAccess::class);
                        Route::get('exploreworkexperience', 'ExploreWorkexperienceController@index')->name('exploreworkexperience.index')->middleware(VweAccess::class);
                        Route::get('wew/skillstraining', 'WewSkillstrainingController@index')->name('wew.skillstraining.index')->middleware(SkillsTrainingAccess::class);
                    });

                    Route::middleware([LessonsAccess::class])->group(function () {

                        Route::get('studentTasks/{id?}', 'TasksController@completedTasks')->name('completedtasks.index');
                        Route::get('tasks/filter', 'TasksController@filter')->name('tasks.filter');
                        Route::get('tasks/{id}', 'TasksController@show')->name('tasks.show');
                        Route::post('tasks/{id}/response', 'TasksController@storeResponse')->name('tasks.storeresponse');
                        Route::post('tasks/{id}/activity-response', 'TasksController@storeActivityResponse')->name('activity.storeresponse');
                        Route::get('tasks/{id}/download-response', 'TasksController@downloadResponse')->name('tasks.downloadresponse');
                        Route::delete('tasks/responses/{id}', 'TasksController@destroyResponse')->name('tasks.responsedelete');
                        Route::get('/tasks/{id}/file-url', 'TasksController@getFileUrl');
                        Route::get('badges/{id}/download', 'BadgesController@downloadBadge')->name('badges.download');
                    });

                    Route::prefix('scholarships')->name('scholarships.')->middleware([ScholarshipFinderAccess::class])->group(function () {
                        Route::get('search', 'ScholarshipsController@searchView')->name('search');
                        //
                        // Route::get('get-scholarships', "ScholarshipsController@getScholarships");
                        Route::post('{id}/gethelp', 'ScholarshipsController@gethelp')->name('gethelp');
                        Route::get('toggle-bookmark', 'ScholarshipsController@toggleBookmark')->name('toggle-bookmark');
                        Route::get('application-assistant', 'ScholarshipsController@applicationAssistant')->name('application-assistant');
                        Route::post('application-assistant', 'ScholarshipsController@storeApplication')->name('application.store');
                        Route::get('application-pdf/{type?}', 'ScholarshipsController@pdf')->name('application.pdf');
                        Route::post('application-email/{id}', 'ScholarshipsController@emailApplication')->name('application.email');
                    });
                    Route::get('scholarships', 'ScholarshipsController@index')->name('scholarships.index')->middleware(ScholarshipFinderAccess::class);

                    // Route::get("sessions", "WebinarController@list")->name("sessions.index");
                    // Route::get("joinsession/{webinar}", "WebinarController@join")->name("sessions.join");
                    // Route::get("getpastsessions", "WebinarController@past")->name("sessions.past");
                    // Route::get("sessions/{id}", "WebinarController@show");

                    // Route::get('explorethingstoknow', 'ExploreThingstoknowController@index')->name('explorethingstoknow.index');
                    // Route::get('explorethingstoknow/{category}', 'ExploreThingstoknowController@view')->name('explorethingstoknowtemplates.view');
                    // Route::get('explorethingstoknow/{category}/template/{template}', 'ExploreThingstoknowController@show')->name('explorethingstoknow.show');
                    // Route::post('storeThingstoknowProgress', 'StudentThingstoknowProgressController@store')->name('storeThingstoknowProgress');
                    // Route::post('storeThingstoknowData', 'StudentThingstoknowProgressController@storeTemplateData')->name('storeThingstoknowData');

                    Route::middleware([NoticeboardAccess::class])->group(function () {
                        Route::get('noticeboard', 'NoticeboardController@noticeboard')->name('noticeboard.index');
                        Route::get('getNotices', 'NoticeboardController@getNotices');
                        Route::get('getFilterNotices', 'NoticeboardController@getFilterNotices');
                    });

                    Route::post('enquire-form', 'ExploreIndustriesController@storeEnquire')->name("enquire");
                    Route::post('send-to-parent', 'ExploreIndustriesController@sendToParent')->name("sendtoparent");
                    Route::get('industryunit/toggle-bookmark', 'ExploreIndustriesController@toggleBookmark')->name('industryunit.toggle-bookmark');
                    Route::get('industry/toggle-bookmark', 'ExploreIndustriesController@industryToggleBookmark')->name('industry.toggle-bookmark');
                    Route::get('thingstoknow/toggle-bookmark', 'ExploreThingstoknowController@toggleBookmark')->name('thingstoknow.toggle-bookmark');
                    Route::middleware([CourseFinderAccess::class])->group(function () {
                        Route::get('/coursefinder', 'CoursesController@courseFinder')->name('coursefinder');
                        Route::get('/course-filter', 'CoursesController@filter')->name('course-filter');
                    });

                    // Route::get('subjectselection', 'SubjectSelectionController@index')->name('subjectselection.index');
                    // Route::get('subjectselection/your-choices', 'SubjectSelectionController@choices')->name('subjectselection.choices');

                    Route::get('getGeoPopupData', 'HomeController@getGeoPopupData');
                });
            });
        });

        Route::get('/updateUserSession', 'UserSessionController@update');
        Route::post('save-year', 'ProfilesController@storeYear')->name('year.store');



        /* Superadmin routes */
        Route::middleware(['admin'])->group(function () {

            // Route::get('phpinfo', 'ClosuresController@phpInfo');

            // Route::get('edit-helpcrunch-detail/', 'ClosuresController@editHelpcrunchDetail');

            Route::get('vwe-template-update', 'ClosuresController@vweUpdate');
            Route::view('video-stream', 'dashboards.video');

            Route::prefix('mailchimp')->group(function () {
                // Route::get('/', 'ClosuresController@mailchimp');
                // Route::get('sync-industries', 'ClosuresController@mailchimpSyncIndustries');
                // Route::get('sync-subjects', 'ClosuresController@mailchimpSyncSubjects');
                Route::get('sync-users', 'ClosuresController@mailchimpSyncUsers');
            });

            Route::get('/filetransfer', "SystemtuneController@filetransfer");
            Route::get('/susu/{id}', "SystemtuneController@loginas");

            Route::get('/php-artisan', "ClosuresController@command");
            Route::get('/remove-cache', "ClosuresController@removeCache");


            Route::view('/emails', "emails.index");
            Route::get('/emails/sample/{email}', "ClosuresController@sendSampleMail")->name('email.sample');

            // Route::get('refresh-invitations', "ClosuresController@refreshInvitations");
            Route::get('sample-invoice', "ClosuresController@sampleInvoice");
            // Route::view("/sample", 'partials.sample');
            // Route::get('/samplemail', "ClosuresController@sampleEmail");
            // Route::get('/filesync', 'HomeController@filesync');
            Route::get("/refreshtemplates", "ClosuresController@refreshTemplates");
            Route::get('getSchools', 'SchoolsController@serverSide');
            Route::get('schools/export', 'SchoolsController@export')->name('schools.export');
            Route::resource('schools', 'SchoolsController');
            Route::get('ordered-schools', 'SchoolsController@ordered');
            Route::get('ordered-schools/{id}/edit', 'SchoolsController@edit')->name('order.edit');
            Route::get('school/{id}/showinfrontend', 'SchoolsController@showInFrontEnd')->name('school.showinfrontend');

            Route::get('schoolToOrg/{id}', 'SchoolsController@schoolToOrg')->name('schoolToOrg');

            Route::get('response/{id}/approve', 'WorkexperienceTemplateController@approve')->name('response.approve');

            // Route::put('ordered-schools/{id}', 'SchoolsController@orderUpdate')->name('order.update');
            Route::get('schoolvisitrequests', 'SchoolsController@visitRequest');

            Route::get('getOrganisations', 'OrganisationsController@serverSide');
            Route::resource('organisations', 'OrganisationsController');
            Route::get('orgToSchool/{id}', 'OrganisationsController@orgToSchool')->name('orgToSchool');

            Route::model('industrycategory', App\IndustryCategory::class);
            Route::resource('industrycategories', 'IndustryCategoriesController');
            Route::get('industries/templates', 'IndustryCategoriesController@showTemplates');
            Route::delete('industries/templates/{id}', 'IndustryCategoriesController@destroyTemplate');
            Route::post('industrycategoriesunit', 'IndustryCategoriesController@storeUnit');
            Route::get('industrycategoriesunit/{id}/edit', 'IndustryCategoriesController@editUnit');
            Route::put('industrycategoriesunit/{id}', 'IndustryCategoriesController@updateUnit');
            // Route::resource('industrytemplates', 'IndustryCategoriesController');
            Route::get('industrytemplates/{industry}/create', 'IndustryCategoriesController@createTemplate')->name('industryunit.add');
            Route::delete('faqimg/{id}', 'IndustryCategoriesController@destroyFactandfaqimg');
            Route::delete('virtualtour/{id}', 'IndustryCategoriesController@destroyVirtualtour');
            Route::delete('video/{id}', 'IndustryCategoriesController@destroyVideo');
            Route::delete('gallery-img/{id}', 'IndustryCategoriesController@destroyGalleryimg');
            Route::delete('phonecall/{id}', 'IndustryCategoriesController@destroyPhonecall');
            Route::post('industrytemplates/store/{industry}', 'IndustryCategoriesController@storeTemplate')->name('industryunit.store');
            Route::post('industrytemplates/assign/{industry}', 'IndustryCategoriesController@assignTemplate')->name('industryunit.assign');
            Route::get('industries/templates/{id}/edit', 'IndustryCategoriesController@editTemplate')->name('industryunit.edit');
            Route::delete('industrycategories/{industry}/industrytemplates/{unit}/', 'IndustryCategoriesController@detachTemplate')->name('industryunit.delete');
            Route::put('industrytemplates/{id}', 'IndustryCategoriesController@updateTemplate')->name('industryunit.update');
            Route::resource('templates', 'TemplatesController');
            Route::get('templates/get/{id}', 'TemplatesController@create');
            Route::get('templates/edit/{id}', 'TemplatesController@edit');


            Route::resource('positions', 'PositionsController');
            Route::resource('universities', 'UniversitiesController');
            Route::resource('colleges', 'CollegesController');
            Route::resource('institutes', 'InstitutesController');
            Route::get('instituteimport', 'InstitutesController@import')->name('instituteimport');
            Route::post('instituteimport', 'InstitutesController@importExcel');
            Route::get('downloadExcel/{type}', 'InstitutesController@downloadExcel');
            Route::model('eventcategory', App\EventCategory::class);
            Route::resource('eventcategories', 'EventCategoriesController');
            Route::prefix('profiler')->group(function () {
                Route::resource('units', 'ProfilerController')->only([
                    'index',
                    'show'
                ]);
            });
            // Route::get('nominees', 'NomineesController@index')->name('nominees.index');
            // Route::get('nominees/{nominee}', 'NomineesController@show')->name('nominees.show');
            // Route::put('nominees/{nominee}', 'NomineesController@update')->name('nominees.update');

            Route::model('group', App\Group::class);
            Route::resource('groups', 'GroupsController');
            Route::model('article', App\Article::class);
            Route::resource('articles', 'ArticleController');
            Route::resource('skills', 'SkillsController');
            Route::model('personalitytrait', App\PersonalityTrait::class);
            Route::resource('personalitytraits', 'PersonalityTraitsController');
            Route::resource('values', 'ValuesController');
            Route::resource('interests', 'InterestsController');
            Route::get('studentimport/{school}', 'StudentsController@import')->name('studentimport');
            Route::get('studentimport', 'StudentsController@import');
            Route::post('studentimport/{school}', 'StudentsController@importStudent');
            Route::post('studentimport/', 'StudentsController@importStudent');
            Route::resource('jobcategories', 'GroupIndustryController');
            Route::post('getindustries', 'GroupIndustryController@getIndustries');
            Route::model('parentcategory', App\ParentCategory::class);
            Route::resource('parentcategories', 'ParentCategoriesController');
            Route::model('parenttemplate', App\ParentTemplate::class);
            Route::resource('parenttemplates', 'ParentTemplatesController');
            Route::model('magazine', App\Magazine::class);
            Route::resource('magazines', 'MagazinesController');
            Route::resource('gameplanQuestions', GameplanQuestionController::class); //added for newGameplan admin page
            Route::delete('/gameplanQuestions/options/{optionId}', [GameplanQuestionController::class, 'deleteOption']);

            // Route::resource('teachers', 'TeachersController');

            Route::resource('markers', 'MarkerController');
            Route::model('teacherresource', App\TeacherResource::class);
            Route::resource('teacherresources', 'TeacherResourcesController')->except('index');
            Route::model('teacherresourcetype', App\TeacherResourceType::class);
            Route::resource('teacherresourcetypes', 'TeacherResourceTypesController');
            Route::model('teacherresourcegroup', App\TeacherResourceGroup::class);
            Route::resource('teacherresourcegroups', 'TeacherResourceGroupsController');
            Route::model('teachingarea', App\TeachingArea::class);
            Route::resource('teachingareas', 'TeachingAreasController');
            Route::view('events/import', 'events.import')->name('events.import');
            Route::post('events/import', 'EventsController@importExcel');
            Route::resource('events', 'EventsController');  /*  Move out of admin routes to show clander for all type of users */
            Route::post('events/filter', 'EventsController@filterState'); /*  Move out of admin routes to show clander for all type of users */
            // Route::view('subjects/import', 'subjects.import')->name('subjects.import');
            // Route::post('subjects/import', 'SubjectsController@importExcel');
            Route::view('subjects/import', 'subjects.import')->name('subjects.import');
            Route::post('subjects/import', 'SubjectsController@saveImport')->name('subjects.saveimport');
            Route::get('/getRelatedContents', "SubjectsController@getRelatedContents");
            Route::get('/getRelatedSubjects', "SubjectsController@getRelatedSubjects")->name('getRelatedSubjects');
            Route::get('get-state-subjects/{state}', 'SubjectsController@getStateSubjects');
            Route::get('subjects/clone/{id}', 'SubjectsController@cloneSubject');

            Route::resource('subjects', 'SubjectsController')
                ->except(['edit', 'create']);

            Route::resource('learning-styles', 'LearningStylesController')
                ->except(['edit', 'create'])
                ->parameters(['learning-styles' => 'learningStyle']);

            Route::resource('subject-types', 'SubjectTypesController')
                ->except(['edit', 'create'])
                ->parameters(['subject-types' => 'subjectType']);

            Route::resource('subject-course-types', 'SubjectCourseTypesController')
                ->except(['edit', 'create'])
                ->parameters(['subject-course-types' => 'subjectCourseType']);

            Route::resource('subject-areas', 'SubjectAreasController')
                ->except(['edit', 'create'])
                ->parameters(['subject-areas' => 'subjectArea']);

            Route::resource('ssq-skills', 'SsqSkillsController')
                ->except(['edit', 'create'])
                ->parameters(['ssq-skills' => 'ssqSkill']);

            Route::resource('ssq-interests', 'SsqInterestsController')
                ->except(['edit', 'create'])
                ->parameters(['ssq-interests' => 'ssqInterest']);

            Route::resource('ssq-languages', 'SsqLanguagesController')
                ->except(['edit', 'create'])
                ->parameters(['ssq-languages' => 'ssqLanguage']);

            Route::resource('ssq-jobs', 'SsqJobsController')
                ->except(['edit', 'create'])
                ->parameters(['ssq-jobs' => 'ssqJob']);

            Route::resource('ssq-study-habits', 'SsqStudyHabitsController')
                ->except(['edit', 'create'])
                ->parameters(['ssq-study-habit' => 'ssqStudyHabit']);

            Route::resource('parents', 'ParentsController');
            Route::get('getParents', 'ParentsController@serverSide');
            Route::get('parents/children/{id}', 'ParentsController@showChildren');
            Route::put('parents/children/{id}', 'ParentsController@updateChildren');

            Route::model('edition', App\MagazineEdition::class);
            Route::resource('editions', 'MagazineEditionsController');
            Route::post('/courses/search', 'CoursesController@search')->name('coursesearch');
            Route::get('/courses/clearfilters', 'CoursesController@search')->name('clearfilters');
            Route::get('courses/import', 'CoursesController@import')->name('courses.import');
            Route::post('courses/import', 'CoursesController@saveimport')->name('courses.saveimport');
            Route::get('courses/export', 'CoursesController@export')->name('courses.export');
            Route::post('courses/bulkedit', 'CoursesController@editCourses');
            Route::resource('courses', 'CoursesController');
            Route::post('toggleVisit', 'SchoolsController@toggleVisit');
            Route::model('workexperiencetemplate', App\WorkexperienceTemplate::class);
            Route::resource('workexperiencetemplates', 'WorkexperienceTemplateController');
            // Route::get('wew/responses/workexp', 'WorkexperienceTemplateController@responses')->name('student.responses');
            // Route::put('workexperiencetemplates/feedback/{id}', 'WorkexperienceTemplateController@storeFeedback');
            Route::get('workexperiencetemplates/message/{id}', 'WorkexperienceTemplateController@getMessage');
            Route::put('workexperiencetemplates/message/{id}', 'WorkexperienceTemplateController@storeMessage');

            Route::model('workhealthsafetytemplate', App\WorkhealthsafetyTemplate::class);
            Route::resource('workhealthsafetytemplates', 'WorkhealthsafetyTemplateController');
            Route::model('skillstrainingtemplate', App\SkillstrainingTemplate::class);
            Route::resource('skillstrainingtemplates', 'SkillstrainingTemplateController');
            Route::model('resumesinterviewstemplate', App\ResumesinterviewsTemplate::class);
            Route::resource('resumesinterviewstemplates', 'ResumesinterviewsTemplateController');
            Route::model('workreadytemplate', App\WorkreadyTemplate::class);
            Route::resource('workreadytemplates', 'WorkreadyTemplateController');

            Route::resource('banners', BannersController::class);

            Route::resource('children', 'ChildrenController');
            // Route::get('child-invitation/{token}', 'Auth\ParentChildLicenseController@confirm');
            Route::post('invite-parent', 'ChildrenController@inviteParent');
            Route::get('child-invitees-reset', 'ClosuresController@childInviteesReset');

            Route::model('media', App\Media::class);
            Route::resource('medias', 'MediaController');
            Route::get('getMedia', 'MediaController@serverSide');


            Route::model('cloudvideo', App\CloudVideo::class);
            Route::resource('cloudvideos', 'CloudVideosController');

            Route::model('seopage', App\SeoPage::class);
            Route::resource('seopages', 'SeoPagesController');
            Route::resource('revslider', 'RevsliderController');

            Route::prefix('thingstoknow')->name('thingstoknow.')->group(function () {
                Route::resource('categories', 'ThingstoknowCategoriesController');
                Route::resource('templates', 'ThingstoknowTemplateController');
            });
            Route::get('thingstoknow/alltemplates', 'ThingstoknowCategoriesController@showTemplates')->name('thingstoknowtemplates.all');
            Route::get('thingstoknowtemplatesget/add/{id}', 'TemplatesController@thingstoknowtemplateCreate')->name('thingstoknowtemplateget.create');
            Route::get('thingstoknowtemplatesget/edit/{id}', 'TemplatesController@thingstoknowtemplateEdit')->name('thingstoknowtemplateget.edit');

            Route::get('getThingstoknowKeywords', 'ThingstoknowTemplateController@getThingstoknowKeywords');

            Route::get('thingstoknowtemplates/{category}/create', 'ThingstoknowCategoriesController@createTemplate')->name('thingstoknowtemplates.create');
            Route::post('thingstoknowtemplates/store/{category}', 'ThingstoknowCategoriesController@storeTemplate')->name('thingstoknowtemplates.store');
            Route::get('thingstoknowtemplate/{id}/edit', 'ThingstoknowCategoriesController@editTemplate')->name('thingstoknowtemplates.edit');
            Route::put('thingstoknowtemplate/{id}', 'ThingstoknowCategoriesController@updateTemplate')->name('thingstoknowtemplates.update');
            Route::delete('/thingstoknowcategory/{category}/thingstoknowtemplate/{template}/', 'ThingstoknowCategoriesController@detachTemplate')->name('thingstoknowtemplates.delete');
            Route::delete('thingstoknow/faqimg/{id}', 'ThingstoknowCategoriesController@destroyFactandfaqimg');
            Route::delete('thingstoknow/virtualtour/{id}', 'ThingstoknowCategoriesController@destroyVirtualtour');
            Route::delete('thingstoknow/video/{id}', 'ThingstoknowCategoriesController@destroyVideo');
            Route::delete('thingstoknow/gallery-img/{id}', 'ThingstoknowCategoriesController@destroyGalleryimg');
            Route::delete('thingstoknow/phonecall/{id}', 'ThingstoknowCategoriesController@destroyPhonecall');
            Route::post('thingstoknowtemplates/assign/{industry}', 'ThingstoknowCategoriesController@assignTemplate')->name('thingstoknowcategory.assign');

            Route::get('getNominees', 'NomineesController@serverSide');

            Route::model('cvtype', App\CvType::class);
            Route::resource('cvtypes', 'CvTypesController');

            Route::model('cvsection', App\CvSection::class);
            Route::resource('cvsections', 'CvSectionsController');

            Route::model('gallery', App\Gallery::class);
            Route::resource('galleries', 'GalleryController');

            Route::model('schoolvisittemplate', App\SchoolvisitTemplate::class);
            Route::resource('schoolvisittemplates', 'SchoolvisitTemplatesController');
            Route::get('checkSchoolSlugExist', 'ClosuresController@checkSchoolSlug');
            Route::model('cvtemplate', App\CvTemplate::class);
            Route::resource('cvtemplates', 'CvTemplatesController');

            Route::model('cvtip', App\CvTip::class);
            Route::resource('cvtips', 'CvTipsController');
            Route::post('getTipsSections', 'CvTipsController@getSections');

            //User Index
            Route::get('userIndex', 'HomeController@userIndex')->name('userIndex');
            Route::delete('userDestroy/{id}', 'HomeController@userDestroy')->name('userDestroy');

            //User Index
            Route::get('users', 'HomeController@users')->name('users');
            Route::delete('userDestroy/{id}', 'HomeController@userDestroy')->name('userDestroy');
            Route::get('usersProfile', 'ProfilesController@usersProfile')->name('users.profile');

            // Route::get('set-keywords-group', 'ClosuresController@setKeywordsGroup');

            Route::get('sync-scholarships', 'ScholarshipsController@sync');

            Route::get('lessons/responses', 'LessonsController@responses');
            Route::get('lessons/responses/feedback/{id}', 'LessonsController@getFeedback');
            Route::get('lessons/responses/{id}/download', 'LessonsController@downloadResponses')->name('lessons.responsedownload');
            Route::delete('lessons/responses/{id}', 'LessonsController@destroyResponse')->name('lessons.responsedelete');
            Route::resource('lessons', 'LessonsController');
            Route::get('getLessons', 'LessonsController@serverSide');

            //Target Group//
            Route::model('targetgroup', App\Targetgroup::class);
            Route::resource('targetgroups', 'TargetgroupController');

            //Target Group//
            Route::model('fieldofstudy', App\Fieldofstudy::class);
            Route::resource('fieldofstudies', 'FieldsofstudyController');

            Route::get('industryunitStudentEnquiries', 'ExploreIndustriesController@getStudentEnquiries')->name('industryunit.studentEnquiries');
            Route::get('industryunitParentEnquiries', 'ExploreIndustriesController@getParentEnquiries')->name('industryunit.parentEnquiries');
            Route::delete('industryunitEnquiries/{id}', 'ExploreIndustriesController@destroyEnquire');
            Route::get('industryunitParentEmails', 'ExploreIndustriesController@getParentEmails')->name('industryunit.parentEmails');
            Route::delete('industryunitParentEmail/{id}', 'ExploreIndustriesController@destroyParentEmail');
            Route::get('delete-empty-enquires', 'ExploreIndustriesController@deleteEmptyEnquires');

            Route::get('delete-empty-enquires', 'ExploreIndustriesController@deleteEmptyEnquires');

            Route::get('delete-empty-enquires', 'ExploreIndustriesController@deleteEmptyEnquires');

            Route::get('scholarships/{id}/toggle-pick', 'ScholarshipsController@togglePick')->name('scholarships.toggle-pick');
            Route::post('scholarshipsBulkImport', 'ScholarshipsController@imports')->name('scholarships.import');

            Route::view('scholarshipshelprequests', 'scholarships.admins.helprequests');
            Route::get('serversideHelpRequests', 'ScholarshipsController@serversideHelpRequests');
            Route::delete('scholarshipshelprequests/{id}', 'ScholarshipsController@destroyHelpRequest');
            Route::resource('scholarships', 'ScholarshipsController')->except(['index', 'show']);
            Route::resource('scholarshiptips', 'ScholarshiptipsController');
            Route::model('scholarshipprovider', App\ScholarshipProvider::class);
            Route::resource('scholarshipproviders', 'ScholarshipProvidersController');
            Route::get('scholarship/nominees', 'ScholarshipNominationController@nominees')->name('scholarship.nominees');
            Route::get('getScholarshipNominees', 'ScholarshipNominationController@serverSide');
            Route::delete('scholarship/nominee/{id}', 'ScholarshipNominationController@deleteNominee');

            Route::get('interview/nominees', 'InterviewNominationController@nominees')->name('interview.nominees');
            Route::get('getInterviewNominees', 'InterviewNominationController@serverSide');
            Route::delete('interview/nominee/{id}', 'InterviewNominationController@deleteNominee');


            Route::get('scholarshipprovider/{id}/filter', 'ScholarshipProvidersController@filter')->name('scholarshipprovider.filter');
            Route::resource('webinar', 'WebinarController');
            // Stats routes...
            // Route::get('stats/{id}', '\Canvas\Http\Controllers\StatsController@show')->name('canvas.stats.show');



            // Route::prefix('canvas')->group(function () {
            //     Route::get('/', '\Canvas\Http\Controllers\StatsController@index')->name('canvas.index');
            //     // Post routes...
            //     Route::get('posts', '\Canvas\Http\Controllers\PostController@index')->name('canvas.post.index');
            //     Route::get('posts/create', '\Canvas\Http\Controllers\PostController@create')->name('canvas.post.create');
            //     Route::post('posts', '\Canvas\Http\Controllers\PostController@store')->name('canvas.post.store');
            //     Route::get('posts/{id}/edit', '\Canvas\Http\Controllers\PostController@edit')->name(('canvas.post.edit'));
            //     Route::put('posts/{id}', '\Canvas\Http\Controllers\PostController@update')->name('canvas.post.update');
            //     Route::delete('posts/{id}', '\Canvas\Http\Controllers\PostController@destroy')->name('canvas.post.destroy');

            //     // Tag routes...
            //     Route::get('tags', '\Canvas\Http\Controllers\TagController@index')->name('canvas.tag.index');
            //     Route::get('tags/create', '\Canvas\Http\Controllers\TagController@create')->name('canvas.tag.create');
            //     Route::post('tags', '\Canvas\Http\Controllers\TagController@store')->name('canvas.tag.store');
            //     Route::get('tags/{id}/edit', '\Canvas\Http\Controllers\TagController@edit')->name(('canvas.tag.edit'));
            //     Route::put('tags/{id}', '\Canvas\Http\Controllers\TagController@update')->name('canvas.tag.update');
            //     Route::delete('tags/{id}', '\Canvas\Http\Controllers\TagController@destroy')->name('canvas.tag.destroy');

            //     // // Topic routes...
            //     Route::get('topics', '\Canvas\Http\Controllers\TopicController@index')->name('canvas.topic.index');
            //     Route::get('topics/create', '\Canvas\Http\Controllers\TopicController@create')->name('canvas.topic.create');
            //     Route::post('topics', '\Canvas\Http\Controllers\TopicController@store')->name('canvas.topic.store');
            //     Route::get('topics/{id}/edit', '\Canvas\Http\Controllers\TopicController@edit')->name('canvas.topic.edit');
            //     Route::put('topics/{id}', '\Canvas\Http\Controllers\TopicController@update')->name('canvas.topic.update');
            //     Route::delete('topics/{id}', '\Canvas\Http\Controllers\TopicController@destroy')->name('canvas.topic.destroy');
            // });

            Route::get('teacher/notices', 'NoticeboardController@teacherNotices')->name('teacher.notices');
            Route::get('staff-notices', 'NoticeboardController@staffNotices')->name('staff.notices');
            Route::get('teacher/teachernotice/{id}', 'NoticeboardController@teacherNotice');
            Route::put('teacher/teachernotice/{id}', 'NoticeboardController@updateTeacherNotice');

            Route::resource('quizzes', 'QuizController');

            Route::resource('questions', 'QuizquestionController');

            Route::resource('roles', 'RolesController');

            Route::resource('experiencecategories', ExperienceCategoriesController::class);

            // Route::get('students-year-rollover', 'StudentsController@yearRollover')->name('yearRollover');
            // Route::get('addVweToUserExperience', 'UserExperiencesController@addVweToUserExperience');
            // Route::get('addExperienceToUserExperience', 'UserExperiencesController@addExperienceToUserExperience');

            Route::get('loginUsingId/{id}', [HomeController::class, 'loginUsingId']);

            Route::get('combineWewModules', 'SkillstrainingTemplateController@combineWewModules');

            Route::resource('skillstrainingcategories', SkillstrainingCategoriesController::class);
            Route::resource('lessoncategories', LessonCategoriesController::class);
            Route::resource('badges', BadgesController::class);
            Route::resource('companies', CompaniesController::class);
            Route::resource('employers', 'EmployersController');
            // Route::get('addVweStepResponses', 'WorkexperienceTemplateController@addStepResponseForCompletedTasks');

            Route::get('newMenuAccess', 'SchoolsController@newMenuAccess');
        });
        /* Superadmin routes Ends */

        /*  Parent's Route start */
        Route::group(['middleware' =>  ['role:Parent']], function () {
            Route::group(['middleware' => ['ensure_parent_subscription_is_active']], function () {
                // Route::get('thingstoknow', 'ParentTemplatesController@thingsToKnow');
                // Route::get('thingstoknow/{template}', 'ParentTemplatesController@showThingsToKnow')->name('thingstoknow.show');
                Route::get('children/{id}/profiling', 'ProfilerController@showProfilerResult')->name('children.profiling');
                Route::get('children/{id}/gameplan', 'GameplansController@studentGameplan')->name('children.gameplan');
                Route::get('children/{id}/timeline', 'StudentsController@timeline')->name('children.timeline');
                Route::put('parent/{id}/industries', 'ProfilesController@saveIndustries')->name('parent-industries.store');
                Route::post('children', 'Auth\ParentChildLicenseController@storeChildren')->name('children.store');
                Route::get('reinvite-child/{invitee}', 'ChildrenController@reinviteChild')->name('child.reinvite');
                Route::get('tasks/{taskId}/child/{studentId}', 'TasksController@studentResponse')->name('tasks.childResponse');
            });

            Route::group(['middleware' => ['parent_subscription_not_active']], function () {
                Route::get('parent/renewlicense/', 'Auth\ParentChildLicenseController@renewlicense')->name('parent.renewlicense');
                Route::post('parent/striperenewlicense/', 'Auth\ParentChildLicenseController@renewparentcheckout')->name('stripe.renewparentcheckout');
                Route::post('parent/renewlicense/', 'Auth\ParentChildLicenseController@storeRenewparent')->name('renewparent.store');
                Route::any('/striperenewparentsuccess', "Auth\ParentChildLicenseController@renewparentsuccess")->name('stripe.renewparentsuccess'); // storing child from parent profile page
            });

            Route::any('/stripechildsuccess', "Auth\ParentChildLicenseController@stripechildsuccess")->name('stripe.childsuccess'); // storing child from parent profile page
            Route::post('renew-toggle', 'Auth\ParentChildLicenseController@renewToggle')->name('renew-toggle');
            Route::post('card-update', 'Auth\ParentChildLicenseController@cardUpdate')->name('card-update');

            Route::get('parent-children', 'ParentsController@parentChildren');
            Route::get('getParentChildren', 'ParentsController@parentChildsserverSide');
            Route::get('childlicense/{id}/autorenew', 'ParentsController@childlicenseRenew')->name('childlicense.autorenew');

            Route::get('/child-license-allocate/{id}', "ParentsController@childLicenseAllocate")->name('child.licenseallocate');
            Route::post('/stripe-purchase-child-session', "Auth\ParentChildLicenseController@stripepurchasechildlicensesession")->name('stripe.purchasechildlicensesession');
            Route::any('/stripepurchasechildsuccess', "Auth\ParentChildLicenseController@stripepurchasechildsuccess")->name('stripe.purchasechildsuccess');
            Route::get('/update-card-session', "Auth\ParentChildLicenseController@updatecardsession")->name('update.cardsession');
            Route::any('/cardUpdateFailed', "Auth\ParentChildLicenseController@stripecardUpdateFailed")->name('stripe.cardUpdateFailed');
            Route::any('/cardUpdateSuccess', "Auth\ParentChildLicenseController@stripecardUpdateSuccess")->name('stripe.cardUpdateSuccess');
        });

        /*  Markers's Route start */
        Route::group(['middleware' =>  ['role:Marker']], function () {
            Route::get('wew/responses/workexp', 'WorkexperienceTemplateController@responses')->name('student.responses');
            // Route::get('workexperiencetemplates/responses/{id}/download', 'WorkexperienceTemplateController@downloadResponses')->name('workexperience.responsedownload');
            Route::put('workexperiencetemplates/feedback/{id}', 'WorkexperienceTemplateController@storeFeedback');
        });

        Route::group(['middleware' =>  ['role:Marker,Teacher,Staff']], function () {
            Route::group(['middleware' =>  ['has_manager_access']], function () {
                Route::get('vwe-certificate/{id}', 'ExploreWorkexperienceController@downloadPDF');
                Route::get('workexperiencetemplates/feedback/{id}', 'WorkexperienceTemplateController@getFeedback');
            });
        });

        /*  Employer's Route start */
        Route::group(['middleware' =>  ['role:Employer']], function () {
            Route::get('employer/get-students', 'Vue\EmployersController@getStudents')->name('employers.get-students');
            Route::get('employer/get-student-state-counts', 'Vue\EmployersController@getStudentStateCounts')->name('employers.get-student-state-counts');
            Route::post('employer/toggle-follow-student', 'Vue\EmployersController@toggleFollowStudent')->name('employers.toggle-follow-student');
            Route::post('employer/get-follow-status', 'Vue\EmployersController@getFollowStatus')->name('employers.get-follow-status');
            Route::get('employer/get-company-modules', 'Vue\EmployersController@getCompanyModules')->name('employers.get-company-modules');
            Route::get('certificate-preview/{id}/{type}', 'Vue\EmployersController@certificatePreview')->name('employers.certificate-preview');

            // Consolidated dashboard data route (single request for all dashboard data)
            Route::get('employer/get-dashboard-data', 'Vue\EmployersController@getDashboardData')->name('employers.get-dashboard-data');
        });
        /*  Employer's Route end */

        Route::get('scholarships/{id}', 'ScholarshipsController@show')->name('scholarships.show')->middleware('plan_must_be_selected'); /* This route conflicts with resource route Therfore it is placed below the resource route */


        Route::resource('plans', 'PlansController')->only(['index', 'store'])->middleware(GamePlanAccess::class);
        Route::resource('storePlans', 'Vue\PlansController')->only(['index', 'store'])->middleware(GamePlanAccess::class);
        Route::post('storePlans', 'Vue\PlansController@store')->middleware(GamePlanAccess::class);

        // Ajax based request
        Route::get('/api/industryTemplates/{industry}', 'Vue\ExploreIndustriesController@industryTemplates');
        Route::post('/api/saveParentIndustries', 'DashboardsController@saveParentIndustries');
        Route::get('/api/getParentIndustries', 'Vue\ExploreIndustriesController@getParentIndustries');
        Route::post('/api/saveParentDashboardChecklist', 'DashboardsController@saveParentDashboardChecklist');
        Route::get('/api/getParentDashboardChecklist', 'DashboardsController@getParentDashboardChecklist');
        Route::get('get-subjects', "ClosuresController@getSubjects");
        Route::get('get-scholarships', "ScholarshipsController@getScholarships");
        Route::get('get-thingstoknowkeywords', "ClosuresController@getKeywords");
        Route::get('imageGallery', "DashboardsController@imageGallery");
        Route::get('recomendations', "DashboardsController@recomendations");
        Route::get('searchResults/{value}', "DashboardsController@searchResults");
        Route::get('getactiveTasks', "DashboardsController@activeTasks");
        Route::get('getnotices', "DashboardsController@noticeboard");
        Route::get('/dashboard/industries', "DashboardsController@industries");

        Route::get('/dashboard/jobfinder', "DashboardsController@jobfinder");
        Route::get('child/{id}/save', 'DashboardsController@childSave')->name('child.save');

        // New Gameplan start.
        Route::get('api/gameplan-questions', [GameplansController::class, 'getQuestions']);
        Route::post('/gameplans', [GameplansController::class, 'store']);
        Route::get('/gameplansSelection', [GameplansController::class, 'getUserSelection']);
        Route::get('get-plans', [GameplansController::class, 'index']);
        Route::get('/getAllCourses',  [GameplansController::class, 'getCourses']);
        Route::get('/api/getinstitutes', [GameplansController::class, 'api_institutes']);
        Route::get('/edit-jobs', [GameplansController::class, 'api_institutes']);


        // New Gameplan end.


        Route::get('/getEmployerDetail', 'Vue\EmployersController@getEmployerDetail');
        Route::post('/employer/{id}', 'Vue\EmployersController@update');
        Route::post('/employer/{employer}/verify-password',  'Vue\EmployersController@verifyCurrentPassword');
        Route::post('/employer/{employer}/update-password',  'Vue\EmployersController@updatePassword');
        Route::post('/account/deactivate', 'Vue\EmployersController@deactivateAccount');
        // Route::get('/completed-lessons', 'Vue\LessonsController@getCompletedLessons');
        Route::get('/completed-modules', 'Vue\EmployersController@getCompletedModules');
        Route::get('/employer/pipeline-timeline','Vue\EmployersController@pipelineTimeline');
    });

    Route::get('/api/getInstituteDetail', [SchoolDetailsController::class, 'api_instituteDetail']);
    Route::get('/api/checkEmail', [SchoolDetailsController::class, 'checkMail']);

    // Route::view('addparents', 'auth.addparents');

    // used in portfolio site also keep these below routes outside of any auth middleware
    Route::get('certificate-download/{id}', 'ExploreWorkexperienceController@downloadPDF'); // created new route was getting access issue on line 1055
    Route::get('certificate-download-skills/{id}', 'WewSkillstrainingController@downloadPDF');

    Route::get('workexperiencetemplates/responses/{id}/download', 'WorkexperienceTemplateController@downloadResponses')->name('workexperience.responsedownload');
    Route::get('cvs/{id}/pdf/{type?}', 'CvsController@pdf')->name('cvs.pdf');
    // </portfolio routes end>


    Route::post('getInTouch', 'HomeController@getInTouch')->name('getInTouch');
    Route::post('parentlaunch', 'HomeController@parentLaunch')->name('parentlaunch.subscribe');

    Route::get('assessment/{token}', 'NomineesController@assessment');
    Route::post('assessment/{token}', 'NomineesController@saveassessment');

    Route::get('/register/confirm', 'Auth\RegisterConfirmationController@index')->name('register.confirm');

    Route::get('school-order', 'Auth\SchoolLicensesController@viewOrder');
    Route::post('school-order', 'Auth\SchoolLicensesController@storeOrder');
    Route::get('school-visit', 'Auth\SchoolLicensesController@viewVisit');
    Route::post('school-visit', 'Auth\SchoolLicensesController@storeVisit');
    // Route::get('school/select-school', 'Auth\RegisterController@selectschool');
    Route::redirect('school/select-school', '/');
    Route::get('school/search', 'Auth\RegisterController@schoolsearchresult');
    Route::get('school/search-email', 'Auth\RegisterController@searchEmail');
    Route::get('school/{slug}', 'Auth\RegisterController@school');
    Route::post('school/{slug}', 'Auth\RegisterController@validateSchoolDetail');
    Route::any('createaccount', 'Auth\RegisterController@createaccount')->name("createaccount");
    Route::get("autoregister/{token}", 'Auth\RegisterController@autoregister')->name('autoregister');
    Route::get('connectschool', 'Auth\RegisterController@showConnectschool');
    Route::post('connectschool', 'Auth\RegisterController@connectschool')->name("connectschool");

    // Route::get('organisation/select-org', 'Auth\OrganisationRegisterController@selectOrg');
    // Route::get('organisation/search', 'Auth\OrganisationRegisterController@searchresult');
    Route::get('organisation/search-email', 'Auth\OrganisationRegisterController@searchEmail');
    Route::get('organisation/{slug}', 'Auth\OrganisationRegisterController@organisation');
    Route::post('organisation/{slug}', 'Auth\OrganisationRegisterController@validateOrg');
    Route::post('createorgaccount', 'Auth\OrganisationRegisterController@createaccount')->name("createorgaccount");
    Route::get('connectorganisation', 'Auth\OrganisationRegisterController@showConnectOrg');
    Route::post('connectorganisation', 'Auth\OrganisationRegisterController@connectOrg')->name("connectorganisation");


    Route::redirect('individual/buylicense', '/', 301);

    // Route::view('individual/buylicense', 'auth.license.individuallicense');
    // Route::view('buyparentlicense', 'auth.license.buyparentlicense');
    Route::post('individual-license', 'Auth\IndividualLicenseController@storeIndividualLicense')->name('individual-license');
    Route::post('/stripe-checkout-individual-session', "Auth\IndividualLicenseController@stripeCheckoutSession")->name('stripe.checkoutindividualsession');
    Route::any('/stripe-individual-success', "Auth\IndividualLicenseController@stripeSuccess")->name('stripe.individualsuccess');
    Route::any('/stripe-individual-failed', "Auth\IndividualLicenseController@stripeFailed")->name('stripe.individualfailed');
    // Route::view('individual', 'frontend.individual');
    Route::any('/stripe-checkout-individual-login', "Auth\IndividualLicenseController@stripeCheckoutLogin")->name('stripe.checkoutindividuallogin');
    Route::any('/stripe-individual-success-login', "Auth\IndividualLicenseController@stripeSuccessLogin")->name('stripe.individualsuccesslogin');

    Route::get('parent/buylicense/{token}', 'Auth\ParentChildLicenseController@parentBuyLicense');
    Route::redirect('parent/buylicense', '/', 301);

    Route::get('parent/invitation/{sendtoparent}/{token}', 'ExploreIndustriesController@parentInvitation');
    Route::post('parent-license', 'Auth\ParentChildLicenseController@storeParentLicense')->name('parent-license');


    Route::post('/stripe-checkout-session', "Auth\ParentChildLicenseController@stripecheckoutsession")->name('stripe.checkoutsession');
    Route::post('/stripe-checkout-parent-session', "Auth\ParentChildLicenseController@stripecheckoutparentsession")->name('stripe.checkoutparentsession');
    Route::any('/stripeparentchildsuccess', "Auth\ParentChildLicenseController@stripeparentchildsuccess")->name('stripe.parentchildsuccess');

    Route::any('/stripechildfailed', "Auth\ParentChildLicenseController@stripechildfailed")->name('stripe.childfailed');

    Route::get('validate-coupon/{code}',  'Auth\ParentChildLicenseController@validateCoupon');
    Route::get('remove-child/{id}', "Auth\ParentChildLicenseController@removechildlicense")->name('removechildlicense');

    // Route::view('individual/search-lastname', 'auth.license.searchlastname');
    Route::get('child-invitation/{token}', 'Auth\ParentChildLicenseController@confirm')->middleware('auth');

    Route::get('individual/get-lastname', 'Auth\IndividualLicenseController@searchlastname');
    // Route::post('individual/createaccount', 'Auth\IndividualLicenseController@createaccount');
    Route::get('individual/createaccount/{token}', 'Auth\IndividualLicenseController@createaccount');
    Route::put('individual/createaccount', 'Auth\IndividualLicenseController@register');
    Route::post('invite-parent', 'ChildrenController@inviteParent')->middleware('auth');

    Route::get('login/{id}', 'Auth\RegisterController@login');


    /* Ajax Based Requests */
    Route::get('/api/tafe', "ClosuresController@api_tafe");
    Route::get('/api/university', "ClosuresController@api_university");
    Route::get('/api/college', "ClosuresController@api_college");

    Route::get('/api/institutes', "ClosuresController@api_institutes");



    Route::get('/getUnitKeywords', "ClosuresController@getUnitKeywords");

    Route::get('searchParentsByName', "ClosuresController@searchParentsByName");

    Route::get('/searchUnitKeyword', "ClosuresController@searchUnitKeyword");

    Route::get('/searchThingstoknowKeyword', "ClosuresController@searchThingstoknowKeyword");

    Route::get('/getSkillKeywords', "ClosuresController@getSkillKeywords");
    Route::get('/getLearningAreas', "ClosuresController@getLearningAreas");
    Route::get('/getGeneralCapabilities', "ClosuresController@getGeneralCapabilities");
    Route::get('/downloadTeacherManual/{id}', 'SkillstrainingTemplateController@downloadTeacherManual')->name("downloadTeacherManual");

    Route::get('/getGroupKeywords', "ClosuresController@getGroupKeywords");

    Route::get('/searchTemplate', "ClosuresController@searchTemplate");
    Route::get('/searchThingstoknowTemplate', "ClosuresController@searchThingstoknowTemplate");

    Route::get('/checkUserName', "ClosuresController@checkUserName")->name('checkUserName');

    Route::get('/checkEmail', "ClosuresController@checkEmail")->name('checkEmail');


    Route::get('/checkMarkerEmail', "ClosuresController@checkMarkerEmail")->name('checkMarkerEmail');

    Route::get('/checkStudentExist', "ClosuresController@checkStudentExist")->name('checkStudentExist');
    Route::get('/checkParentExist', "ClosuresController@checkParentExist")->name('checkParentExist');
    Route::get('/checkParentEmail', "ClosuresController@checkParentEmail")->name('checkParentEmail');

    Route::get('/checkFollowerEmail', "ClosuresController@checkFollowerEmail")->name('checkFollowerEmail');

    Route::get('/checkEmailOnUpdate', "ClosuresController@checkEmailOnUpdate")->name('checkEmailOnUpdate');

    Route::get('/checkSchoolName', "ClosuresController@checkSchoolName")->name('checkSchoolName');

    Route::get('/checkSchoolNameOnUpdate', "ClosuresController@checkSchoolNameOnUpdate")->name('checkSchoolNameOnUpdate');

    Route::get('/checkOrganisationName', "ClosuresController@checkOrganisationName")->name('checkOrganisationName');

    Route::get('/checkOrganisationNameOnUpdate', "ClosuresController@checkOrganisationNameOnUpdate")->name('checkOrganisationNameOnUpdate');

    Route::get('/api/events', "ClosuresController@api_events");
    // Route::post('events', 'EventsController@addEvent');
    Route::get('planPopup', "ClosuresController@planPopup");

    Route::get("/get-states", "ClosuresController@getStates")->name('getStates');
    Route::get("/getSchoolCampuses", "ClosuresController@getSchoolCampuses")->name('getSchoolCampuses');

    Route::get("/fetchAllCompanies", "ClosuresController@fetchAllCompanies")->name('fetchAllCompanies');
    Route::get("/fetchAllStates", "ClosuresController@fetchAllStates")->name('fetchAllStates');


    // Route::get('/subscribe', function () {
    //     return view('auth/license/subscribe');
    // });
    // Route::post('/subscribe_process', 'Auth\IndividualLicenseController@subscribe_process');

    //Contact
    Route::post('/contact', 'DashboardsController@contactUs');

    // Route::prefix('blog')->group(function () {
    //     Route::get('/', 'BlogController@index')->name('blog.index');
    //     Route::middleware('App\Http\Middleware\ViewThrottle')->get('{slug}', 'BlogController@post')->name('blog.post');
    //     Route::get('tag/{slug}', 'BlogController@tag')->name('blog.tag');
    //     Route::get('topic/{slug}', 'BlogController@topic')->name('blog.topic');
    // });

    Route::any('/aws/sns/ses', 'TCDSesController@handle');
    // Route::view('e-portfolio', 'eportfolio.index');
    // Route::view('e-portfolio/design-content', 'eportfolio.design_content');
    // Route::view('e-portfolio/template', 'eportfolio.templates.template_1');

    Route::get('compass-user', [PrimarySchoolsController::class, 'authenticateCompassUser']);

    Route::get('wew/skillstraining/{id}', 'WewSkillstrainingController@show')->name('wew.skillstraining.show');
    // Route::get('wew/compassskillstraining/{id}', [WewSkillstrainingController::class, 'show']);


    /*
        |--------------------------------------------------------------------------
        | ANZSCO Occupation Routes
        |--------------------------------------------------------------------------
    */
    Route::group(['prefix' => 'anzsco-occupations', 'as' => 'anzsco-occupations.'], function () {
        Route::get('/search', [AnzscoOccupationController::class, 'search'])->name('search');
    });

    /*
        |--------------------------------------------------------------------------
        | Specialist Tasks Routes
        |--------------------------------------------------------------------------
    */
    Route::group(['prefix' => 'specialist-tasks', 'as' => 'specialist-tasks.'], function () {
        Route::get('/search', [SpecialistTaskController::class, 'search'])->name('search');
    });

    Route::post('/register/validate-email', [RegisterationController::class, 'validateEmail']);
});
