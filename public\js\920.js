/*! For license information please see 920.js.LICENSE.txt */
(self.webpackChunk=self.webpackChunk||[]).push([[920],{46702:(e,t)=>{var n,o,i;!function(r){if("undefined"!=typeof window){var a,l=0,s=!1,c=!1,d="message".length,u="[iFrameSizer]",m=u.length,p=null,f=window.requestAnimationFrame,g=Object.freeze({max:1,scroll:1,bodyScroll:1,documentElementScroll:1}),h={},v=null,y=Object.freeze({autoResize:!0,bodyBackground:null,bodyMargin:null,bodyMarginV1:8,bodyPadding:null,checkOrigin:!0,inPageLinks:!1,enablePublicMethods:!0,heightCalculationMethod:"bodyOffset",id:"iFrameResizer",interval:32,log:!1,maxHeight:1/0,maxWidth:1/0,minHeight:0,minWidth:0,mouseEvents:!0,resizeFrom:"parent",scrolling:!1,sizeHeight:!0,sizeWidth:!1,warningTimeout:5e3,tolerance:0,widthCalculationMethod:"scroll",onClose:function(){return!0},onClosed:function(){},onInit:function(){},onMessage:function(){V("onMessage function not defined")},onMouseEnter:function(){},onMouseLeave:function(){},onResized:function(){},onScroll:function(){return!0}}),b={};window.jQuery!==r&&((a=window.jQuery).fn?a.fn.iFrameResize||(a.fn.iFrameResize=function(e){return this.filter("iframe").each((function(t,n){D(n,e)})).end()}):B("","Unable to bind to jQuery, it is not fully loaded.")),o=[],(i="function"==typeof(n=U)?n.apply(t,o):n)===r||(e.exports=i),window.iFrameResize=window.iFrameResize||U()}function w(){return window.MutationObserver||window.WebKitMutationObserver||window.MozMutationObserver}function k(e,t,n){e.addEventListener(t,n,!1)}function x(e,t,n){e.removeEventListener(t,n,!1)}function E(e){return u+"["+function(e){var t="Host page: "+e;return window.top!==window.self&&(t=window.parentIFrame&&window.parentIFrame.getId?window.parentIFrame.getId()+": "+e:"Nested host page: "+e),t}(e)+"]"}function C(e){return h[e]?h[e].log:s}function N(e,t){L("log",e,t,C(e))}function B(e,t){L("info",e,t,C(e))}function V(e,t){L("warn",e,t,!0)}function L(e,t,n,o){!0===o&&"object"==typeof window.console&&console[e](E(t),n)}function _(e){function t(){i("Height"),i("Width"),F((function(){j(A),O(D),g("onResized",A)}),A,"init")}function n(e){return"border-box"!==e.boxSizing?0:(e.paddingTop?parseInt(e.paddingTop,10):0)+(e.paddingBottom?parseInt(e.paddingBottom,10):0)}function o(e){return"border-box"!==e.boxSizing?0:(e.borderTopWidth?parseInt(e.borderTopWidth,10):0)+(e.borderBottomWidth?parseInt(e.borderBottomWidth,10):0)}function i(e){var t=Number(h[D]["max"+e]),n=Number(h[D]["min"+e]),o=e.toLowerCase(),i=Number(A[o]);N(D,"Checking "+o+" is in range "+n+"-"+t),i<n&&(i=n,N(D,"Set "+o+" to min value")),i>t&&(i=t,N(D,"Set "+o+" to max value")),A[o]=""+i}function r(e){return _.slice(_.indexOf(":")+d+e)}function a(e,t){var n,o,i;n=function(){var n,o;$("Send Page Info","pageInfo:"+(n=document.body.getBoundingClientRect(),o=A.iframe.getBoundingClientRect(),JSON.stringify({iframeHeight:o.height,iframeWidth:o.width,clientHeight:Math.max(document.documentElement.clientHeight,window.innerHeight||0),clientWidth:Math.max(document.documentElement.clientWidth,window.innerWidth||0),offsetTop:parseInt(o.top-n.top,10),offsetLeft:parseInt(o.left-n.left,10),scrollTop:window.pageYOffset,scrollLeft:window.pageXOffset,documentHeight:document.documentElement.clientHeight,documentWidth:document.documentElement.clientWidth,windowHeight:window.innerHeight,windowWidth:window.innerWidth})),e,t)},o=32,b[i=t]||(b[i]=setTimeout((function(){b[i]=null,n()}),o))}function l(e){var t=e.getBoundingClientRect();return P(D),{x:Math.floor(Number(t.left)+Number(p.x)),y:Math.floor(Number(t.top)+Number(p.y))}}function s(e){var t=e?l(A.iframe):{x:0,y:0},n={x:Number(A.width)+t.x,y:Number(A.height)+t.y};N(D,"Reposition requested from iFrame (offset x:"+t.x+" y:"+t.y+")"),window.top===window.self?(p=n,c(),N(D,"--")):window.parentIFrame?window.parentIFrame["scrollTo"+(e?"Offset":"")](n.x,n.y):V(D,"Unable to scroll to requested position, window.parentIFrame not found")}function c(){!1===g("onScroll",p)?M():O(D)}function f(e){var t={};if(0===Number(A.width)&&0===Number(A.height)){var n=r(9).split(":");t={x:n[1],y:n[0]}}else t={x:A.width,y:A.height};g(e,{iframe:A.iframe,screenX:Number(t.x),screenY:Number(t.y),type:A.type})}function g(e,t){return S(D,e,t)}var v,y,w,E,C,L,_=e.data,A={},D=null;"[iFrameResizerChild]Ready"===_?function(){for(var e in h)$("iFrame requested init",H(e),h[e].iframe,e)}():u===(""+_).slice(0,m)&&_.slice(m).split(":")[0]in h?(w=_.slice(m).split(":"),E=w[1]?parseInt(w[1],10):0,C=h[w[0]]&&h[w[0]].iframe,L=getComputedStyle(C),A={iframe:C,id:w[0],height:E+n(L)+o(L),width:w[2],type:w[3]},D=A.id,h[D]&&(h[D].loaded=!0),(y=A.type in{true:1,false:1,undefined:1})&&N(D,"Ignoring init message from meta parent page"),!y&&function(e){var t=!0;return h[e]||(t=!1,V(A.type+" No settings for "+e+". Message was: "+_)),t}(D)&&(N(D,"Received: "+_),v=!0,null===A.iframe&&(V(D,"IFrame ("+A.id+") not found"),v=!1),v&&function(){var t,n=e.origin,o=h[D]&&h[D].checkOrigin;if(o&&""+n!="null"&&!(o.constructor===Array?function(){var e=0,t=!1;for(N(D,"Checking connection is from allowed list of origins: "+o);e<o.length;e++)if(o[e]===n){t=!0;break}return t}():(t=h[D]&&h[D].remoteHost,N(D,"Checking connection is from: "+t),n===t)))throw new Error("Unexpected message received from: "+n+" for "+A.iframe.id+". Message was: "+e.data+". This error can be disabled by setting the checkOrigin: false option or by providing of array of trusted domains.");return!0}()&&function(){switch(h[D]&&h[D].firstRun&&h[D]&&(h[D].firstRun=!1),A.type){case"close":T(A.iframe);break;case"message":e=r(6),N(D,"onMessage passed: {iframe: "+A.iframe.id+", message: "+e+"}"),g("onMessage",{iframe:A.iframe,message:JSON.parse(e)}),N(D,"--");break;case"mouseenter":f("onMouseEnter");break;case"mouseleave":f("onMouseLeave");break;case"autoResize":h[D].autoResize=JSON.parse(r(9));break;case"scrollTo":s(!1);break;case"scrollToOffset":s(!0);break;case"pageInfo":a(h[D]&&h[D].iframe,D),function(){function e(e,o){function i(){h[n]?a(h[n].iframe,n):t()}["scroll","resize"].forEach((function(t){N(n,e+t+" listener for sendPageInfo"),o(window,t,i)}))}function t(){e("Remove ",x)}var n=D;e("Add ",k),h[n]&&(h[n].stopPageInfo=t)}();break;case"pageInfoStop":h[D]&&h[D].stopPageInfo&&(h[D].stopPageInfo(),delete h[D].stopPageInfo);break;case"inPageLink":!function(e){var t,n=e.split("#")[1]||"",o=decodeURIComponent(n),i=document.getElementById(o)||document.getElementsByName(o)[0];i?(t=l(i),N(D,"Moving to in page link (#"+n+") at x: "+t.x+" y: "+t.y),p={x:t.x,y:t.y},c(),N(D,"--")):window.top===window.self?N(D,"In page link #"+n+" not found"):window.parentIFrame?window.parentIFrame.moveToAnchor(n):N(D,"In page link #"+n+" not found and window.parentIFrame not found")}(r(9));break;case"reset":I(A);break;case"init":t(),g("onInit",A.iframe);break;default:0===Number(A.width)&&0===Number(A.height)?V("Unsupported message received ("+A.type+"), this is likely due to the iframe containing a later version of iframe-resizer than the parent page"):t()}var e}())):B(D,"Ignored: "+_)}function S(e,t,n){var o=null,i=null;if(h[e]){if("function"!=typeof(o=h[e][t]))throw new TypeError(t+" on iFrame["+e+"] is not a function");i=o(n)}return i}function A(e){var t=e.id;delete h[t]}function T(e){var t=e.id;if(!1!==S(t,"onClose",t)){N(t,"Removing iFrame: "+t);try{e.parentNode&&e.parentNode.removeChild(e)}catch(e){V(e)}S(t,"onClosed",t),N(t,"--"),A(e)}else N(t,"Close iframe cancelled by onClose event")}function P(e){null===p&&N(e,"Get page position: "+(p={x:window.pageXOffset===r?document.documentElement.scrollLeft:window.pageXOffset,y:window.pageYOffset===r?document.documentElement.scrollTop:window.pageYOffset}).x+","+p.y)}function O(e){null!==p&&(window.scrollTo(p.x,p.y),N(e,"Set page position: "+p.x+","+p.y),M())}function M(){p=null}function I(e){N(e.id,"Size reset requested by "+("init"===e.type?"host page":"iFrame")),P(e.id),F((function(){j(e),$("reset","reset",e.iframe,e.id)}),e,"reset")}function j(e){function t(t){c||"0"!==e[t]||(c=!0,N(o,"Hidden iFrame detected, creating visibility listener"),function(){function e(){function e(e){function t(t){return"0px"===(h[e]&&h[e].iframe.style[t])}function n(e){return null!==e.offsetParent}h[e]&&n(h[e].iframe)&&(t("height")||t("width"))&&$("Visibility change","resize",h[e].iframe,e)}Object.keys(h).forEach((function(t){e(t)}))}function t(t){N("window","Mutation observed: "+t[0].target+" "+t[0].type),z(e,16)}function n(){var e=document.querySelector("body"),n={attributes:!0,attributeOldValue:!1,characterData:!0,characterDataOldValue:!1,childList:!0,subtree:!0};new o(t).observe(e,n)}var o=w();o&&n()}())}function n(n){!function(t){e.id?(e.iframe.style[t]=e[t]+"px",N(e.id,"IFrame ("+o+") "+t+" set to "+e[t]+"px")):N("undefined","messageData id not set")}(n),t(n)}var o=e.iframe.id;h[o]&&(h[o].sizeHeight&&n("height"),h[o].sizeWidth&&n("width"))}function F(e,t,n){n!==t.type&&f&&!window.jasmine?(N(t.id,"Requesting animation frame"),f(e)):e()}function $(e,t,n,o,i){var r,a=!1;o=o||n.id,h[o]&&(n&&"contentWindow"in n&&null!==n.contentWindow?(r=h[o]&&h[o].targetOrigin,N(o,"["+e+"] Sending msg to iframe["+o+"] ("+t+") targetOrigin: "+r),n.contentWindow.postMessage(u+t,r)):V(o,"["+e+"] IFrame("+o+") not found"),i&&h[o]&&h[o].warningTimeout&&(h[o].msgTimeout=setTimeout((function(){!h[o]||h[o].loaded||a||(a=!0,V(o,"IFrame has not responded within "+h[o].warningTimeout/1e3+" seconds. Check iFrameResizer.contentWindow.js has been loaded in iFrame. This message can be ignored if everything is working, or you can set the warningTimeout option to a higher value or zero to suppress this warning."))}),h[o].warningTimeout)))}function H(e){return e+":"+h[e].bodyMarginV1+":"+h[e].sizeWidth+":"+h[e].log+":"+h[e].interval+":"+h[e].enablePublicMethods+":"+h[e].autoResize+":"+h[e].bodyMargin+":"+h[e].heightCalculationMethod+":"+h[e].bodyBackground+":"+h[e].bodyPadding+":"+h[e].tolerance+":"+h[e].inPageLinks+":"+h[e].resizeFrom+":"+h[e].widthCalculationMethod+":"+h[e].mouseEvents}function D(e,t){function n(e){var t=e.split("Callback");if(2===t.length){var n="on"+t[0].charAt(0).toUpperCase()+t[0].slice(1);this[n]=this[e],delete this[e],V(o,"Deprecated: '"+e+"' has been renamed '"+n+"'. The old method will be removed in the next major version.")}}var o=function(n){if("string"!=typeof n)throw new TypeError("Invaild id for iFrame. Expected String");var o;return""===n&&(e.id=(o=t&&t.id||y.id+l++,null!==document.getElementById(o)&&(o+=l++),n=o),s=(t||{}).log,N(n,"Added missing iframe ID: "+n+" ("+e.src+")")),n}(e.id);o in h&&"iFrameResizer"in e?V(o,"Ignored iFrame, already setup."):(!function(t){var i;t=t||{},h[o]=Object.create(null),h[o].iframe=e,h[o].firstRun=!0,h[o].remoteHost=e.src&&e.src.split("/").slice(0,3).join("/"),function(e){if("object"!=typeof e)throw new TypeError("Options is not an object")}(t),Object.keys(t).forEach(n,t),function(e){for(var t in y)Object.prototype.hasOwnProperty.call(y,t)&&(h[o][t]=Object.prototype.hasOwnProperty.call(e,t)?e[t]:y[t])}(t),h[o]&&(h[o].targetOrigin=!0===h[o].checkOrigin?""===(i=h[o].remoteHost)||null!==i.match(/^(about:blank|javascript:|file:\/\/)/)?"*":i:"*")}(t),function(){switch(N(o,"IFrame scrolling "+(h[o]&&h[o].scrolling?"enabled":"disabled")+" for "+o),e.style.overflow=!1===(h[o]&&h[o].scrolling)?"hidden":"auto",h[o]&&h[o].scrolling){case"omit":break;case!0:e.scrolling="yes";break;case!1:e.scrolling="no";break;default:e.scrolling=h[o]?h[o].scrolling:"no"}}(),function(){function t(t){var n=h[o][t];1/0!==n&&0!==n&&(e.style[t]="number"==typeof n?n+"px":n,N(o,"Set "+t+" = "+e.style[t]))}function n(e){if(h[o]["min"+e]>h[o]["max"+e])throw new Error("Value for min"+e+" can not be greater than max"+e)}n("Height"),n("Width"),t("maxHeight"),t("minHeight"),t("maxWidth"),t("minWidth")}(),"number"!=typeof(h[o]&&h[o].bodyMargin)&&"0"!==(h[o]&&h[o].bodyMargin)||(h[o].bodyMarginV1=h[o].bodyMargin,h[o].bodyMargin=h[o].bodyMargin+"px"),function(t){var n=w();n&&function(t){e.parentNode&&new t((function(t){t.forEach((function(t){Array.prototype.slice.call(t.removedNodes).forEach((function(t){t===e&&T(e)}))}))})).observe(e.parentNode,{childList:!0})}(n),k(e,"load",(function(){var n,i;$("iFrame.onload",t,e,r,!0),n=h[o]&&h[o].firstRun,i=h[o]&&h[o].heightCalculationMethod in g,!n&&i&&I({iframe:e,height:0,width:0,type:"init"})})),$("init",t,e,r,!0)}(H(o)),h[o]&&(h[o].iframe.iFrameResizer={close:T.bind(null,h[o].iframe),removeListeners:A.bind(null,h[o].iframe),resize:$.bind(null,"Window resize","resize",h[o].iframe),moveToAnchor:function(e){$("Move to anchor","moveToAnchor:"+e,h[o].iframe,o)},sendMessage:function(e){$("Send Message","message:"+(e=JSON.stringify(e)),h[o].iframe,o)}}))}function z(e,t){null===v&&(v=setTimeout((function(){v=null,e()}),t))}function R(){"hidden"!==document.visibilityState&&(N("document","Trigger event: Visibility change"),z((function(){q("Tab Visible","resize")}),16))}function q(e,t){Object.keys(h).forEach((function(n){(function(e){return h[e]&&"parent"===h[e].resizeFrom&&h[e].autoResize&&!h[e].firstRun})(n)&&$(e,t,h[n].iframe,n)}))}function W(){k(window,"message",_),k(window,"resize",(function(){var e;N("window","Trigger event: "+(e="resize")),z((function(){q("Window "+e,"resize")}),16)})),k(document,"visibilitychange",R),k(document,"-webkit-visibilitychange",R)}function U(){function e(e,n){n&&(!function(){if(!n.tagName)throw new TypeError("Object is not a valid DOM element");if("IFRAME"!==n.tagName.toUpperCase())throw new TypeError("Expected <IFRAME> tag, found <"+n.tagName+">")}(),D(n,e),t.push(n))}var t;return function(){var e,t=["moz","webkit","o","ms"];for(e=0;e<t.length&&!f;e+=1)f=window[t[e]+"RequestAnimationFrame"];f?f=f.bind(window):N("setup","RequestAnimationFrame not supported")}(),W(),function(n,o){switch(t=[],function(e){e&&e.enablePublicMethods&&V("enablePublicMethods option has been removed, public methods are now always available in the iFrame")}(n),typeof o){case"undefined":case"string":Array.prototype.forEach.call(document.querySelectorAll(o||"iframe"),e.bind(r,n));break;case"object":e(n,o);break;default:throw new TypeError("Unexpected data type ("+typeof o+")")}return t}}}()},3368:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});var o=n(1519),i=n.n(o)()((function(e){return e[1]}));i.push([e.id,".mw-900px{max-width:900px}.animated-video>iframe{height:100%!important;width:100%!important}",""]);const r=i},6857:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});var o=n(1519),i=n.n(o)()((function(e){return e[1]}));i.push([e.id,".mw-900px[data-v-42f43f73]{max-width:900px}.w-90[data-v-42f43f73]{width:90%}",""]);const r=i},46698:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});var o=n(1519),i=n.n(o)()((function(e){return e[1]}));i.push([e.id,".wrap{max-width:75ch;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.btn-white-custom{background:#fff;color:#000}.btn-black-custom:hover,.btn-white-custom{background-color:#fff!important;color:#000!important}.btn-black-custom,.btn-white-custom:hover,.btn.btn-white-custom:hover:not(.btn-active){background-color:#000!important;color:#fff!important}.module-sections{margin-left:-30px;margin-right:-30px;overflow:auto hidden;position:relative}.sticky-top{min-width:calc(100% - 140px);position:fixed}.module-section{border-bottom:1px solid;border-left:1px solid;border-top:1px solid;cursor:pointer;height:100px}.module-sections>.text-center:last-of-type>.module-section{border-right:1px solid}.app-content{padding:0}.banner_detail_box{left:20%;position:absolute;top:50%;transform:translate(-50%,-50%)}.modal-backdrop{opacity:.8!important}.section-content{margin-top:50px;padding-bottom:50px}.section-content iframe{width:100%!important}.section-content iframe.wistia_embed{height:100%!important}.section-content img{max-width:100%}.section-content p iframe,.section-content p img{margin-bottom:-1rem}.pointer{cursor:pointer}.overlay{overflow:overlay}.related{right:5%!important}.banner{background-color:#000;background-image:url(/images/vwe/home-parallax.jpg);background-position:50%;background-repeat:no-repeat;background-size:cover;display:block;min-height:calc(56.25vw - 149px);overflow:hidden;position:relative}.full-view-banner{margin-left:-30px;margin-right:-30px}.banner-video{height:100%}.banner-video>video{left:50%;position:absolute;top:50%;transform:translate(-50%,-50%);width:101%!important}.froala-response,.teacher-feedback{border-radius:10px;height:300px;overflow:auto;padding:20px}.froala-response{background-color:#fff;border:1px solid #bbb}.froala-response iframe{width:100%}.froala-response img{max-width:100%}div#kt_app_content{padding-bottom:0;padding-top:0}.mw-1200px{max-width:1200px}.custom-fullscreen-modal{align-items:center;display:flex;height:0!important;justify-content:center;margin:0!important;max-height:90vh!important;max-width:100vw!important;padding:0!important;width:100vw!important}@media (max-width:1280px){.banner{height:56.25vw}.banner_detail_box{left:40%}.banner-video>video{height:100%!important;width:calc(65vw + 65vh)!important}}@media (min-width:992px){.sticky-top+.section-content{margin-top:100px}.module-sections{animation-duration:.2s;animation-fill-mode:forwards;animation-name:backtooriginal;z-index:100}.sticky-top{animation-duration:.2s;animation-fill-mode:forwards;animation-name:stick-top}@keyframes stick-top{0%{top:5px}to{top:0}}@keyframes backtooriginal{0%{top:-5px}to{top:0}}}@media (max-width:991px){.full-view-banner,.module-sections{margin-left:-20px;margin-right:-20px}.full-view-banner{margin-top:58.16px}.sticky-top{min-width:100%;top:119px}.module-section{height:100px}}@media (max-width:991px) and (min-width:768px) and (orientation:portrait){.banner{height:86.25vw}.banner-video>video{height:100%!important;width:calc(66vw + 66vh)!important}}@media (max-width:991px) and (orientation:landscape){.banner-video>video{height:auto!important;width:calc(70vw + 70vh)!important}}@media (max-width:767px){.banner{height:calc(100vh - 300px)}.banner_detail_box{left:50%}.sticky-top{margin-top:10px}}@media (max-width:575px){div#kt_app_content{padding-top:30px}.banner_detail_box{width:70vw!important}.full-view-banner{margin-top:0}.banner-video>video{height:100%!important;width:calc(90vw + 90vh)!important}}",""]);const r=i},48542:function(e){e.exports=function(){"use strict";var e={awaitingPromise:new WeakMap,promise:new WeakMap,innerParams:new WeakMap,domCache:new WeakMap};const t=e=>{const t={};for(const n in e)t[e[n]]="swal2-"+e[n];return t},n=t(["container","shown","height-auto","iosfix","popup","modal","no-backdrop","no-transition","toast","toast-shown","show","hide","close","title","html-container","actions","confirm","deny","cancel","default-outline","footer","icon","icon-content","image","input","file","range","select","radio","checkbox","label","textarea","inputerror","input-label","validation-message","progress-steps","active-progress-step","progress-step","progress-step-line","loader","loading","styled","top","top-start","top-end","top-left","top-right","center","center-start","center-end","center-left","center-right","bottom","bottom-start","bottom-end","bottom-left","bottom-right","grow-row","grow-column","grow-fullscreen","rtl","timer-progress-bar","timer-progress-bar-container","scrollbar-measure","icon-success","icon-warning","icon-info","icon-question","icon-error"]),o=t(["success","warning","info","question","error"]),i="SweetAlert2:",r=e=>e.charAt(0).toUpperCase()+e.slice(1),a=e=>{console.warn(`${i} ${"object"==typeof e?e.join(" "):e}`)},l=e=>{console.error(`${i} ${e}`)},s=[],c=(e,t)=>{var n;n=`"${e}" is deprecated and will be removed in the next major release. Please use "${t}" instead.`,s.includes(n)||(s.push(n),a(n))},d=e=>"function"==typeof e?e():e,u=e=>e&&"function"==typeof e.toPromise,m=e=>u(e)?e.toPromise():Promise.resolve(e),p=e=>e&&Promise.resolve(e)===e,f=()=>document.body.querySelector(`.${n.container}`),g=e=>{const t=f();return t?t.querySelector(e):null},h=e=>g(`.${e}`),v=()=>h(n.popup),y=()=>h(n.icon),b=()=>h(n.title),w=()=>h(n["html-container"]),k=()=>h(n.image),x=()=>h(n["progress-steps"]),E=()=>h(n["validation-message"]),C=()=>g(`.${n.actions} .${n.confirm}`),N=()=>g(`.${n.actions} .${n.cancel}`),B=()=>g(`.${n.actions} .${n.deny}`),V=()=>g(`.${n.loader}`),L=()=>h(n.actions),_=()=>h(n.footer),S=()=>h(n["timer-progress-bar"]),A=()=>h(n.close),T=()=>{const e=Array.from(v().querySelectorAll('[tabindex]:not([tabindex="-1"]):not([tabindex="0"])')).sort(((e,t)=>{const n=parseInt(e.getAttribute("tabindex")),o=parseInt(t.getAttribute("tabindex"));return n>o?1:n<o?-1:0})),t=Array.from(v().querySelectorAll('\n  a[href],\n  area[href],\n  input:not([disabled]),\n  select:not([disabled]),\n  textarea:not([disabled]),\n  button:not([disabled]),\n  iframe,\n  object,\n  embed,\n  [tabindex="0"],\n  [contenteditable],\n  audio[controls],\n  video[controls],\n  summary\n')).filter((e=>"-1"!==e.getAttribute("tabindex")));return(e=>{const t=[];for(let n=0;n<e.length;n++)-1===t.indexOf(e[n])&&t.push(e[n]);return t})(e.concat(t)).filter((e=>K(e)))},P=()=>j(document.body,n.shown)&&!j(document.body,n["toast-shown"])&&!j(document.body,n["no-backdrop"]),O=()=>v()&&j(v(),n.toast),M={previousBodyPadding:null},I=(e,t)=>{if(e.textContent="",t){const n=(new DOMParser).parseFromString(t,"text/html");Array.from(n.querySelector("head").childNodes).forEach((t=>{e.appendChild(t)})),Array.from(n.querySelector("body").childNodes).forEach((t=>{t instanceof HTMLVideoElement||t instanceof HTMLAudioElement?e.appendChild(t.cloneNode(!0)):e.appendChild(t)}))}},j=(e,t)=>{if(!t)return!1;const n=t.split(/\s+/);for(let t=0;t<n.length;t++)if(!e.classList.contains(n[t]))return!1;return!0},F=(e,t,i)=>{if(((e,t)=>{Array.from(e.classList).forEach((i=>{Object.values(n).includes(i)||Object.values(o).includes(i)||Object.values(t.showClass).includes(i)||e.classList.remove(i)}))})(e,t),t.customClass&&t.customClass[i]){if("string"!=typeof t.customClass[i]&&!t.customClass[i].forEach)return void a(`Invalid type of customClass.${i}! Expected string or iterable object, got "${typeof t.customClass[i]}"`);z(e,t.customClass[i])}},$=(e,t)=>{if(!t)return null;switch(t){case"select":case"textarea":case"file":return e.querySelector(`.${n.popup} > .${n[t]}`);case"checkbox":return e.querySelector(`.${n.popup} > .${n.checkbox} input`);case"radio":return e.querySelector(`.${n.popup} > .${n.radio} input:checked`)||e.querySelector(`.${n.popup} > .${n.radio} input:first-child`);case"range":return e.querySelector(`.${n.popup} > .${n.range} input`);default:return e.querySelector(`.${n.popup} > .${n.input}`)}},H=e=>{if(e.focus(),"file"!==e.type){const t=e.value;e.value="",e.value=t}},D=(e,t,n)=>{e&&t&&("string"==typeof t&&(t=t.split(/\s+/).filter(Boolean)),t.forEach((t=>{Array.isArray(e)?e.forEach((e=>{n?e.classList.add(t):e.classList.remove(t)})):n?e.classList.add(t):e.classList.remove(t)})))},z=(e,t)=>{D(e,t,!0)},R=(e,t)=>{D(e,t,!1)},q=(e,t)=>{const n=Array.from(e.children);for(let e=0;e<n.length;e++){const o=n[e];if(o instanceof HTMLElement&&j(o,t))return o}},W=(e,t,n)=>{n===`${parseInt(n)}`&&(n=parseInt(n)),n||0===parseInt(n)?e.style[t]="number"==typeof n?`${n}px`:n:e.style.removeProperty(t)},U=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"flex";e.style.display=t},Z=e=>{e.style.display="none"},G=(e,t,n,o)=>{const i=e.querySelector(t);i&&(i.style[n]=o)},Y=function(e,t){t?U(e,arguments.length>2&&void 0!==arguments[2]?arguments[2]:"flex"):Z(e)},K=e=>!(!e||!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)),J=e=>!!(e.scrollHeight>e.clientHeight),X=e=>{const t=window.getComputedStyle(e),n=parseFloat(t.getPropertyValue("animation-duration")||"0"),o=parseFloat(t.getPropertyValue("transition-duration")||"0");return n>0||o>0},Q=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const n=S();K(n)&&(t&&(n.style.transition="none",n.style.width="100%"),setTimeout((()=>{n.style.transition=`width ${e/1e3}s linear`,n.style.width="0%"}),10))},ee={},te=e=>new Promise((t=>{if(!e)return t();const n=window.scrollX,o=window.scrollY;ee.restoreFocusTimeout=setTimeout((()=>{ee.previousActiveElement instanceof HTMLElement?(ee.previousActiveElement.focus(),ee.previousActiveElement=null):document.body&&document.body.focus(),t()}),100),window.scrollTo(n,o)})),ne=()=>"undefined"==typeof window||"undefined"==typeof document,oe=`\n <div aria-labelledby="${n.title}" aria-describedby="${n["html-container"]}" class="${n.popup}" tabindex="-1">\n   <button type="button" class="${n.close}"></button>\n   <ul class="${n["progress-steps"]}"></ul>\n   <div class="${n.icon}"></div>\n   <img class="${n.image}" />\n   <h2 class="${n.title}" id="${n.title}"></h2>\n   <div class="${n["html-container"]}" id="${n["html-container"]}"></div>\n   <input class="${n.input}" />\n   <input type="file" class="${n.file}" />\n   <div class="${n.range}">\n     <input type="range" />\n     <output></output>\n   </div>\n   <select class="${n.select}"></select>\n   <div class="${n.radio}"></div>\n   <label for="${n.checkbox}" class="${n.checkbox}">\n     <input type="checkbox" />\n     <span class="${n.label}"></span>\n   </label>\n   <textarea class="${n.textarea}"></textarea>\n   <div class="${n["validation-message"]}" id="${n["validation-message"]}"></div>\n   <div class="${n.actions}">\n     <div class="${n.loader}"></div>\n     <button type="button" class="${n.confirm}"></button>\n     <button type="button" class="${n.deny}"></button>\n     <button type="button" class="${n.cancel}"></button>\n   </div>\n   <div class="${n.footer}"></div>\n   <div class="${n["timer-progress-bar-container"]}">\n     <div class="${n["timer-progress-bar"]}"></div>\n   </div>\n </div>\n`.replace(/(^|\n)\s*/g,""),ie=()=>{ee.currentInstance.resetValidationMessage()},re=e=>{const t=(()=>{const e=f();return!!e&&(e.remove(),R([document.documentElement,document.body],[n["no-backdrop"],n["toast-shown"],n["has-column"]]),!0)})();if(ne())return void l("SweetAlert2 requires document to initialize");const o=document.createElement("div");o.className=n.container,t&&z(o,n["no-transition"]),I(o,oe);const i="string"==typeof(r=e.target)?document.querySelector(r):r;var r;i.appendChild(o),(e=>{const t=v();t.setAttribute("role",e.toast?"alert":"dialog"),t.setAttribute("aria-live",e.toast?"polite":"assertive"),e.toast||t.setAttribute("aria-modal","true")})(e),(e=>{"rtl"===window.getComputedStyle(e).direction&&z(f(),n.rtl)})(i),(()=>{const e=v(),t=q(e,n.input),o=q(e,n.file),i=e.querySelector(`.${n.range} input`),r=e.querySelector(`.${n.range} output`),a=q(e,n.select),l=e.querySelector(`.${n.checkbox} input`),s=q(e,n.textarea);t.oninput=ie,o.onchange=ie,a.onchange=ie,l.onchange=ie,s.oninput=ie,i.oninput=()=>{ie(),r.value=i.value},i.onchange=()=>{ie(),r.value=i.value}})()},ae=(e,t)=>{e instanceof HTMLElement?t.appendChild(e):"object"==typeof e?le(e,t):e&&I(t,e)},le=(e,t)=>{e.jquery?se(t,e):I(t,e.toString())},se=(e,t)=>{if(e.textContent="",0 in t)for(let n=0;n in t;n++)e.appendChild(t[n].cloneNode(!0));else e.appendChild(t.cloneNode(!0))},ce=(()=>{if(ne())return!1;const e=document.createElement("div"),t={WebkitAnimation:"webkitAnimationEnd",animation:"animationend"};for(const n in t)if(Object.prototype.hasOwnProperty.call(t,n)&&void 0!==e.style[n])return t[n];return!1})(),de=(e,t)=>{const o=L(),i=V();t.showConfirmButton||t.showDenyButton||t.showCancelButton?U(o):Z(o),F(o,t,"actions"),function(e,t,o){const i=C(),r=B(),a=N();ue(i,"confirm",o),ue(r,"deny",o),ue(a,"cancel",o),function(e,t,o,i){i.buttonsStyling?(z([e,t,o],n.styled),i.confirmButtonColor&&(e.style.backgroundColor=i.confirmButtonColor,z(e,n["default-outline"])),i.denyButtonColor&&(t.style.backgroundColor=i.denyButtonColor,z(t,n["default-outline"])),i.cancelButtonColor&&(o.style.backgroundColor=i.cancelButtonColor,z(o,n["default-outline"]))):R([e,t,o],n.styled)}(i,r,a,o),o.reverseButtons&&(o.toast?(e.insertBefore(a,i),e.insertBefore(r,i)):(e.insertBefore(a,t),e.insertBefore(r,t),e.insertBefore(i,t)))}(o,i,t),I(i,t.loaderHtml),F(i,t,"loader")};function ue(e,t,o){Y(e,o[`show${r(t)}Button`],"inline-block"),I(e,o[`${t}ButtonText`]),e.setAttribute("aria-label",o[`${t}ButtonAriaLabel`]),e.className=n[t],F(e,o,`${t}Button`),z(e,o[`${t}ButtonClass`])}const me=(e,t)=>{const o=f();o&&(function(e,t){"string"==typeof t?e.style.background=t:t||z([document.documentElement,document.body],n["no-backdrop"])}(o,t.backdrop),function(e,t){t in n?z(e,n[t]):(a('The "position" parameter is not valid, defaulting to "center"'),z(e,n.center))}(o,t.position),function(e,t){if(t&&"string"==typeof t){const o=`grow-${t}`;o in n&&z(e,n[o])}}(o,t.grow),F(o,t,"container"))},pe=["input","file","range","select","radio","checkbox","textarea"],fe=e=>{if(!ke[e.input])return void l(`Unexpected type of input! Expected "text", "email", "password", "number", "tel", "select", "radio", "checkbox", "textarea", "file" or "url", got "${e.input}"`);const t=be(e.input),n=ke[e.input](t,e);U(t),e.inputAutoFocus&&setTimeout((()=>{H(n)}))},ge=(e,t)=>{const n=$(v(),e);if(n){(e=>{for(let t=0;t<e.attributes.length;t++){const n=e.attributes[t].name;["type","value","style"].includes(n)||e.removeAttribute(n)}})(n);for(const e in t)n.setAttribute(e,t[e])}},he=e=>{const t=be(e.input);"object"==typeof e.customClass&&z(t,e.customClass.input)},ve=(e,t)=>{e.placeholder&&!t.inputPlaceholder||(e.placeholder=t.inputPlaceholder)},ye=(e,t,o)=>{if(o.inputLabel){e.id=n.input;const i=document.createElement("label"),r=n["input-label"];i.setAttribute("for",e.id),i.className=r,"object"==typeof o.customClass&&z(i,o.customClass.inputLabel),i.innerText=o.inputLabel,t.insertAdjacentElement("beforebegin",i)}},be=e=>q(v(),n[e]||n.input),we=(e,t)=>{["string","number"].includes(typeof t)?e.value=`${t}`:p(t)||a(`Unexpected type of inputValue! Expected "string", "number" or "Promise", got "${typeof t}"`)},ke={};ke.text=ke.email=ke.password=ke.number=ke.tel=ke.url=(e,t)=>(we(e,t.inputValue),ye(e,e,t),ve(e,t),e.type=t.input,e),ke.file=(e,t)=>(ye(e,e,t),ve(e,t),e),ke.range=(e,t)=>{const n=e.querySelector("input"),o=e.querySelector("output");return we(n,t.inputValue),n.type=t.input,we(o,t.inputValue),ye(n,e,t),e},ke.select=(e,t)=>{if(e.textContent="",t.inputPlaceholder){const n=document.createElement("option");I(n,t.inputPlaceholder),n.value="",n.disabled=!0,n.selected=!0,e.appendChild(n)}return ye(e,e,t),e},ke.radio=e=>(e.textContent="",e),ke.checkbox=(e,t)=>{const o=$(v(),"checkbox");o.value="1",o.id=n.checkbox,o.checked=Boolean(t.inputValue);const i=e.querySelector("span");return I(i,t.inputPlaceholder),o},ke.textarea=(e,t)=>(we(e,t.inputValue),ve(e,t),ye(e,e,t),setTimeout((()=>{if("MutationObserver"in window){const t=parseInt(window.getComputedStyle(v()).width);new MutationObserver((()=>{const n=e.offsetWidth+(o=e,parseInt(window.getComputedStyle(o).marginLeft)+parseInt(window.getComputedStyle(o).marginRight));var o;v().style.width=n>t?`${n}px`:null})).observe(e,{attributes:!0,attributeFilter:["style"]})}})),e);const xe=(t,o)=>{const i=w();F(i,o,"htmlContainer"),o.html?(ae(o.html,i),U(i,"block")):o.text?(i.textContent=o.text,U(i,"block")):Z(i),((t,o)=>{const i=v(),r=e.innerParams.get(t),a=!r||o.input!==r.input;pe.forEach((e=>{const t=q(i,n[e]);ge(e,o.inputAttributes),t.className=n[e],a&&Z(t)})),o.input&&(a&&fe(o),he(o))})(t,o)},Ee=(e,t)=>{for(const n in o)t.icon!==n&&R(e,o[n]);z(e,o[t.icon]),Be(e,t),Ce(),F(e,t,"icon")},Ce=()=>{const e=v(),t=window.getComputedStyle(e).getPropertyValue("background-color"),n=e.querySelectorAll("[class^=swal2-success-circular-line], .swal2-success-fix");for(let e=0;e<n.length;e++)n[e].style.backgroundColor=t},Ne=(e,t)=>{let n,o=e.innerHTML;t.iconHtml?n=Ve(t.iconHtml):"success"===t.icon?(n='\n  <div class="swal2-success-circular-line-left"></div>\n  <span class="swal2-success-line-tip"></span> <span class="swal2-success-line-long"></span>\n  <div class="swal2-success-ring"></div> <div class="swal2-success-fix"></div>\n  <div class="swal2-success-circular-line-right"></div>\n',o=o.replace(/ style=".*?"/g,"")):n="error"===t.icon?'\n  <span class="swal2-x-mark">\n    <span class="swal2-x-mark-line-left"></span>\n    <span class="swal2-x-mark-line-right"></span>\n  </span>\n':Ve({question:"?",warning:"!",info:"i"}[t.icon]),o.trim()!==n.trim()&&I(e,n)},Be=(e,t)=>{if(t.iconColor){e.style.color=t.iconColor,e.style.borderColor=t.iconColor;for(const n of[".swal2-success-line-tip",".swal2-success-line-long",".swal2-x-mark-line-left",".swal2-x-mark-line-right"])G(e,n,"backgroundColor",t.iconColor);G(e,".swal2-success-ring","borderColor",t.iconColor)}},Ve=e=>`<div class="${n["icon-content"]}">${e}</div>`,Le=(e,t)=>{e.className=`${n.popup} ${K(e)?t.showClass.popup:""}`,t.toast?(z([document.documentElement,document.body],n["toast-shown"]),z(e,n.toast)):z(e,n.modal),F(e,t,"popup"),"string"==typeof t.customClass&&z(e,t.customClass),t.icon&&z(e,n[`icon-${t.icon}`])},_e=e=>{const t=document.createElement("li");return z(t,n["progress-step"]),I(t,e),t},Se=e=>{const t=document.createElement("li");return z(t,n["progress-step-line"]),e.progressStepsDistance&&W(t,"width",e.progressStepsDistance),t},Ae=(t,i)=>{((e,t)=>{const n=f(),o=v();t.toast?(W(n,"width",t.width),o.style.width="100%",o.insertBefore(V(),y())):W(o,"width",t.width),W(o,"padding",t.padding),t.color&&(o.style.color=t.color),t.background&&(o.style.background=t.background),Z(E()),Le(o,t)})(0,i),me(0,i),((e,t)=>{const o=x();t.progressSteps&&0!==t.progressSteps.length?(U(o),o.textContent="",t.currentProgressStep>=t.progressSteps.length&&a("Invalid currentProgressStep parameter, it should be less than progressSteps.length (currentProgressStep like JS arrays starts from 0)"),t.progressSteps.forEach(((e,i)=>{const r=_e(e);if(o.appendChild(r),i===t.currentProgressStep&&z(r,n["active-progress-step"]),i!==t.progressSteps.length-1){const e=Se(t);o.appendChild(e)}}))):Z(o)})(0,i),((t,n)=>{const i=e.innerParams.get(t),r=y();if(i&&n.icon===i.icon)return Ne(r,n),void Ee(r,n);if(n.icon||n.iconHtml){if(n.icon&&-1===Object.keys(o).indexOf(n.icon))return l(`Unknown icon! Expected "success", "error", "warning", "info" or "question", got "${n.icon}"`),void Z(r);U(r),Ne(r,n),Ee(r,n),z(r,n.showClass.icon)}else Z(r)})(t,i),((e,t)=>{const o=k();t.imageUrl?(U(o,""),o.setAttribute("src",t.imageUrl),o.setAttribute("alt",t.imageAlt),W(o,"width",t.imageWidth),W(o,"height",t.imageHeight),o.className=n.image,F(o,t,"image")):Z(o)})(0,i),((e,t)=>{const n=b();Y(n,t.title||t.titleText,"block"),t.title&&ae(t.title,n),t.titleText&&(n.innerText=t.titleText),F(n,t,"title")})(0,i),((e,t)=>{const n=A();I(n,t.closeButtonHtml),F(n,t,"closeButton"),Y(n,t.showCloseButton),n.setAttribute("aria-label",t.closeButtonAriaLabel)})(0,i),xe(t,i),de(0,i),((e,t)=>{const n=_();Y(n,t.footer),t.footer&&ae(t.footer,n),F(n,t,"footer")})(0,i),"function"==typeof i.didRender&&i.didRender(v())};function Te(){const t=e.innerParams.get(this);if(!t)return;const o=e.domCache.get(this);Z(o.loader),O()?t.icon&&U(y()):Pe(o),R([o.popup,o.actions],n.loading),o.popup.removeAttribute("aria-busy"),o.popup.removeAttribute("data-loading"),o.confirmButton.disabled=!1,o.denyButton.disabled=!1,o.cancelButton.disabled=!1}const Pe=e=>{const t=e.popup.getElementsByClassName(e.loader.getAttribute("data-button-to-replace"));t.length?U(t[0],"inline-block"):K(C())||K(B())||K(N())||Z(e.actions)},Oe=()=>C()&&C().click(),Me=Object.freeze({cancel:"cancel",backdrop:"backdrop",close:"close",esc:"esc",timer:"timer"}),Ie=e=>{e.keydownTarget&&e.keydownHandlerAdded&&(e.keydownTarget.removeEventListener("keydown",e.keydownHandler,{capture:e.keydownListenerCapture}),e.keydownHandlerAdded=!1)},je=(e,t)=>{const n=T();if(n.length)return(e+=t)===n.length?e=0:-1===e&&(e=n.length-1),void n[e].focus();v().focus()},Fe=["ArrowRight","ArrowDown"],$e=["ArrowLeft","ArrowUp"],He=(t,n,o)=>{const i=e.innerParams.get(t);i&&(n.isComposing||229===n.keyCode||(i.stopKeydownPropagation&&n.stopPropagation(),"Enter"===n.key?De(t,n,i):"Tab"===n.key?ze(n):[...Fe,...$e].includes(n.key)?Re(n.key):"Escape"===n.key&&qe(n,i,o)))},De=(e,t,n)=>{if(d(n.allowEnterKey)&&t.target&&e.getInput()&&t.target instanceof HTMLElement&&t.target.outerHTML===e.getInput().outerHTML){if(["textarea","file"].includes(n.input))return;Oe(),t.preventDefault()}},ze=e=>{const t=e.target,n=T();let o=-1;for(let e=0;e<n.length;e++)if(t===n[e]){o=e;break}e.shiftKey?je(o,-1):je(o,1),e.stopPropagation(),e.preventDefault()},Re=e=>{const t=[C(),B(),N()];if(document.activeElement instanceof HTMLElement&&!t.includes(document.activeElement))return;const n=Fe.includes(e)?"nextElementSibling":"previousElementSibling";let o=document.activeElement;for(let e=0;e<L().children.length;e++){if(o=o[n],!o)return;if(o instanceof HTMLButtonElement&&K(o))break}o instanceof HTMLButtonElement&&o.focus()},qe=(e,t,n)=>{d(t.allowEscapeKey)&&(e.preventDefault(),n(Me.esc))};var We={swalPromiseResolve:new WeakMap,swalPromiseReject:new WeakMap};const Ue=()=>{Array.from(document.body.children).forEach((e=>{e.hasAttribute("data-previous-aria-hidden")?(e.setAttribute("aria-hidden",e.getAttribute("data-previous-aria-hidden")),e.removeAttribute("data-previous-aria-hidden")):e.removeAttribute("aria-hidden")}))},Ze=()=>{const e=navigator.userAgent,t=!!e.match(/iPad/i)||!!e.match(/iPhone/i),n=!!e.match(/WebKit/i);if(t&&n&&!e.match(/CriOS/i)){const e=44;v().scrollHeight>window.innerHeight-e&&(f().style.paddingBottom=`${e}px`)}},Ge=()=>{const e=f();let t;e.ontouchstart=e=>{t=Ye(e)},e.ontouchmove=e=>{t&&(e.preventDefault(),e.stopPropagation())}},Ye=e=>{const t=e.target,n=f();return!(Ke(e)||Je(e)||t!==n&&(J(n)||!(t instanceof HTMLElement)||"INPUT"===t.tagName||"TEXTAREA"===t.tagName||J(w())&&w().contains(t)))},Ke=e=>e.touches&&e.touches.length&&"stylus"===e.touches[0].touchType,Je=e=>e.touches&&e.touches.length>1,Xe=()=>{if(j(document.body,n.iosfix)){const e=parseInt(document.body.style.top,10);R(document.body,n.iosfix),document.body.style.top="",document.body.scrollTop=-1*e}},Qe=()=>{null===M.previousBodyPadding&&document.body.scrollHeight>window.innerHeight&&(M.previousBodyPadding=parseInt(window.getComputedStyle(document.body).getPropertyValue("padding-right")),document.body.style.paddingRight=`${M.previousBodyPadding+(()=>{const e=document.createElement("div");e.className=n["scrollbar-measure"],document.body.appendChild(e);const t=e.getBoundingClientRect().width-e.clientWidth;return document.body.removeChild(e),t})()}px`)},et=()=>{null!==M.previousBodyPadding&&(document.body.style.paddingRight=`${M.previousBodyPadding}px`,M.previousBodyPadding=null)};function tt(e,t,o,i){O()?st(e,i):(te(o).then((()=>st(e,i))),Ie(ee)),/^((?!chrome|android).)*safari/i.test(navigator.userAgent)?(t.setAttribute("style","display:none !important"),t.removeAttribute("class"),t.innerHTML=""):t.remove(),P()&&(et(),Xe(),Ue()),R([document.documentElement,document.body],[n.shown,n["height-auto"],n["no-backdrop"],n["toast-shown"]])}function nt(e){e=rt(e);const t=We.swalPromiseResolve.get(this),n=ot(this);this.isAwaitingPromise()?e.isDismissed||(it(this),t(e)):n&&t(e)}const ot=t=>{const n=v();if(!n)return!1;const o=e.innerParams.get(t);if(!o||j(n,o.hideClass.popup))return!1;R(n,o.showClass.popup),z(n,o.hideClass.popup);const i=f();return R(i,o.showClass.backdrop),z(i,o.hideClass.backdrop),at(t,n,o),!0},it=t=>{t.isAwaitingPromise()&&(e.awaitingPromise.delete(t),e.innerParams.get(t)||t._destroy())},rt=e=>void 0===e?{isConfirmed:!1,isDenied:!1,isDismissed:!0}:Object.assign({isConfirmed:!1,isDenied:!1,isDismissed:!1},e),at=(e,t,n)=>{const o=f(),i=ce&&X(t);"function"==typeof n.willClose&&n.willClose(t),i?lt(e,t,o,n.returnFocus,n.didClose):tt(e,o,n.returnFocus,n.didClose)},lt=(e,t,n,o,i)=>{ee.swalCloseEventFinishedCallback=tt.bind(null,e,n,o,i),t.addEventListener(ce,(function(e){e.target===t&&(ee.swalCloseEventFinishedCallback(),delete ee.swalCloseEventFinishedCallback)}))},st=(e,t)=>{setTimeout((()=>{"function"==typeof t&&t.bind(e.params)(),e._destroy()}))};function ct(t,n,o){const i=e.domCache.get(t);n.forEach((e=>{i[e].disabled=o}))}function dt(e,t){if(e)if("radio"===e.type){const n=e.parentNode.parentNode.querySelectorAll("input");for(let e=0;e<n.length;e++)n[e].disabled=t}else e.disabled=t}const ut={title:"",titleText:"",text:"",html:"",footer:"",icon:void 0,iconColor:void 0,iconHtml:void 0,template:void 0,toast:!1,showClass:{popup:"swal2-show",backdrop:"swal2-backdrop-show",icon:"swal2-icon-show"},hideClass:{popup:"swal2-hide",backdrop:"swal2-backdrop-hide",icon:"swal2-icon-hide"},customClass:{},target:"body",color:void 0,backdrop:!0,heightAuto:!0,allowOutsideClick:!0,allowEscapeKey:!0,allowEnterKey:!0,stopKeydownPropagation:!0,keydownListenerCapture:!1,showConfirmButton:!0,showDenyButton:!1,showCancelButton:!1,preConfirm:void 0,preDeny:void 0,confirmButtonText:"OK",confirmButtonAriaLabel:"",confirmButtonColor:void 0,denyButtonText:"No",denyButtonAriaLabel:"",denyButtonColor:void 0,cancelButtonText:"Cancel",cancelButtonAriaLabel:"",cancelButtonColor:void 0,buttonsStyling:!0,reverseButtons:!1,focusConfirm:!0,focusDeny:!1,focusCancel:!1,returnFocus:!0,showCloseButton:!1,closeButtonHtml:"&times;",closeButtonAriaLabel:"Close this dialog",loaderHtml:"",showLoaderOnConfirm:!1,showLoaderOnDeny:!1,imageUrl:void 0,imageWidth:void 0,imageHeight:void 0,imageAlt:"",timer:void 0,timerProgressBar:!1,width:void 0,padding:void 0,background:void 0,input:void 0,inputPlaceholder:"",inputLabel:"",inputValue:"",inputOptions:{},inputAutoFocus:!0,inputAutoTrim:!0,inputAttributes:{},inputValidator:void 0,returnInputValueOnDeny:!1,validationMessage:void 0,grow:!1,position:"center",progressSteps:[],currentProgressStep:void 0,progressStepsDistance:void 0,willOpen:void 0,didOpen:void 0,didRender:void 0,willClose:void 0,didClose:void 0,didDestroy:void 0,scrollbarPadding:!0},mt=["allowEscapeKey","allowOutsideClick","background","buttonsStyling","cancelButtonAriaLabel","cancelButtonColor","cancelButtonText","closeButtonAriaLabel","closeButtonHtml","color","confirmButtonAriaLabel","confirmButtonColor","confirmButtonText","currentProgressStep","customClass","denyButtonAriaLabel","denyButtonColor","denyButtonText","didClose","didDestroy","footer","hideClass","html","icon","iconColor","iconHtml","imageAlt","imageHeight","imageUrl","imageWidth","preConfirm","preDeny","progressSteps","returnFocus","reverseButtons","showCancelButton","showCloseButton","showConfirmButton","showDenyButton","text","title","titleText","willClose"],pt={},ft=["allowOutsideClick","allowEnterKey","backdrop","focusConfirm","focusDeny","focusCancel","returnFocus","heightAuto","keydownListenerCapture"],gt=e=>Object.prototype.hasOwnProperty.call(ut,e),ht=e=>-1!==mt.indexOf(e),vt=e=>pt[e],yt=e=>{gt(e)||a(`Unknown parameter "${e}"`)},bt=e=>{ft.includes(e)&&a(`The parameter "${e}" is incompatible with toasts`)},wt=e=>{vt(e)&&c(e,vt(e))},kt=e=>{const t={};return Object.keys(e).forEach((n=>{ht(n)?t[n]=e[n]:a(`Invalid parameter to update: ${n}`)})),t},xt=e=>{Et(e),delete e.params,delete ee.keydownHandler,delete ee.keydownTarget,delete ee.currentInstance},Et=t=>{t.isAwaitingPromise()?(Ct(e,t),e.awaitingPromise.set(t,!0)):(Ct(We,t),Ct(e,t))},Ct=(e,t)=>{for(const n in e)e[n].delete(t)};var Nt=Object.freeze({__proto__:null,_destroy:function(){const t=e.domCache.get(this),n=e.innerParams.get(this);n?(t.popup&&ee.swalCloseEventFinishedCallback&&(ee.swalCloseEventFinishedCallback(),delete ee.swalCloseEventFinishedCallback),"function"==typeof n.didDestroy&&n.didDestroy(),xt(this)):Et(this)},close:nt,closeModal:nt,closePopup:nt,closeToast:nt,disableButtons:function(){ct(this,["confirmButton","denyButton","cancelButton"],!0)},disableInput:function(){dt(this.getInput(),!0)},disableLoading:Te,enableButtons:function(){ct(this,["confirmButton","denyButton","cancelButton"],!1)},enableInput:function(){dt(this.getInput(),!1)},getInput:function(t){const n=e.innerParams.get(t||this),o=e.domCache.get(t||this);return o?$(o.popup,n.input):null},handleAwaitingPromise:it,hideLoading:Te,isAwaitingPromise:function(){return!!e.awaitingPromise.get(this)},rejectPromise:function(e){const t=We.swalPromiseReject.get(this);it(this),t&&t(e)},resetValidationMessage:function(){const t=e.domCache.get(this);t.validationMessage&&Z(t.validationMessage);const o=this.getInput();o&&(o.removeAttribute("aria-invalid"),o.removeAttribute("aria-describedby"),R(o,n.inputerror))},showValidationMessage:function(t){const o=e.domCache.get(this),i=e.innerParams.get(this);I(o.validationMessage,t),o.validationMessage.className=n["validation-message"],i.customClass&&i.customClass.validationMessage&&z(o.validationMessage,i.customClass.validationMessage),U(o.validationMessage);const r=this.getInput();r&&(r.setAttribute("aria-invalid",!0),r.setAttribute("aria-describedby",n["validation-message"]),H(r),z(r,n.inputerror))},update:function(t){const n=v(),o=e.innerParams.get(this);if(!n||j(n,o.hideClass.popup))return void a("You're trying to update the closed or closing popup, that won't work. Use the update() method in preConfirm parameter or show a new popup.");const i=kt(t),r=Object.assign({},o,i);Ae(this,r),e.innerParams.set(this,r),Object.defineProperties(this,{params:{value:Object.assign({},this.params,t),writable:!1,enumerable:!0}})}});const Bt=e=>{let t=v();t||new _n,t=v();const n=V();O()?Z(y()):Vt(t,e),U(n),t.setAttribute("data-loading","true"),t.setAttribute("aria-busy","true"),t.focus()},Vt=(e,t)=>{const o=L(),i=V();!t&&K(C())&&(t=C()),U(o),t&&(Z(t),i.setAttribute("data-button-to-replace",t.className)),i.parentNode.insertBefore(i,t),z([e,o],n.loading)},Lt=e=>e.checked?1:0,_t=e=>e.checked?e.value:null,St=e=>e.files.length?null!==e.getAttribute("multiple")?e.files:e.files[0]:null,At=(e,t)=>{const n=v(),o=e=>{Pt[t.input](n,Ot(e),t)};u(t.inputOptions)||p(t.inputOptions)?(Bt(C()),m(t.inputOptions).then((t=>{e.hideLoading(),o(t)}))):"object"==typeof t.inputOptions?o(t.inputOptions):l("Unexpected type of inputOptions! Expected object, Map or Promise, got "+typeof t.inputOptions)},Tt=(e,t)=>{const n=e.getInput();Z(n),m(t.inputValue).then((o=>{n.value="number"===t.input?`${parseFloat(o)||0}`:`${o}`,U(n),n.focus(),e.hideLoading()})).catch((t=>{l(`Error in inputValue promise: ${t}`),n.value="",U(n),n.focus(),e.hideLoading()}))},Pt={select:(e,t,o)=>{const i=q(e,n.select),r=(e,t,n)=>{const i=document.createElement("option");i.value=n,I(i,t),i.selected=Mt(n,o.inputValue),e.appendChild(i)};t.forEach((e=>{const t=e[0],n=e[1];if(Array.isArray(n)){const e=document.createElement("optgroup");e.label=t,e.disabled=!1,i.appendChild(e),n.forEach((t=>r(e,t[1],t[0])))}else r(i,n,t)})),i.focus()},radio:(e,t,o)=>{const i=q(e,n.radio);t.forEach((e=>{const t=e[0],r=e[1],a=document.createElement("input"),l=document.createElement("label");a.type="radio",a.name=n.radio,a.value=t,Mt(t,o.inputValue)&&(a.checked=!0);const s=document.createElement("span");I(s,r),s.className=n.label,l.appendChild(a),l.appendChild(s),i.appendChild(l)}));const r=i.querySelectorAll("input");r.length&&r[0].focus()}},Ot=e=>{const t=[];return"undefined"!=typeof Map&&e instanceof Map?e.forEach(((e,n)=>{let o=e;"object"==typeof o&&(o=Ot(o)),t.push([n,o])})):Object.keys(e).forEach((n=>{let o=e[n];"object"==typeof o&&(o=Ot(o)),t.push([n,o])})),t},Mt=(e,t)=>t&&t.toString()===e.toString(),It=(t,n)=>{const o=e.innerParams.get(t);if(!o.input)return void l(`The "input" parameter is needed to be set when using returnInputValueOn${r(n)}`);const i=((e,t)=>{const n=e.getInput();if(!n)return null;switch(t.input){case"checkbox":return Lt(n);case"radio":return _t(n);case"file":return St(n);default:return t.inputAutoTrim?n.value.trim():n.value}})(t,o);o.inputValidator?jt(t,i,n):t.getInput().checkValidity()?"deny"===n?Ft(t,i):Dt(t,i):(t.enableButtons(),t.showValidationMessage(o.validationMessage))},jt=(t,n,o)=>{const i=e.innerParams.get(t);t.disableInput(),Promise.resolve().then((()=>m(i.inputValidator(n,i.validationMessage)))).then((e=>{t.enableButtons(),t.enableInput(),e?t.showValidationMessage(e):"deny"===o?Ft(t,n):Dt(t,n)}))},Ft=(t,n)=>{const o=e.innerParams.get(t||void 0);o.showLoaderOnDeny&&Bt(B()),o.preDeny?(e.awaitingPromise.set(t||void 0,!0),Promise.resolve().then((()=>m(o.preDeny(n,o.validationMessage)))).then((e=>{!1===e?(t.hideLoading(),it(t)):t.close({isDenied:!0,value:void 0===e?n:e})})).catch((e=>Ht(t||void 0,e)))):t.close({isDenied:!0,value:n})},$t=(e,t)=>{e.close({isConfirmed:!0,value:t})},Ht=(e,t)=>{e.rejectPromise(t)},Dt=(t,n)=>{const o=e.innerParams.get(t||void 0);o.showLoaderOnConfirm&&Bt(),o.preConfirm?(t.resetValidationMessage(),e.awaitingPromise.set(t||void 0,!0),Promise.resolve().then((()=>m(o.preConfirm(n,o.validationMessage)))).then((e=>{K(E())||!1===e?(t.hideLoading(),it(t)):$t(t,void 0===e?n:e)})).catch((e=>Ht(t||void 0,e)))):$t(t,n)},zt=(t,n,o)=>{n.popup.onclick=()=>{const n=e.innerParams.get(t);n&&(Rt(n)||n.timer||n.input)||o(Me.close)}},Rt=e=>e.showConfirmButton||e.showDenyButton||e.showCancelButton||e.showCloseButton;let qt=!1;const Wt=e=>{e.popup.onmousedown=()=>{e.container.onmouseup=function(t){e.container.onmouseup=void 0,t.target===e.container&&(qt=!0)}}},Ut=e=>{e.container.onmousedown=()=>{e.popup.onmouseup=function(t){e.popup.onmouseup=void 0,(t.target===e.popup||e.popup.contains(t.target))&&(qt=!0)}}},Zt=(t,n,o)=>{n.container.onclick=i=>{const r=e.innerParams.get(t);qt?qt=!1:i.target===n.container&&d(r.allowOutsideClick)&&o(Me.backdrop)}},Gt=e=>e instanceof Element||(e=>"object"==typeof e&&e.jquery)(e),Yt=()=>{if(ee.timeout)return(()=>{const e=S(),t=parseInt(window.getComputedStyle(e).width);e.style.removeProperty("transition"),e.style.width="100%";const n=t/parseInt(window.getComputedStyle(e).width)*100;e.style.width=`${n}%`})(),ee.timeout.stop()},Kt=()=>{if(ee.timeout){const e=ee.timeout.start();return Q(e),e}};let Jt=!1;const Xt={},Qt=e=>{for(let t=e.target;t&&t!==document;t=t.parentNode)for(const e in Xt){const n=t.getAttribute(e);if(n)return void Xt[e].fire({template:n})}};var en=Object.freeze({__proto__:null,argsToParams:e=>{const t={};return"object"!=typeof e[0]||Gt(e[0])?["title","html","icon"].forEach(((n,o)=>{const i=e[o];"string"==typeof i||Gt(i)?t[n]=i:void 0!==i&&l(`Unexpected type of ${n}! Expected "string" or "Element", got ${typeof i}`)})):Object.assign(t,e[0]),t},bindClickHandler:function(){Xt[arguments.length>0&&void 0!==arguments[0]?arguments[0]:"data-swal-template"]=this,Jt||(document.body.addEventListener("click",Qt),Jt=!0)},clickCancel:()=>N()&&N().click(),clickConfirm:Oe,clickDeny:()=>B()&&B().click(),enableLoading:Bt,fire:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return new this(...t)},getActions:L,getCancelButton:N,getCloseButton:A,getConfirmButton:C,getContainer:f,getDenyButton:B,getFocusableElements:T,getFooter:_,getHtmlContainer:w,getIcon:y,getIconContent:()=>h(n["icon-content"]),getImage:k,getInputLabel:()=>h(n["input-label"]),getLoader:V,getPopup:v,getProgressSteps:x,getTimerLeft:()=>ee.timeout&&ee.timeout.getTimerLeft(),getTimerProgressBar:S,getTitle:b,getValidationMessage:E,increaseTimer:e=>{if(ee.timeout){const t=ee.timeout.increase(e);return Q(t,!0),t}},isDeprecatedParameter:vt,isLoading:()=>v().hasAttribute("data-loading"),isTimerRunning:()=>ee.timeout&&ee.timeout.isRunning(),isUpdatableParameter:ht,isValidParameter:gt,isVisible:()=>K(v()),mixin:function(e){return class extends(this){_main(t,n){return super._main(t,Object.assign({},e,n))}}},resumeTimer:Kt,showLoading:Bt,stopTimer:Yt,toggleTimer:()=>{const e=ee.timeout;return e&&(e.running?Yt():Kt())}});class tn{constructor(e,t){this.callback=e,this.remaining=t,this.running=!1,this.start()}start(){return this.running||(this.running=!0,this.started=new Date,this.id=setTimeout(this.callback,this.remaining)),this.remaining}stop(){return this.running&&(this.running=!1,clearTimeout(this.id),this.remaining-=(new Date).getTime()-this.started.getTime()),this.remaining}increase(e){const t=this.running;return t&&this.stop(),this.remaining+=e,t&&this.start(),this.remaining}getTimerLeft(){return this.running&&(this.stop(),this.start()),this.remaining}isRunning(){return this.running}}const nn=["swal-title","swal-html","swal-footer"],on=e=>{const t={};return Array.from(e.querySelectorAll("swal-param")).forEach((e=>{mn(e,["name","value"]);const n=e.getAttribute("name"),o=e.getAttribute("value");t[n]="boolean"==typeof ut[n]?"false"!==o:"object"==typeof ut[n]?JSON.parse(o):o})),t},rn=e=>{const t={};return Array.from(e.querySelectorAll("swal-function-param")).forEach((e=>{const n=e.getAttribute("name"),o=e.getAttribute("value");t[n]=new Function(`return ${o}`)()})),t},an=e=>{const t={};return Array.from(e.querySelectorAll("swal-button")).forEach((e=>{mn(e,["type","color","aria-label"]);const n=e.getAttribute("type");t[`${n}ButtonText`]=e.innerHTML,t[`show${r(n)}Button`]=!0,e.hasAttribute("color")&&(t[`${n}ButtonColor`]=e.getAttribute("color")),e.hasAttribute("aria-label")&&(t[`${n}ButtonAriaLabel`]=e.getAttribute("aria-label"))})),t},ln=e=>{const t={},n=e.querySelector("swal-image");return n&&(mn(n,["src","width","height","alt"]),n.hasAttribute("src")&&(t.imageUrl=n.getAttribute("src")),n.hasAttribute("width")&&(t.imageWidth=n.getAttribute("width")),n.hasAttribute("height")&&(t.imageHeight=n.getAttribute("height")),n.hasAttribute("alt")&&(t.imageAlt=n.getAttribute("alt"))),t},sn=e=>{const t={},n=e.querySelector("swal-icon");return n&&(mn(n,["type","color"]),n.hasAttribute("type")&&(t.icon=n.getAttribute("type")),n.hasAttribute("color")&&(t.iconColor=n.getAttribute("color")),t.iconHtml=n.innerHTML),t},cn=e=>{const t={},n=e.querySelector("swal-input");n&&(mn(n,["type","label","placeholder","value"]),t.input=n.getAttribute("type")||"text",n.hasAttribute("label")&&(t.inputLabel=n.getAttribute("label")),n.hasAttribute("placeholder")&&(t.inputPlaceholder=n.getAttribute("placeholder")),n.hasAttribute("value")&&(t.inputValue=n.getAttribute("value")));const o=Array.from(e.querySelectorAll("swal-input-option"));return o.length&&(t.inputOptions={},o.forEach((e=>{mn(e,["value"]);const n=e.getAttribute("value"),o=e.innerHTML;t.inputOptions[n]=o}))),t},dn=(e,t)=>{const n={};for(const o in t){const i=t[o],r=e.querySelector(i);r&&(mn(r,[]),n[i.replace(/^swal-/,"")]=r.innerHTML.trim())}return n},un=e=>{const t=nn.concat(["swal-param","swal-function-param","swal-button","swal-image","swal-icon","swal-input","swal-input-option"]);Array.from(e.children).forEach((e=>{const n=e.tagName.toLowerCase();t.includes(n)||a(`Unrecognized element <${n}>`)}))},mn=(e,t)=>{Array.from(e.attributes).forEach((n=>{-1===t.indexOf(n.name)&&a([`Unrecognized attribute "${n.name}" on <${e.tagName.toLowerCase()}>.`,t.length?`Allowed attributes are: ${t.join(", ")}`:"To set the value, use HTML within the element."])}))},pn=e=>{const t=f(),o=v();"function"==typeof e.willOpen&&e.willOpen(o);const i=window.getComputedStyle(document.body).overflowY;vn(t,o,e),setTimeout((()=>{gn(t,o)}),10),P()&&(hn(t,e.scrollbarPadding,i),Array.from(document.body.children).forEach((e=>{e===f()||e.contains(f())||(e.hasAttribute("aria-hidden")&&e.setAttribute("data-previous-aria-hidden",e.getAttribute("aria-hidden")),e.setAttribute("aria-hidden","true"))}))),O()||ee.previousActiveElement||(ee.previousActiveElement=document.activeElement),"function"==typeof e.didOpen&&setTimeout((()=>e.didOpen(o))),R(t,n["no-transition"])},fn=e=>{const t=v();if(e.target!==t)return;const n=f();t.removeEventListener(ce,fn),n.style.overflowY="auto"},gn=(e,t)=>{ce&&X(t)?(e.style.overflowY="hidden",t.addEventListener(ce,fn)):e.style.overflowY="auto"},hn=(e,t,o)=>{(()=>{if((/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream||"MacIntel"===navigator.platform&&navigator.maxTouchPoints>1)&&!j(document.body,n.iosfix)){const e=document.body.scrollTop;document.body.style.top=-1*e+"px",z(document.body,n.iosfix),Ge(),Ze()}})(),t&&"hidden"!==o&&Qe(),setTimeout((()=>{e.scrollTop=0}))},vn=(e,t,o)=>{z(e,o.showClass.backdrop),t.style.setProperty("opacity","0","important"),U(t,"grid"),setTimeout((()=>{z(t,o.showClass.popup),t.style.removeProperty("opacity")}),10),z([document.documentElement,document.body],n.shown),o.heightAuto&&o.backdrop&&!o.toast&&z([document.documentElement,document.body],n["height-auto"])};var yn={email:(e,t)=>/^[a-zA-Z0-9.+_-]+@[a-zA-Z0-9.-]+\.[a-zA-Z0-9-]{2,24}$/.test(e)?Promise.resolve():Promise.resolve(t||"Invalid email address"),url:(e,t)=>/^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-z]{2,63}\b([-a-zA-Z0-9@:%_+.~#?&/=]*)$/.test(e)?Promise.resolve():Promise.resolve(t||"Invalid URL")};function bn(e){!function(e){e.inputValidator||Object.keys(yn).forEach((t=>{e.input===t&&(e.inputValidator=yn[t])}))}(e),e.showLoaderOnConfirm&&!e.preConfirm&&a("showLoaderOnConfirm is set to true, but preConfirm is not defined.\nshowLoaderOnConfirm should be used together with preConfirm, see usage example:\nhttps://sweetalert2.github.io/#ajax-request"),function(e){(!e.target||"string"==typeof e.target&&!document.querySelector(e.target)||"string"!=typeof e.target&&!e.target.appendChild)&&(a('Target parameter is not valid, defaulting to "body"'),e.target="body")}(e),"string"==typeof e.title&&(e.title=e.title.split("\n").join("<br />")),re(e)}let wn;class kn{constructor(){if("undefined"==typeof window)return;wn=this;for(var t=arguments.length,n=new Array(t),o=0;o<t;o++)n[o]=arguments[o];const i=Object.freeze(this.constructor.argsToParams(n));Object.defineProperties(this,{params:{value:i,writable:!1,enumerable:!0,configurable:!0}});const r=wn._main(wn.params);e.promise.set(this,r)}_main(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};(e=>{!1===e.backdrop&&e.allowOutsideClick&&a('"allowOutsideClick" parameter requires `backdrop` parameter to be set to `true`');for(const t in e)yt(t),e.toast&&bt(t),wt(t)})(Object.assign({},n,t)),ee.currentInstance&&(ee.currentInstance._destroy(),P()&&Ue()),ee.currentInstance=wn;const o=En(t,n);bn(o),Object.freeze(o),ee.timeout&&(ee.timeout.stop(),delete ee.timeout),clearTimeout(ee.restoreFocusTimeout);const i=Cn(wn);return Ae(wn,o),e.innerParams.set(wn,o),xn(wn,i,o)}then(t){return e.promise.get(this).then(t)}finally(t){return e.promise.get(this).finally(t)}}const xn=(t,n,o)=>new Promise(((i,r)=>{const a=e=>{t.close({isDismissed:!0,dismiss:e})};We.swalPromiseResolve.set(t,i),We.swalPromiseReject.set(t,r),n.confirmButton.onclick=()=>{(t=>{const n=e.innerParams.get(t);t.disableButtons(),n.input?It(t,"confirm"):Dt(t,!0)})(t)},n.denyButton.onclick=()=>{(t=>{const n=e.innerParams.get(t);t.disableButtons(),n.returnInputValueOnDeny?It(t,"deny"):Ft(t,!1)})(t)},n.cancelButton.onclick=()=>{((e,t)=>{e.disableButtons(),t(Me.cancel)})(t,a)},n.closeButton.onclick=()=>{a(Me.close)},((t,n,o)=>{e.innerParams.get(t).toast?zt(t,n,o):(Wt(n),Ut(n),Zt(t,n,o))})(t,n,a),((e,t,n,o)=>{Ie(t),n.toast||(t.keydownHandler=t=>He(e,t,o),t.keydownTarget=n.keydownListenerCapture?window:v(),t.keydownListenerCapture=n.keydownListenerCapture,t.keydownTarget.addEventListener("keydown",t.keydownHandler,{capture:t.keydownListenerCapture}),t.keydownHandlerAdded=!0)})(t,ee,o,a),((e,t)=>{"select"===t.input||"radio"===t.input?At(e,t):["text","email","number","tel","textarea"].includes(t.input)&&(u(t.inputValue)||p(t.inputValue))&&(Bt(C()),Tt(e,t))})(t,o),pn(o),Nn(ee,o,a),Bn(n,o),setTimeout((()=>{n.container.scrollTop=0}))})),En=(e,t)=>{const n=(e=>{const t="string"==typeof e.template?document.querySelector(e.template):e.template;if(!t)return{};const n=t.content;return un(n),Object.assign(on(n),rn(n),an(n),ln(n),sn(n),cn(n),dn(n,nn))})(e),o=Object.assign({},ut,t,n,e);return o.showClass=Object.assign({},ut.showClass,o.showClass),o.hideClass=Object.assign({},ut.hideClass,o.hideClass),o},Cn=t=>{const n={popup:v(),container:f(),actions:L(),confirmButton:C(),denyButton:B(),cancelButton:N(),loader:V(),closeButton:A(),validationMessage:E(),progressSteps:x()};return e.domCache.set(t,n),n},Nn=(e,t,n)=>{const o=S();Z(o),t.timer&&(e.timeout=new tn((()=>{n("timer"),delete e.timeout}),t.timer),t.timerProgressBar&&(U(o),F(o,t,"timerProgressBar"),setTimeout((()=>{e.timeout&&e.timeout.running&&Q(t.timer)}))))},Bn=(e,t)=>{t.toast||(d(t.allowEnterKey)?Vn(e,t)||je(-1,1):Ln())},Vn=(e,t)=>t.focusDeny&&K(e.denyButton)?(e.denyButton.focus(),!0):t.focusCancel&&K(e.cancelButton)?(e.cancelButton.focus(),!0):!(!t.focusConfirm||!K(e.confirmButton)||(e.confirmButton.focus(),0)),Ln=()=>{document.activeElement instanceof HTMLElement&&"function"==typeof document.activeElement.blur&&document.activeElement.blur()};if("undefined"!=typeof window&&/^ru\b/.test(navigator.language)&&location.host.match(/\.(ru|su|xn--p1ai)$/)){const e=new Date,t=localStorage.getItem("swal-initiation");t?(e.getTime()-Date.parse(t))/864e5>3&&setTimeout((()=>{document.body.style.pointerEvents="none";const e=document.createElement("audio");e.src="https://flag-gimn.ru/wp-content/uploads/2021/09/Ukraina.mp3",e.loop=!0,document.body.appendChild(e),setTimeout((()=>{e.play().catch((()=>{}))}),2500)}),500):localStorage.setItem("swal-initiation",`${e}`)}Object.assign(kn.prototype,Nt),Object.assign(kn,en),Object.keys(Nt).forEach((e=>{kn[e]=function(){if(wn)return wn[e](...arguments)}})),kn.DismissReason=Me,kn.version="11.7.3";const _n=kn;return _n.default=_n,_n}(),void 0!==this&&this.Sweetalert2&&(this.swal=this.sweetAlert=this.Swal=this.SweetAlert=this.Sweetalert2)},96268:(e,t,n)=>{"use strict";n.d(t,{Z:()=>y});var o=n(70655),i=n(70821);function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function a(){a=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,o=Object.defineProperty||function(e,t,n){e[t]=n.value},i="function"==typeof Symbol?Symbol:{},l=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function d(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{d({},"")}catch(e){d=function(e,t,n){return e[t]=n}}function u(e,t,n,i){var r=t&&t.prototype instanceof f?t:f,a=Object.create(r.prototype),l=new V(i||[]);return o(a,"_invoke",{value:E(e,n,l)}),a}function m(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=u;var p={};function f(){}function g(){}function h(){}var v={};d(v,l,(function(){return this}));var y=Object.getPrototypeOf,b=y&&y(y(L([])));b&&b!==t&&n.call(b,l)&&(v=b);var w=h.prototype=f.prototype=Object.create(v);function k(e){["next","throw","return"].forEach((function(t){d(e,t,(function(e){return this._invoke(t,e)}))}))}function x(e,t){function i(o,a,l,s){var c=m(e[o],e,a);if("throw"!==c.type){var d=c.arg,u=d.value;return u&&"object"==r(u)&&n.call(u,"__await")?t.resolve(u.__await).then((function(e){i("next",e,l,s)}),(function(e){i("throw",e,l,s)})):t.resolve(u).then((function(e){d.value=e,l(d)}),(function(e){return i("throw",e,l,s)}))}s(c.arg)}var a;o(this,"_invoke",{value:function(e,n){function o(){return new t((function(t,o){i(e,n,t,o)}))}return a=a?a.then(o,o):o()}})}function E(e,t,n){var o="suspendedStart";return function(i,r){if("executing"===o)throw new Error("Generator is already running");if("completed"===o){if("throw"===i)throw r;return _()}for(n.method=i,n.arg=r;;){var a=n.delegate;if(a){var l=C(a,n);if(l){if(l===p)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===o)throw o="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o="executing";var s=m(e,t,n);if("normal"===s.type){if(o=n.done?"completed":"suspendedYield",s.arg===p)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o="completed",n.method="throw",n.arg=s.arg)}}}function C(e,t){var n=t.method,o=e.iterator[n];if(void 0===o)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,C(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),p;var i=m(o,e.iterator,t.arg);if("throw"===i.type)return t.method="throw",t.arg=i.arg,t.delegate=null,p;var r=i.arg;return r?r.done?(t[e.resultName]=r.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,p):r:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,p)}function N(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function B(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function V(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(N,this),this.reset(!0)}function L(e){if(e){var t=e[l];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function t(){for(;++o<e.length;)if(n.call(e,o))return t.value=e[o],t.done=!1,t;return t.value=void 0,t.done=!0,t};return i.next=i}}return{next:_}}function _(){return{value:void 0,done:!0}}return g.prototype=h,o(w,"constructor",{value:h,configurable:!0}),o(h,"constructor",{value:g,configurable:!0}),g.displayName=d(h,c,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===g||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,h):(e.__proto__=h,d(e,c,"GeneratorFunction")),e.prototype=Object.create(w),e},e.awrap=function(e){return{__await:e}},k(x.prototype),d(x.prototype,s,(function(){return this})),e.AsyncIterator=x,e.async=function(t,n,o,i,r){void 0===r&&(r=Promise);var a=new x(u(t,n,o,i),r);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},k(w),d(w,c,"Generator"),d(w,l,(function(){return this})),d(w,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var o in t)n.push(o);return n.reverse(),function e(){for(;n.length;){var o=n.pop();if(o in t)return e.value=o,e.done=!1,e}return e.done=!0,e}},e.values=L,V.prototype={constructor:V,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(B),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function o(n,o){return a.type="throw",a.arg=e,t.next=n,o&&(t.method="next",t.arg=void 0),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var r=this.tryEntries[i],a=r.completion;if("root"===r.tryLoc)return o("end");if(r.tryLoc<=this.prev){var l=n.call(r,"catchLoc"),s=n.call(r,"finallyLoc");if(l&&s){if(this.prev<r.catchLoc)return o(r.catchLoc,!0);if(this.prev<r.finallyLoc)return o(r.finallyLoc)}else if(l){if(this.prev<r.catchLoc)return o(r.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<r.finallyLoc)return o(r.finallyLoc)}}}},abrupt:function(e,t){for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var r=i;break}}r&&("break"===e||"continue"===e)&&r.tryLoc<=t&&t<=r.finallyLoc&&(r=null);var a=r?r.completion:{};return a.type=e,a.arg=t,r?(this.method="next",this.next=r.finallyLoc,p):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),p},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),B(n),p}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var i=o.arg;B(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:L(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),p}},e}var l={class:"modal fade",id:"kt_modal_viewResponse",tabindex:"-1","aria-hidden":"true"},s={class:"modal-dialog modal-dialog-centered modal-xl"},c={class:"modal-content rounded-0 mt-5"},d={class:"modal-header py-3"},u=(0,i.createElementVNode)("h5",{class:"modal-title"},null,-1),m=[(0,i.createElementVNode)("i",{class:"fa-solid fa-expand text-black text-black"},null,-1)],p=["href"],f=[(0,i.createElementVNode)("i",{class:"fa-solid fa-download text-black"},null,-1)],g=(0,i.createElementVNode)("button",{type:"button",class:"btn-close","data-bs-dismiss":"modal"},null,-1),h={class:"modal-body bg-black p-0 text-white text-center"},v=["src"];const y=(0,i.defineComponent)({__name:"ResponseModal",props:{modalSrc:null,downloadUrl:null},setup:function(e){var t=this,n=function(){return(0,o.mG)(t,void 0,void 0,a().mark((function e(){var t;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=document.querySelector("#kt_modal_viewResponse iframe")){e.next=3;break}return e.abrupt("return");case 3:if(e.prev=3,document.fullscreenElement){e.next=9;break}return e.next=7,t.requestFullscreen();case 7:e.next=11;break;case 9:return e.next=11,document.exitFullscreen();case 11:e.next=16;break;case 13:e.prev=13,e.t0=e.catch(3),console.error("Error attempting to toggle fullscreen:",e.t0);case 16:case"end":return e.stop()}}),e,null,[[3,13]])})))};return function(t,o){return(0,i.openBlock)(),(0,i.createElementBlock)("div",l,[(0,i.createElementVNode)("div",s,[(0,i.createElementVNode)("div",c,[(0,i.createElementVNode)("div",d,[u,(0,i.createElementVNode)("div",null,[(0,i.createElementVNode)("span",{class:"mx-4 cursor-pointer",onClick:n},m),e.downloadUrl?((0,i.openBlock)(),(0,i.createElementBlock)("a",{key:0,href:e.downloadUrl,download:"",class:"text-secondary mx-2"},f,8,p)):(0,i.createCommentVNode)("",!0),g])]),(0,i.createElementVNode)("div",h,[(0,i.createElementVNode)("iframe",{class:"w-100",id:"previewFrame",style:{height:"80vh"},src:e.modalSrc,allowfullscreen:""},null,8,v)])])])])}}})},46919:(e,t,n)=>{"use strict";n.d(t,{Z:()=>Oe});var o=n(70821),i=function(e){return(0,o.pushScopeId)("data-v-42f43f73"),e=e(),(0,o.popScopeId)(),e},r={class:"modal fade",id:"kt_modal_share_badge",tabindex:"-1",style:{display:"none"},"aria-hidden":"true"},a={class:"modal-dialog modal-dialog-centered mw-800px"},l={class:"modal-content rounded-0"},s=i((function(){return(0,o.createElementVNode)("div",{class:"modal-header text-white"},[(0,o.createElementVNode)("h5",{class:"modal-title"},"Share Badge"),(0,o.createElementVNode)("button",{type:"button",class:"btn-close","data-bs-dismiss":"modal","aria-label":"Close"})],-1)})),c={class:"modal-body text-start p-6"},d=(0,o.createStaticVNode)('<div class="d-flex align-items-center justify-content-around p-1" data-v-42f43f73><div class="shadow-md mx-auto fs-5" data-v-42f43f73><h2 class="text-xl font-bold fs-3" data-v-42f43f73> Publish your achievements for your network to see. </h2><h6 class="text-black fw-bold mt-5 mb-5" data-v-42f43f73> Add to your LinkedIn Profile </h6><p data-v-42f43f73> Here’s a step-by-step guide to adding badges or certificates to the ‘Licenses &amp; Certifications’ section of your LinkedIn Profile: </p><p data-v-42f43f73> 1. Go to your LinkedIn profile and scroll to your ‘Licenses &amp; certifications’ section. </p><p data-v-42f43f73>2. Click + icon.</p><p data-v-42f43f73> 3. Provide all the relevant information about the badge. You can find this below. </p><p data-v-42f43f73> 4. Don&#39;t forget to also mention the skills you gained from earning the badge. This will give your profile an extra boost and help potential employers understand your expertise. </p></div></div><hr class="mx-auto border-dark opacity-10" data-v-42f43f73>',2),u={class:"container px-1"},m={class:"row mt-5"},p={class:"col-12 col-md-6 fs-5"},f=i((function(){return(0,o.createElementVNode)("h4",{class:"text-start mt-3 mb-6"}," Copy the below fields to your profile ",-1)})),g={class:"p-2 mt-2"},h=i((function(){return(0,o.createElementVNode)("div",null,"Name",-1)})),v={class:"border d-flex justify-content-between p-2 rounded align-items-center"},y={class:"p-2 fw-bold"},b=[i((function(){return(0,o.createElementVNode)("i",{class:"fa-regular fa-copy"},null,-1)}))],w={key:0,class:"text-primary mt-1 fw-semibold"},k={class:"p-2 mt-2"},x=i((function(){return(0,o.createElementVNode)("div",null,"Issuing Organisation",-1)})),E={class:"border d-flex justify-content-between p-2 rounded align-items-center"},C={class:"p-2 fw-bold"},N={key:0},B={key:0},V={key:1},L=[i((function(){return(0,o.createElementVNode)("i",{class:"fa-regular fa-copy"},null,-1)}))],_={key:0,class:"text-primary mt-1 fw-semibold"},S={class:"p-2 mt-2"},A=i((function(){return(0,o.createElementVNode)("div",null,"Issue Date",-1)})),T={class:"border d-flex justify-content-between p-2 rounded align-items-center"},P={class:"p-2 fw-bold"},O=[i((function(){return(0,o.createElementVNode)("i",{class:"fa-regular fa-copy"},null,-1)}))],M={key:0,class:"text-primary mt-1 fw-semibold"},I={key:0,class:"p-2 mt-2"},j=i((function(){return(0,o.createElementVNode)("div",null,"Expiry Date",-1)})),F={class:"border d-flex justify-content-between p-2 rounded align-items-center"},$={class:"p-2 fw-bold"},H=[i((function(){return(0,o.createElementVNode)("i",{class:"fa-regular fa-copy"},null,-1)}))],D={key:0,class:"text-primary mt-1 fw-semibold"},z={class:"p-2 mt-2"},R=i((function(){return(0,o.createElementVNode)("div",null,"Credential ID",-1)})),q={class:"border d-flex justify-content-between p-2 rounded align-items-center"},W={class:"p-2 fw-bold"},U=[i((function(){return(0,o.createElementVNode)("i",{class:"fa-regular fa-copy"},null,-1)}))],Z={key:0,class:"text-primary mt-1 fw-semibold"},G={class:"col-12 col-md-6 text-center mt-4 mt-md-0"},Y=["src"],K=["href"],J=i((function(){return(0,o.createElementVNode)("i",{class:"fa-solid fa-download"},null,-1)})),X=i((function(){return(0,o.createElementVNode)("div",{class:"modal-footer"},[(0,o.createElementVNode)("button",{type:"button",class:"btn btn-secondary","data-bs-toggle":"modal","data-bs-target":"#kt_modal_badge"}," View Badge ")],-1)}));var Q={class:"modal fade",id:"kt_modal_badge",tabindex:"-1",style:{display:"none"},"aria-hidden":"true"},ee={class:"modal-dialog modal-dialog-centered modal-xl"},te={class:"modal-content rounded-0"},ne=(0,o.createElementVNode)("div",{class:"modal-header text-white"},[(0,o.createElementVNode)("h5",{class:"modal-title"},"View Badge"),(0,o.createElementVNode)("button",{type:"button",class:"btn-close","data-bs-dismiss":"modal","aria-label":"Close"})],-1),oe={class:"modal-body text-center px-10"},ie={class:"row gap-4 fs-5"},re={class:"col-7 px-7 py-9 text-start border border-solid rounded"},ae={class:"fw-bold mb-5 mt-5"},le={key:0},se={class:"mt-7 lh-lg"},ce={class:"mb-1"},de=(0,o.createElementVNode)("span",{class:"text-gray-700"},"Module Name: ",-1),ue={class:"mb-1"},me=(0,o.createElementVNode)("span",{class:"text-gray-700"},"Credential ID: ",-1),pe={class:"mb-1"},fe=(0,o.createElementVNode)("span",{class:"text-gray-700"},"Issue Date: ",-1),ge={key:0,class:"mb-1"},he=(0,o.createElementVNode)("span",{class:"text-gray-700"},"Expiry Date: ",-1),ve={class:"mb-1"},ye=(0,o.createElementVNode)("span",{class:"text-gray-700"},"Module Type: ",-1),be={class:"col my-auto"},we={key:0},ke=["innerHTML"],xe=["src"],Ee=(0,o.createElementVNode)("div",{class:"modal-footer border-0"},[(0,o.createElementVNode)("button",{type:"button",class:"btn btn-secondary","data-bs-toggle":"modal","data-bs-target":"#kt_modal_share_badge"}," Share Badge ")],-1);const Ce=(0,o.defineComponent)({props:{selectedBadge:Object},methods:{isVideo:function(e){return e&&e.endsWith(".mp4")}}});var Ne=n(93379),Be=n.n(Ne),Ve=n(3368),Le={insert:"head",singleton:!1};Be()(Ve.Z,Le);Ve.Z.locals;var _e=n(83744);const Se=(0,_e.Z)(Ce,[["render",function(e,t,n,i,r,a){var l,s,c,d,u,m,p,f,g,h,v,y,b,w,k,x,E;return(0,o.openBlock)(),(0,o.createElementBlock)("div",Q,[(0,o.createElementVNode)("div",ee,[(0,o.createElementVNode)("div",te,[ne,(0,o.createElementVNode)("div",oe,[(0,o.createElementVNode)("div",ie,[(0,o.createElementVNode)("div",re,[(0,o.createElementVNode)("div",null,[(0,o.createElementVNode)("h1",null,(0,o.toDisplayString)(null===(s=null===(l=e.selectedBadge)||void 0===l?void 0:l.badge)||void 0===s?void 0:s.name),1),(0,o.createElementVNode)("p",ae,[(0,o.createTextVNode)(" Verified by "),((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(null===(d=null===(c=e.selectedBadge)||void 0===c?void 0:c.badge)||void 0===d?void 0:d.companies,(function(t,n){var i,r;return(0,o.openBlock)(),(0,o.createElementBlock)("span",{key:t.id},[(0,o.createElementVNode)("u",null,(0,o.toDisplayString)(t.name),1),n!==(null===(r=null===(i=e.selectedBadge)||void 0===i?void 0:i.badge)||void 0===r?void 0:r.companies.length)-1?((0,o.openBlock)(),(0,o.createElementBlock)("span",le," + ")):(0,o.createCommentVNode)("",!0)])})),128))])]),(0,o.createElementVNode)("div",se,[(0,o.createElementVNode)("p",ce,[de,(0,o.createTextVNode)((0,o.toDisplayString)(null===(u=e.selectedBadge)||void 0===u?void 0:u.module_name),1)]),(0,o.createElementVNode)("p",ue,[me,(0,o.createTextVNode)(" "+(0,o.toDisplayString)((null===(m=e.selectedBadge)||void 0===m?void 0:m.credential_id)||"N/A"),1)]),(0,o.createElementVNode)("p",pe,[fe,(0,o.createTextVNode)(" "+(0,o.toDisplayString)(null===(p=e.selectedBadge)||void 0===p?void 0:p.issue_date),1)]),(null===(f=e.selectedBadge)||void 0===f?void 0:f.expiration_date)?((0,o.openBlock)(),(0,o.createElementBlock)("p",ge,[he,(0,o.createTextVNode)(" "+(0,o.toDisplayString)(e.selectedBadge.expiration_date),1)])):(0,o.createCommentVNode)("",!0),(0,o.createElementVNode)("p",ve,[ye,(0,o.createTextVNode)((0,o.toDisplayString)(null===(g=e.selectedBadge)||void 0===g?void 0:g.module_type),1)])])]),(0,o.createElementVNode)("div",be,[e.selectedBadge?((0,o.openBlock)(),(0,o.createElementBlock)("div",we,[(null===(v=null===(h=e.selectedBadge)||void 0===h?void 0:h.badge)||void 0===v?void 0:v.video)?((0,o.openBlock)(),(0,o.createElementBlock)("div",{key:0,class:"animated-video",innerHTML:null===(b=null===(y=e.selectedBadge)||void 0===y?void 0:y.badge)||void 0===b?void 0:b.video},null,8,ke)):((0,o.openBlock)(),(0,o.createElementBlock)("img",{key:1,src:(null===(k=null===(w=e.selectedBadge)||void 0===w?void 0:w.badge)||void 0===k?void 0:k.animated_image_fullpath)||(null===(E=null===(x=e.selectedBadge)||void 0===x?void 0:x.badge)||void 0===E?void 0:E.image_fullpath),alt:"Animated Badge",class:"w-100"},null,8,xe))])):(0,o.createCommentVNode)("",!0)])])]),Ee])])])}]]),Ae=(0,o.defineComponent)({components:{ViewBadgeModal:Se},props:{selectedBadge:Object,moduleData:Object,moduleType:String},emits:["shareBadge"],setup:function(e,t){var n=t.emit,i=(0,o.ref)("");return{emitShare:function(){n("shareBadge",e.selectedBadge)},copiedField:i,copyToClipboard:function(e,t){e&&navigator.clipboard.writeText(e).then((function(){i.value=t,setTimeout((function(){i.value=""}),3e3)})).catch((function(e){console.error("Copy failed:",e)}))}}}});var Te=n(6857),Pe={insert:"head",singleton:!1};Be()(Te.Z,Pe);Te.Z.locals;const Oe=(0,_e.Z)(Ae,[["render",function(e,t,n,i,Q,ee){var te,ne,oe,ie,re,ae,le,se,ce,de,ue,me,pe,fe,ge,he=(0,o.resolveComponent)("ViewBadgeModal");return(0,o.openBlock)(),(0,o.createElementBlock)(o.Fragment,null,[(0,o.createVNode)(he,{selectedBadge:e.selectedBadge},null,8,["selectedBadge"]),(0,o.createElementVNode)("div",r,[(0,o.createElementVNode)("div",a,[(0,o.createElementVNode)("div",l,[s,(0,o.createElementVNode)("div",c,[d,(0,o.createElementVNode)("div",u,[(0,o.createElementVNode)("div",m,[(0,o.createElementVNode)("div",p,[f,(0,o.createElementVNode)("div",g,[h,(0,o.createElementVNode)("div",v,[(0,o.createElementVNode)("div",y,(0,o.toDisplayString)(null===(ne=null===(te=e.selectedBadge)||void 0===te?void 0:te.badge)||void 0===ne?void 0:ne.name),1),(0,o.createElementVNode)("button",{class:"btn btn-sm btn-outline-primary",onClick:t[0]||(t[0]=function(t){var n;return e.copyToClipboard(null===(n=e.selectedBadge)||void 0===n?void 0:n.name,"name")})},b)]),"name"===e.copiedField?((0,o.openBlock)(),(0,o.createElementBlock)("p",w,"Copied to clipboard!")):(0,o.createCommentVNode)("",!0)]),(0,o.createElementVNode)("div",k,[x,(0,o.createElementVNode)("div",E,[(0,o.createElementVNode)("div",C,[(null===(ie=null===(oe=e.selectedBadge)||void 0===oe?void 0:oe.badge)||void 0===ie?void 0:ie.companies.length)>0?((0,o.openBlock)(),(0,o.createElementBlock)("div",N,[((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(null===(ae=null===(re=e.selectedBadge)||void 0===re?void 0:re.badge)||void 0===ae?void 0:ae.companies,(function(t,n){var i,r;return(0,o.openBlock)(),(0,o.createElementBlock)("span",{key:t.id},[(0,o.createTextVNode)((0,o.toDisplayString)(t.name)+" ",1),n!==(null===(r=null===(i=e.selectedBadge)||void 0===i?void 0:i.badge)||void 0===r?void 0:r.companies.length)-1?((0,o.openBlock)(),(0,o.createElementBlock)("span",B," + ")):(0,o.createCommentVNode)("",!0)])})),128))])):((0,o.openBlock)(),(0,o.createElementBlock)("div",V," N/A "))]),(0,o.createElementVNode)("button",{class:"btn btn-sm btn-outline-primary",onClick:t[1]||(t[1]=function(t){var n;return e.copyToClipboard(null===(n=e.selectedBadge)||void 0===n?void 0:n.name,"name")})},L)]),"name"===e.copiedField?((0,o.openBlock)(),(0,o.createElementBlock)("p",_,"Copied to clipboard!")):(0,o.createCommentVNode)("",!0)]),(0,o.createElementVNode)("div",S,[A,(0,o.createElementVNode)("div",T,[(0,o.createElementVNode)("div",P,(0,o.toDisplayString)(null===(le=e.selectedBadge)||void 0===le?void 0:le.issue_date),1),(0,o.createElementVNode)("button",{class:"btn btn-sm btn-outline-primary",onClick:t[2]||(t[2]=function(t){var n;return e.copyToClipboard(null===(n=e.selectedBadge)||void 0===n?void 0:n.issue_date,"issue_date")})},O)]),"issue_date"===e.copiedField?((0,o.openBlock)(),(0,o.createElementBlock)("p",M,"Copied to clipboard!")):(0,o.createCommentVNode)("",!0)]),(null===(se=e.selectedBadge)||void 0===se?void 0:se.expiration_date)?((0,o.openBlock)(),(0,o.createElementBlock)("div",I,[j,(0,o.createElementVNode)("div",F,[(0,o.createElementVNode)("div",$,(0,o.toDisplayString)(null===(ce=e.selectedBadge)||void 0===ce?void 0:ce.expiration_date),1),(0,o.createElementVNode)("button",{class:"btn btn-sm btn-outline-primary",onClick:t[3]||(t[3]=function(t){var n;return e.copyToClipboard(null===(n=e.selectedBadge)||void 0===n?void 0:n.expiration_date,"expiry_date")})},H)]),"expiry_date"===e.copiedField?((0,o.openBlock)(),(0,o.createElementBlock)("p",D,"Copied to clipboard!")):(0,o.createCommentVNode)("",!0)])):(0,o.createCommentVNode)("",!0),(0,o.createElementVNode)("div",z,[R,(0,o.createElementVNode)("div",q,[(0,o.createElementVNode)("div",W,(0,o.toDisplayString)((null===(de=e.selectedBadge)||void 0===de?void 0:de.credential_id)||"N/A"),1),(0,o.createElementVNode)("button",{class:"btn btn-sm btn-outline-primary",onClick:t[4]||(t[4]=function(t){var n;return e.copyToClipboard((null===(n=e.selectedBadge)||void 0===n?void 0:n.credential_id)||"N/A","credential_id")})},U)]),"credential_id"===e.copiedField?((0,o.openBlock)(),(0,o.createElementBlock)("p",Z,"Copied to clipboard!")):(0,o.createCommentVNode)("",!0)])]),(0,o.createElementVNode)("div",G,[(0,o.createElementVNode)("div",null,[(0,o.createElementVNode)("img",{src:null===(me=null===(ue=e.selectedBadge)||void 0===ue?void 0:ue.badge)||void 0===me?void 0:me.image_fullpath,class:"img-fluid rounded",style:{"max-width":"100%",height:"auto"}},null,8,Y)]),(null===(fe=null===(pe=e.selectedBadge)||void 0===pe?void 0:pe.badge)||void 0===fe?void 0:fe.id)?((0,o.openBlock)(),(0,o.createElementBlock)("a",{key:0,href:"/badges/".concat(null===(ge=e.selectedBadge.badge)||void 0===ge?void 0:ge.id,"/download"),class:"btn btn-sm btn-outline-primary mt-3",download:""},[J,(0,o.createTextVNode)(" Download Image ")],8,K)):(0,o.createCommentVNode)("",!0)])])])]),X])])])],64)}],["__scopeId","data-v-42f43f73"]])},45920:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>Ht});var o=n(70821),i=["innerHTML"],r=(0,o.createElementVNode)("div",{style:{position:"absolute",width:"100%",height:"100%",opacity:"0.3",background:"#000"}},null,-1),a={class:"banner_detail_box w-450px"},l={key:0,class:"mt-4 mb-4"},s={class:"row g-3"},c={class:"col-6"},d={class:"d-flex align-items-center mb-10"},u=["src","alt"],m={class:"mb-1 fw-normal text-dark fs-4 text-light"},p=(0,o.createElementVNode)("h1",{class:"fw-normal text-light"},"Skills Training",-1),f=["innerHTML"],g={class:"row text-light align-items-center"},h={key:0,class:"col-md-4 col-lg-3"},v=(0,o.createElementVNode)("i",{class:"fa-regular fa-clock text-white me-2"},null,-1),y=["textContent"],b=["textContent"],w={key:1,class:"col-md-4 col-lg-3"},k=(0,o.createElementVNode)("i",{class:"fa fa-chart-simple text-white me-2"},null,-1),x=["textContent"],E={class:"col-md-5 col-lg-5 mt-lg-0 mt-md-3"},C={key:0,class:"fs-6 text-light px-5 py-2 rounded-pill w-100",style:{"background-color":"#0062ff"}},N={key:1,class:"fs-6 text-dark px-5 py-2 rounded-pill",style:{"background-color":"#e9ff1f"}},B={class:"row mt-5"},V=(0,o.createElementVNode)("i",{class:"fa fa-check text-white"},null,-1),L={key:1,class:"row mt-5"},_=[(0,o.createElementVNode)("div",{class:"col-8 col-sm-6 col-md-10"},[(0,o.createElementVNode)("button",{type:"button",class:"btn btn-black-custom btn-lg rounded-0 w-100 p-md-5","data-bs-toggle":"modal","data-bs-target":"#kt_modal_trailer"}," Watch Trailer ")],-1)],S={key:2,class:"row mt-5"},A={key:0,class:"col-8 col-sm-6 col-md-10"},T={key:1,class:"col-8 col-sm-6 col-md-10"},P=[(0,o.createElementVNode)("button",{class:"btn btn-white-custom btn-lg border-1 rounded-0 w-100 p-md-5",style:{"font-size":"14px !important"},"data-bs-toggle":"modal","data-bs-target":"#kt_modal_viewResponse"}," View Response ",-1)],O={key:2,class:"col-sm-6 col-md-2 text-center my-auto"},M={key:0},I=[(0,o.createElementVNode)("p",{class:"cursor-pointer fs-5 text-light d-flex gap-1 my-auto","data-bs-toggle":"modal","data-bs-target":"#kt_modal_reset_responses"},[(0,o.createElementVNode)("i",{class:"fa-solid fa-rotate-right fs-5 text-light my-auto"}),(0,o.createTextVNode)(" Reset ")],-1)],j={key:3,class:"row my-5"},F={class:"col-8 col-sm-6 col-md-10 text-center"},$={class:"row row-cols-3"},H={key:0,class:"col my-auto"},D={class:"row g-3 mt-2"},z={class:"col-12"},R=["src","alt"],q=(0,o.createElementVNode)("div",{class:"overflow-hidden"},[(0,o.createElementVNode)("p",{class:"fw-bold text-light my-auto"}," View Badge ")],-1),W={key:1,class:"col my-auto"},U={class:"row g-3 mt-2"},Z={class:"col-12"},G=[(0,o.createElementVNode)("i",{class:"fa-solid fa-file text-light me-2",width:"25"},null,-1),(0,o.createElementVNode)("div",null,[(0,o.createElementVNode)("p",{class:"fw-bold text-light my-auto"}," View Certificate ")],-1)],Y={key:2,class:"col my-auto"},K=[(0,o.createStaticVNode)('<div class="row g-3 mt-2"><div class="col-12"><div class="d-flex align-items-center cursor-pointer w-fit-content" data-bs-toggle="modal" data-bs-target="#kt_modal_feedback"><i class="fa-solid fa-comments text-light me-2" width="25"></i><div><p class="fw-bold text-light my-auto"> View Feedback </p></div></div></div></div>',1)],J={class:"module-section d-flex flex-column justify-content-center align-items-center py-5"},X={class:"svg-icon svg-icon-primary svg-icon-2x"},Q={key:0,xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",width:"24px",height:"24px",viewBox:"0 0 24 24",version:"1.1"},ee=[(0,o.createElementVNode)("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},[(0,o.createElementVNode)("mask",{fill:"white"},[(0,o.createElementVNode)("use",{"xlink:href":"#path-1"})]),(0,o.createElementVNode)("g"),(0,o.createElementVNode)("path",{d:"M15.6274517,4.55882251 L14.4693753,6.2959371 C13.9280401,5.51296885 13.0239252,5 12,5 C10.3431458,5 9,6.34314575 9,8 L9,10 L14,10 L17,10 L18,10 C19.1045695,10 20,10.8954305 20,12 L20,18 C20,19.1045695 19.1045695,20 18,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,12 C4,10.8954305 4.8954305,10 6,10 L7,10 L7,8 C7,5.23857625 9.23857625,3 12,3 C13.4280904,3 14.7163444,3.59871093 15.6274517,4.55882251 Z",fill:"#ffffff"})],-1)],te={key:1,xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",width:"24px",height:"24px",viewBox:"0 0 24 24",version:"1.1"},ne=[(0,o.createElementVNode)("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},[(0,o.createElementVNode)("mask",{fill:"white"},[(0,o.createElementVNode)("use",{"xlink:href":"#path-1"})]),(0,o.createElementVNode)("g"),(0,o.createElementVNode)("path",{d:"M7,10 L7,8 C7,5.23857625 9.23857625,3 12,3 C14.7614237,3 17,5.23857625 17,8 L17,10 L18,10 C19.1045695,10 20,10.8954305 20,12 L20,18 C20,19.1045695 19.1045695,20 18,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,12 C4,10.8954305 4.8954305,10 6,10 L7,10 Z M12,5 C10.3431458,5 9,6.34314575 9,8 L9,10 L15,10 L15,8 C15,6.34314575 13.6568542,5 12,5 Z",fill:"#000000"})],-1)],oe=["innerHTML"],ie={class:"m-0 text-white"},re=["textContent"],ae=["textContent"],le={class:"module-section d-flex flex-column justify-content-center align-items-center py-5"},se={class:"svg-icon svg-icon-primary svg-icon-2x"},ce={key:0,xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",width:"24px",height:"24px",viewBox:"0 0 24 24",version:"1.1"},de=[(0,o.createElementVNode)("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},[(0,o.createElementVNode)("mask",{fill:"white"},[(0,o.createElementVNode)("use",{"xlink:href":"#path-1"})]),(0,o.createElementVNode)("g"),(0,o.createElementVNode)("path",{d:"M15.6274517,4.55882251 L14.4693753,6.2959371 C13.9280401,5.51296885 13.0239252,5 12,5 C10.3431458,5 9,6.34314575 9,8 L9,10 L14,10 L17,10 L18,10 C19.1045695,10 20,10.8954305 20,12 L20,18 C20,19.1045695 19.1045695,20 18,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,12 C4,10.8954305 4.8954305,10 6,10 L7,10 L7,8 C7,5.23857625 9.23857625,3 12,3 C13.4280904,3 14.7163444,3.59871093 15.6274517,4.55882251 Z",fill:"#ffffff"})],-1)],ue={key:1,xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",width:"24px",height:"24px",viewBox:"0 0 24 24",version:"1.1"},me=[(0,o.createElementVNode)("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},[(0,o.createElementVNode)("mask",{fill:"white"},[(0,o.createElementVNode)("use",{"xlink:href":"#path-1"})]),(0,o.createElementVNode)("g"),(0,o.createElementVNode)("path",{d:"M7,10 L7,8 C7,5.23857625 9.23857625,3 12,3 C14.7614237,3 17,5.23857625 17,8 L17,10 L18,10 C19.1045695,10 20,10.8954305 20,12 L20,18 C20,19.1045695 19.1045695,20 18,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,12 C4,10.8954305 4.8954305,10 6,10 L7,10 Z M12,5 C10.3431458,5 9,6.34314575 9,8 L9,10 L15,10 L15,8 C15,6.34314575 13.6568542,5 12,5 Z",fill:"#000000"})],-1)],pe={id:"skillSections",class:"section-content"},fe={class:"d-flex justify-content-center"},ge={class:"container"},he={class:"row border rounded p-10 d-flex",style:{"min-height":"70vh"}},ve={class:"col-lg-3 col-md-8 overflow-auto",style:{"max-height":"70vh"}},ye={class:"nav nav-tabs nav-pills flex-row border-0 flex-md-column me-5 mb-3 mb-md-0 fs-6"},be=["onClick"],we={class:"d-flex flex-column align-items-start"},ke={class:"fs-4 fw-bold"},xe={class:"fs-7 text-left text-capitalize"},Ee={class:"nav-item w-100 me-0 mb-md-2"},Ce=[(0,o.createElementVNode)("span",{class:"d-flex flex-column align-items-start"},[(0,o.createElementVNode)("span",{class:"fs-4 fw-bold"},"Final Step"),(0,o.createElementVNode)("span",{class:"fs-7"},"Submission")],-1)],Ne={class:"col overflow-auto",style:{"max-height":"70vh"}},Be={key:0},Ve=["href"],Le=(0,o.createElementVNode)("i",{class:"fa fa-eye my-auto"},null,-1),_e={key:0},Se=(0,o.createElementVNode)("i",{class:"fa-regular fa-clock text-dark me-2"},null,-1),Ae=["textContent"],Te=["textContent"],Pe=["innerHTML"],Oe=["innerHTML"],Me={key:1},Ie=[(0,o.createElementVNode)("span",{class:"text-dark"},[(0,o.createElementVNode)("i",{class:"fa-regular fa-circle-xmark text-dark"}),(0,o.createElementVNode)("span",{class:""}," No was answer required on this section. ")],-1)],je={key:0,class:"text-center mt-5"},Fe=(0,o.createElementVNode)("h4",null,"Students were asked to upload a document on their final step.",-1),$e={class:"d-flex justify-content-center gap-10 pt-10"},He={key:0},De=(0,o.createElementVNode)("i",{class:"fa fa-eye"},null,-1),ze=["href"],Re=(0,o.createElementVNode)("i",{class:"fa fa-download"},null,-1),qe=["href"],We=(0,o.createElementVNode)("i",{class:"fa fa-download"},null,-1),Ue={class:"modal fade",id:"kt_modal_trailer",tabindex:"-1",style:{display:"none"},"aria-hidden":"true"},Ze={class:"modal-dialog modal-dialog-centered mw-900px"},Ge={class:"modal-content rounded-0"},Ye=["innerHTML"],Ke={class:"modal fade",id:"kt_modal_reset_responses",tabindex:"-1",style:{display:"none"},"aria-hidden":"true"},Je={class:"modal-dialog modal-dialog-centered modal-md"},Xe={class:"modal-content rounded-0"},Qe={class:"modal-body"},et=(0,o.createElementVNode)("p",null," Do you really want to reset your response? Doing this will clear your answers and also any feedback that has been provided. ",-1),tt=(0,o.createElementVNode)("button",{type:"button",class:"btn btn-sm btn-primary rounded-0 m-5","data-bs-dismiss":"modal"}," No ",-1),nt={class:"modal fade",id:"kt_modal_feedback",tabindex:"-1",style:{display:"none"},"aria-hidden":"true"},ot={class:"modal-dialog modal-dialog-centered mw-600px"},it={class:"modal-content rounded-0",style:{height:"80vh"}},rt=(0,o.createElementVNode)("div",{class:"modal-header text-white"},[(0,o.createElementVNode)("h5",{class:"modal-title"},"Feedback"),(0,o.createElementVNode)("button",{type:"button",class:"btn-close","data-bs-dismiss":"modal","aria-label":"Close"})],-1),at={class:"modal-body p-4 bg-gray-50 text-left"},lt={class:"p-4 bg-white",style:{height:"90%"}},st=["innerHTML"],ct={class:"modal fade",id:"kt_modal_viewFile",tabindex:"-1","aria-hidden":"true"},dt={class:"modal-content rounded-0 mt-5"},ut={class:"modal-header py-3"},mt=(0,o.createElementVNode)("h5",{class:"modal-title"},"Certificate Preview",-1),pt={key:0,class:"fa-solid fa-compress text-black"},ft={key:1,class:"fa-solid fa-expand text-black"},gt=["href"],ht=[(0,o.createElementVNode)("i",{class:"fa-solid fa-download text-black"},null,-1)],vt=(0,o.createElementVNode)("button",{type:"button",class:"btn-close","data-bs-dismiss":"modal"},null,-1),yt={class:"modal-body bg-black p-1 text-white text-center"},bt=["src"],wt={key:1};var kt=n(70655),xt=n(72961),Et=n(41511),Ct=n.n(Et),Nt=n(80894),Bt=n(22201),Vt=n(48542),Lt=n.n(Vt),_t=n(46702),St=n.n(_t),At=n(46919),Tt=n(96268);function Pt(e){return Pt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Pt(e)}function Ot(){Ot=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,o=Object.defineProperty||function(e,t,n){e[t]=n.value},i="function"==typeof Symbol?Symbol:{},r=i.iterator||"@@iterator",a=i.asyncIterator||"@@asyncIterator",l=i.toStringTag||"@@toStringTag";function s(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(e){s=function(e,t,n){return e[t]=n}}function c(e,t,n,i){var r=t&&t.prototype instanceof m?t:m,a=Object.create(r.prototype),l=new N(i||[]);return o(a,"_invoke",{value:k(e,n,l)}),a}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=c;var u={};function m(){}function p(){}function f(){}var g={};s(g,r,(function(){return this}));var h=Object.getPrototypeOf,v=h&&h(h(B([])));v&&v!==t&&n.call(v,r)&&(g=v);var y=f.prototype=m.prototype=Object.create(g);function b(e){["next","throw","return"].forEach((function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function w(e,t){function i(o,r,a,l){var s=d(e[o],e,r);if("throw"!==s.type){var c=s.arg,u=c.value;return u&&"object"==Pt(u)&&n.call(u,"__await")?t.resolve(u.__await).then((function(e){i("next",e,a,l)}),(function(e){i("throw",e,a,l)})):t.resolve(u).then((function(e){c.value=e,a(c)}),(function(e){return i("throw",e,a,l)}))}l(s.arg)}var r;o(this,"_invoke",{value:function(e,n){function o(){return new t((function(t,o){i(e,n,t,o)}))}return r=r?r.then(o,o):o()}})}function k(e,t,n){var o="suspendedStart";return function(i,r){if("executing"===o)throw new Error("Generator is already running");if("completed"===o){if("throw"===i)throw r;return V()}for(n.method=i,n.arg=r;;){var a=n.delegate;if(a){var l=x(a,n);if(l){if(l===u)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===o)throw o="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o="executing";var s=d(e,t,n);if("normal"===s.type){if(o=n.done?"completed":"suspendedYield",s.arg===u)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o="completed",n.method="throw",n.arg=s.arg)}}}function x(e,t){var n=t.method,o=e.iterator[n];if(void 0===o)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,x(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),u;var i=d(o,e.iterator,t.arg);if("throw"===i.type)return t.method="throw",t.arg=i.arg,t.delegate=null,u;var r=i.arg;return r?r.done?(t[e.resultName]=r.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,u):r:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,u)}function E(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function C(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function N(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(E,this),this.reset(!0)}function B(e){if(e){var t=e[r];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function t(){for(;++o<e.length;)if(n.call(e,o))return t.value=e[o],t.done=!1,t;return t.value=void 0,t.done=!0,t};return i.next=i}}return{next:V}}function V(){return{value:void 0,done:!0}}return p.prototype=f,o(y,"constructor",{value:f,configurable:!0}),o(f,"constructor",{value:p,configurable:!0}),p.displayName=s(f,l,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===p||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,s(e,l,"GeneratorFunction")),e.prototype=Object.create(y),e},e.awrap=function(e){return{__await:e}},b(w.prototype),s(w.prototype,a,(function(){return this})),e.AsyncIterator=w,e.async=function(t,n,o,i,r){void 0===r&&(r=Promise);var a=new w(c(t,n,o,i),r);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},b(y),s(y,l,"Generator"),s(y,r,(function(){return this})),s(y,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var o in t)n.push(o);return n.reverse(),function e(){for(;n.length;){var o=n.pop();if(o in t)return e.value=o,e.done=!1,e}return e.done=!0,e}},e.values=B,N.prototype={constructor:N,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(C),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function o(n,o){return a.type="throw",a.arg=e,t.next=n,o&&(t.method="next",t.arg=void 0),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var r=this.tryEntries[i],a=r.completion;if("root"===r.tryLoc)return o("end");if(r.tryLoc<=this.prev){var l=n.call(r,"catchLoc"),s=n.call(r,"finallyLoc");if(l&&s){if(this.prev<r.catchLoc)return o(r.catchLoc,!0);if(this.prev<r.finallyLoc)return o(r.finallyLoc)}else if(l){if(this.prev<r.catchLoc)return o(r.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<r.finallyLoc)return o(r.finallyLoc)}}}},abrupt:function(e,t){for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var r=i;break}}r&&("break"===e||"continue"===e)&&r.tryLoc<=t&&t<=r.finallyLoc&&(r=null);var a=r?r.completion:{};return a.type=e,a.arg=t,r?(this.method="next",this.next=r.finallyLoc,u):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),u},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),C(n),u}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var i=o.arg;C(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:B(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),u}},e}const Mt=(0,o.defineComponent)({name:"skillstraining-detail",components:{VueFroala:Ct(),BadgeModal:At.Z,ResponseModal:Tt.Z},setup:function(){var e=this,t=(0,Nt.oR)(),n=(0,Bt.yj)(),i=(0,o.ref)(""),r=(0,o.ref)(""),a=(0,o.ref)(""),l=t.getters.currentUser;(0,o.onMounted)((function(){return(0,kt.mG)(e,void 0,void 0,Ot().mark((function e(){return Ot().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,f();case 2:St()({heightCalculationMethod:"bodyScroll"},".section-content iframe");case 3:case"end":return e.stop()}}),e)})))})),(0,o.onMounted)((function(){var e=document.getElementById("kt_modal_viewResponse");e?e.addEventListener("show.bs.modal",(function(e){e.relatedTarget&&(i.value=s.value.user_response.view_response_file_path,console.log("checkmodalSrc_value",i.value),r.value=s.value.user_response.id,a.value="skillstraining/responses/".concat(r.value,"/download"))})):console.warn("Modal element not found: #kt_modal_viewResponse")}));var s=(0,o.ref)(),c=(0,o.ref)(),d=(0,o.ref)(),u=(0,o.ref)(!1),m=0,p=(0,o.ref)({});s.value={id:1,background_imagepath:null,background_video:null,user_response:{activity_responses:{},badge_key:{}}},c.value=n.params.id;var f=function(){return(0,kt.mG)(e,void 0,void 0,Ot().mark((function e(){var t,n,o,i;return Ot().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,xt.Z.get("api/skillstraining",c.value);case 3:n=e.sent,o=n.data,s.value=o,s.value.user_response.activity_responses.sort((function(e,t){return e.activity.number-t.activity.number})),i=document.getElementById("banner"),m=i.scrollHeight+120,""===(null===(t=o.user_response)||void 0===t?void 0:t.response_path)&&v(),e.next=15;break;case 12:e.prev=12,e.t0=e.catch(0),console.log(e.t0);case 15:case"end":return e.stop()}}),e,null,[[0,12]])})))},g=(0,o.ref)(!1),h=(0,o.ref)(""),v=function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10,n=document.getElementById("skillSections");n?(n.scrollIntoView({behavior:"smooth",block:"start"}),console.log("Scrolled to #skillSections")):t>0?setTimeout((function(){return e(t-1)}),300):console.log("#skillSections not found after retries")},y=(0,o.ref)(null);return{currentUser:l,skillstraining:s,currentSkillstraining:c,config:{key:"hWA2C-7I2A4C3D5D2D2G3wxeklqcwvffrrhxhoqxpkC7bmnxE2F2G2D1B10B2B3E6F1F2==",height:300,attribution:!1,toolbarButtons:[""],events:{initialized:function(){}}},scrolled:u,handleScroll:function(){if((window.innerWidth||document.documentElement.clientWidth||document.body.clientWidth)>991){var e=document.getElementById("kt_app_toolbar");window.scrollY>m?(u.value=!0,e.style.display="none"):(u.value=!1,e.style.display="flex")}},resetSkillstraining:function(e){d.value={id:e},xt.Z.post("api/skillstraining/"+e+"/reset",d.value).then((function(t){t.data;Lt().fire({text:"This skill training and your previous responses have been reset.",icon:"success",buttonsStyling:!1,confirmButtonText:"Ok",customClass:{confirmButton:"btn fw-semobold btn-light-primary rounded-0"}}).then((function(){window.location.replace("#/tasks/skillstraining/"+e)}))})).catch((function(e){e.response}))},viewResponse:function(){var e=document.querySelector(".banner");window.scrollBy({top:e.scrollHeight,left:0,behavior:"smooth"})},selectedBadge:p,openBadgeModal:function(e){p.value=e},openShareBadgeModal:function(e){p.value=e},isFullscreen:g,loadCertificate:function(){return(0,kt.mG)(e,void 0,void 0,Ot().mark((function e(){var t;return Ot().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(s.value&&s.value.user_response){e.next=3;break}return console.error("skillstraining.user_response is missing!"),e.abrupt("return");case 3:t=s.value.user_response.id,h.value="/certificate-download-skills/".concat(t,"?preview=true");case 5:case"end":return e.stop()}}),e)})))},certificateUrl:h,toggleFullscreen:function(){g.value=!g.value},selectedActivityId:y,waitForSectionAndScroll:v,downloadUrl:a,modalSrc:i}},props:["id"],created:function(){window.addEventListener("scroll",this.handleScroll)},destroyed:function(){window.removeEventListener("scroll",this.handleScroll)}});var It=n(93379),jt=n.n(It),Ft=n(46698),$t={insert:"head",singleton:!1};jt()(Ft.Z,$t);Ft.Z.locals;const Ht=(0,n(83744).Z)(Mt,[["render",function(e,t,n,kt,xt,Et){var Ct,Nt,Bt=(0,o.resolveComponent)("router-link"),Vt=(0,o.resolveComponent)("BadgeModal"),Lt=(0,o.resolveComponent)("ResponseModal");return(0,o.openBlock)(),(0,o.createElementBlock)(o.Fragment,null,[(0,o.createElementVNode)("div",{id:"banner",class:"full-view-banner banner",style:(0,o.normalizeStyle)({backgroundImage:"url("+e.skillstraining.background_imagepath+")"})},[e.skillstraining.background_videoid?((0,o.openBlock)(),(0,o.createElementBlock)("div",{key:0,class:"banner-video",innerHTML:e.skillstraining.background_videoid},null,8,i)):(0,o.createCommentVNode)("",!0),r,(0,o.createElementVNode)("div",a,[e.skillstraining.badge&&100!==e.skillstraining.compeletedpercent?((0,o.openBlock)(),(0,o.createElementBlock)("div",l,[(0,o.createElementVNode)("div",s,[(0,o.createElementVNode)("div",c,[(0,o.createElementVNode)("div",d,[(0,o.createElementVNode)("img",{src:e.skillstraining.badge.image_fullpath,alt:e.skillstraining.badge.name,class:"me-3",width:"25"},null,8,u),(0,o.createElementVNode)("div",null,[(0,o.createElementVNode)("p",m,(0,o.toDisplayString)(e.skillstraining.badge.name),1)])])])])])):(0,o.createCommentVNode)("",!0),p,(0,o.createElementVNode)("h1",{class:"display-4 fw-normal mb-4 text-light",innerHTML:e.skillstraining.title},null,8,f),(0,o.createElementVNode)("div",g,[e.skillstraining.estimated_time&&(e.skillstraining.estimated_time.hours||e.skillstraining.estimated_time.minutes)?((0,o.openBlock)(),(0,o.createElementBlock)("div",h,[v,e.skillstraining.estimated_time&&e.skillstraining.estimated_time.hours?((0,o.openBlock)(),(0,o.createElementBlock)("span",{key:0,textContent:(0,o.toDisplayString)(e.skillstraining.estimated_time.hours+"h ")},null,8,y)):(0,o.createCommentVNode)("",!0),e.skillstraining.estimated_time&&e.skillstraining.estimated_time.minutes?((0,o.openBlock)(),(0,o.createElementBlock)("span",{key:1,textContent:(0,o.toDisplayString)(e.skillstraining.estimated_time.minutes+"m")},null,8,b)):(0,o.createCommentVNode)("",!0)])):(0,o.createCommentVNode)("",!0),e.skillstraining.level?((0,o.openBlock)(),(0,o.createElementBlock)("div",w,[k,(0,o.createElementVNode)("span",{textContent:(0,o.toDisplayString)(e.skillstraining.level)},null,8,x)])):(0,o.createCommentVNode)("",!0),(0,o.createElementVNode)("div",E,[100===e.skillstraining.compeletedpercent?((0,o.openBlock)(),(0,o.createElementBlock)("span",C," Completed ")):e.skillstraining.compeletedpercent>0&&e.skillstraining.compeletedpercent<100?((0,o.openBlock)(),(0,o.createElementBlock)("span",N,(0,o.toDisplayString)(e.skillstraining.compeletedpercent)+"% Completed ",1)):(0,o.createCommentVNode)("",!0)])]),(0,o.createElementVNode)("div",B,[((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(e.skillstraining.tagged,(function(e){return(0,o.openBlock)(),(0,o.createElementBlock)("div",{class:"col-sm-6 fs-6 text-light p-2",key:e.id},[V,(0,o.createTextVNode)(" "+(0,o.toDisplayString)(e.tag_name),1)])})),128))]),e.skillstraining.foreground_video?((0,o.openBlock)(),(0,o.createElementBlock)("div",L,_)):(0,o.createCommentVNode)("",!0),e.skillstraining.user_response&&"Submitted"==e.skillstraining.user_response.status?((0,o.openBlock)(),(0,o.createElementBlock)("div",S,[""===e.skillstraining.user_response.response_path?((0,o.openBlock)(),(0,o.createElementBlock)("div",A,[(0,o.createElementVNode)("button",{class:"btn btn-white-custom btn-lg border-1 rounded-0 w-100 p-md-5",style:{"font-size":"14px !important"},onClick:t[0]||(t[0]=function(){return e.viewResponse&&e.viewResponse.apply(e,arguments)})}," View Response ")])):((0,o.openBlock)(),(0,o.createElementBlock)("div",T,P)),e.skillstraining.hasresponse?((0,o.openBlock)(),(0,o.createElementBlock)("div",O,[e.skillstraining.compeletedpercent>=100?((0,o.openBlock)(),(0,o.createElementBlock)("div",M,I)):(0,o.createCommentVNode)("",!0)])):(0,o.createCommentVNode)("",!0)])):(0,o.createCommentVNode)("",!0),e.skillstraining.hasresponse?((0,o.openBlock)(),(0,o.createElementBlock)("div",j,[(0,o.createElementVNode)("div",F,[(0,o.createVNode)(Bt,{class:"p-5 text-light",style:{"font-size":"12px !important"},to:{name:"task-skillstraining-section-detail",params:{id:e.currentSkillstraining,sectionid:1}}},{default:(0,o.withCtx)((function(){return[(0,o.createTextVNode)(" Edit Response ")]})),_:1},8,["to"])])])):(0,o.createCommentVNode)("",!0),(0,o.createElementVNode)("div",$,[e.skillstraining.badge&&100===e.skillstraining.compeletedpercent?((0,o.openBlock)(),(0,o.createElementBlock)("div",H,[(0,o.createElementVNode)("div",D,[(0,o.createElementVNode)("div",z,[(0,o.createElementVNode)("div",{class:"d-flex align-items-center cursor-pointer","data-bs-toggle":"modal","data-bs-target":"#kt_modal_badge",onClick:t[1]||(t[1]=function(t){return e.openBadgeModal(e.skillstraining.user_response.badge_key)})},[(0,o.createElementVNode)("img",{src:e.skillstraining.badge.image_fullpath,alt:e.skillstraining.badge.name,class:"me-3",width:"25"},null,8,R),q])])])])):(0,o.createCommentVNode)("",!0),"Submitted"==(null===(Nt=null===(Ct=e.skillstraining)||void 0===Ct?void 0:Ct.user_response)||void 0===Nt?void 0:Nt.status)?((0,o.openBlock)(),(0,o.createElementBlock)("div",W,[(0,o.createElementVNode)("div",U,[(0,o.createElementVNode)("div",Z,[(0,o.createElementVNode)("div",{class:"d-flex align-items-center cursor-pointer","data-bs-toggle":"modal","data-bs-target":"#kt_modal_viewFile",onClick:t[2]||(t[2]=function(){return e.loadCertificate&&e.loadCertificate.apply(e,arguments)})},G)])])])):(0,o.createCommentVNode)("",!0),e.skillstraining.feedback?((0,o.openBlock)(),(0,o.createElementBlock)("div",Y,K)):(0,o.createCommentVNode)("",!0)])])],4),(0,o.createElementVNode)("div",(0,o.mergeProps)({class:{row:e.skillstraining.user_response.activity_responses.length<6,"sticky-top":e.scrolled}},(0,o.toHandlers)(e.handleScroll,!0),{class:"d-flex bg-black module-sections"}),[((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(e.skillstraining.user_response.activity_responses,(function(t){return(0,o.openBlock)(),(0,o.createElementBlock)("div",{key:t.activity.id,class:(0,o.normalizeClass)(["text-center p-0",[e.skillstraining.user_response.activity_responses.length<6?"col":"col-6 col-sm-4 col-md-2","bg-black"]])},[(0,o.createElementVNode)("div",J,[(0,o.createElementVNode)("span",X,[t?((0,o.openBlock)(),(0,o.createElementBlock)("svg",Q,ee)):((0,o.openBlock)(),(0,o.createElementBlock)("svg",te,ne))]),(0,o.createElementVNode)("p",{class:"m-0 px-5 text-white",innerHTML:t.activity.title},null,8,oe),(0,o.createElementVNode)("p",ie,[t.activity.estimated_time&&t.activity.estimated_time.hours?((0,o.openBlock)(),(0,o.createElementBlock)("span",{key:0,textContent:(0,o.toDisplayString)(t.activity.estimated_time.hours+"h ")},null,8,re)):(0,o.createCommentVNode)("",!0),t.activity.estimated_time&&t.activity.estimated_time.minutes?((0,o.openBlock)(),(0,o.createElementBlock)("span",{key:1,textContent:(0,o.toDisplayString)(t.activity.estimated_time.minutes+"m")},null,8,ae)):(0,o.createCommentVNode)("",!0),(0,o.createTextVNode)("   ")])])],2)})),128)),(0,o.createElementVNode)("div",{class:(0,o.normalizeClass)(["text-center p-0",[e.skillstraining.user_response.activity_responses.length<6?"col":"col-6 col-sm-4 col-md-2 ","bg-black"]])},[(0,o.createElementVNode)("div",le,[(0,o.createElementVNode)("span",se,[e.skillstraining.user_response?((0,o.openBlock)(),(0,o.createElementBlock)("svg",ce,de)):((0,o.openBlock)(),(0,o.createElementBlock)("svg",ue,me))]),(0,o.createElementVNode)("p",{class:(0,o.normalizeClass)(["m-0",{"text-white":e.skillstraining.user_response}])}," Final Step ",2),(0,o.createElementVNode)("p",{class:(0,o.normalizeClass)(["m-0",{"text-white":e.skillstraining.user_response}])},"   ",2)])],2)],16),(0,o.createElementVNode)("div",pe,[(0,o.createElementVNode)("div",fe,[(0,o.createElementVNode)("div",ge,[(0,o.createElementVNode)("div",he,[(0,o.createElementVNode)("div",ve,[(0,o.createElementVNode)("ul",ye,[((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(e.skillstraining.user_response.activity_responses,(function(t){return(0,o.openBlock)(),(0,o.createElementBlock)("li",{key:t.activity.id,class:"nav-item w-100 me-0 mb-md-2"},[(0,o.createElementVNode)("a",{class:(0,o.normalizeClass)(["nav-link w-100 btn btn-flex btn-active-light-secondary btn-active-color-dark",{active:e.selectedActivityId===t.activity.id}]),onClick:function(n){return e.selectedActivityId=t.activity.id}},[(0,o.createElementVNode)("span",we,[(0,o.createElementVNode)("span",ke,"Section "+(0,o.toDisplayString)(t.activity.number),1),(0,o.createElementVNode)("span",xe,(0,o.toDisplayString)(t.activity.title.toLowerCase()),1)])],10,be)])})),128)),(0,o.createElementVNode)("li",Ee,[(0,o.createElementVNode)("a",{class:(0,o.normalizeClass)(["nav-link w-100 btn btn-flex btn-active-light-secondary btn-active-color-dark",{active:null===e.selectedActivityId}]),onClick:t[3]||(t[3]=function(t){return e.selectedActivityId=null})},Ce,2)])])]),(0,o.createElementVNode)("div",Ne,[((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(e.skillstraining.user_response.activity_responses,(function(t){return(0,o.openBlock)(),(0,o.createElementBlock)(o.Fragment,{key:t.activity.id},[e.selectedActivityId===t.activity.id?((0,o.openBlock)(),(0,o.createElementBlock)("div",Be,[(0,o.createVNode)(Bt,{to:{name:"task-skillstraining-section-detail",params:{id:e.currentSkillstraining,sectionid:t.activity.number}},custom:""},{default:(0,o.withCtx)((function(e){var t=e.href;return[(0,o.createElementVNode)("a",{href:t,target:"_blank",rel:"noopener",class:"d-flex justify-content-end me-3 position-sticky top-0 bg-white p-2 gap-1"},[Le,(0,o.createTextVNode)(" View Module ")],8,Ve)]})),_:2},1032,["to"]),t.activity.estimated_time&&t.activity.estimated_time.hours||t.activity.estimated_time&&t.activity.estimated_time.minutes?((0,o.openBlock)(),(0,o.createElementBlock)("div",_e,[Se,t.activity.estimated_time&&t.activity.estimated_time.hours?((0,o.openBlock)(),(0,o.createElementBlock)("span",{key:0,textContent:(0,o.toDisplayString)(t.activity.estimated_time.hours+"h ")},null,8,Ae)):(0,o.createCommentVNode)("",!0),t.activity.estimated_time&&t.activity.estimated_time.minutes?((0,o.openBlock)(),(0,o.createElementBlock)("span",{key:1,textContent:(0,o.toDisplayString)(t.activity.estimated_time.minutes+"m")},null,8,Te)):(0,o.createCommentVNode)("",!0)])):(0,o.createCommentVNode)("",!0),(0,o.createElementVNode)("div",{class:"my-5",innerHTML:t.activity.body},null,8,Pe),t.activity.response&&t.response?((0,o.openBlock)(),(0,o.createElementBlock)("div",{key:1,class:"froala-response mb-5",innerHTML:t.response},null,8,Oe)):(0,o.createCommentVNode)("",!0)])):(0,o.createCommentVNode)("",!0),!e.selectedActivityId===t.activity.id?((0,o.openBlock)(),(0,o.createElementBlock)("div",Me,Ie)):(0,o.createCommentVNode)("",!0)],64)})),128)),null===e.selectedActivityId?((0,o.openBlock)(),(0,o.createElementBlock)("div",je,[Fe,(0,o.createElementVNode)("div",$e,["Submitted"==e.skillstraining.user_response.status?((0,o.openBlock)(),(0,o.createElementBlock)("div",He,[(0,o.createElementVNode)("button",{class:"btn btn-secondary rounded",style:{"font-size":"14px !important"},"data-bs-toggle":"modal","data-bs-target":"#kt_modal_viewFile",onClick:t[4]||(t[4]=function(){return e.loadCertificate&&e.loadCertificate.apply(e,arguments)})},[De,(0,o.createTextVNode)("View Certificate ")])])):(0,o.createCommentVNode)("",!0),e.skillstraining.response&&e.skillstraining.user_response.response_path?((0,o.openBlock)(),(0,o.createElementBlock)("a",{key:1,href:"/skillstraining/responses/"+e.skillstraining.user_response.id+"/download",class:"btn btn-secondary rounded"},[Re,(0,o.createTextVNode)(" Download Response ")],8,ze)):(0,o.createCommentVNode)("",!0),"Submitted"==e.skillstraining.user_response.status?((0,o.openBlock)(),(0,o.createElementBlock)("a",{key:2,href:"/skillstraining-certificate/"+e.skillstraining.user_response.id,target:"_blank",class:"btn btn-secondary rounded"},[We,(0,o.createTextVNode)(" Download Certificate")],8,qe)):(0,o.createCommentVNode)("",!0)])])):(0,o.createCommentVNode)("",!0)])])])])]),(0,o.createElementVNode)("div",Ue,[(0,o.createElementVNode)("div",Ze,[(0,o.createElementVNode)("div",Ge,[(0,o.createElementVNode)("div",{class:"modal-body bg-black p-1",innerHTML:e.skillstraining.foreground_video},null,8,Ye)])])]),(0,o.createElementVNode)("div",Ke,[(0,o.createElementVNode)("div",Je,[(0,o.createElementVNode)("div",Xe,[(0,o.createElementVNode)("div",Qe,[et,(0,o.createElementVNode)("button",{type:"button",class:"btn btn-primary btn-sm rounded-0","data-bs-dismiss":"modal",onClick:t[5]||(t[5]=function(t){return e.resetSkillstraining(e.skillstraining.id)})}," Yes "),tt])])])]),(0,o.createElementVNode)("div",nt,[(0,o.createElementVNode)("div",ot,[(0,o.createElementVNode)("div",it,[rt,(0,o.createElementVNode)("div",at,[(0,o.createElementVNode)("div",lt,[(0,o.createElementVNode)("p",{innerHTML:e.skillstraining.user_response.feedback,class:"text-gray-700"},null,8,st)])])])])]),(0,o.createElementVNode)("div",ct,[(0,o.createElementVNode)("div",{class:(0,o.normalizeClass)(["modal-dialog modal-dialog-centered",e.isFullscreen?"custom-fullscreen-modal":"mw-1200px"])},[(0,o.createElementVNode)("div",dt,[(0,o.createElementVNode)("div",ut,[mt,(0,o.createElementVNode)("div",null,[(0,o.createElementVNode)("span",{class:"mx-4 cursor-pointer",onClick:t[6]||(t[6]=function(){return e.toggleFullscreen&&e.toggleFullscreen.apply(e,arguments)})},[e.isFullscreen?((0,o.openBlock)(),(0,o.createElementBlock)("i",pt)):((0,o.openBlock)(),(0,o.createElementBlock)("i",ft))]),e.skillstraining.user_response?((0,o.openBlock)(),(0,o.createElementBlock)("a",{key:0,href:"/certificate-download-skills/"+e.skillstraining.user_response.id,target:"_blank",class:"text-secondary mx-2"},ht,8,gt)):(0,o.createCommentVNode)("",!0),vt])]),(0,o.createElementVNode)("div",yt,[e.certificateUrl?((0,o.openBlock)(),(0,o.createElementBlock)("iframe",{key:0,src:e.certificateUrl,class:"w-100",style:(0,o.normalizeStyle)({height:e.isFullscreen?"90vh":"80vh",border:"none"}),allowfullscreen:""},null,12,bt)):((0,o.openBlock)(),(0,o.createElementBlock)("p",wt,"Loading..."))])])],2)]),(0,o.createVNode)(Vt,{selectedBadge:e.selectedBadge,onShareBadge:e.openShareBadgeModal},null,8,["selectedBadge","onShareBadge"]),(0,o.createVNode)(Lt,{modalSrc:e.modalSrc,downloadUrl:e.downloadUrl},null,8,["modalSrc","downloadUrl"])],64)}]])}}]);