/*! For license information please see 933.js.LICENSE.txt */
(self.webpackChunk=self.webpackChunk||[]).push([[933],{3368:(e,t,o)=>{"use strict";o.d(t,{Z:()=>i});var n=o(1519),a=o.n(n)()((function(e){return e[1]}));a.push([e.id,".mw-900px{max-width:900px}.animated-video>iframe{height:100%!important;width:100%!important}",""]);const i=a},6857:(e,t,o)=>{"use strict";o.d(t,{Z:()=>i});var n=o(1519),a=o.n(n)()((function(e){return e[1]}));a.push([e.id,".mw-900px[data-v-42f43f73]{max-width:900px}.w-90[data-v-42f43f73]{width:90%}",""]);const i=a},2937:(e,t,o)=>{"use strict";o.d(t,{Z:()=>i});var n=o(1519),a=o.n(n)()((function(e){return e[1]}));a.push([e.id,"#audio{overflow:hidden;transition:height .5s ease}.new-cards li{padding:0 15px;width:220px}.profile-cards{margin:0 -5px}.list-inline{list-style:none;padding-left:0}.new-cards li,.profile-cards li,.template-tiles li{vertical-align:top}.profile-cards li{display:inline-table;margin-top:10px;width:200px}.hover-colored .percentage{overflow:hidden;position:relative}.profile-cards .percentage{background-position:50%;background-repeat:no-repeat;background-size:100%;color:#fff;height:190px;line-height:190px;transition:all .2s ease}.img-fluid{max-width:100%}.hover-colored .percentage>:not(.tile-label){left:50%;position:absolute;top:50%;transform:translate(-50%,-50%);-webkit-transform:translate(-50%,-50%);-moz-transform:translate(-50%,-50%)}.blue-check{line-height:26px}.bg-blue{background-color:#0a0afd!important}.blue-check{height:25px;line-height:26px!important;width:25px}.profile-cards .topic{font-weight:700;line-height:15px;margin:10px 0}.text-master{color:#000!important}.swal2-popup{border-radius:0}.wrap{max-width:75ch;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.banner_detail_box{left:20%;position:absolute;top:50%;transform:translate(-50%,-50%)}.modal-backdrop{opacity:.8!important}#kt_modal_trailer .modal-content{background-color:transparent}.sticky-bottom{z-index:auto}.fa-heart:hover{font-weight:900!important}.btn-black-custom,.btn-white-custom{height:55px;line-height:55px;padding:0!important}.btn-black-custom>i,.btn-white-custom>i{font-size:30px;padding-right:0;vertical-align:-9px}.btn-black-custom>img,.btn-white-custom>img{margin-left:5px;vertical-align:-8px;width:29px}.btn-black-custom:hover,.btn-white-custom{background-color:#fff!important;color:#000!important}.btn-black-custom,.btn-white-custom:hover{background-color:#000!important;color:#fff!important}.btn-black-custom:hover *,.btn-white-custom *{color:#000!important}.btn-black-custom *,.btn-white-custom:hover *{color:#fff!important}.btn-black-custom:hover>.white-icon{display:none}.btn-black-custom:hover>.black-icon{display:inline!important}.pointer{cursor:pointer}.related-overlay{height:0;overflow:overlay;transition:height .3s}.slide-up{height:calc(100vh - 320px)!important}.related{right:5%!important}.related-tile-content>p:first-child{flex:75%}.banner{background-color:#000;background-position:50%;background-repeat:no-repeat;background-size:cover;display:block;min-height:calc(56.25vw - 149px);overflow:hidden;position:relative}.banner-video{height:100%}.banner-video>video{left:50%;position:absolute;top:50%;transform:translate(-50%,-50%);width:101%!important}.bg-dark,.black-strip,.full-view-banner{margin-left:-30px;margin-right:-30px}.bg-dark{background:#000!important}div#kt_app_content{padding-bottom:0;padding-top:0}@media (max-width:1280px){.banner{height:56.25vw}.banner_detail_box{left:40%}.banner-video>video{height:100%!important;width:calc(65vw + 65vh)!important}}@media (max-width:991px){.black-strip,.full-view-banner{margin-left:-20px;margin-right:-20px}.full-view-banner{margin-top:58.16px}}@media (max-width:991px) and (min-width:768px) and (orientation:portrait){.slide-up{height:calc(100vw - 220px)!important}.banner{height:86.25vw}.banner-video>video{height:100%!important;width:calc(66vw + 66vh)!important}}@media (max-width:991px) and (orientation:landscape){.banner-video>video{height:auto!important;width:calc(70vw + 70vh)!important}}@media (max-width:767px){.banner{height:calc(100vh - 300px)}.banner_detail_box{left:50%}}@media (max-width:575px){div#kt_app_content{padding-top:30px}.banner_detail_box{width:70vw!important}.banner{height:calc(100vh - 242px)}.banner-video>video{height:100%!important;width:calc(90vw + 90vh)!important}.full-view-banner{margin-top:0}}",""]);const i=a},48542:function(e){e.exports=function(){"use strict";var e={awaitingPromise:new WeakMap,promise:new WeakMap,innerParams:new WeakMap,domCache:new WeakMap};const t=e=>{const t={};for(const o in e)t[e[o]]="swal2-"+e[o];return t},o=t(["container","shown","height-auto","iosfix","popup","modal","no-backdrop","no-transition","toast","toast-shown","show","hide","close","title","html-container","actions","confirm","deny","cancel","default-outline","footer","icon","icon-content","image","input","file","range","select","radio","checkbox","label","textarea","inputerror","input-label","validation-message","progress-steps","active-progress-step","progress-step","progress-step-line","loader","loading","styled","top","top-start","top-end","top-left","top-right","center","center-start","center-end","center-left","center-right","bottom","bottom-start","bottom-end","bottom-left","bottom-right","grow-row","grow-column","grow-fullscreen","rtl","timer-progress-bar","timer-progress-bar-container","scrollbar-measure","icon-success","icon-warning","icon-info","icon-question","icon-error"]),n=t(["success","warning","info","question","error"]),a="SweetAlert2:",i=e=>e.charAt(0).toUpperCase()+e.slice(1),s=e=>{console.warn(`${a} ${"object"==typeof e?e.join(" "):e}`)},l=e=>{console.error(`${a} ${e}`)},r=[],c=(e,t)=>{var o;o=`"${e}" is deprecated and will be removed in the next major release. Please use "${t}" instead.`,r.includes(o)||(r.push(o),s(o))},d=e=>"function"==typeof e?e():e,u=e=>e&&"function"==typeof e.toPromise,m=e=>u(e)?e.toPromise():Promise.resolve(e),p=e=>e&&Promise.resolve(e)===e,g=()=>document.body.querySelector(`.${o.container}`),h=e=>{const t=g();return t?t.querySelector(e):null},f=e=>h(`.${e}`),b=()=>f(o.popup),v=()=>f(o.icon),y=()=>f(o.title),w=()=>f(o["html-container"]),k=()=>f(o.image),E=()=>f(o["progress-steps"]),x=()=>f(o["validation-message"]),C=()=>h(`.${o.actions} .${o.confirm}`),B=()=>h(`.${o.actions} .${o.cancel}`),V=()=>h(`.${o.actions} .${o.deny}`),N=()=>h(`.${o.loader}`),A=()=>f(o.actions),T=()=>f(o.footer),_=()=>f(o["timer-progress-bar"]),S=()=>f(o.close),L=()=>{const e=Array.from(b().querySelectorAll('[tabindex]:not([tabindex="-1"]):not([tabindex="0"])')).sort(((e,t)=>{const o=parseInt(e.getAttribute("tabindex")),n=parseInt(t.getAttribute("tabindex"));return o>n?1:o<n?-1:0})),t=Array.from(b().querySelectorAll('\n  a[href],\n  area[href],\n  input:not([disabled]),\n  select:not([disabled]),\n  textarea:not([disabled]),\n  button:not([disabled]),\n  iframe,\n  object,\n  embed,\n  [tabindex="0"],\n  [contenteditable],\n  audio[controls],\n  video[controls],\n  summary\n')).filter((e=>"-1"!==e.getAttribute("tabindex")));return(e=>{const t=[];for(let o=0;o<e.length;o++)-1===t.indexOf(e[o])&&t.push(e[o]);return t})(e.concat(t)).filter((e=>G(e)))},P=()=>O(document.body,o.shown)&&!O(document.body,o["toast-shown"])&&!O(document.body,o["no-backdrop"]),$=()=>b()&&O(b(),o.toast),M={previousBodyPadding:null},D=(e,t)=>{if(e.textContent="",t){const o=(new DOMParser).parseFromString(t,"text/html");Array.from(o.querySelector("head").childNodes).forEach((t=>{e.appendChild(t)})),Array.from(o.querySelector("body").childNodes).forEach((t=>{t instanceof HTMLVideoElement||t instanceof HTMLAudioElement?e.appendChild(t.cloneNode(!0)):e.appendChild(t)}))}},O=(e,t)=>{if(!t)return!1;const o=t.split(/\s+/);for(let t=0;t<o.length;t++)if(!e.classList.contains(o[t]))return!1;return!0},H=(e,t,a)=>{if(((e,t)=>{Array.from(e.classList).forEach((a=>{Object.values(o).includes(a)||Object.values(n).includes(a)||Object.values(t.showClass).includes(a)||e.classList.remove(a)}))})(e,t),t.customClass&&t.customClass[a]){if("string"!=typeof t.customClass[a]&&!t.customClass[a].forEach)return void s(`Invalid type of customClass.${a}! Expected string or iterable object, got "${typeof t.customClass[a]}"`);F(e,t.customClass[a])}},j=(e,t)=>{if(!t)return null;switch(t){case"select":case"textarea":case"file":return e.querySelector(`.${o.popup} > .${o[t]}`);case"checkbox":return e.querySelector(`.${o.popup} > .${o.checkbox} input`);case"radio":return e.querySelector(`.${o.popup} > .${o.radio} input:checked`)||e.querySelector(`.${o.popup} > .${o.radio} input:first-child`);case"range":return e.querySelector(`.${o.popup} > .${o.range} input`);default:return e.querySelector(`.${o.popup} > .${o.input}`)}},I=e=>{if(e.focus(),"file"!==e.type){const t=e.value;e.value="",e.value=t}},q=(e,t,o)=>{e&&t&&("string"==typeof t&&(t=t.split(/\s+/).filter(Boolean)),t.forEach((t=>{Array.isArray(e)?e.forEach((e=>{o?e.classList.add(t):e.classList.remove(t)})):o?e.classList.add(t):e.classList.remove(t)})))},F=(e,t)=>{q(e,t,!0)},R=(e,t)=>{q(e,t,!1)},U=(e,t)=>{const o=Array.from(e.children);for(let e=0;e<o.length;e++){const n=o[e];if(n instanceof HTMLElement&&O(n,t))return n}},z=(e,t,o)=>{o===`${parseInt(o)}`&&(o=parseInt(o)),o||0===parseInt(o)?e.style[t]="number"==typeof o?`${o}px`:o:e.style.removeProperty(t)},Z=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"flex";e.style.display=t},W=e=>{e.style.display="none"},K=(e,t,o,n)=>{const a=e.querySelector(t);a&&(a.style[o]=n)},Y=function(e,t){t?Z(e,arguments.length>2&&void 0!==arguments[2]?arguments[2]:"flex"):W(e)},G=e=>!(!e||!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)),J=e=>!!(e.scrollHeight>e.clientHeight),X=e=>{const t=window.getComputedStyle(e),o=parseFloat(t.getPropertyValue("animation-duration")||"0"),n=parseFloat(t.getPropertyValue("transition-duration")||"0");return o>0||n>0},Q=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const o=_();G(o)&&(t&&(o.style.transition="none",o.style.width="100%"),setTimeout((()=>{o.style.transition=`width ${e/1e3}s linear`,o.style.width="0%"}),10))},ee={},te=e=>new Promise((t=>{if(!e)return t();const o=window.scrollX,n=window.scrollY;ee.restoreFocusTimeout=setTimeout((()=>{ee.previousActiveElement instanceof HTMLElement?(ee.previousActiveElement.focus(),ee.previousActiveElement=null):document.body&&document.body.focus(),t()}),100),window.scrollTo(o,n)})),oe=()=>"undefined"==typeof window||"undefined"==typeof document,ne=`\n <div aria-labelledby="${o.title}" aria-describedby="${o["html-container"]}" class="${o.popup}" tabindex="-1">\n   <button type="button" class="${o.close}"></button>\n   <ul class="${o["progress-steps"]}"></ul>\n   <div class="${o.icon}"></div>\n   <img class="${o.image}" />\n   <h2 class="${o.title}" id="${o.title}"></h2>\n   <div class="${o["html-container"]}" id="${o["html-container"]}"></div>\n   <input class="${o.input}" />\n   <input type="file" class="${o.file}" />\n   <div class="${o.range}">\n     <input type="range" />\n     <output></output>\n   </div>\n   <select class="${o.select}"></select>\n   <div class="${o.radio}"></div>\n   <label for="${o.checkbox}" class="${o.checkbox}">\n     <input type="checkbox" />\n     <span class="${o.label}"></span>\n   </label>\n   <textarea class="${o.textarea}"></textarea>\n   <div class="${o["validation-message"]}" id="${o["validation-message"]}"></div>\n   <div class="${o.actions}">\n     <div class="${o.loader}"></div>\n     <button type="button" class="${o.confirm}"></button>\n     <button type="button" class="${o.deny}"></button>\n     <button type="button" class="${o.cancel}"></button>\n   </div>\n   <div class="${o.footer}"></div>\n   <div class="${o["timer-progress-bar-container"]}">\n     <div class="${o["timer-progress-bar"]}"></div>\n   </div>\n </div>\n`.replace(/(^|\n)\s*/g,""),ae=()=>{ee.currentInstance.resetValidationMessage()},ie=e=>{const t=(()=>{const e=g();return!!e&&(e.remove(),R([document.documentElement,document.body],[o["no-backdrop"],o["toast-shown"],o["has-column"]]),!0)})();if(oe())return void l("SweetAlert2 requires document to initialize");const n=document.createElement("div");n.className=o.container,t&&F(n,o["no-transition"]),D(n,ne);const a="string"==typeof(i=e.target)?document.querySelector(i):i;var i;a.appendChild(n),(e=>{const t=b();t.setAttribute("role",e.toast?"alert":"dialog"),t.setAttribute("aria-live",e.toast?"polite":"assertive"),e.toast||t.setAttribute("aria-modal","true")})(e),(e=>{"rtl"===window.getComputedStyle(e).direction&&F(g(),o.rtl)})(a),(()=>{const e=b(),t=U(e,o.input),n=U(e,o.file),a=e.querySelector(`.${o.range} input`),i=e.querySelector(`.${o.range} output`),s=U(e,o.select),l=e.querySelector(`.${o.checkbox} input`),r=U(e,o.textarea);t.oninput=ae,n.onchange=ae,s.onchange=ae,l.onchange=ae,r.oninput=ae,a.oninput=()=>{ae(),i.value=a.value},a.onchange=()=>{ae(),i.value=a.value}})()},se=(e,t)=>{e instanceof HTMLElement?t.appendChild(e):"object"==typeof e?le(e,t):e&&D(t,e)},le=(e,t)=>{e.jquery?re(t,e):D(t,e.toString())},re=(e,t)=>{if(e.textContent="",0 in t)for(let o=0;o in t;o++)e.appendChild(t[o].cloneNode(!0));else e.appendChild(t.cloneNode(!0))},ce=(()=>{if(oe())return!1;const e=document.createElement("div"),t={WebkitAnimation:"webkitAnimationEnd",animation:"animationend"};for(const o in t)if(Object.prototype.hasOwnProperty.call(t,o)&&void 0!==e.style[o])return t[o];return!1})(),de=(e,t)=>{const n=A(),a=N();t.showConfirmButton||t.showDenyButton||t.showCancelButton?Z(n):W(n),H(n,t,"actions"),function(e,t,n){const a=C(),i=V(),s=B();ue(a,"confirm",n),ue(i,"deny",n),ue(s,"cancel",n),function(e,t,n,a){a.buttonsStyling?(F([e,t,n],o.styled),a.confirmButtonColor&&(e.style.backgroundColor=a.confirmButtonColor,F(e,o["default-outline"])),a.denyButtonColor&&(t.style.backgroundColor=a.denyButtonColor,F(t,o["default-outline"])),a.cancelButtonColor&&(n.style.backgroundColor=a.cancelButtonColor,F(n,o["default-outline"]))):R([e,t,n],o.styled)}(a,i,s,n),n.reverseButtons&&(n.toast?(e.insertBefore(s,a),e.insertBefore(i,a)):(e.insertBefore(s,t),e.insertBefore(i,t),e.insertBefore(a,t)))}(n,a,t),D(a,t.loaderHtml),H(a,t,"loader")};function ue(e,t,n){Y(e,n[`show${i(t)}Button`],"inline-block"),D(e,n[`${t}ButtonText`]),e.setAttribute("aria-label",n[`${t}ButtonAriaLabel`]),e.className=o[t],H(e,n,`${t}Button`),F(e,n[`${t}ButtonClass`])}const me=(e,t)=>{const n=g();n&&(function(e,t){"string"==typeof t?e.style.background=t:t||F([document.documentElement,document.body],o["no-backdrop"])}(n,t.backdrop),function(e,t){t in o?F(e,o[t]):(s('The "position" parameter is not valid, defaulting to "center"'),F(e,o.center))}(n,t.position),function(e,t){if(t&&"string"==typeof t){const n=`grow-${t}`;n in o&&F(e,o[n])}}(n,t.grow),H(n,t,"container"))},pe=["input","file","range","select","radio","checkbox","textarea"],ge=e=>{if(!ke[e.input])return void l(`Unexpected type of input! Expected "text", "email", "password", "number", "tel", "select", "radio", "checkbox", "textarea", "file" or "url", got "${e.input}"`);const t=ye(e.input),o=ke[e.input](t,e);Z(t),e.inputAutoFocus&&setTimeout((()=>{I(o)}))},he=(e,t)=>{const o=j(b(),e);if(o){(e=>{for(let t=0;t<e.attributes.length;t++){const o=e.attributes[t].name;["type","value","style"].includes(o)||e.removeAttribute(o)}})(o);for(const e in t)o.setAttribute(e,t[e])}},fe=e=>{const t=ye(e.input);"object"==typeof e.customClass&&F(t,e.customClass.input)},be=(e,t)=>{e.placeholder&&!t.inputPlaceholder||(e.placeholder=t.inputPlaceholder)},ve=(e,t,n)=>{if(n.inputLabel){e.id=o.input;const a=document.createElement("label"),i=o["input-label"];a.setAttribute("for",e.id),a.className=i,"object"==typeof n.customClass&&F(a,n.customClass.inputLabel),a.innerText=n.inputLabel,t.insertAdjacentElement("beforebegin",a)}},ye=e=>U(b(),o[e]||o.input),we=(e,t)=>{["string","number"].includes(typeof t)?e.value=`${t}`:p(t)||s(`Unexpected type of inputValue! Expected "string", "number" or "Promise", got "${typeof t}"`)},ke={};ke.text=ke.email=ke.password=ke.number=ke.tel=ke.url=(e,t)=>(we(e,t.inputValue),ve(e,e,t),be(e,t),e.type=t.input,e),ke.file=(e,t)=>(ve(e,e,t),be(e,t),e),ke.range=(e,t)=>{const o=e.querySelector("input"),n=e.querySelector("output");return we(o,t.inputValue),o.type=t.input,we(n,t.inputValue),ve(o,e,t),e},ke.select=(e,t)=>{if(e.textContent="",t.inputPlaceholder){const o=document.createElement("option");D(o,t.inputPlaceholder),o.value="",o.disabled=!0,o.selected=!0,e.appendChild(o)}return ve(e,e,t),e},ke.radio=e=>(e.textContent="",e),ke.checkbox=(e,t)=>{const n=j(b(),"checkbox");n.value="1",n.id=o.checkbox,n.checked=Boolean(t.inputValue);const a=e.querySelector("span");return D(a,t.inputPlaceholder),n},ke.textarea=(e,t)=>(we(e,t.inputValue),be(e,t),ve(e,e,t),setTimeout((()=>{if("MutationObserver"in window){const t=parseInt(window.getComputedStyle(b()).width);new MutationObserver((()=>{const o=e.offsetWidth+(n=e,parseInt(window.getComputedStyle(n).marginLeft)+parseInt(window.getComputedStyle(n).marginRight));var n;b().style.width=o>t?`${o}px`:null})).observe(e,{attributes:!0,attributeFilter:["style"]})}})),e);const Ee=(t,n)=>{const a=w();H(a,n,"htmlContainer"),n.html?(se(n.html,a),Z(a,"block")):n.text?(a.textContent=n.text,Z(a,"block")):W(a),((t,n)=>{const a=b(),i=e.innerParams.get(t),s=!i||n.input!==i.input;pe.forEach((e=>{const t=U(a,o[e]);he(e,n.inputAttributes),t.className=o[e],s&&W(t)})),n.input&&(s&&ge(n),fe(n))})(t,n)},xe=(e,t)=>{for(const o in n)t.icon!==o&&R(e,n[o]);F(e,n[t.icon]),Ve(e,t),Ce(),H(e,t,"icon")},Ce=()=>{const e=b(),t=window.getComputedStyle(e).getPropertyValue("background-color"),o=e.querySelectorAll("[class^=swal2-success-circular-line], .swal2-success-fix");for(let e=0;e<o.length;e++)o[e].style.backgroundColor=t},Be=(e,t)=>{let o,n=e.innerHTML;t.iconHtml?o=Ne(t.iconHtml):"success"===t.icon?(o='\n  <div class="swal2-success-circular-line-left"></div>\n  <span class="swal2-success-line-tip"></span> <span class="swal2-success-line-long"></span>\n  <div class="swal2-success-ring"></div> <div class="swal2-success-fix"></div>\n  <div class="swal2-success-circular-line-right"></div>\n',n=n.replace(/ style=".*?"/g,"")):o="error"===t.icon?'\n  <span class="swal2-x-mark">\n    <span class="swal2-x-mark-line-left"></span>\n    <span class="swal2-x-mark-line-right"></span>\n  </span>\n':Ne({question:"?",warning:"!",info:"i"}[t.icon]),n.trim()!==o.trim()&&D(e,o)},Ve=(e,t)=>{if(t.iconColor){e.style.color=t.iconColor,e.style.borderColor=t.iconColor;for(const o of[".swal2-success-line-tip",".swal2-success-line-long",".swal2-x-mark-line-left",".swal2-x-mark-line-right"])K(e,o,"backgroundColor",t.iconColor);K(e,".swal2-success-ring","borderColor",t.iconColor)}},Ne=e=>`<div class="${o["icon-content"]}">${e}</div>`,Ae=(e,t)=>{e.className=`${o.popup} ${G(e)?t.showClass.popup:""}`,t.toast?(F([document.documentElement,document.body],o["toast-shown"]),F(e,o.toast)):F(e,o.modal),H(e,t,"popup"),"string"==typeof t.customClass&&F(e,t.customClass),t.icon&&F(e,o[`icon-${t.icon}`])},Te=e=>{const t=document.createElement("li");return F(t,o["progress-step"]),D(t,e),t},_e=e=>{const t=document.createElement("li");return F(t,o["progress-step-line"]),e.progressStepsDistance&&z(t,"width",e.progressStepsDistance),t},Se=(t,a)=>{((e,t)=>{const o=g(),n=b();t.toast?(z(o,"width",t.width),n.style.width="100%",n.insertBefore(N(),v())):z(n,"width",t.width),z(n,"padding",t.padding),t.color&&(n.style.color=t.color),t.background&&(n.style.background=t.background),W(x()),Ae(n,t)})(0,a),me(0,a),((e,t)=>{const n=E();t.progressSteps&&0!==t.progressSteps.length?(Z(n),n.textContent="",t.currentProgressStep>=t.progressSteps.length&&s("Invalid currentProgressStep parameter, it should be less than progressSteps.length (currentProgressStep like JS arrays starts from 0)"),t.progressSteps.forEach(((e,a)=>{const i=Te(e);if(n.appendChild(i),a===t.currentProgressStep&&F(i,o["active-progress-step"]),a!==t.progressSteps.length-1){const e=_e(t);n.appendChild(e)}}))):W(n)})(0,a),((t,o)=>{const a=e.innerParams.get(t),i=v();if(a&&o.icon===a.icon)return Be(i,o),void xe(i,o);if(o.icon||o.iconHtml){if(o.icon&&-1===Object.keys(n).indexOf(o.icon))return l(`Unknown icon! Expected "success", "error", "warning", "info" or "question", got "${o.icon}"`),void W(i);Z(i),Be(i,o),xe(i,o),F(i,o.showClass.icon)}else W(i)})(t,a),((e,t)=>{const n=k();t.imageUrl?(Z(n,""),n.setAttribute("src",t.imageUrl),n.setAttribute("alt",t.imageAlt),z(n,"width",t.imageWidth),z(n,"height",t.imageHeight),n.className=o.image,H(n,t,"image")):W(n)})(0,a),((e,t)=>{const o=y();Y(o,t.title||t.titleText,"block"),t.title&&se(t.title,o),t.titleText&&(o.innerText=t.titleText),H(o,t,"title")})(0,a),((e,t)=>{const o=S();D(o,t.closeButtonHtml),H(o,t,"closeButton"),Y(o,t.showCloseButton),o.setAttribute("aria-label",t.closeButtonAriaLabel)})(0,a),Ee(t,a),de(0,a),((e,t)=>{const o=T();Y(o,t.footer),t.footer&&se(t.footer,o),H(o,t,"footer")})(0,a),"function"==typeof a.didRender&&a.didRender(b())};function Le(){const t=e.innerParams.get(this);if(!t)return;const n=e.domCache.get(this);W(n.loader),$()?t.icon&&Z(v()):Pe(n),R([n.popup,n.actions],o.loading),n.popup.removeAttribute("aria-busy"),n.popup.removeAttribute("data-loading"),n.confirmButton.disabled=!1,n.denyButton.disabled=!1,n.cancelButton.disabled=!1}const Pe=e=>{const t=e.popup.getElementsByClassName(e.loader.getAttribute("data-button-to-replace"));t.length?Z(t[0],"inline-block"):G(C())||G(V())||G(B())||W(e.actions)},$e=()=>C()&&C().click(),Me=Object.freeze({cancel:"cancel",backdrop:"backdrop",close:"close",esc:"esc",timer:"timer"}),De=e=>{e.keydownTarget&&e.keydownHandlerAdded&&(e.keydownTarget.removeEventListener("keydown",e.keydownHandler,{capture:e.keydownListenerCapture}),e.keydownHandlerAdded=!1)},Oe=(e,t)=>{const o=L();if(o.length)return(e+=t)===o.length?e=0:-1===e&&(e=o.length-1),void o[e].focus();b().focus()},He=["ArrowRight","ArrowDown"],je=["ArrowLeft","ArrowUp"],Ie=(t,o,n)=>{const a=e.innerParams.get(t);a&&(o.isComposing||229===o.keyCode||(a.stopKeydownPropagation&&o.stopPropagation(),"Enter"===o.key?qe(t,o,a):"Tab"===o.key?Fe(o):[...He,...je].includes(o.key)?Re(o.key):"Escape"===o.key&&Ue(o,a,n)))},qe=(e,t,o)=>{if(d(o.allowEnterKey)&&t.target&&e.getInput()&&t.target instanceof HTMLElement&&t.target.outerHTML===e.getInput().outerHTML){if(["textarea","file"].includes(o.input))return;$e(),t.preventDefault()}},Fe=e=>{const t=e.target,o=L();let n=-1;for(let e=0;e<o.length;e++)if(t===o[e]){n=e;break}e.shiftKey?Oe(n,-1):Oe(n,1),e.stopPropagation(),e.preventDefault()},Re=e=>{const t=[C(),V(),B()];if(document.activeElement instanceof HTMLElement&&!t.includes(document.activeElement))return;const o=He.includes(e)?"nextElementSibling":"previousElementSibling";let n=document.activeElement;for(let e=0;e<A().children.length;e++){if(n=n[o],!n)return;if(n instanceof HTMLButtonElement&&G(n))break}n instanceof HTMLButtonElement&&n.focus()},Ue=(e,t,o)=>{d(t.allowEscapeKey)&&(e.preventDefault(),o(Me.esc))};var ze={swalPromiseResolve:new WeakMap,swalPromiseReject:new WeakMap};const Ze=()=>{Array.from(document.body.children).forEach((e=>{e.hasAttribute("data-previous-aria-hidden")?(e.setAttribute("aria-hidden",e.getAttribute("data-previous-aria-hidden")),e.removeAttribute("data-previous-aria-hidden")):e.removeAttribute("aria-hidden")}))},We=()=>{const e=navigator.userAgent,t=!!e.match(/iPad/i)||!!e.match(/iPhone/i),o=!!e.match(/WebKit/i);if(t&&o&&!e.match(/CriOS/i)){const e=44;b().scrollHeight>window.innerHeight-e&&(g().style.paddingBottom=`${e}px`)}},Ke=()=>{const e=g();let t;e.ontouchstart=e=>{t=Ye(e)},e.ontouchmove=e=>{t&&(e.preventDefault(),e.stopPropagation())}},Ye=e=>{const t=e.target,o=g();return!(Ge(e)||Je(e)||t!==o&&(J(o)||!(t instanceof HTMLElement)||"INPUT"===t.tagName||"TEXTAREA"===t.tagName||J(w())&&w().contains(t)))},Ge=e=>e.touches&&e.touches.length&&"stylus"===e.touches[0].touchType,Je=e=>e.touches&&e.touches.length>1,Xe=()=>{if(O(document.body,o.iosfix)){const e=parseInt(document.body.style.top,10);R(document.body,o.iosfix),document.body.style.top="",document.body.scrollTop=-1*e}},Qe=()=>{null===M.previousBodyPadding&&document.body.scrollHeight>window.innerHeight&&(M.previousBodyPadding=parseInt(window.getComputedStyle(document.body).getPropertyValue("padding-right")),document.body.style.paddingRight=`${M.previousBodyPadding+(()=>{const e=document.createElement("div");e.className=o["scrollbar-measure"],document.body.appendChild(e);const t=e.getBoundingClientRect().width-e.clientWidth;return document.body.removeChild(e),t})()}px`)},et=()=>{null!==M.previousBodyPadding&&(document.body.style.paddingRight=`${M.previousBodyPadding}px`,M.previousBodyPadding=null)};function tt(e,t,n,a){$()?rt(e,a):(te(n).then((()=>rt(e,a))),De(ee)),/^((?!chrome|android).)*safari/i.test(navigator.userAgent)?(t.setAttribute("style","display:none !important"),t.removeAttribute("class"),t.innerHTML=""):t.remove(),P()&&(et(),Xe(),Ze()),R([document.documentElement,document.body],[o.shown,o["height-auto"],o["no-backdrop"],o["toast-shown"]])}function ot(e){e=it(e);const t=ze.swalPromiseResolve.get(this),o=nt(this);this.isAwaitingPromise()?e.isDismissed||(at(this),t(e)):o&&t(e)}const nt=t=>{const o=b();if(!o)return!1;const n=e.innerParams.get(t);if(!n||O(o,n.hideClass.popup))return!1;R(o,n.showClass.popup),F(o,n.hideClass.popup);const a=g();return R(a,n.showClass.backdrop),F(a,n.hideClass.backdrop),st(t,o,n),!0},at=t=>{t.isAwaitingPromise()&&(e.awaitingPromise.delete(t),e.innerParams.get(t)||t._destroy())},it=e=>void 0===e?{isConfirmed:!1,isDenied:!1,isDismissed:!0}:Object.assign({isConfirmed:!1,isDenied:!1,isDismissed:!1},e),st=(e,t,o)=>{const n=g(),a=ce&&X(t);"function"==typeof o.willClose&&o.willClose(t),a?lt(e,t,n,o.returnFocus,o.didClose):tt(e,n,o.returnFocus,o.didClose)},lt=(e,t,o,n,a)=>{ee.swalCloseEventFinishedCallback=tt.bind(null,e,o,n,a),t.addEventListener(ce,(function(e){e.target===t&&(ee.swalCloseEventFinishedCallback(),delete ee.swalCloseEventFinishedCallback)}))},rt=(e,t)=>{setTimeout((()=>{"function"==typeof t&&t.bind(e.params)(),e._destroy()}))};function ct(t,o,n){const a=e.domCache.get(t);o.forEach((e=>{a[e].disabled=n}))}function dt(e,t){if(e)if("radio"===e.type){const o=e.parentNode.parentNode.querySelectorAll("input");for(let e=0;e<o.length;e++)o[e].disabled=t}else e.disabled=t}const ut={title:"",titleText:"",text:"",html:"",footer:"",icon:void 0,iconColor:void 0,iconHtml:void 0,template:void 0,toast:!1,showClass:{popup:"swal2-show",backdrop:"swal2-backdrop-show",icon:"swal2-icon-show"},hideClass:{popup:"swal2-hide",backdrop:"swal2-backdrop-hide",icon:"swal2-icon-hide"},customClass:{},target:"body",color:void 0,backdrop:!0,heightAuto:!0,allowOutsideClick:!0,allowEscapeKey:!0,allowEnterKey:!0,stopKeydownPropagation:!0,keydownListenerCapture:!1,showConfirmButton:!0,showDenyButton:!1,showCancelButton:!1,preConfirm:void 0,preDeny:void 0,confirmButtonText:"OK",confirmButtonAriaLabel:"",confirmButtonColor:void 0,denyButtonText:"No",denyButtonAriaLabel:"",denyButtonColor:void 0,cancelButtonText:"Cancel",cancelButtonAriaLabel:"",cancelButtonColor:void 0,buttonsStyling:!0,reverseButtons:!1,focusConfirm:!0,focusDeny:!1,focusCancel:!1,returnFocus:!0,showCloseButton:!1,closeButtonHtml:"&times;",closeButtonAriaLabel:"Close this dialog",loaderHtml:"",showLoaderOnConfirm:!1,showLoaderOnDeny:!1,imageUrl:void 0,imageWidth:void 0,imageHeight:void 0,imageAlt:"",timer:void 0,timerProgressBar:!1,width:void 0,padding:void 0,background:void 0,input:void 0,inputPlaceholder:"",inputLabel:"",inputValue:"",inputOptions:{},inputAutoFocus:!0,inputAutoTrim:!0,inputAttributes:{},inputValidator:void 0,returnInputValueOnDeny:!1,validationMessage:void 0,grow:!1,position:"center",progressSteps:[],currentProgressStep:void 0,progressStepsDistance:void 0,willOpen:void 0,didOpen:void 0,didRender:void 0,willClose:void 0,didClose:void 0,didDestroy:void 0,scrollbarPadding:!0},mt=["allowEscapeKey","allowOutsideClick","background","buttonsStyling","cancelButtonAriaLabel","cancelButtonColor","cancelButtonText","closeButtonAriaLabel","closeButtonHtml","color","confirmButtonAriaLabel","confirmButtonColor","confirmButtonText","currentProgressStep","customClass","denyButtonAriaLabel","denyButtonColor","denyButtonText","didClose","didDestroy","footer","hideClass","html","icon","iconColor","iconHtml","imageAlt","imageHeight","imageUrl","imageWidth","preConfirm","preDeny","progressSteps","returnFocus","reverseButtons","showCancelButton","showCloseButton","showConfirmButton","showDenyButton","text","title","titleText","willClose"],pt={},gt=["allowOutsideClick","allowEnterKey","backdrop","focusConfirm","focusDeny","focusCancel","returnFocus","heightAuto","keydownListenerCapture"],ht=e=>Object.prototype.hasOwnProperty.call(ut,e),ft=e=>-1!==mt.indexOf(e),bt=e=>pt[e],vt=e=>{ht(e)||s(`Unknown parameter "${e}"`)},yt=e=>{gt.includes(e)&&s(`The parameter "${e}" is incompatible with toasts`)},wt=e=>{bt(e)&&c(e,bt(e))},kt=e=>{const t={};return Object.keys(e).forEach((o=>{ft(o)?t[o]=e[o]:s(`Invalid parameter to update: ${o}`)})),t},Et=e=>{xt(e),delete e.params,delete ee.keydownHandler,delete ee.keydownTarget,delete ee.currentInstance},xt=t=>{t.isAwaitingPromise()?(Ct(e,t),e.awaitingPromise.set(t,!0)):(Ct(ze,t),Ct(e,t))},Ct=(e,t)=>{for(const o in e)e[o].delete(t)};var Bt=Object.freeze({__proto__:null,_destroy:function(){const t=e.domCache.get(this),o=e.innerParams.get(this);o?(t.popup&&ee.swalCloseEventFinishedCallback&&(ee.swalCloseEventFinishedCallback(),delete ee.swalCloseEventFinishedCallback),"function"==typeof o.didDestroy&&o.didDestroy(),Et(this)):xt(this)},close:ot,closeModal:ot,closePopup:ot,closeToast:ot,disableButtons:function(){ct(this,["confirmButton","denyButton","cancelButton"],!0)},disableInput:function(){dt(this.getInput(),!0)},disableLoading:Le,enableButtons:function(){ct(this,["confirmButton","denyButton","cancelButton"],!1)},enableInput:function(){dt(this.getInput(),!1)},getInput:function(t){const o=e.innerParams.get(t||this),n=e.domCache.get(t||this);return n?j(n.popup,o.input):null},handleAwaitingPromise:at,hideLoading:Le,isAwaitingPromise:function(){return!!e.awaitingPromise.get(this)},rejectPromise:function(e){const t=ze.swalPromiseReject.get(this);at(this),t&&t(e)},resetValidationMessage:function(){const t=e.domCache.get(this);t.validationMessage&&W(t.validationMessage);const n=this.getInput();n&&(n.removeAttribute("aria-invalid"),n.removeAttribute("aria-describedby"),R(n,o.inputerror))},showValidationMessage:function(t){const n=e.domCache.get(this),a=e.innerParams.get(this);D(n.validationMessage,t),n.validationMessage.className=o["validation-message"],a.customClass&&a.customClass.validationMessage&&F(n.validationMessage,a.customClass.validationMessage),Z(n.validationMessage);const i=this.getInput();i&&(i.setAttribute("aria-invalid",!0),i.setAttribute("aria-describedby",o["validation-message"]),I(i),F(i,o.inputerror))},update:function(t){const o=b(),n=e.innerParams.get(this);if(!o||O(o,n.hideClass.popup))return void s("You're trying to update the closed or closing popup, that won't work. Use the update() method in preConfirm parameter or show a new popup.");const a=kt(t),i=Object.assign({},n,a);Se(this,i),e.innerParams.set(this,i),Object.defineProperties(this,{params:{value:Object.assign({},this.params,t),writable:!1,enumerable:!0}})}});const Vt=e=>{let t=b();t||new To,t=b();const o=N();$()?W(v()):Nt(t,e),Z(o),t.setAttribute("data-loading","true"),t.setAttribute("aria-busy","true"),t.focus()},Nt=(e,t)=>{const n=A(),a=N();!t&&G(C())&&(t=C()),Z(n),t&&(W(t),a.setAttribute("data-button-to-replace",t.className)),a.parentNode.insertBefore(a,t),F([e,n],o.loading)},At=e=>e.checked?1:0,Tt=e=>e.checked?e.value:null,_t=e=>e.files.length?null!==e.getAttribute("multiple")?e.files:e.files[0]:null,St=(e,t)=>{const o=b(),n=e=>{Pt[t.input](o,$t(e),t)};u(t.inputOptions)||p(t.inputOptions)?(Vt(C()),m(t.inputOptions).then((t=>{e.hideLoading(),n(t)}))):"object"==typeof t.inputOptions?n(t.inputOptions):l("Unexpected type of inputOptions! Expected object, Map or Promise, got "+typeof t.inputOptions)},Lt=(e,t)=>{const o=e.getInput();W(o),m(t.inputValue).then((n=>{o.value="number"===t.input?`${parseFloat(n)||0}`:`${n}`,Z(o),o.focus(),e.hideLoading()})).catch((t=>{l(`Error in inputValue promise: ${t}`),o.value="",Z(o),o.focus(),e.hideLoading()}))},Pt={select:(e,t,n)=>{const a=U(e,o.select),i=(e,t,o)=>{const a=document.createElement("option");a.value=o,D(a,t),a.selected=Mt(o,n.inputValue),e.appendChild(a)};t.forEach((e=>{const t=e[0],o=e[1];if(Array.isArray(o)){const e=document.createElement("optgroup");e.label=t,e.disabled=!1,a.appendChild(e),o.forEach((t=>i(e,t[1],t[0])))}else i(a,o,t)})),a.focus()},radio:(e,t,n)=>{const a=U(e,o.radio);t.forEach((e=>{const t=e[0],i=e[1],s=document.createElement("input"),l=document.createElement("label");s.type="radio",s.name=o.radio,s.value=t,Mt(t,n.inputValue)&&(s.checked=!0);const r=document.createElement("span");D(r,i),r.className=o.label,l.appendChild(s),l.appendChild(r),a.appendChild(l)}));const i=a.querySelectorAll("input");i.length&&i[0].focus()}},$t=e=>{const t=[];return"undefined"!=typeof Map&&e instanceof Map?e.forEach(((e,o)=>{let n=e;"object"==typeof n&&(n=$t(n)),t.push([o,n])})):Object.keys(e).forEach((o=>{let n=e[o];"object"==typeof n&&(n=$t(n)),t.push([o,n])})),t},Mt=(e,t)=>t&&t.toString()===e.toString(),Dt=(t,o)=>{const n=e.innerParams.get(t);if(!n.input)return void l(`The "input" parameter is needed to be set when using returnInputValueOn${i(o)}`);const a=((e,t)=>{const o=e.getInput();if(!o)return null;switch(t.input){case"checkbox":return At(o);case"radio":return Tt(o);case"file":return _t(o);default:return t.inputAutoTrim?o.value.trim():o.value}})(t,n);n.inputValidator?Ot(t,a,o):t.getInput().checkValidity()?"deny"===o?Ht(t,a):qt(t,a):(t.enableButtons(),t.showValidationMessage(n.validationMessage))},Ot=(t,o,n)=>{const a=e.innerParams.get(t);t.disableInput(),Promise.resolve().then((()=>m(a.inputValidator(o,a.validationMessage)))).then((e=>{t.enableButtons(),t.enableInput(),e?t.showValidationMessage(e):"deny"===n?Ht(t,o):qt(t,o)}))},Ht=(t,o)=>{const n=e.innerParams.get(t||void 0);n.showLoaderOnDeny&&Vt(V()),n.preDeny?(e.awaitingPromise.set(t||void 0,!0),Promise.resolve().then((()=>m(n.preDeny(o,n.validationMessage)))).then((e=>{!1===e?(t.hideLoading(),at(t)):t.close({isDenied:!0,value:void 0===e?o:e})})).catch((e=>It(t||void 0,e)))):t.close({isDenied:!0,value:o})},jt=(e,t)=>{e.close({isConfirmed:!0,value:t})},It=(e,t)=>{e.rejectPromise(t)},qt=(t,o)=>{const n=e.innerParams.get(t||void 0);n.showLoaderOnConfirm&&Vt(),n.preConfirm?(t.resetValidationMessage(),e.awaitingPromise.set(t||void 0,!0),Promise.resolve().then((()=>m(n.preConfirm(o,n.validationMessage)))).then((e=>{G(x())||!1===e?(t.hideLoading(),at(t)):jt(t,void 0===e?o:e)})).catch((e=>It(t||void 0,e)))):jt(t,o)},Ft=(t,o,n)=>{o.popup.onclick=()=>{const o=e.innerParams.get(t);o&&(Rt(o)||o.timer||o.input)||n(Me.close)}},Rt=e=>e.showConfirmButton||e.showDenyButton||e.showCancelButton||e.showCloseButton;let Ut=!1;const zt=e=>{e.popup.onmousedown=()=>{e.container.onmouseup=function(t){e.container.onmouseup=void 0,t.target===e.container&&(Ut=!0)}}},Zt=e=>{e.container.onmousedown=()=>{e.popup.onmouseup=function(t){e.popup.onmouseup=void 0,(t.target===e.popup||e.popup.contains(t.target))&&(Ut=!0)}}},Wt=(t,o,n)=>{o.container.onclick=a=>{const i=e.innerParams.get(t);Ut?Ut=!1:a.target===o.container&&d(i.allowOutsideClick)&&n(Me.backdrop)}},Kt=e=>e instanceof Element||(e=>"object"==typeof e&&e.jquery)(e),Yt=()=>{if(ee.timeout)return(()=>{const e=_(),t=parseInt(window.getComputedStyle(e).width);e.style.removeProperty("transition"),e.style.width="100%";const o=t/parseInt(window.getComputedStyle(e).width)*100;e.style.width=`${o}%`})(),ee.timeout.stop()},Gt=()=>{if(ee.timeout){const e=ee.timeout.start();return Q(e),e}};let Jt=!1;const Xt={},Qt=e=>{for(let t=e.target;t&&t!==document;t=t.parentNode)for(const e in Xt){const o=t.getAttribute(e);if(o)return void Xt[e].fire({template:o})}};var eo=Object.freeze({__proto__:null,argsToParams:e=>{const t={};return"object"!=typeof e[0]||Kt(e[0])?["title","html","icon"].forEach(((o,n)=>{const a=e[n];"string"==typeof a||Kt(a)?t[o]=a:void 0!==a&&l(`Unexpected type of ${o}! Expected "string" or "Element", got ${typeof a}`)})):Object.assign(t,e[0]),t},bindClickHandler:function(){Xt[arguments.length>0&&void 0!==arguments[0]?arguments[0]:"data-swal-template"]=this,Jt||(document.body.addEventListener("click",Qt),Jt=!0)},clickCancel:()=>B()&&B().click(),clickConfirm:$e,clickDeny:()=>V()&&V().click(),enableLoading:Vt,fire:function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return new this(...t)},getActions:A,getCancelButton:B,getCloseButton:S,getConfirmButton:C,getContainer:g,getDenyButton:V,getFocusableElements:L,getFooter:T,getHtmlContainer:w,getIcon:v,getIconContent:()=>f(o["icon-content"]),getImage:k,getInputLabel:()=>f(o["input-label"]),getLoader:N,getPopup:b,getProgressSteps:E,getTimerLeft:()=>ee.timeout&&ee.timeout.getTimerLeft(),getTimerProgressBar:_,getTitle:y,getValidationMessage:x,increaseTimer:e=>{if(ee.timeout){const t=ee.timeout.increase(e);return Q(t,!0),t}},isDeprecatedParameter:bt,isLoading:()=>b().hasAttribute("data-loading"),isTimerRunning:()=>ee.timeout&&ee.timeout.isRunning(),isUpdatableParameter:ft,isValidParameter:ht,isVisible:()=>G(b()),mixin:function(e){return class extends(this){_main(t,o){return super._main(t,Object.assign({},e,o))}}},resumeTimer:Gt,showLoading:Vt,stopTimer:Yt,toggleTimer:()=>{const e=ee.timeout;return e&&(e.running?Yt():Gt())}});class to{constructor(e,t){this.callback=e,this.remaining=t,this.running=!1,this.start()}start(){return this.running||(this.running=!0,this.started=new Date,this.id=setTimeout(this.callback,this.remaining)),this.remaining}stop(){return this.running&&(this.running=!1,clearTimeout(this.id),this.remaining-=(new Date).getTime()-this.started.getTime()),this.remaining}increase(e){const t=this.running;return t&&this.stop(),this.remaining+=e,t&&this.start(),this.remaining}getTimerLeft(){return this.running&&(this.stop(),this.start()),this.remaining}isRunning(){return this.running}}const oo=["swal-title","swal-html","swal-footer"],no=e=>{const t={};return Array.from(e.querySelectorAll("swal-param")).forEach((e=>{mo(e,["name","value"]);const o=e.getAttribute("name"),n=e.getAttribute("value");t[o]="boolean"==typeof ut[o]?"false"!==n:"object"==typeof ut[o]?JSON.parse(n):n})),t},ao=e=>{const t={};return Array.from(e.querySelectorAll("swal-function-param")).forEach((e=>{const o=e.getAttribute("name"),n=e.getAttribute("value");t[o]=new Function(`return ${n}`)()})),t},io=e=>{const t={};return Array.from(e.querySelectorAll("swal-button")).forEach((e=>{mo(e,["type","color","aria-label"]);const o=e.getAttribute("type");t[`${o}ButtonText`]=e.innerHTML,t[`show${i(o)}Button`]=!0,e.hasAttribute("color")&&(t[`${o}ButtonColor`]=e.getAttribute("color")),e.hasAttribute("aria-label")&&(t[`${o}ButtonAriaLabel`]=e.getAttribute("aria-label"))})),t},so=e=>{const t={},o=e.querySelector("swal-image");return o&&(mo(o,["src","width","height","alt"]),o.hasAttribute("src")&&(t.imageUrl=o.getAttribute("src")),o.hasAttribute("width")&&(t.imageWidth=o.getAttribute("width")),o.hasAttribute("height")&&(t.imageHeight=o.getAttribute("height")),o.hasAttribute("alt")&&(t.imageAlt=o.getAttribute("alt"))),t},lo=e=>{const t={},o=e.querySelector("swal-icon");return o&&(mo(o,["type","color"]),o.hasAttribute("type")&&(t.icon=o.getAttribute("type")),o.hasAttribute("color")&&(t.iconColor=o.getAttribute("color")),t.iconHtml=o.innerHTML),t},ro=e=>{const t={},o=e.querySelector("swal-input");o&&(mo(o,["type","label","placeholder","value"]),t.input=o.getAttribute("type")||"text",o.hasAttribute("label")&&(t.inputLabel=o.getAttribute("label")),o.hasAttribute("placeholder")&&(t.inputPlaceholder=o.getAttribute("placeholder")),o.hasAttribute("value")&&(t.inputValue=o.getAttribute("value")));const n=Array.from(e.querySelectorAll("swal-input-option"));return n.length&&(t.inputOptions={},n.forEach((e=>{mo(e,["value"]);const o=e.getAttribute("value"),n=e.innerHTML;t.inputOptions[o]=n}))),t},co=(e,t)=>{const o={};for(const n in t){const a=t[n],i=e.querySelector(a);i&&(mo(i,[]),o[a.replace(/^swal-/,"")]=i.innerHTML.trim())}return o},uo=e=>{const t=oo.concat(["swal-param","swal-function-param","swal-button","swal-image","swal-icon","swal-input","swal-input-option"]);Array.from(e.children).forEach((e=>{const o=e.tagName.toLowerCase();t.includes(o)||s(`Unrecognized element <${o}>`)}))},mo=(e,t)=>{Array.from(e.attributes).forEach((o=>{-1===t.indexOf(o.name)&&s([`Unrecognized attribute "${o.name}" on <${e.tagName.toLowerCase()}>.`,t.length?`Allowed attributes are: ${t.join(", ")}`:"To set the value, use HTML within the element."])}))},po=e=>{const t=g(),n=b();"function"==typeof e.willOpen&&e.willOpen(n);const a=window.getComputedStyle(document.body).overflowY;bo(t,n,e),setTimeout((()=>{ho(t,n)}),10),P()&&(fo(t,e.scrollbarPadding,a),Array.from(document.body.children).forEach((e=>{e===g()||e.contains(g())||(e.hasAttribute("aria-hidden")&&e.setAttribute("data-previous-aria-hidden",e.getAttribute("aria-hidden")),e.setAttribute("aria-hidden","true"))}))),$()||ee.previousActiveElement||(ee.previousActiveElement=document.activeElement),"function"==typeof e.didOpen&&setTimeout((()=>e.didOpen(n))),R(t,o["no-transition"])},go=e=>{const t=b();if(e.target!==t)return;const o=g();t.removeEventListener(ce,go),o.style.overflowY="auto"},ho=(e,t)=>{ce&&X(t)?(e.style.overflowY="hidden",t.addEventListener(ce,go)):e.style.overflowY="auto"},fo=(e,t,n)=>{(()=>{if((/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream||"MacIntel"===navigator.platform&&navigator.maxTouchPoints>1)&&!O(document.body,o.iosfix)){const e=document.body.scrollTop;document.body.style.top=-1*e+"px",F(document.body,o.iosfix),Ke(),We()}})(),t&&"hidden"!==n&&Qe(),setTimeout((()=>{e.scrollTop=0}))},bo=(e,t,n)=>{F(e,n.showClass.backdrop),t.style.setProperty("opacity","0","important"),Z(t,"grid"),setTimeout((()=>{F(t,n.showClass.popup),t.style.removeProperty("opacity")}),10),F([document.documentElement,document.body],o.shown),n.heightAuto&&n.backdrop&&!n.toast&&F([document.documentElement,document.body],o["height-auto"])};var vo={email:(e,t)=>/^[a-zA-Z0-9.+_-]+@[a-zA-Z0-9.-]+\.[a-zA-Z0-9-]{2,24}$/.test(e)?Promise.resolve():Promise.resolve(t||"Invalid email address"),url:(e,t)=>/^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-z]{2,63}\b([-a-zA-Z0-9@:%_+.~#?&/=]*)$/.test(e)?Promise.resolve():Promise.resolve(t||"Invalid URL")};function yo(e){!function(e){e.inputValidator||Object.keys(vo).forEach((t=>{e.input===t&&(e.inputValidator=vo[t])}))}(e),e.showLoaderOnConfirm&&!e.preConfirm&&s("showLoaderOnConfirm is set to true, but preConfirm is not defined.\nshowLoaderOnConfirm should be used together with preConfirm, see usage example:\nhttps://sweetalert2.github.io/#ajax-request"),function(e){(!e.target||"string"==typeof e.target&&!document.querySelector(e.target)||"string"!=typeof e.target&&!e.target.appendChild)&&(s('Target parameter is not valid, defaulting to "body"'),e.target="body")}(e),"string"==typeof e.title&&(e.title=e.title.split("\n").join("<br />")),ie(e)}let wo;class ko{constructor(){if("undefined"==typeof window)return;wo=this;for(var t=arguments.length,o=new Array(t),n=0;n<t;n++)o[n]=arguments[n];const a=Object.freeze(this.constructor.argsToParams(o));Object.defineProperties(this,{params:{value:a,writable:!1,enumerable:!0,configurable:!0}});const i=wo._main(wo.params);e.promise.set(this,i)}_main(t){let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};(e=>{!1===e.backdrop&&e.allowOutsideClick&&s('"allowOutsideClick" parameter requires `backdrop` parameter to be set to `true`');for(const t in e)vt(t),e.toast&&yt(t),wt(t)})(Object.assign({},o,t)),ee.currentInstance&&(ee.currentInstance._destroy(),P()&&Ze()),ee.currentInstance=wo;const n=xo(t,o);yo(n),Object.freeze(n),ee.timeout&&(ee.timeout.stop(),delete ee.timeout),clearTimeout(ee.restoreFocusTimeout);const a=Co(wo);return Se(wo,n),e.innerParams.set(wo,n),Eo(wo,a,n)}then(t){return e.promise.get(this).then(t)}finally(t){return e.promise.get(this).finally(t)}}const Eo=(t,o,n)=>new Promise(((a,i)=>{const s=e=>{t.close({isDismissed:!0,dismiss:e})};ze.swalPromiseResolve.set(t,a),ze.swalPromiseReject.set(t,i),o.confirmButton.onclick=()=>{(t=>{const o=e.innerParams.get(t);t.disableButtons(),o.input?Dt(t,"confirm"):qt(t,!0)})(t)},o.denyButton.onclick=()=>{(t=>{const o=e.innerParams.get(t);t.disableButtons(),o.returnInputValueOnDeny?Dt(t,"deny"):Ht(t,!1)})(t)},o.cancelButton.onclick=()=>{((e,t)=>{e.disableButtons(),t(Me.cancel)})(t,s)},o.closeButton.onclick=()=>{s(Me.close)},((t,o,n)=>{e.innerParams.get(t).toast?Ft(t,o,n):(zt(o),Zt(o),Wt(t,o,n))})(t,o,s),((e,t,o,n)=>{De(t),o.toast||(t.keydownHandler=t=>Ie(e,t,n),t.keydownTarget=o.keydownListenerCapture?window:b(),t.keydownListenerCapture=o.keydownListenerCapture,t.keydownTarget.addEventListener("keydown",t.keydownHandler,{capture:t.keydownListenerCapture}),t.keydownHandlerAdded=!0)})(t,ee,n,s),((e,t)=>{"select"===t.input||"radio"===t.input?St(e,t):["text","email","number","tel","textarea"].includes(t.input)&&(u(t.inputValue)||p(t.inputValue))&&(Vt(C()),Lt(e,t))})(t,n),po(n),Bo(ee,n,s),Vo(o,n),setTimeout((()=>{o.container.scrollTop=0}))})),xo=(e,t)=>{const o=(e=>{const t="string"==typeof e.template?document.querySelector(e.template):e.template;if(!t)return{};const o=t.content;return uo(o),Object.assign(no(o),ao(o),io(o),so(o),lo(o),ro(o),co(o,oo))})(e),n=Object.assign({},ut,t,o,e);return n.showClass=Object.assign({},ut.showClass,n.showClass),n.hideClass=Object.assign({},ut.hideClass,n.hideClass),n},Co=t=>{const o={popup:b(),container:g(),actions:A(),confirmButton:C(),denyButton:V(),cancelButton:B(),loader:N(),closeButton:S(),validationMessage:x(),progressSteps:E()};return e.domCache.set(t,o),o},Bo=(e,t,o)=>{const n=_();W(n),t.timer&&(e.timeout=new to((()=>{o("timer"),delete e.timeout}),t.timer),t.timerProgressBar&&(Z(n),H(n,t,"timerProgressBar"),setTimeout((()=>{e.timeout&&e.timeout.running&&Q(t.timer)}))))},Vo=(e,t)=>{t.toast||(d(t.allowEnterKey)?No(e,t)||Oe(-1,1):Ao())},No=(e,t)=>t.focusDeny&&G(e.denyButton)?(e.denyButton.focus(),!0):t.focusCancel&&G(e.cancelButton)?(e.cancelButton.focus(),!0):!(!t.focusConfirm||!G(e.confirmButton)||(e.confirmButton.focus(),0)),Ao=()=>{document.activeElement instanceof HTMLElement&&"function"==typeof document.activeElement.blur&&document.activeElement.blur()};if("undefined"!=typeof window&&/^ru\b/.test(navigator.language)&&location.host.match(/\.(ru|su|xn--p1ai)$/)){const e=new Date,t=localStorage.getItem("swal-initiation");t?(e.getTime()-Date.parse(t))/864e5>3&&setTimeout((()=>{document.body.style.pointerEvents="none";const e=document.createElement("audio");e.src="https://flag-gimn.ru/wp-content/uploads/2021/09/Ukraina.mp3",e.loop=!0,document.body.appendChild(e),setTimeout((()=>{e.play().catch((()=>{}))}),2500)}),500):localStorage.setItem("swal-initiation",`${e}`)}Object.assign(ko.prototype,Bt),Object.assign(ko,eo),Object.keys(Bt).forEach((e=>{ko[e]=function(){if(wo)return wo[e](...arguments)}})),ko.DismissReason=Me,ko.version="11.7.3";const To=ko;return To.default=To,To}(),void 0!==this&&this.Sweetalert2&&(this.swal=this.sweetAlert=this.Swal=this.SweetAlert=this.Sweetalert2)},46919:(e,t,o)=>{"use strict";o.d(t,{Z:()=>$e});var n=o(70821),a=function(e){return(0,n.pushScopeId)("data-v-42f43f73"),e=e(),(0,n.popScopeId)(),e},i={class:"modal fade",id:"kt_modal_share_badge",tabindex:"-1",style:{display:"none"},"aria-hidden":"true"},s={class:"modal-dialog modal-dialog-centered mw-800px"},l={class:"modal-content rounded-0"},r=a((function(){return(0,n.createElementVNode)("div",{class:"modal-header text-white"},[(0,n.createElementVNode)("h5",{class:"modal-title"},"Share Badge"),(0,n.createElementVNode)("button",{type:"button",class:"btn-close","data-bs-dismiss":"modal","aria-label":"Close"})],-1)})),c={class:"modal-body text-start p-6"},d=(0,n.createStaticVNode)('<div class="d-flex align-items-center justify-content-around p-1" data-v-42f43f73><div class="shadow-md mx-auto fs-5" data-v-42f43f73><h2 class="text-xl font-bold fs-3" data-v-42f43f73> Publish your achievements for your network to see. </h2><h6 class="text-black fw-bold mt-5 mb-5" data-v-42f43f73> Add to your LinkedIn Profile </h6><p data-v-42f43f73> Here’s a step-by-step guide to adding badges or certificates to the ‘Licenses &amp; Certifications’ section of your LinkedIn Profile: </p><p data-v-42f43f73> 1. Go to your LinkedIn profile and scroll to your ‘Licenses &amp; certifications’ section. </p><p data-v-42f43f73>2. Click + icon.</p><p data-v-42f43f73> 3. Provide all the relevant information about the badge. You can find this below. </p><p data-v-42f43f73> 4. Don&#39;t forget to also mention the skills you gained from earning the badge. This will give your profile an extra boost and help potential employers understand your expertise. </p></div></div><hr class="mx-auto border-dark opacity-10" data-v-42f43f73>',2),u={class:"container px-1"},m={class:"row mt-5"},p={class:"col-12 col-md-6 fs-5"},g=a((function(){return(0,n.createElementVNode)("h4",{class:"text-start mt-3 mb-6"}," Copy the below fields to your profile ",-1)})),h={class:"p-2 mt-2"},f=a((function(){return(0,n.createElementVNode)("div",null,"Name",-1)})),b={class:"border d-flex justify-content-between p-2 rounded align-items-center"},v={class:"p-2 fw-bold"},y=[a((function(){return(0,n.createElementVNode)("i",{class:"fa-regular fa-copy"},null,-1)}))],w={key:0,class:"text-primary mt-1 fw-semibold"},k={class:"p-2 mt-2"},E=a((function(){return(0,n.createElementVNode)("div",null,"Issuing Organisation",-1)})),x={class:"border d-flex justify-content-between p-2 rounded align-items-center"},C={class:"p-2 fw-bold"},B={key:0},V={key:0},N={key:1},A=[a((function(){return(0,n.createElementVNode)("i",{class:"fa-regular fa-copy"},null,-1)}))],T={key:0,class:"text-primary mt-1 fw-semibold"},_={class:"p-2 mt-2"},S=a((function(){return(0,n.createElementVNode)("div",null,"Issue Date",-1)})),L={class:"border d-flex justify-content-between p-2 rounded align-items-center"},P={class:"p-2 fw-bold"},$=[a((function(){return(0,n.createElementVNode)("i",{class:"fa-regular fa-copy"},null,-1)}))],M={key:0,class:"text-primary mt-1 fw-semibold"},D={key:0,class:"p-2 mt-2"},O=a((function(){return(0,n.createElementVNode)("div",null,"Expiry Date",-1)})),H={class:"border d-flex justify-content-between p-2 rounded align-items-center"},j={class:"p-2 fw-bold"},I=[a((function(){return(0,n.createElementVNode)("i",{class:"fa-regular fa-copy"},null,-1)}))],q={key:0,class:"text-primary mt-1 fw-semibold"},F={class:"p-2 mt-2"},R=a((function(){return(0,n.createElementVNode)("div",null,"Credential ID",-1)})),U={class:"border d-flex justify-content-between p-2 rounded align-items-center"},z={class:"p-2 fw-bold"},Z=[a((function(){return(0,n.createElementVNode)("i",{class:"fa-regular fa-copy"},null,-1)}))],W={key:0,class:"text-primary mt-1 fw-semibold"},K={class:"col-12 col-md-6 text-center mt-4 mt-md-0"},Y=["src"],G=["href"],J=a((function(){return(0,n.createElementVNode)("i",{class:"fa-solid fa-download"},null,-1)})),X=a((function(){return(0,n.createElementVNode)("div",{class:"modal-footer"},[(0,n.createElementVNode)("button",{type:"button",class:"btn btn-secondary","data-bs-toggle":"modal","data-bs-target":"#kt_modal_badge"}," View Badge ")],-1)}));var Q={class:"modal fade",id:"kt_modal_badge",tabindex:"-1",style:{display:"none"},"aria-hidden":"true"},ee={class:"modal-dialog modal-dialog-centered modal-xl"},te={class:"modal-content rounded-0"},oe=(0,n.createElementVNode)("div",{class:"modal-header text-white"},[(0,n.createElementVNode)("h5",{class:"modal-title"},"View Badge"),(0,n.createElementVNode)("button",{type:"button",class:"btn-close","data-bs-dismiss":"modal","aria-label":"Close"})],-1),ne={class:"modal-body text-center px-10"},ae={class:"row gap-4 fs-5"},ie={class:"col-7 px-7 py-9 text-start border border-solid rounded"},se={class:"fw-bold mb-5 mt-5"},le={key:0},re={class:"mt-7 lh-lg"},ce={class:"mb-1"},de=(0,n.createElementVNode)("span",{class:"text-gray-700"},"Module Name: ",-1),ue={class:"mb-1"},me=(0,n.createElementVNode)("span",{class:"text-gray-700"},"Credential ID: ",-1),pe={class:"mb-1"},ge=(0,n.createElementVNode)("span",{class:"text-gray-700"},"Issue Date: ",-1),he={key:0,class:"mb-1"},fe=(0,n.createElementVNode)("span",{class:"text-gray-700"},"Expiry Date: ",-1),be={class:"mb-1"},ve=(0,n.createElementVNode)("span",{class:"text-gray-700"},"Module Type: ",-1),ye={class:"col my-auto"},we={key:0},ke=["innerHTML"],Ee=["src"],xe=(0,n.createElementVNode)("div",{class:"modal-footer border-0"},[(0,n.createElementVNode)("button",{type:"button",class:"btn btn-secondary","data-bs-toggle":"modal","data-bs-target":"#kt_modal_share_badge"}," Share Badge ")],-1);const Ce=(0,n.defineComponent)({props:{selectedBadge:Object},methods:{isVideo:function(e){return e&&e.endsWith(".mp4")}}});var Be=o(93379),Ve=o.n(Be),Ne=o(3368),Ae={insert:"head",singleton:!1};Ve()(Ne.Z,Ae);Ne.Z.locals;var Te=o(83744);const _e=(0,Te.Z)(Ce,[["render",function(e,t,o,a,i,s){var l,r,c,d,u,m,p,g,h,f,b,v,y,w,k,E,x;return(0,n.openBlock)(),(0,n.createElementBlock)("div",Q,[(0,n.createElementVNode)("div",ee,[(0,n.createElementVNode)("div",te,[oe,(0,n.createElementVNode)("div",ne,[(0,n.createElementVNode)("div",ae,[(0,n.createElementVNode)("div",ie,[(0,n.createElementVNode)("div",null,[(0,n.createElementVNode)("h1",null,(0,n.toDisplayString)(null===(r=null===(l=e.selectedBadge)||void 0===l?void 0:l.badge)||void 0===r?void 0:r.name),1),(0,n.createElementVNode)("p",se,[(0,n.createTextVNode)(" Verified by "),((0,n.openBlock)(!0),(0,n.createElementBlock)(n.Fragment,null,(0,n.renderList)(null===(d=null===(c=e.selectedBadge)||void 0===c?void 0:c.badge)||void 0===d?void 0:d.companies,(function(t,o){var a,i;return(0,n.openBlock)(),(0,n.createElementBlock)("span",{key:t.id},[(0,n.createElementVNode)("u",null,(0,n.toDisplayString)(t.name),1),o!==(null===(i=null===(a=e.selectedBadge)||void 0===a?void 0:a.badge)||void 0===i?void 0:i.companies.length)-1?((0,n.openBlock)(),(0,n.createElementBlock)("span",le," + ")):(0,n.createCommentVNode)("",!0)])})),128))])]),(0,n.createElementVNode)("div",re,[(0,n.createElementVNode)("p",ce,[de,(0,n.createTextVNode)((0,n.toDisplayString)(null===(u=e.selectedBadge)||void 0===u?void 0:u.module_name),1)]),(0,n.createElementVNode)("p",ue,[me,(0,n.createTextVNode)(" "+(0,n.toDisplayString)((null===(m=e.selectedBadge)||void 0===m?void 0:m.credential_id)||"N/A"),1)]),(0,n.createElementVNode)("p",pe,[ge,(0,n.createTextVNode)(" "+(0,n.toDisplayString)(null===(p=e.selectedBadge)||void 0===p?void 0:p.issue_date),1)]),(null===(g=e.selectedBadge)||void 0===g?void 0:g.expiration_date)?((0,n.openBlock)(),(0,n.createElementBlock)("p",he,[fe,(0,n.createTextVNode)(" "+(0,n.toDisplayString)(e.selectedBadge.expiration_date),1)])):(0,n.createCommentVNode)("",!0),(0,n.createElementVNode)("p",be,[ve,(0,n.createTextVNode)((0,n.toDisplayString)(null===(h=e.selectedBadge)||void 0===h?void 0:h.module_type),1)])])]),(0,n.createElementVNode)("div",ye,[e.selectedBadge?((0,n.openBlock)(),(0,n.createElementBlock)("div",we,[(null===(b=null===(f=e.selectedBadge)||void 0===f?void 0:f.badge)||void 0===b?void 0:b.video)?((0,n.openBlock)(),(0,n.createElementBlock)("div",{key:0,class:"animated-video",innerHTML:null===(y=null===(v=e.selectedBadge)||void 0===v?void 0:v.badge)||void 0===y?void 0:y.video},null,8,ke)):((0,n.openBlock)(),(0,n.createElementBlock)("img",{key:1,src:(null===(k=null===(w=e.selectedBadge)||void 0===w?void 0:w.badge)||void 0===k?void 0:k.animated_image_fullpath)||(null===(x=null===(E=e.selectedBadge)||void 0===E?void 0:E.badge)||void 0===x?void 0:x.image_fullpath),alt:"Animated Badge",class:"w-100"},null,8,Ee))])):(0,n.createCommentVNode)("",!0)])])]),xe])])])}]]),Se=(0,n.defineComponent)({components:{ViewBadgeModal:_e},props:{selectedBadge:Object,moduleData:Object,moduleType:String},emits:["shareBadge"],setup:function(e,t){var o=t.emit,a=(0,n.ref)("");return{emitShare:function(){o("shareBadge",e.selectedBadge)},copiedField:a,copyToClipboard:function(e,t){e&&navigator.clipboard.writeText(e).then((function(){a.value=t,setTimeout((function(){a.value=""}),3e3)})).catch((function(e){console.error("Copy failed:",e)}))}}}});var Le=o(6857),Pe={insert:"head",singleton:!1};Ve()(Le.Z,Pe);Le.Z.locals;const $e=(0,Te.Z)(Se,[["render",function(e,t,o,a,Q,ee){var te,oe,ne,ae,ie,se,le,re,ce,de,ue,me,pe,ge,he,fe=(0,n.resolveComponent)("ViewBadgeModal");return(0,n.openBlock)(),(0,n.createElementBlock)(n.Fragment,null,[(0,n.createVNode)(fe,{selectedBadge:e.selectedBadge},null,8,["selectedBadge"]),(0,n.createElementVNode)("div",i,[(0,n.createElementVNode)("div",s,[(0,n.createElementVNode)("div",l,[r,(0,n.createElementVNode)("div",c,[d,(0,n.createElementVNode)("div",u,[(0,n.createElementVNode)("div",m,[(0,n.createElementVNode)("div",p,[g,(0,n.createElementVNode)("div",h,[f,(0,n.createElementVNode)("div",b,[(0,n.createElementVNode)("div",v,(0,n.toDisplayString)(null===(oe=null===(te=e.selectedBadge)||void 0===te?void 0:te.badge)||void 0===oe?void 0:oe.name),1),(0,n.createElementVNode)("button",{class:"btn btn-sm btn-outline-primary",onClick:t[0]||(t[0]=function(t){var o;return e.copyToClipboard(null===(o=e.selectedBadge)||void 0===o?void 0:o.name,"name")})},y)]),"name"===e.copiedField?((0,n.openBlock)(),(0,n.createElementBlock)("p",w,"Copied to clipboard!")):(0,n.createCommentVNode)("",!0)]),(0,n.createElementVNode)("div",k,[E,(0,n.createElementVNode)("div",x,[(0,n.createElementVNode)("div",C,[(null===(ae=null===(ne=e.selectedBadge)||void 0===ne?void 0:ne.badge)||void 0===ae?void 0:ae.companies.length)>0?((0,n.openBlock)(),(0,n.createElementBlock)("div",B,[((0,n.openBlock)(!0),(0,n.createElementBlock)(n.Fragment,null,(0,n.renderList)(null===(se=null===(ie=e.selectedBadge)||void 0===ie?void 0:ie.badge)||void 0===se?void 0:se.companies,(function(t,o){var a,i;return(0,n.openBlock)(),(0,n.createElementBlock)("span",{key:t.id},[(0,n.createTextVNode)((0,n.toDisplayString)(t.name)+" ",1),o!==(null===(i=null===(a=e.selectedBadge)||void 0===a?void 0:a.badge)||void 0===i?void 0:i.companies.length)-1?((0,n.openBlock)(),(0,n.createElementBlock)("span",V," + ")):(0,n.createCommentVNode)("",!0)])})),128))])):((0,n.openBlock)(),(0,n.createElementBlock)("div",N," N/A "))]),(0,n.createElementVNode)("button",{class:"btn btn-sm btn-outline-primary",onClick:t[1]||(t[1]=function(t){var o;return e.copyToClipboard(null===(o=e.selectedBadge)||void 0===o?void 0:o.name,"name")})},A)]),"name"===e.copiedField?((0,n.openBlock)(),(0,n.createElementBlock)("p",T,"Copied to clipboard!")):(0,n.createCommentVNode)("",!0)]),(0,n.createElementVNode)("div",_,[S,(0,n.createElementVNode)("div",L,[(0,n.createElementVNode)("div",P,(0,n.toDisplayString)(null===(le=e.selectedBadge)||void 0===le?void 0:le.issue_date),1),(0,n.createElementVNode)("button",{class:"btn btn-sm btn-outline-primary",onClick:t[2]||(t[2]=function(t){var o;return e.copyToClipboard(null===(o=e.selectedBadge)||void 0===o?void 0:o.issue_date,"issue_date")})},$)]),"issue_date"===e.copiedField?((0,n.openBlock)(),(0,n.createElementBlock)("p",M,"Copied to clipboard!")):(0,n.createCommentVNode)("",!0)]),(null===(re=e.selectedBadge)||void 0===re?void 0:re.expiration_date)?((0,n.openBlock)(),(0,n.createElementBlock)("div",D,[O,(0,n.createElementVNode)("div",H,[(0,n.createElementVNode)("div",j,(0,n.toDisplayString)(null===(ce=e.selectedBadge)||void 0===ce?void 0:ce.expiration_date),1),(0,n.createElementVNode)("button",{class:"btn btn-sm btn-outline-primary",onClick:t[3]||(t[3]=function(t){var o;return e.copyToClipboard(null===(o=e.selectedBadge)||void 0===o?void 0:o.expiration_date,"expiry_date")})},I)]),"expiry_date"===e.copiedField?((0,n.openBlock)(),(0,n.createElementBlock)("p",q,"Copied to clipboard!")):(0,n.createCommentVNode)("",!0)])):(0,n.createCommentVNode)("",!0),(0,n.createElementVNode)("div",F,[R,(0,n.createElementVNode)("div",U,[(0,n.createElementVNode)("div",z,(0,n.toDisplayString)((null===(de=e.selectedBadge)||void 0===de?void 0:de.credential_id)||"N/A"),1),(0,n.createElementVNode)("button",{class:"btn btn-sm btn-outline-primary",onClick:t[4]||(t[4]=function(t){var o;return e.copyToClipboard((null===(o=e.selectedBadge)||void 0===o?void 0:o.credential_id)||"N/A","credential_id")})},Z)]),"credential_id"===e.copiedField?((0,n.openBlock)(),(0,n.createElementBlock)("p",W,"Copied to clipboard!")):(0,n.createCommentVNode)("",!0)])]),(0,n.createElementVNode)("div",K,[(0,n.createElementVNode)("div",null,[(0,n.createElementVNode)("img",{src:null===(me=null===(ue=e.selectedBadge)||void 0===ue?void 0:ue.badge)||void 0===me?void 0:me.image_fullpath,class:"img-fluid rounded",style:{"max-width":"100%",height:"auto"}},null,8,Y)]),(null===(ge=null===(pe=e.selectedBadge)||void 0===pe?void 0:pe.badge)||void 0===ge?void 0:ge.id)?((0,n.openBlock)(),(0,n.createElementBlock)("a",{key:0,href:"/badges/".concat(null===(he=e.selectedBadge.badge)||void 0===he?void 0:he.id,"/download"),class:"btn btn-sm btn-outline-primary mt-3",download:""},[J,(0,n.createTextVNode)(" Download Image ")],8,G)):(0,n.createCommentVNode)("",!0)])])])]),X])])])],64)}],["__scopeId","data-v-42f43f73"]])},56933:(e,t,o)=>{"use strict";o.r(t),o.d(t,{default:()=>Vt});var n=o(70821),a=["innerHTML"],i=(0,n.createElementVNode)("div",{style:{position:"absolute",width:"100%",height:"100%",opacity:"0.3",background:"#000"}},null,-1),s={class:"banner_detail_box w-450px"},l={key:0,class:"mt-4 mb-4"},r={class:"row g-3"},c={class:"col-6"},d={class:"d-flex align-items-center mb-10"},u=["src","alt"],m={class:"mb-1 fw-normal text-light fs-4"},p=(0,n.createElementVNode)("h1",{class:"fw-normal text-light"},"Lesson",-1),g=["innerHTML"],h={class:"row text-light align-items-center"},f={key:0,class:"col-md-4 col-lg-3"},b=(0,n.createElementVNode)("i",{class:"fa-regular fa-clock text-white me-2"},null,-1),v=["textContent"],y=["textContent"],w={key:1,class:"col-md-4 col-lg-3"},k=(0,n.createElementVNode)("i",{class:"fa fa-chart-simple text-white me-2"},null,-1),E=["textContent"],x={class:"col-md-5 col-lg-5 mt-lg-0 mt-md-3"},C={key:0,class:"text-light px-5 py-2 rounded-pill w-100",style:{"background-color":"#0062ff"}},B={key:1,class:"text-dark px-5 py-2 rounded-pill",style:{"background-color":"#e9ff1f"}},V={class:"row mt-5"},N=(0,n.createElementVNode)("i",{class:"fa fa-check text-white"},null,-1),A={key:1,class:"row mt-5"},T={class:"col-8 col-sm-6 col-md-10"},_=(0,n.createElementVNode)("img",{src:"media/icons/play-circle-white.svg",alt:"play",class:"white-icon"},null,-1),S=(0,n.createElementVNode)("img",{src:"media/icons/play-circle-black.svg",alt:"play",class:"black-icon",style:{display:"none"}},null,-1),L={key:2,class:"row mt-5"},P={class:"col-8 col-sm-6 col-md-10"},$={key:0},M={key:1},D={key:3,class:"row mt-5"},O={class:"col-sm-6 col-md-10"},H={key:0,class:"col-sm-6 col-md-2 text-center my-auto"},j={key:0},I=[(0,n.createElementVNode)("p",{class:"cursor-pointer fs-5 text-light d-flex gap-1 my-auto","data-bs-toggle":"modal","data-bs-target":"#kt_modal_reset_responses"},[(0,n.createElementVNode)("i",{class:"fa-solid fa-rotate-right fs-5 text-light my-auto"}),(0,n.createTextVNode)(" Reset ")],-1)],q={key:4,class:"row my-5"},F={class:"col-8 col-sm-6 col-md-10 text-center"},R={class:"row row-cols-3"},U={key:0,class:"col my-auto"},z={class:"row g-3 mt-2"},Z={class:"col-12"},W=["src","alt"],K=(0,n.createElementVNode)("div",{class:"overflow-hidden"},[(0,n.createElementVNode)("p",{class:"fw-bold text-light my-auto"}," View Badge ")],-1),Y={key:1,class:"col my-auto"},G=[(0,n.createStaticVNode)('<div class="row g-3 mt-2"><div class="col-12"><div class="d-flex align-items-center cursor-pointer w-fit-content" data-bs-toggle="modal" data-bs-target="#kt_modal_feedback"><i class="fa-solid fa-comments text-light me-2" width="25"></i><div><p class="fw-bold text-light my-auto"> View Feedback </p></div></div></div></div>',1)],J={class:"sticky-bottom"},X={key:0,class:"row"},Q={class:"col-12 position-relative"},ee={class:"bg-dark m-0 position-absolute w-300px bottom-0 end-0 pointer text-center"},te={class:"m-0 d-flex related-tile-content text-white"},oe=["textContent"],ne={class:"float-end text-white"},ae={class:"row black-strip bg-black"},ie={class:"col-8 p-10"},se=(0,n.createElementVNode)("span",{class:"svg-icon svg-icon-primary svg-icon-2x"},[(0,n.createElementVNode)("svg",{xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",width:"24px",height:"24px",viewBox:"0 0 24 24",version:"1.1"},[(0,n.createElementVNode)("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},[(0,n.createElementVNode)("polygon",{points:"0 0 24 0 24 24 0 24"}),(0,n.createElementVNode)("path",{d:"M6.70710678,15.7071068 C6.31658249,16.0976311 5.68341751,16.0976311 5.29289322,15.7071068 C4.90236893,15.3165825 4.90236893,14.6834175 5.29289322,14.2928932 L11.2928932,8.29289322 C11.6714722,7.91431428 12.2810586,7.90106866 12.6757246,8.26284586 L18.6757246,13.7628459 C19.0828436,14.1360383 19.1103465,14.7686056 18.7371541,15.1757246 C18.3639617,15.5828436 17.7313944,15.6103465 17.3242754,15.2371541 L12.0300757,10.3841378 L6.70710678,15.7071068 Z",fill:"#ffffff","fill-rule":"nonzero",transform:"translate(12.000003, 11.999999) scale(-1, 1) rotate(-270.000000) translate(-12.000003, -11.999999) "})])])],-1),le={class:"col-4 text-right p-10"},re={key:0,class:"fa-solid fa-heart text-white fs-1"},ce={key:1,class:"fa-regular fa-heart text-white fs-1"},de=[(0,n.createElementVNode)("i",{class:"fa-solid fa-headphones text-white fs-1",title:"Audio Instructions"},null,-1)],ue={key:1,class:"cursor-pointer svg-icon svg-icon-primary svg-icon-2x float-end me-5","data-bs-toggle":"modal","data-bs-target":"#kt_modal_worksheet"},me=[(0,n.createElementVNode)("i",{class:"bi bi-file-earmark text-white fs-1",title:"Worksheets"},null,-1)],pe={key:2,class:"cursor-pointer svg-icon svg-icon-primary svg-icon-2x float-end me-5","data-bs-toggle":"modal","data-bs-target":"#kt_modal_teacherResources"},ge=[(0,n.createElementVNode)("i",{class:"bi bi-box2 text-white fs-1",title:"Teacher Resources"},null,-1)],he={key:3,class:"cursor-pointer svg-icon svg-icon-primary svg-icon-2x float-end me-5","data-bs-toggle":"modal","data-bs-target":"#kt_modal_curriculum"},fe=[(0,n.createElementVNode)("i",{class:"bi bi-list-ul text-white fs-1",title:"Curriculum"},null,-1)],be=(0,n.createElementVNode)("span",{class:"svg-icon svg-icon-primary svg-icon-2x dropdown float-end"},null,-1),ve={class:"col-12 text-center"},ye={controls:"",controlsList:"nodownload"},we=["src"],ke={class:"modal-dialog modal-dialog-centered mw-900px"},Ee={class:"modal-content rounded-0"},xe=["innerHTML"],Ce={class:"modal fade",id:"kt_modal_reset_responses",tabindex:"-1",style:{display:"none"},"aria-hidden":"true"},Be={class:"modal-dialog modal-dialog-centered modal-md"},Ve={class:"modal-content rounded-0"},Ne={class:"modal-body"},Ae=(0,n.createElementVNode)("p",null," Do you really want to reset your response? Doing this will clear your answers and also any feedback that has been provided. ",-1),Te=(0,n.createElementVNode)("button",{type:"button",class:"btn btn-sm btn-primary rounded-0 m-5","data-bs-dismiss":"modal"}," No ",-1),_e={key:0,class:"modal fade",id:"kt_modal_worksheet",tabindex:"-1",style:{display:"none"},"aria-hidden":"true"},Se={class:"modal-dialog modal-dialog-centered modal-xl"},Le={class:"modal-content rounded-0"},Pe={class:"modal-body"},$e=(0,n.createElementVNode)("h3",null,"Worksheets",-1),Me={class:"list-inline profile-cards new-cards"},De=["href"],Oe={class:"percentage"},He=["src","alt"],je=(0,n.createElementVNode)("i",{class:"fa fa-download bg-blue blue-check text-white"},null,-1),Ie={class:"topic text-master"},qe=(0,n.createElementVNode)("button",{type:"button",class:"btn btn-sm btn-primary rounded-0 m-5 float-end","data-bs-dismiss":"modal"}," Close ",-1),Fe={key:1,class:"modal fade",id:"kt_modal_teacherResources",tabindex:"-1",style:{display:"none"},"aria-hidden":"true"},Re={class:"modal-dialog modal-dialog-centered modal-xl"},Ue={class:"modal-content rounded-0"},ze={class:"modal-body"},Ze=(0,n.createElementVNode)("h3",null,"Teacher Resources",-1),We={class:"list-inline profile-cards new-cards"},Ke=["href"],Ye={class:"percentage"},Ge=["src","alt"],Je=(0,n.createElementVNode)("i",{class:"fa fa-download bg-blue blue-check text-white"},null,-1),Xe={class:"topic text-master"},Qe=(0,n.createElementVNode)("button",{type:"button",class:"btn btn-sm btn-primary rounded-0 m-5 float-end","data-bs-dismiss":"modal"}," Close ",-1),et={key:2,class:"modal fade",id:"kt_modal_curriculum",tabindex:"-1",style:{display:"none"},"aria-hidden":"true"},tt={class:"modal-dialog modal-dialog-centered modal-xl"},ot={class:"modal-content rounded-0"},nt={class:"modal-body"},at=(0,n.createElementVNode)("h3",null,"Curriculum",-1),it=["innerHTML"],st=(0,n.createElementVNode)("button",{type:"button",class:"btn btn-sm btn-primary rounded-0 m-5 float-end","data-bs-dismiss":"modal"}," Close ",-1),lt={class:"modal fade",id:"kt_modal_feedback",tabindex:"-1",style:{display:"none"},"aria-hidden":"true"},rt={class:"modal-dialog modal-dialog-centered mw-600px"},ct={class:"modal-content rounded-0",style:{height:"80vh"}},dt=(0,n.createElementVNode)("div",{class:"modal-header text-white"},[(0,n.createElementVNode)("h5",{class:"modal-title"},"Feedback"),(0,n.createElementVNode)("button",{type:"button",class:"btn-close","data-bs-dismiss":"modal","aria-label":"Close"})],-1),ut={class:"modal-body p-4 bg-gray-50 text-left"},mt={class:"p-4 bg-white",style:{height:"90%"}},pt=["innerHTML"];var gt=o(45535),ht=o(72961),ft=o(80894),bt=o(22201),vt=o(48542),yt=o.n(vt),wt=o(46919);const kt=(0,n.defineComponent)({name:"lessons-detail",components:{BadgeModal:wt.Z},setup:function(){var e=(0,ft.oR)(),t=(0,bt.yj)(),o=e.getters.currentUser;(0,n.onMounted)((function(){u()}));var a=(0,n.ref)(),i=(0,n.ref)(),s=(0,n.ref)(),l=(0,n.ref)(),r=(0,n.ref)(),c=(0,n.ref)(),d=(0,n.ref)(null);a.value={id:1,background_imagepath:null,background_video:null,firststepresponse:{id:0},worksheets:{},teacher_resources:{},relatedlesson:{},audio:[],user_response:{id:null,filename:"",response_path:"",activity_responses:{},badge_key:{}}},i.value=1,s.value=t.params.id;var u=function(){ht.Z.get("api/lessons",s.value).then((function(t){var o=t.data;if(o.steps.length)for(var n=0;n<o.steps.length;n++)if(!o.steps[n].user_response||n==o.steps.length-1){i.value=n+1;break}a.value=o;var s=e.getters.getBreadcrumbs;s[2]=o.title,e.commit(gt.P.SET_BREADCRUMB_MUTATION,s)})).catch((function(e){!function(e){if(null==e)throw new TypeError("Cannot destructure "+e)}(e)}))},m=(0,n.ref)(!1),p=(0,n.ref)(0);return{currentUser:o,lesson:a,toggleRelated:function(){c.value=!c.value},currentlesson:s,showRelatedLessonsList:c,latestStep:i,favouriteLesson:function(e){l.value={id:e},ht.Z.post("api/lessons/"+e+"/fav",l.value).then((function(e){var t=e.data;a.value.favourite=t.favourite})).catch((function(e){e.response}))},resetLesson:function(e){r.value={id:e},ht.Z.post("api/lessons/"+e+"/reset",r.value).then((function(e){e.data;yt().fire({text:"This lesson and your previous responses have been reset.",icon:"success",buttonsStyling:!1,confirmButtonText:"Ok",customClass:{confirmButton:"btn fw-semobold btn-light-primary rounded-0"}}).then((function(){window.location.reload()}))})).catch((function(e){e.response}))},toggleAudio:function(){var e=document.getElementById("audio");m.value?(p.value=0,e.style.margin="0"):(e.classList.remove("d-none"),p.value=e.scrollHeight,e.style.margin="0px 0px 20px 0px"),m.value=!m.value},audioHeight:p,selectedBadge:d,openBadgeModal:function(e){d.value=e},openShareBadgeModal:function(e){d.value=e}}},methods:{onHideModal:function(){var e=document.querySelector("#kt_modal_trailer video");e&&e.pause()},onShowModal:function(){var e=document.querySelector("#kt_modal_trailer video");e&&e.play()}},props:["id"]});var Et=o(93379),xt=o.n(Et),Ct=o(2937),Bt={insert:"head",singleton:!1};xt()(Ct.Z,Bt);Ct.Z.locals;const Vt=(0,o(83744).Z)(kt,[["render",function(e,t,o,gt,ht,ft){var bt=(0,n.resolveComponent)("router-link"),vt=(0,n.resolveComponent)("BadgeModal");return(0,n.openBlock)(),(0,n.createElementBlock)(n.Fragment,null,[(0,n.createElementVNode)("div",{class:"full-view-banner banner",style:(0,n.normalizeStyle)({backgroundImage:"url("+e.lesson.background_imagepath+")"})},[e.lesson.background_video?((0,n.openBlock)(),(0,n.createElementBlock)("div",{key:0,class:"banner-video",innerHTML:e.lesson.background_video},null,8,a)):(0,n.createCommentVNode)("",!0),i,(0,n.createElementVNode)("div",s,[e.lesson.badge&&!e.lesson.feedback&&100!==e.lesson.compeletedpercent?((0,n.openBlock)(),(0,n.createElementBlock)("div",l,[(0,n.createElementVNode)("div",r,[(0,n.createElementVNode)("div",c,[(0,n.createElementVNode)("div",d,[(0,n.createElementVNode)("img",{src:e.lesson.badge.image_fullpath,alt:e.lesson.badge.name,class:"me-3",width:"25"},null,8,u),(0,n.createElementVNode)("div",null,[(0,n.createElementVNode)("p",m,(0,n.toDisplayString)(e.lesson.badge.name),1)])])])])])):(0,n.createCommentVNode)("",!0),p,(0,n.createElementVNode)("h1",{class:"display-4 fw-normal mb-4 text-light",innerHTML:e.lesson.title},null,8,g),(0,n.createElementVNode)("div",h,[e.lesson.estimated_time&&(e.lesson.estimated_time.hours||e.lesson.estimated_time.minutes)?((0,n.openBlock)(),(0,n.createElementBlock)("div",f,[b,e.lesson.estimated_time&&e.lesson.estimated_time.hours?((0,n.openBlock)(),(0,n.createElementBlock)("span",{key:0,textContent:(0,n.toDisplayString)(e.lesson.estimated_time.hours+"h ")},null,8,v)):(0,n.createCommentVNode)("",!0),e.lesson.estimated_time&&e.lesson.estimated_time.minutes?((0,n.openBlock)(),(0,n.createElementBlock)("span",{key:1,textContent:(0,n.toDisplayString)(e.lesson.estimated_time.minutes+"m")},null,8,y)):(0,n.createCommentVNode)("",!0)])):(0,n.createCommentVNode)("",!0),e.lesson.level?((0,n.openBlock)(),(0,n.createElementBlock)("div",w,[k,(0,n.createElementVNode)("span",{textContent:(0,n.toDisplayString)(e.lesson.level)},null,8,E)])):(0,n.createCommentVNode)("",!0),(0,n.createElementVNode)("div",x,[100===e.lesson.compeletedpercent?((0,n.openBlock)(),(0,n.createElementBlock)("span",C," Completed ")):e.lesson.compeletedpercent>0&&e.lesson.compeletedpercent<100?((0,n.openBlock)(),(0,n.createElementBlock)("span",B,(0,n.toDisplayString)(e.lesson.compeletedpercent)+"% Completed ",1)):(0,n.createCommentVNode)("",!0)])]),(0,n.createElementVNode)("div",V,[((0,n.openBlock)(!0),(0,n.createElementBlock)(n.Fragment,null,(0,n.renderList)(e.lesson.tagged,(function(e){return(0,n.openBlock)(),(0,n.createElementBlock)("div",{class:"col-sm-6 fs-6 text-light p-2",key:e.id},[N,(0,n.createTextVNode)(" "+(0,n.toDisplayString)(e.tag_name),1)])})),128))]),e.lesson.foreground_video&&0===e.lesson.compeletedpercent?((0,n.openBlock)(),(0,n.createElementBlock)("div",A,[(0,n.createElementVNode)("div",T,[(0,n.createElementVNode)("button",{type:"button",class:"btn btn-black-custom btn-lg rounded-0 w-100","data-bs-toggle":"modal","data-bs-target":"#kt_modal_trailer",onClick:t[0]||(t[0]=function(){return e.onShowModal&&e.onShowModal.apply(e,arguments)})},[(0,n.createTextVNode)(" Watch Trailer "),_,S])])])):(0,n.createCommentVNode)("",!0),e.lesson.user_response&&"Draft"!=e.lesson.user_response.status?(0,n.createCommentVNode)("",!0):((0,n.openBlock)(),(0,n.createElementBlock)("div",L,[(0,n.createElementVNode)("div",P,[(0,n.createVNode)(bt,{class:"btn btn-white-custom text-black btn-lg border-1 rounded-0 w-100",to:{name:"task-lessons-section-detail",params:{id:e.currentlesson,sectionid:e.latestStep}}},{default:(0,n.withCtx)((function(){return[!e.lesson.hasresponse&&e.lesson.compeletedpercent<100?((0,n.openBlock)(),(0,n.createElementBlock)("span",$," Get Started ")):(0,n.createCommentVNode)("",!0),e.lesson.hasresponse?((0,n.openBlock)(),(0,n.createElementBlock)("span",M,"Continue")):(0,n.createCommentVNode)("",!0)]})),_:1},8,["to"])])])),e.lesson.user_response&&"Submitted"==e.lesson.user_response.status?((0,n.openBlock)(),(0,n.createElementBlock)("div",D,[(0,n.createElementVNode)("div",O,[(0,n.createVNode)(bt,{class:"btn btn-white-custom btn-lg border-1 rounded-0 w-100",to:{name:"task-lessons-view-response",params:{id:e.currentlesson}}},{default:(0,n.withCtx)((function(){return[(0,n.createTextVNode)(" View Response ")]})),_:1},8,["to"])]),e.lesson.hasresponse?((0,n.openBlock)(),(0,n.createElementBlock)("div",H,[e.lesson.compeletedpercent>=100?((0,n.openBlock)(),(0,n.createElementBlock)("div",j,I)):(0,n.createCommentVNode)("",!0)])):(0,n.createCommentVNode)("",!0)])):(0,n.createCommentVNode)("",!0),e.lesson.hasresponse?((0,n.openBlock)(),(0,n.createElementBlock)("div",q,[(0,n.createElementVNode)("div",F,[(0,n.createVNode)(bt,{style:{"font-size":"12px !important"},class:"p-5 text-light fs-11px",to:{name:"task-lessons-section-detail",params:{id:e.currentlesson,sectionid:1}}},{default:(0,n.withCtx)((function(){return[(0,n.createTextVNode)(" Edit Response ")]})),_:1},8,["to"])])])):(0,n.createCommentVNode)("",!0),(0,n.createElementVNode)("div",R,[e.lesson.badge&&100===e.lesson.compeletedpercent?((0,n.openBlock)(),(0,n.createElementBlock)("div",U,[(0,n.createElementVNode)("div",z,[(0,n.createElementVNode)("div",Z,[(0,n.createElementVNode)("div",{class:"d-flex align-items-center cursor-pointer","data-bs-toggle":"modal","data-bs-target":"#kt_modal_badge",onClick:t[1]||(t[1]=function(t){return e.openBadgeModal(e.lesson.user_response.badge_key)})},[(0,n.createElementVNode)("img",{src:e.lesson.badge.image_fullpath,alt:e.lesson.badge.name,class:"me-3",width:"25"},null,8,W),K])])])])):(0,n.createCommentVNode)("",!0),e.lesson.feedback?((0,n.openBlock)(),(0,n.createElementBlock)("div",Y,G)):(0,n.createCommentVNode)("",!0)])])],4),(0,n.createElementVNode)("div",J,[e.lesson.relatedlesson.length?((0,n.openBlock)(),(0,n.createElementBlock)("div",X,[(0,n.createElementVNode)("div",Q,[(0,n.createElementVNode)("div",ee,[(0,n.createElementVNode)("div",{class:"text-white p-4 pointer",onClick:t[2]||(t[2]=function(){return e.toggleRelated&&e.toggleRelated.apply(e,arguments)})},[(0,n.createTextVNode)(" Related Modules "),(0,n.createElementVNode)("i",{class:(0,n.normalizeClass)(["fa text-white ms-2",e.showRelatedLessonsList?"fa-angle-down":"fa-angle-up"])},null,2)]),(0,n.createElementVNode)("div",{class:(0,n.normalizeClass)(["related-overlay",{"slide-up":e.showRelatedLessonsList}])},[e.showRelatedLessonsList?((0,n.openBlock)(!0),(0,n.createElementBlock)(n.Fragment,{key:0},(0,n.renderList)(e.lesson.relatedlesson,(function(e){return(0,n.openBlock)(),(0,n.createElementBlock)("div",{key:e.id,class:"related-card pb-5 px-10"},[(0,n.createVNode)(bt,{class:"d-block",to:{name:"task-lessons-detail",params:{id:e.id}}},{default:(0,n.withCtx)((function(){return[(0,n.createElementVNode)("div",{class:"mb-3",style:(0,n.normalizeStyle)([{height:"235px","background-color":"white","background-size":"100%"},{backgroundImage:"url("+e.tileimage_fullpath+")"}])},null,4)]})),_:2},1032,["to"]),(0,n.createElementVNode)("div",te,[(0,n.createElementVNode)("p",{class:"float-start fs-7 wrap",textContent:(0,n.toDisplayString)(e.title)},null,8,oe),(0,n.createElementVNode)("p",ne,(0,n.toDisplayString)(e.compeletedpercent)+"% ",1)])])})),128)):(0,n.createCommentVNode)("",!0)],2)])])])):(0,n.createCommentVNode)("",!0),(0,n.createElementVNode)("div",ae,[(0,n.createElementVNode)("div",ie,[(0,n.createVNode)(bt,{class:"fs-4 m-0 text-white",to:{name:"tasks-lessons-list"}},{default:(0,n.withCtx)((function(){return[se,(0,n.createTextVNode)(" Back to Lessons ")]})),_:1})]),(0,n.createElementVNode)("div",le,[(0,n.createElementVNode)("span",{class:"cursor-pointer svg-icon svg-icon-primary svg-icon-2x float-end",onClick:t[3]||(t[3]=function(t){return e.favouriteLesson(e.lesson.id)})},[e.lesson.favourite?((0,n.openBlock)(),(0,n.createElementBlock)("i",re)):(0,n.createCommentVNode)("",!0),e.lesson.favourite?(0,n.createCommentVNode)("",!0):((0,n.openBlock)(),(0,n.createElementBlock)("i",ce))]),e.lesson.audio?((0,n.openBlock)(),(0,n.createElementBlock)("span",{key:0,onClick:t[4]||(t[4]=function(){return e.toggleAudio&&e.toggleAudio.apply(e,arguments)}),class:"cursor-pointer svg-icon svg-icon-primary svg-icon-2x float-end me-5"},de)):(0,n.createCommentVNode)("",!0),e.lesson.worksheets.length?((0,n.openBlock)(),(0,n.createElementBlock)("span",ue,me)):(0,n.createCommentVNode)("",!0),e.currentUser.isTeacher&&e.lesson.teacher_resources.length?((0,n.openBlock)(),(0,n.createElementBlock)("span",pe,ge)):(0,n.createCommentVNode)("",!0),e.currentUser.isTeacher&&e.lesson.curriculum?((0,n.openBlock)(),(0,n.createElementBlock)("span",he,fe)):(0,n.createCommentVNode)("",!0),be]),(0,n.createElementVNode)("div",{class:"row",id:"audio",style:(0,n.normalizeStyle)({height:e.audioHeight+"px"})},[(0,n.createElementVNode)("div",ve,[(0,n.createElementVNode)("audio",ye,[(0,n.createElementVNode)("source",{src:e.lesson.audiofullpath,type:"audio/mpeg"},null,8,we),(0,n.createTextVNode)(" Your browser does not support the audio element. ")])])],4)])]),(0,n.createElementVNode)("div",{class:"modal fade",id:"kt_modal_trailer",tabindex:"-1",style:{display:"none"},"aria-hidden":"true",onClick:t[5]||(t[5]=function(){return e.onShowModal&&e.onShowModal.apply(e,arguments)})},[(0,n.createElementVNode)("div",ke,[(0,n.createElementVNode)("div",Ee,[(0,n.createElementVNode)("div",{class:"modal-body p-0",innerHTML:e.lesson.foreground_video},null,8,xe)])])]),(0,n.createElementVNode)("div",Ce,[(0,n.createElementVNode)("div",Be,[(0,n.createElementVNode)("div",Ve,[(0,n.createElementVNode)("div",Ne,[Ae,(0,n.createElementVNode)("button",{type:"button",class:"btn btn-primary btn-sm rounded-0","data-bs-dismiss":"modal",onClick:t[6]||(t[6]=function(t){return e.resetLesson(e.lesson.id)})}," Yes "),Te])])])]),e.lesson.worksheets.length?((0,n.openBlock)(),(0,n.createElementBlock)("div",_e,[(0,n.createElementVNode)("div",Se,[(0,n.createElementVNode)("div",Le,[(0,n.createElementVNode)("div",Pe,[$e,(0,n.createElementVNode)("ul",Me,[((0,n.openBlock)(!0),(0,n.createElementBlock)(n.Fragment,null,(0,n.renderList)(e.lesson.worksheets,(function(e){return(0,n.openBlock)(),(0,n.createElementBlock)("li",{class:"text-center hover-colored",key:e.id},[(0,n.createElementVNode)("a",{href:"/teacherresources/"+e.id+"/download"},[(0,n.createElementVNode)("div",Oe,[(0,n.createElementVNode)("img",{src:e.imagefullpath,alt:e.title,class:"img-fluid"},null,8,He),je]),(0,n.createElementVNode)("div",Ie,(0,n.toDisplayString)(e.title),1)],8,De)])})),128))]),qe])])])])):(0,n.createCommentVNode)("",!0),e.currentUser.isTeacher&&e.lesson.teacher_resources.length?((0,n.openBlock)(),(0,n.createElementBlock)("div",Fe,[(0,n.createElementVNode)("div",Re,[(0,n.createElementVNode)("div",Ue,[(0,n.createElementVNode)("div",ze,[Ze,(0,n.createElementVNode)("ul",We,[((0,n.openBlock)(!0),(0,n.createElementBlock)(n.Fragment,null,(0,n.renderList)(e.lesson.teacher_resources,(function(e){return(0,n.openBlock)(),(0,n.createElementBlock)("li",{class:"text-center hover-colored",key:e.id},[(0,n.createElementVNode)("a",{href:"/teacherresources/"+e.id+"/download"},[(0,n.createElementVNode)("div",Ye,[(0,n.createElementVNode)("img",{src:e.imagefullpath,alt:e.title,class:"img-fluid"},null,8,Ge),Je]),(0,n.createElementVNode)("div",Xe,(0,n.toDisplayString)(e.title),1)],8,Ke)])})),128))]),Qe])])])])):(0,n.createCommentVNode)("",!0),e.currentUser.isTeacher&&e.lesson.curriculum?((0,n.openBlock)(),(0,n.createElementBlock)("div",et,[(0,n.createElementVNode)("div",tt,[(0,n.createElementVNode)("div",ot,[(0,n.createElementVNode)("div",nt,[at,(0,n.createElementVNode)("div",{innerHTML:e.lesson.curriculum},null,8,it),st])])])])):(0,n.createCommentVNode)("",!0),(0,n.createElementVNode)("div",lt,[(0,n.createElementVNode)("div",rt,[(0,n.createElementVNode)("div",ct,[dt,(0,n.createElementVNode)("div",ut,[(0,n.createElementVNode)("div",mt,[(0,n.createElementVNode)("p",{innerHTML:e.lesson.feedback,class:"text-gray-700"},null,8,pt)])])])])]),(0,n.createVNode)(vt,{selectedBadge:e.selectedBadge,onShareBadge:e.openShareBadgeModal},null,8,["selectedBadge","onShareBadge"])],64)}]])}}]);