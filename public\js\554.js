/*! For license information please see 554.js.LICENSE.txt */
(self.webpackChunk=self.webpackChunk||[]).push([[554],{46702:(e,t)=>{var n,o,i;!function(r){if("undefined"!=typeof window){var a,s=0,l=!1,c=!1,d="message".length,u="[iFrameSizer]",m=u.length,f=null,p=window.requestAnimationFrame,h=Object.freeze({max:1,scroll:1,bodyScroll:1,documentElementScroll:1}),g={},v=null,w=Object.freeze({autoResize:!0,bodyBackground:null,bodyMargin:null,bodyMarginV1:8,bodyPadding:null,checkOrigin:!0,inPageLinks:!1,enablePublicMethods:!0,heightCalculationMethod:"bodyOffset",id:"iFrameResizer",interval:32,log:!1,maxHeight:1/0,maxWidth:1/0,minHeight:0,minWidth:0,mouseEvents:!0,resizeFrom:"parent",scrolling:!1,sizeHeight:!0,sizeWidth:!1,warningTimeout:5e3,tolerance:0,widthCalculationMethod:"scroll",onClose:function(){return!0},onClosed:function(){},onInit:function(){},onMessage:function(){S("onMessage function not defined")},onMouseEnter:function(){},onMouseLeave:function(){},onResized:function(){},onScroll:function(){return!0}}),b={};window.jQuery!==r&&((a=window.jQuery).fn?a.fn.iFrameResize||(a.fn.iFrameResize=function(e){return this.filter("iframe").each((function(t,n){D(n,e)})).end()}):L("","Unable to bind to jQuery, it is not fully loaded.")),o=[],(i="function"==typeof(n=Z)?n.apply(t,o):n)===r||(e.exports=i),window.iFrameResize=window.iFrameResize||Z()}function y(){return window.MutationObserver||window.WebKitMutationObserver||window.MozMutationObserver}function x(e,t,n){e.addEventListener(t,n,!1)}function k(e,t,n){e.removeEventListener(t,n,!1)}function E(e){return u+"["+function(e){var t="Host page: "+e;return window.top!==window.self&&(t=window.parentIFrame&&window.parentIFrame.getId?window.parentIFrame.getId()+": "+e:"Nested host page: "+e),t}(e)+"]"}function N(e){return g[e]?g[e].log:l}function C(e,t){V("log",e,t,N(e))}function L(e,t){V("info",e,t,N(e))}function S(e,t){V("warn",e,t,!0)}function V(e,t,n,o){!0===o&&"object"==typeof window.console&&console[e](E(t),n)}function B(e){function t(){i("Height"),i("Width"),R((function(){j(M),I(D),h("onResized",M)}),M,"init")}function n(e){return"border-box"!==e.boxSizing?0:(e.paddingTop?parseInt(e.paddingTop,10):0)+(e.paddingBottom?parseInt(e.paddingBottom,10):0)}function o(e){return"border-box"!==e.boxSizing?0:(e.borderTopWidth?parseInt(e.borderTopWidth,10):0)+(e.borderBottomWidth?parseInt(e.borderBottomWidth,10):0)}function i(e){var t=Number(g[D]["max"+e]),n=Number(g[D]["min"+e]),o=e.toLowerCase(),i=Number(M[o]);C(D,"Checking "+o+" is in range "+n+"-"+t),i<n&&(i=n,C(D,"Set "+o+" to min value")),i>t&&(i=t,C(D,"Set "+o+" to max value")),M[o]=""+i}function r(e){return B.slice(B.indexOf(":")+d+e)}function a(e,t){var n,o,i;n=function(){var n,o;H("Send Page Info","pageInfo:"+(n=document.body.getBoundingClientRect(),o=M.iframe.getBoundingClientRect(),JSON.stringify({iframeHeight:o.height,iframeWidth:o.width,clientHeight:Math.max(document.documentElement.clientHeight,window.innerHeight||0),clientWidth:Math.max(document.documentElement.clientWidth,window.innerWidth||0),offsetTop:parseInt(o.top-n.top,10),offsetLeft:parseInt(o.left-n.left,10),scrollTop:window.pageYOffset,scrollLeft:window.pageXOffset,documentHeight:document.documentElement.clientHeight,documentWidth:document.documentElement.clientWidth,windowHeight:window.innerHeight,windowWidth:window.innerWidth})),e,t)},o=32,b[i=t]||(b[i]=setTimeout((function(){b[i]=null,n()}),o))}function s(e){var t=e.getBoundingClientRect();return T(D),{x:Math.floor(Number(t.left)+Number(f.x)),y:Math.floor(Number(t.top)+Number(f.y))}}function l(e){var t=e?s(M.iframe):{x:0,y:0},n={x:Number(M.width)+t.x,y:Number(M.height)+t.y};C(D,"Reposition requested from iFrame (offset x:"+t.x+" y:"+t.y+")"),window.top===window.self?(f=n,c(),C(D,"--")):window.parentIFrame?window.parentIFrame["scrollTo"+(e?"Offset":"")](n.x,n.y):S(D,"Unable to scroll to requested position, window.parentIFrame not found")}function c(){!1===h("onScroll",f)?z():I(D)}function p(e){var t={};if(0===Number(M.width)&&0===Number(M.height)){var n=r(9).split(":");t={x:n[1],y:n[0]}}else t={x:M.width,y:M.height};h(e,{iframe:M.iframe,screenX:Number(t.x),screenY:Number(t.y),type:M.type})}function h(e,t){return _(D,e,t)}var v,w,y,E,N,V,B=e.data,M={},D=null;"[iFrameResizerChild]Ready"===B?function(){for(var e in g)H("iFrame requested init",W(e),g[e].iframe,e)}():u===(""+B).slice(0,m)&&B.slice(m).split(":")[0]in g?(y=B.slice(m).split(":"),E=y[1]?parseInt(y[1],10):0,N=g[y[0]]&&g[y[0]].iframe,V=getComputedStyle(N),M={iframe:N,id:y[0],height:E+n(V)+o(V),width:y[2],type:y[3]},D=M.id,g[D]&&(g[D].loaded=!0),(w=M.type in{true:1,false:1,undefined:1})&&C(D,"Ignoring init message from meta parent page"),!w&&function(e){var t=!0;return g[e]||(t=!1,S(M.type+" No settings for "+e+". Message was: "+B)),t}(D)&&(C(D,"Received: "+B),v=!0,null===M.iframe&&(S(D,"IFrame ("+M.id+") not found"),v=!1),v&&function(){var t,n=e.origin,o=g[D]&&g[D].checkOrigin;if(o&&""+n!="null"&&!(o.constructor===Array?function(){var e=0,t=!1;for(C(D,"Checking connection is from allowed list of origins: "+o);e<o.length;e++)if(o[e]===n){t=!0;break}return t}():(t=g[D]&&g[D].remoteHost,C(D,"Checking connection is from: "+t),n===t)))throw new Error("Unexpected message received from: "+n+" for "+M.iframe.id+". Message was: "+e.data+". This error can be disabled by setting the checkOrigin: false option or by providing of array of trusted domains.");return!0}()&&function(){switch(g[D]&&g[D].firstRun&&g[D]&&(g[D].firstRun=!1),M.type){case"close":F(M.iframe);break;case"message":d=r(6),C(D,"onMessage passed: {iframe: "+M.iframe.id+", message: "+d+"}"),h("onMessage",{iframe:M.iframe,message:JSON.parse(d)}),C(D,"--");break;case"mouseenter":p("onMouseEnter");break;case"mouseleave":p("onMouseLeave");break;case"autoResize":g[D].autoResize=JSON.parse(r(9));break;case"scrollTo":l(!1);break;case"scrollToOffset":l(!0);break;case"pageInfo":a(g[D]&&g[D].iframe,D),function(){function e(e,o){function i(){g[n]?a(g[n].iframe,n):t()}["scroll","resize"].forEach((function(t){C(n,e+t+" listener for sendPageInfo"),o(window,t,i)}))}function t(){e("Remove ",k)}var n=D;e("Add ",x),g[n]&&(g[n].stopPageInfo=t)}();break;case"pageInfoStop":g[D]&&g[D].stopPageInfo&&(g[D].stopPageInfo(),delete g[D].stopPageInfo);break;case"inPageLink":n=r(9).split("#")[1]||"",o=decodeURIComponent(n),(i=document.getElementById(o)||document.getElementsByName(o)[0])?(e=s(i),C(D,"Moving to in page link (#"+n+") at x: "+e.x+" y: "+e.y),f={x:e.x,y:e.y},c(),C(D,"--")):window.top===window.self?C(D,"In page link #"+n+" not found"):window.parentIFrame?window.parentIFrame.moveToAnchor(n):C(D,"In page link #"+n+" not found and window.parentIFrame not found");break;case"reset":O(M);break;case"init":t(),h("onInit",M.iframe);break;default:0===Number(M.width)&&0===Number(M.height)?S("Unsupported message received ("+M.type+"), this is likely due to the iframe containing a later version of iframe-resizer than the parent page"):t()}var e,n,o,i,d}())):L(D,"Ignored: "+B)}function _(e,t,n){var o=null,i=null;if(g[e]){if("function"!=typeof(o=g[e][t]))throw new TypeError(t+" on iFrame["+e+"] is not a function");i=o(n)}return i}function M(e){var t=e.id;delete g[t]}function F(e){var t=e.id;if(!1!==_(t,"onClose",t)){C(t,"Removing iFrame: "+t);try{e.parentNode&&e.parentNode.removeChild(e)}catch(e){S(e)}_(t,"onClosed",t),C(t,"--"),M(e)}else C(t,"Close iframe cancelled by onClose event")}function T(e){null===f&&C(e,"Get page position: "+(f={x:window.pageXOffset===r?document.documentElement.scrollLeft:window.pageXOffset,y:window.pageYOffset===r?document.documentElement.scrollTop:window.pageYOffset}).x+","+f.y)}function I(e){null!==f&&(window.scrollTo(f.x,f.y),C(e,"Set page position: "+f.x+","+f.y),z())}function z(){f=null}function O(e){C(e.id,"Size reset requested by "+("init"===e.type?"host page":"iFrame")),T(e.id),R((function(){j(e),H("reset","reset",e.iframe,e.id)}),e,"reset")}function j(e){function t(t){c||"0"!==e[t]||(c=!0,C(o,"Hidden iFrame detected, creating visibility listener"),function(){function e(){function e(e){function t(t){return"0px"===(g[e]&&g[e].iframe.style[t])}function n(e){return null!==e.offsetParent}g[e]&&n(g[e].iframe)&&(t("height")||t("width"))&&H("Visibility change","resize",g[e].iframe,e)}Object.keys(g).forEach((function(t){e(t)}))}function t(t){C("window","Mutation observed: "+t[0].target+" "+t[0].type),P(e,16)}function n(){var e=document.querySelector("body"),n={attributes:!0,attributeOldValue:!1,characterData:!0,characterDataOldValue:!1,childList:!0,subtree:!0};new o(t).observe(e,n)}var o=y();o&&n()}())}function n(n){!function(t){e.id?(e.iframe.style[t]=e[t]+"px",C(e.id,"IFrame ("+o+") "+t+" set to "+e[t]+"px")):C("undefined","messageData id not set")}(n),t(n)}var o=e.iframe.id;g[o]&&(g[o].sizeHeight&&n("height"),g[o].sizeWidth&&n("width"))}function R(e,t,n){n!==t.type&&p&&!window.jasmine?(C(t.id,"Requesting animation frame"),p(e)):e()}function H(e,t,n,o,i){var r,a=!1;o=o||n.id,g[o]&&(n&&"contentWindow"in n&&null!==n.contentWindow?(r=g[o]&&g[o].targetOrigin,C(o,"["+e+"] Sending msg to iframe["+o+"] ("+t+") targetOrigin: "+r),n.contentWindow.postMessage(u+t,r)):S(o,"["+e+"] IFrame("+o+") not found"),i&&g[o]&&g[o].warningTimeout&&(g[o].msgTimeout=setTimeout((function(){!g[o]||g[o].loaded||a||(a=!0,S(o,"IFrame has not responded within "+g[o].warningTimeout/1e3+" seconds. Check iFrameResizer.contentWindow.js has been loaded in iFrame. This message can be ignored if everything is working, or you can set the warningTimeout option to a higher value or zero to suppress this warning."))}),g[o].warningTimeout)))}function W(e){return e+":"+g[e].bodyMarginV1+":"+g[e].sizeWidth+":"+g[e].log+":"+g[e].interval+":"+g[e].enablePublicMethods+":"+g[e].autoResize+":"+g[e].bodyMargin+":"+g[e].heightCalculationMethod+":"+g[e].bodyBackground+":"+g[e].bodyPadding+":"+g[e].tolerance+":"+g[e].inPageLinks+":"+g[e].resizeFrom+":"+g[e].widthCalculationMethod+":"+g[e].mouseEvents}function D(e,t){function n(e){var t=e.split("Callback");if(2===t.length){var n="on"+t[0].charAt(0).toUpperCase()+t[0].slice(1);this[n]=this[e],delete this[e],S(a,"Deprecated: '"+e+"' has been renamed '"+n+"'. The old method will be removed in the next major version.")}}var o,i,a=function(n){if("string"!=typeof n)throw new TypeError("Invaild id for iFrame. Expected String");var o;return""===n&&(e.id=(o=t&&t.id||w.id+s++,null!==document.getElementById(o)&&(o+=s++),n=o),l=(t||{}).log,C(n,"Added missing iframe ID: "+n+" ("+e.src+")")),n}(e.id);a in g&&"iFrameResizer"in e?S(a,"Ignored iFrame, already setup."):(!function(t){var o;t=t||{},g[a]=Object.create(null),g[a].iframe=e,g[a].firstRun=!0,g[a].remoteHost=e.src&&e.src.split("/").slice(0,3).join("/"),function(e){if("object"!=typeof e)throw new TypeError("Options is not an object")}(t),Object.keys(t).forEach(n,t),function(e){for(var t in w)Object.prototype.hasOwnProperty.call(w,t)&&(g[a][t]=Object.prototype.hasOwnProperty.call(e,t)?e[t]:w[t])}(t),g[a]&&(g[a].targetOrigin=!0===g[a].checkOrigin?""===(o=g[a].remoteHost)||null!==o.match(/^(about:blank|javascript:|file:\/\/)/)?"*":o:"*")}(t),function(){switch(C(a,"IFrame scrolling "+(g[a]&&g[a].scrolling?"enabled":"disabled")+" for "+a),e.style.overflow=!1===(g[a]&&g[a].scrolling)?"hidden":"auto",g[a]&&g[a].scrolling){case"omit":break;case!0:e.scrolling="yes";break;case!1:e.scrolling="no";break;default:e.scrolling=g[a]?g[a].scrolling:"no"}}(),function(){function t(t){var n=g[a][t];1/0!==n&&0!==n&&(e.style[t]="number"==typeof n?n+"px":n,C(a,"Set "+t+" = "+e.style[t]))}function n(e){if(g[a]["min"+e]>g[a]["max"+e])throw new Error("Value for min"+e+" can not be greater than max"+e)}n("Height"),n("Width"),t("maxHeight"),t("minHeight"),t("maxWidth"),t("minWidth")}(),"number"!=typeof(g[a]&&g[a].bodyMargin)&&"0"!==(g[a]&&g[a].bodyMargin)||(g[a].bodyMarginV1=g[a].bodyMargin,g[a].bodyMargin=g[a].bodyMargin+"px"),o=W(a),(i=y())&&function(t){e.parentNode&&new t((function(t){t.forEach((function(t){Array.prototype.slice.call(t.removedNodes).forEach((function(t){t===e&&F(e)}))}))})).observe(e.parentNode,{childList:!0})}(i),x(e,"load",(function(){var t,n;H("iFrame.onload",o,e,r,!0),t=g[a]&&g[a].firstRun,n=g[a]&&g[a].heightCalculationMethod in h,!t&&n&&O({iframe:e,height:0,width:0,type:"init"})})),H("init",o,e,r,!0),g[a]&&(g[a].iframe.iFrameResizer={close:F.bind(null,g[a].iframe),removeListeners:M.bind(null,g[a].iframe),resize:H.bind(null,"Window resize","resize",g[a].iframe),moveToAnchor:function(e){H("Move to anchor","moveToAnchor:"+e,g[a].iframe,a)},sendMessage:function(e){H("Send Message","message:"+(e=JSON.stringify(e)),g[a].iframe,a)}}))}function P(e,t){null===v&&(v=setTimeout((function(){v=null,e()}),t))}function A(){"hidden"!==document.visibilityState&&(C("document","Trigger event: Visibility change"),P((function(){q("Tab Visible","resize")}),16))}function q(e,t){Object.keys(g).forEach((function(n){(function(e){return g[e]&&"parent"===g[e].resizeFrom&&g[e].autoResize&&!g[e].firstRun})(n)&&H(e,t,g[n].iframe,n)}))}function U(){x(window,"message",B),x(window,"resize",(function(){var e;C("window","Trigger event: "+(e="resize")),P((function(){q("Window "+e,"resize")}),16)})),x(document,"visibilitychange",A),x(document,"-webkit-visibilitychange",A)}function Z(){function e(e,n){n&&(!function(){if(!n.tagName)throw new TypeError("Object is not a valid DOM element");if("IFRAME"!==n.tagName.toUpperCase())throw new TypeError("Expected <IFRAME> tag, found <"+n.tagName+">")}(),D(n,e),t.push(n))}var t;return function(){var e,t=["moz","webkit","o","ms"];for(e=0;e<t.length&&!p;e+=1)p=window[t[e]+"RequestAnimationFrame"];p?p=p.bind(window):C("setup","RequestAnimationFrame not supported")}(),U(),function(n,o){switch(t=[],function(e){e&&e.enablePublicMethods&&S("enablePublicMethods option has been removed, public methods are now always available in the iFrame")}(n),typeof o){case"undefined":case"string":Array.prototype.forEach.call(document.querySelectorAll(o||"iframe"),e.bind(r,n));break;case"object":e(n,o);break;default:throw new TypeError("Unexpected data type ("+typeof o+")")}return t}}}()},73168:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});var o=n(1519),i=n.n(o)()((function(e){return e[1]}));i.push([e.id,".fr-box{z-index:0}.wrap{max-width:55ch;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.btn-black{background:#000;border:1px solid!important;color:#fff}.btn-black:hover{background:#fff;color:#000}.section-content{margin:0}.section-content iframe{width:100%!important}.section-content iframe.wistia_embed{height:100%!important}.section-content img{max-width:100%}.section-content p iframe,.section-content p img{margin-bottom:-1rem}.section-content iframe,.section-content img{margin-left:-31px;margin-right:-30px;max-width:calc(100% + 61px)!important;width:calc(100% + 61px)!important}.btn-white{border:1px solid #000!important}.btn-white:hover,.btn.btn-white:hover:not(.btn-active){background-color:#000!important;color:#fff!important}::-webkit-scrollbar-thumb,::-webkit-scrollbar-thumb:hover{background:#000!important}.module-sections{margin-left:-30px;margin-right:-30px;overflow:auto hidden;position:relative}.sticky-top{min-width:calc(100% - 140px);position:fixed}.app-content{padding:0}.full-page{margin-left:-20px;margin-right:-20px}.banner{background-color:#000;background-position:50%;background-repeat:no-repeat;background-size:cover;display:block;min-height:calc(56.25vw - 149px);overflow:hidden;position:relative}.footer-buttons,.full-view-banner{margin-left:-30px;margin-right:-30px}.banner-video{height:100%}.banner-video>video{left:50%;position:absolute;top:50%;transform:translate(-50%,-50%);width:101%!important}.page-content{padding:0 15px;position:absolute;top:40%;width:100%}.module-section{border-bottom:1px solid;border-left:1px solid;border-top:1px solid;cursor:pointer;height:100px}.module-sections>.text-center:last-of-type>.module-section{border-right:1px solid}@media (max-width:1280px){.banner{height:56.25vw}.banner-video>video{height:100%!important;width:calc(65vw + 65vh)!important}}@media (min-width:992px){.sticky-top+.section-content{margin-top:100px}.module-sections{animation-duration:.2s;animation-fill-mode:forwards;animation-name:backtooriginal;z-index:100}.sticky-top{animation-duration:.2s;animation-fill-mode:forwards;animation-name:stick-top}@keyframes stick-top{0%{top:5px}to{top:0}}@keyframes backtooriginal{0%{top:-5px}to{top:0}}}@media (max-width:991px){.footer-buttons,.full-view-banner,.module-sections{margin-left:-20px;margin-right:-20px}.sticky-top{min-width:100%;top:119px}.full-view-banner{margin-top:58.16px}.module-section{height:100px}}@media (max-width:991px) and (min-width:768px) and (orientation:portrait){.banner{height:86.25vw}.banner-video>video{height:100%!important;width:calc(66vw + 66vh)!important}}@media (max-width:991px) and (orientation:landscape){.banner-video>video{height:auto!important;width:calc(70vw + 70vh)!important}}@media (max-width:767px){.banner{height:calc(100vh - 300px)}.full-view-banner{margin-top:0}.banner-video>video{height:100%!important;width:calc(90vw + 90vh)!important}.sticky-top{margin-top:10px}}",""]);const r=i},82554:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>ue});var o=n(70821),i=["innerHTML"],r=["innerHTML"],a=(0,o.createElementVNode)("div",{style:{position:"absolute",width:"100%",height:"100%",opacity:".3",background:"#000"}},null,-1),s={class:"page-content"},l={class:"banner-textarea px-20 w-450px"},c={key:0,class:"mt-4 mb-4"},d={class:"row g-3"},u={class:"d-flex align-items-center bg-light border border-secondary rounded shadow-sm p-3"},m=["src","alt"],f={class:"mb-1 fw-bold text-dark"},p={class:"fs-2x m-0 text-white"},h=["innerHTML"],g={key:1,class:"mt-4 mb-4"},v={class:"row g-3"},w={class:"d-flex align-items-center bg-light border border-secondary rounded shadow-sm p-3"},b=["src","alt"],y={class:"mb-1 fw-bold text-dark"},x={class:"m-0 text-white"},k={key:0,class:"svg-icon svg-icon-primary svg-icon-2x"},E=[(0,o.createElementVNode)("svg",{xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",width:"24px",height:"24px",viewBox:"0 0 24 24",version:"1.1"},[(0,o.createElementVNode)("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},[(0,o.createElementVNode)("rect",{x:"0",y:"0",width:"24",height:"24"}),(0,o.createElementVNode)("path",{d:"M12,22 C7.02943725,22 3,17.9705627 3,13 C3,8.02943725 7.02943725,4 12,4 C16.9705627,4 21,8.02943725 21,13 C21,17.9705627 16.9705627,22 12,22 Z",fill:"#ffffff",opacity:"0.3"}),(0,o.createElementVNode)("path",{d:"M11.9630156,7.5 L12.0475062,7.5 C12.3043819,7.5 12.5194647,7.69464724 12.5450248,7.95024814 L13,12.5 L16.2480695,14.3560397 C16.403857,14.4450611 16.5,14.6107328 16.5,14.7901613 L16.5,15 C16.5,15.2109164 16.3290185,15.3818979 16.1181021,15.3818979 C16.0841582,15.3818979 16.0503659,15.3773725 16.0176181,15.3684413 L11.3986612,14.1087258 C11.1672824,14.0456225 11.0132986,13.8271186 11.0316926,13.5879956 L11.4644883,7.96165175 C11.4845267,7.70115317 11.7017474,7.5 11.9630156,7.5 Z",fill:"#ffffff"})])],-1)],N={style:{"vertical-align":"middle"}},C=["textContent"],L=["textContent"],S=["onClick"],V={class:"svg-icon svg-icon-primary svg-icon-2x"},B={key:0,xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",width:"24px",height:"24px",viewBox:"0 0 24 24",version:"1.1"},_=[(0,o.createElementVNode)("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},[(0,o.createElementVNode)("mask",{fill:"white"},[(0,o.createElementVNode)("use",{"xlink:href":"#path-1"})]),(0,o.createElementVNode)("g"),(0,o.createElementVNode)("path",{d:"M15.6274517,4.55882251 L14.4693753,6.2959371 C13.9280401,5.51296885 13.0239252,5 12,5 C10.3431458,5 9,6.34314575 9,8 L9,10 L14,10 L17,10 L18,10 C19.1045695,10 20,10.8954305 20,12 L20,18 C20,19.1045695 19.1045695,20 18,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,12 C4,10.8954305 4.8954305,10 6,10 L7,10 L7,8 C7,5.23857625 9.23857625,3 12,3 C13.4280904,3 14.7163444,3.59871093 15.6274517,4.55882251 Z",fill:"#ffffff"})],-1)],M={key:1,xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",width:"24px",height:"24px",viewBox:"0 0 24 24",version:"1.1"},F=[(0,o.createElementVNode)("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},[(0,o.createElementVNode)("mask",{fill:"white"},[(0,o.createElementVNode)("use",{"xlink:href":"#path-1"})]),(0,o.createElementVNode)("g"),(0,o.createElementVNode)("path",{d:"M7,10 L7,8 C7,5.23857625 9.23857625,3 12,3 C14.7614237,3 17,5.23857625 17,8 L17,10 L18,10 C19.1045695,10 20,10.8954305 20,12 L20,18 C20,19.1045695 19.1045695,20 18,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,12 C4,10.8954305 4.8954305,10 6,10 L7,10 Z M12,5 C10.3431458,5 9,6.34314575 9,8 L9,10 L15,10 L15,8 C15,6.34314575 13.6568542,5 12,5 Z",fill:"#000000"})],-1)],T=["innerHTML"],I=["textContent"],z=["textContent"],O={class:"svg-icon svg-icon-primary svg-icon-2x"},j={key:0,xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",width:"24px",height:"24px",viewBox:"0 0 24 24",version:"1.1"},R=[(0,o.createElementVNode)("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},[(0,o.createElementVNode)("mask",{fill:"white"},[(0,o.createElementVNode)("use",{"xlink:href":"#path-1"})]),(0,o.createElementVNode)("g"),(0,o.createElementVNode)("path",{d:"M15.6274517,4.55882251 L14.4693753,6.2959371 C13.9280401,5.51296885 13.0239252,5 12,5 C10.3431458,5 9,6.34314575 9,8 L9,10 L14,10 L17,10 L18,10 C19.1045695,10 20,10.8954305 20,12 L20,18 C20,19.1045695 19.1045695,20 18,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,12 C4,10.8954305 4.8954305,10 6,10 L7,10 L7,8 C7,5.23857625 9.23857625,3 12,3 C13.4280904,3 14.7163444,3.59871093 15.6274517,4.55882251 Z",fill:"#ffffff"})],-1)],H={key:1,xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",width:"24px",height:"24px",viewBox:"0 0 24 24",version:"1.1"},W=[(0,o.createElementVNode)("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},[(0,o.createElementVNode)("mask",{fill:"white"},[(0,o.createElementVNode)("use",{"xlink:href":"#path-1"})]),(0,o.createElementVNode)("g"),(0,o.createElementVNode)("path",{d:"M7,10 L7,8 C7,5.23857625 9.23857625,3 12,3 C14.7614237,3 17,5.23857625 17,8 L17,10 L18,10 C19.1045695,10 20,10.8954305 20,12 L20,18 C20,19.1045695 19.1045695,20 18,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,12 C4,10.8954305 4.8954305,10 6,10 L7,10 Z M12,5 C10.3431458,5 9,6.34314575 9,8 L9,10 L15,10 L15,8 C15,6.34314575 13.6568542,5 12,5 Z",fill:"#000000"})],-1)],D={class:"section-content"},P=["innerHTML"],A={key:0,class:"text-gray-700",id:"lesson_section_resonpse"},q={class:"mt-4 pb-4 add-padding-to-text"},U={id:"app"},Z=["textContent"],G={class:"footer-buttons bg-black clearfix"},Y={class:"col-12"};var J=n(70655),X=n(80894),Q=n(45535),K=n(72961),$=n(41511),ee=n.n($),te=n(46702),ne=n.n(te),oe=n(22201);function ie(e){return ie="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ie(e)}function re(){re=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,o=Object.defineProperty||function(e,t,n){e[t]=n.value},i="function"==typeof Symbol?Symbol:{},r=i.iterator||"@@iterator",a=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function l(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{l({},"")}catch(e){l=function(e,t,n){return e[t]=n}}function c(e,t,n,i){var r=t&&t.prototype instanceof m?t:m,a=Object.create(r.prototype),s=new C(i||[]);return o(a,"_invoke",{value:x(e,n,s)}),a}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=c;var u={};function m(){}function f(){}function p(){}var h={};l(h,r,(function(){return this}));var g=Object.getPrototypeOf,v=g&&g(g(L([])));v&&v!==t&&n.call(v,r)&&(h=v);var w=p.prototype=m.prototype=Object.create(h);function b(e){["next","throw","return"].forEach((function(t){l(e,t,(function(e){return this._invoke(t,e)}))}))}function y(e,t){function i(o,r,a,s){var l=d(e[o],e,r);if("throw"!==l.type){var c=l.arg,u=c.value;return u&&"object"==ie(u)&&n.call(u,"__await")?t.resolve(u.__await).then((function(e){i("next",e,a,s)}),(function(e){i("throw",e,a,s)})):t.resolve(u).then((function(e){c.value=e,a(c)}),(function(e){return i("throw",e,a,s)}))}s(l.arg)}var r;o(this,"_invoke",{value:function(e,n){function o(){return new t((function(t,o){i(e,n,t,o)}))}return r=r?r.then(o,o):o()}})}function x(e,t,n){var o="suspendedStart";return function(i,r){if("executing"===o)throw new Error("Generator is already running");if("completed"===o){if("throw"===i)throw r;return S()}for(n.method=i,n.arg=r;;){var a=n.delegate;if(a){var s=k(a,n);if(s){if(s===u)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===o)throw o="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o="executing";var l=d(e,t,n);if("normal"===l.type){if(o=n.done?"completed":"suspendedYield",l.arg===u)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(o="completed",n.method="throw",n.arg=l.arg)}}}function k(e,t){var n=t.method,o=e.iterator[n];if(void 0===o)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,k(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),u;var i=d(o,e.iterator,t.arg);if("throw"===i.type)return t.method="throw",t.arg=i.arg,t.delegate=null,u;var r=i.arg;return r?r.done?(t[e.resultName]=r.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,u):r:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,u)}function E(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function N(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function C(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(E,this),this.reset(!0)}function L(e){if(e){var t=e[r];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function t(){for(;++o<e.length;)if(n.call(e,o))return t.value=e[o],t.done=!1,t;return t.value=void 0,t.done=!0,t};return i.next=i}}return{next:S}}function S(){return{value:void 0,done:!0}}return f.prototype=p,o(w,"constructor",{value:p,configurable:!0}),o(p,"constructor",{value:f,configurable:!0}),f.displayName=l(p,s,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===f||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,p):(e.__proto__=p,l(e,s,"GeneratorFunction")),e.prototype=Object.create(w),e},e.awrap=function(e){return{__await:e}},b(y.prototype),l(y.prototype,a,(function(){return this})),e.AsyncIterator=y,e.async=function(t,n,o,i,r){void 0===r&&(r=Promise);var a=new y(c(t,n,o,i),r);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},b(w),l(w,s,"Generator"),l(w,r,(function(){return this})),l(w,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var o in t)n.push(o);return n.reverse(),function e(){for(;n.length;){var o=n.pop();if(o in t)return e.value=o,e.done=!1,e}return e.done=!0,e}},e.values=L,C.prototype={constructor:C,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(N),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function o(n,o){return a.type="throw",a.arg=e,t.next=n,o&&(t.method="next",t.arg=void 0),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var r=this.tryEntries[i],a=r.completion;if("root"===r.tryLoc)return o("end");if(r.tryLoc<=this.prev){var s=n.call(r,"catchLoc"),l=n.call(r,"finallyLoc");if(s&&l){if(this.prev<r.catchLoc)return o(r.catchLoc,!0);if(this.prev<r.finallyLoc)return o(r.finallyLoc)}else if(s){if(this.prev<r.catchLoc)return o(r.catchLoc,!0)}else{if(!l)throw new Error("try statement without catch or finally");if(this.prev<r.finallyLoc)return o(r.finallyLoc)}}}},abrupt:function(e,t){for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var r=i;break}}r&&("break"===e||"continue"===e)&&r.tryLoc<=t&&t<=r.finallyLoc&&(r=null);var a=r?r.completion:{};return a.type=e,a.arg=t,r?(this.method="next",this.next=r.finallyLoc,u):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),u},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),N(n),u}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var i=o.arg;N(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:L(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),u}},e}const ae=(0,o.defineComponent)({name:"module-section-detail",components:{VueFroala:ee()},setup:function(e){var t=this,n=(0,X.oR)(),i=(0,oe.tv)(),r=n.getters.currentUser;(0,o.onMounted)((function(){return(0,J.mG)(t,void 0,void 0,re().mark((function e(){return re().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,x();case 2:ne()({heightCalculationMethod:"bodyScroll"},".section-content iframe");case 3:case"end":return e.stop()}}),e)})))}));var a=new URL(window.location.href).hash,s=a.lastIndexOf("/"),l=(0,o.ref)(a.substring(s+1)),c=(0,o.ref)(),d=(0,o.ref)(),u=(0,o.ref)(),m=(0,o.ref)(),f=(0,o.ref)(),p=(0,o.ref)(),h=(0,o.ref)(!1),g=(0,o.ref)(!1),v=0,w=(0,o.ref)(!1);d.value={};var b=(0,o.ref)(),y=(0,o.ref)("");b.value={response:""},p.value=1,c.value={id:"",steps:[]},f.value={title:""},u.value={title:""};var x=function o(){return(0,J.mG)(t,void 0,void 0,re().mark((function t(){var a,s,w,y,x;return re().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,K.Z.get("api/lessons/"+e.id+"/sections",e.sectionid);case 3:if(a=t.sent,!(s=a.data).steps.length){t.next=16;break}w=0;case 7:if(!(w<s.steps.length)){t.next=16;break}if(s.steps[w].user_response||!(w<e.sectionid-1)||r.isTeacher){t.next=12;break}return b.value.response="",i.push({name:"task-lessons-section-detail",params:{id:s.steps[w].lesson_id,sectionid:w+1}}).then((function(){o()})),t.abrupt("break",16);case 12:w==e.sectionid-1&&(w==s.steps.length-1?h.value=!0:(h.value=!1,f.value=s.steps[w+1],p.value=w+2),g.value=0==w,d.value=s.steps[w],(y=n.getters.getBreadcrumbs)[2]=s.title,y[3]=d.value.title,n.commit(Q.P.SET_BREADCRUMB_MUTATION,y),d.value.user_response&&(b.value.response=d.value.user_response.response),0!=w&&(u.value=s.steps[w-1],m.value=w));case 13:w++,t.next=7;break;case 16:c.value=s,l.value=d.value.id,x=document.getElementById("banner"),v=x.scrollHeight+120,t.next=26;break;case 22:t.prev=22,t.t0=t.catch(0),t.t0.response?(console.log(t.t0.response.data),console.log(t.t0.response.status),console.log(t.t0.response.headers)):t.t0.request?console.log(t.t0.request):console.log("Error",t.t0.message),console.log(t.t0.config);case 26:case"end":return t.stop()}}),t,null,[[0,22]])})))};return{currentUser:r,lesson:c,isFirstSection:g,isLastSection:h,currentSection:d,config:{key:"hWA2C-7I2A4C3D5D2D2G3wxeklqcwvffrrhxhoqxpkC7bmnxE2F2G2D1B10B2B3E6F1F2==",height:300,attribution:!1},nextSection:function(){if(r.isTeacher)h.value?(b.value.response="",i.push({name:"task-lessons-section-final-step",params:{id:c.value.id}}).then((function(){x()}))):(b.value.response="",i.push({name:"task-lessons-section-detail",params:{id:f.value.lesson_id,sectionid:p.value}}).then((function(){x()})));else{if(d.value.response&&!b.value.response.length)return void(y.value="Please add a response");y.value="",K.Z.post("api/lessons/"+e.id+"/sections/"+d.value.id,b.value).then((function(e){var t=e.data;y.value=t,h.value?(b.value.response="",i.push({name:"task-lessons-section-final-step",params:{id:c.value.id}}).then((function(){x()}))):(b.value.response="",i.push({name:"task-lessons-section-detail",params:{id:f.value.lesson_id,sectionid:p.value}}).then((function(){x()})))})).catch((function(e){console.error("catch error",e)}))}},prevSection:function(){b.value.response="",i.push({name:"task-lessons-section-detail",params:{id:u.value.lesson_id,sectionid:m.value}}).then((function(){x()}))},lessonresponseform:b,responseError:y,gotosection:function(e,t,n){t&&i.push({name:"task-lessons-section-detail",params:{id:c.value.id,sectionid:n+1}}).then((function(){x()}))},nextSectionDetail:f,prevSectionDetail:u,presentSectionid:l,scrolled:w,nextSectionDetailIndex:p,handleScroll:function(){if((window.innerWidth||document.documentElement.clientWidth||document.body.clientWidth)>991){var e=document.getElementById("kt_app_toolbar");window.scrollY>v?(w.value=!0,e.style.display="none"):(w.value=!1,e.style.display="flex")}},backgroundFile:function(){var e;return null!==(e=d.value.bg_image)&&void 0!==e?e:d.value.bg_video?"":c.value.background_imagepath}}},props:["id","sectionid"],created:function(){window.addEventListener("scroll",this.handleScroll)},destroyed:function(){window.removeEventListener("scroll",this.handleScroll)}});var se=n(93379),le=n.n(se),ce=n(73168),de={insert:"head",singleton:!1};le()(ce.Z,de);ce.Z.locals;const ue=(0,n(83744).Z)(ae,[["render",function(e,t,n,J,X,Q){var K,$,ee=(0,o.resolveComponent)("froala");return(0,o.openBlock)(),(0,o.createElementBlock)(o.Fragment,null,[(0,o.createElementVNode)("div",{id:"banner",class:"full-view-banner banner",style:(0,o.normalizeStyle)({backgroundImage:"url("+e.backgroundFile()+")"})},[e.currentSection.bg_video?((0,o.openBlock)(),(0,o.createElementBlock)("div",{key:0,class:"banner-video",innerHTML:e.currentSection.bg_video},null,8,i)):(0,o.createCommentVNode)("",!0),e.currentSection.bg_video||e.currentSection.bg_image||!e.lesson.background_video?(0,o.createCommentVNode)("",!0):((0,o.openBlock)(),(0,o.createElementBlock)("div",{key:1,class:"banner-video",innerHTML:e.lesson.background_video},null,8,r)),a,(0,o.createElementVNode)("div",s,[(0,o.createElementVNode)("div",l,[(null===(K=e.lesson.badges)||void 0===K?void 0:K.length)&&!e.lesson.feedback&&100!==e.lesson.compeletedpercent?((0,o.openBlock)(),(0,o.createElementBlock)("div",c,[(0,o.createElementVNode)("div",d,[((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(e.lesson.badges,(function(e){return(0,o.openBlock)(),(0,o.createElementBlock)("div",{key:e.id,class:"col-8"},[(0,o.createElementVNode)("div",u,[(0,o.createElementVNode)("img",{src:e.image_fullpath,alt:e.name,class:"me-3",width:"60",height:"60"},null,8,m),(0,o.createElementVNode)("div",null,[(0,o.createElementVNode)("p",f,(0,o.toDisplayString)(e.name),1)])])])})),128))])])):(0,o.createCommentVNode)("",!0),(0,o.createElementVNode)("p",p,"Section "+(0,o.toDisplayString)(e.currentSection.number),1),(0,o.createElementVNode)("p",{class:"fs-3x m-0 text-white",innerHTML:e.currentSection.title},null,8,h),(null===($=e.lesson.badges)||void 0===$?void 0:$.length)&&e.lesson.feedback&&100==e.lesson.compeletedpercent?((0,o.openBlock)(),(0,o.createElementBlock)("div",g,[(0,o.createElementVNode)("div",v,[((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(e.lesson.badges,(function(e){return(0,o.openBlock)(),(0,o.createElementBlock)("div",{key:e.id,class:"col-6"},[(0,o.createElementVNode)("div",w,[(0,o.createElementVNode)("img",{src:e.image_fullpath,alt:e.name,class:"me-3",width:"60",height:"60"},null,8,b),(0,o.createElementVNode)("div",null,[(0,o.createElementVNode)("p",y,(0,o.toDisplayString)(e.name),1)])])])})),128))])])):(0,o.createCommentVNode)("",!0),(0,o.createElementVNode)("p",x,[!e.currentSection.estimated_time||null==e.currentSection.estimated_time.hours&&null==e.currentSection.estimated_time.minutes?(0,o.createCommentVNode)("",!0):((0,o.openBlock)(),(0,o.createElementBlock)("span",k,E)),(0,o.createElementVNode)("span",N,[e.currentSection.estimated_time&&e.currentSection.estimated_time.hours?((0,o.openBlock)(),(0,o.createElementBlock)("span",{key:0,textContent:(0,o.toDisplayString)(e.currentSection.estimated_time.hours+"h ")},null,8,C)):(0,o.createCommentVNode)("",!0),(0,o.createTextVNode)(),e.currentSection.estimated_time&&e.currentSection.estimated_time.minutes?((0,o.openBlock)(),(0,o.createElementBlock)("span",{key:1,textContent:(0,o.toDisplayString)(e.currentSection.estimated_time.minutes+"m")},null,8,L)):(0,o.createCommentVNode)("",!0)])])])])],4),(0,o.createElementVNode)("div",(0,o.mergeProps)({class:{row:e.lesson.steps.length<6,"sticky-top":e.scrolled}},(0,o.toHandlers)(e.handleScroll,!0),{class:"d-flex module-sections"}),[((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(e.lesson.steps,(function(t,n){return(0,o.openBlock)(),(0,o.createElementBlock)("div",{key:t.id,class:(0,o.normalizeClass)([[e.lesson.steps.length<6?"col":"col-6 col-sm-4 col-md-2",t.user_response||t.id==e.presentSectionid?"bg-black":""],"text-center p-0"]),onClick:function(o){return e.gotosection(t.id,t.user_response,n)}},[(0,o.createElementVNode)("div",{class:(0,o.normalizeClass)(["module-section d-flex flex-column justify-content-center align-items-center py-5",[t.user_response||t.id==e.presentSectionid?"":"bg-white"]])},[(0,o.createElementVNode)("span",V,[t.user_response||t.id==e.presentSectionid?((0,o.openBlock)(),(0,o.createElementBlock)("svg",B,_)):((0,o.openBlock)(),(0,o.createElementBlock)("svg",M,F))]),(0,o.createElementVNode)("p",{class:(0,o.normalizeClass)(["m-0 px-5",[t.user_response||t.id==e.presentSectionid?"text-white":""]]),innerHTML:t.title},null,10,T),(0,o.createElementVNode)("p",{class:(0,o.normalizeClass)(["m-0",[t.user_response||t.id==e.presentSectionid?"text-white":""]])},[t.estimated_time&&t.estimated_time.hours?((0,o.openBlock)(),(0,o.createElementBlock)("span",{key:0,textContent:(0,o.toDisplayString)(t.estimated_time.hours+"h ")},null,8,I)):(0,o.createCommentVNode)("",!0),t.estimated_time&&t.estimated_time.minutes?((0,o.openBlock)(),(0,o.createElementBlock)("span",{key:1,textContent:(0,o.toDisplayString)(t.estimated_time.minutes+"m")},null,8,z)):(0,o.createCommentVNode)("",!0),(0,o.createTextVNode)("   ")],2)],2)],10,S)})),128)),(0,o.createElementVNode)("div",{class:(0,o.normalizeClass)([[e.lesson.steps.length<6?"col":"col-6 col-sm-4 col-md-2",e.lesson.user_response&&"Draft"!=e.lesson.user_response.status?"bg-black":""],"text-center p-0"])},[(0,o.createElementVNode)("div",{class:(0,o.normalizeClass)(["module-section d-flex flex-column justify-content-center align-items-center py-5",{"bg-white":!(e.lesson.user_response&&"Draft"!=e.lesson.user_response.status)}])},[(0,o.createElementVNode)("span",O,[e.lesson.user_response&&"Draft"!=e.lesson.user_response.status?((0,o.openBlock)(),(0,o.createElementBlock)("svg",j,R)):((0,o.openBlock)(),(0,o.createElementBlock)("svg",H,W))]),(0,o.createElementVNode)("p",{class:(0,o.normalizeClass)(["m-0",{"text-white":e.lesson.user_response&&"Draft"!=e.lesson.user_response.status}])}," Final Step ",2),(0,o.createElementVNode)("p",{class:(0,o.normalizeClass)(["m-0",{"text-white":e.lesson.user_response&&"Draft"!=e.lesson.user_response.status}])},"   ",2)],2)],2)],16),(0,o.createElementVNode)("div",D,[(0,o.createElementVNode)("div",{class:"",innerHTML:e.currentSection.body},null,8,P),e.currentSection.response?((0,o.openBlock)(),(0,o.createElementBlock)("form",A,[(0,o.createElementVNode)("div",q,[(0,o.createElementVNode)("div",U,[(0,o.createVNode)(ee,{tag:"textarea",config:e.config,modelValue:e.lessonresponseform.response,"onUpdate:modelValue":t[0]||(t[0]=function(t){return e.lessonresponseform.response=t})},null,8,["config","modelValue"]),e.responseError.length?((0,o.openBlock)(),(0,o.createElementBlock)("p",{key:0,textContent:(0,o.toDisplayString)(e.responseError),class:"form-error mt-2 ms-2"},null,8,Z)):(0,o.createCommentVNode)("",!0)])])])):(0,o.createCommentVNode)("",!0)]),(0,o.createElementVNode)("div",G,[(0,o.createElementVNode)("div",Y,[e.isFirstSection?(0,o.createCommentVNode)("",!0):((0,o.openBlock)(),(0,o.createElementBlock)("button",{key:0,class:"m-10 btn btn-black rounded-0 w-150px wrap",href:"javascript:void()",onClick:t[1]||(t[1]=function(){return e.prevSection&&e.prevSection.apply(e,arguments)})},(0,o.toDisplayString)(e.prevSectionDetail.title),1)),e.isLastSection?(0,o.createCommentVNode)("",!0):((0,o.openBlock)(),(0,o.createElementBlock)("button",{key:1,class:"m-10 btn btn-black float-end rounded-0 w-150px wrap",href:"javascript:void()",onClick:t[2]||(t[2]=function(){return e.nextSection&&e.nextSection.apply(e,arguments)})},(0,o.toDisplayString)(e.nextSectionDetail.title),1)),e.isLastSection?((0,o.openBlock)(),(0,o.createElementBlock)("button",{key:2,class:"m-10 btn btn-black float-end rounded-0 w-150px wrap",href:"javascript:void()",onClick:t[3]||(t[3]=function(){return e.nextSection&&e.nextSection.apply(e,arguments)})}," Final Step ")):(0,o.createCommentVNode)("",!0)])])],64)}]])}}]);