<template>
    <div class="modal fade" id="kt_modal_badge" tabindex="-1" style="display: none" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-xl">
            <div class="modal-content rounded-0">
                <div class="modal-header text-white">
                    <h5 class="modal-title">View Badge</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center px-10">

                    <div class="row gap-4 fs-5">

                        <div class="col-7 px-7 py-9 text-start border border-solid rounded">
                            <div>
                                <h1>{{ selectedBadge?.badge?.name }}</h1>
                                <p class="fw-bold mb-5 mt-5">
                                    Verified by
                                    <span v-for=" (company, index) in selectedBadge?.badge?.companies" :key="company.id">
                                        <u>{{ company.name }}</u>
                                        <span v-if="index !== selectedBadge?.badge?.companies.length - 1"> + </span>
                                    </span>
                                </p>
                            </div>

                            <div class="mt-7 lh-lg">
                                <p class="mb-1"><span class="text-gray-700">Module Name: </span>{{ selectedBadge?.module_name }}</p>
                                <p class=" mb-1">
                                    <span class="text-gray-700">Credential ID: </span>
                                    {{ selectedBadge?.credential_id || "N/A" }}
                                </p>
                                <p class=" mb-1"><span class="text-gray-700">Issue Date: </span> {{ selectedBadge?.issue_date }}</p>

                                <p v-if="selectedBadge?.expiration_date" class=" mb-1">
                                    <span class="text-gray-700">Expiry Date: </span> {{ selectedBadge.expiration_date }}
                                </p>

                                <p class=" mb-1"><span class="text-gray-700">Module Type: </span>{{  selectedBadge?.module_type }}</p>
                            </div>
                            <!-- Badge Skills layout -->
                            <!-- <div class="mt-15">
                                <p class=" fw-bold fs-6"> Specialist Skills</p>
                                <div class="d-flex flex-wrap gap-2" style="font-size: 12px;">
                                    <span class=" p-1 rounded rounded-2 bg-secondary me-2"><i class="fa fa-check text-dark p-1"></i> Monitor artistic, design, and fashion trends and innovation </span>
                                    <span class=" p-1 rounded rounded-2 bg-secondary me-2"><i class="fa fa-check text-dark p-1"></i> Advise others  </span>
                                    <span class=" p-1 rounded rounded-2 bg-secondary me-2"><i class="fa fa-check text-dark p-1"></i> Advise others on financial matters </span>
                                    <span class=" p-1 rounded rounded-2 bg-secondary me-2"><i class="fa fa-check text-dark p-1"></i> Advise others on financial matters </span>
                                    <span class=" p-1 rounded rounded-2 bg-secondary me-2"><i class="fa fa-check text-dark p-1"></i> Monitor artistic, design, and fashion trends and innovation    </span>

                                </div>
                            </div> -->
                        </div>

                        <div class="col my-auto">
                            <div v-if="selectedBadge">
                                <div class="animated-video" v-if="selectedBadge?.badge?.video" v-html="selectedBadge?.badge?.video"></div>

                                <!-- If no video, fallback to an image -->
                                <img
                                  v-else
                                  :src="selectedBadge?.badge?.animated_image_fullpath || selectedBadge?.badge?.image_fullpath"
                                  alt="Animated Badge"
                                  class="w-100">

                                <!-- <img v-if="selectedBadge" :src="selectedBadge?.badge?.animated_image_fullpath || selectedBadge?.badge?.image_fullpath" alt="Animated Badge" class="w-100"> -->
                            </div>

                        </div>

                    </div>

                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-secondary" @click="openShareModal">
                        Share Badge
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent } from "vue";
import * as bootstrap from 'bootstrap';

export default defineComponent({
    props: {
        selectedBadge: Object,
    },
    emits: ["openShareModal"],

    methods: {
        isVideo(filePath: string) {
            return filePath && filePath.endsWith('.mp4');
        },
        openShareModal() {
            // First, hide the current modal
            const currentModal = document.getElementById('kt_modal_badge');
            if (currentModal) {
                const bsCurrentModal = bootstrap.Modal.getInstance(currentModal);
                if (bsCurrentModal) {
                    bsCurrentModal.hide();
                }
            }

            // Then emit an event to the parent to handle opening the share modal
            this.$emit('openShareModal', this.selectedBadge);
        }
    }
});
</script>

<!-- <script>
export default {
    methods: {
        isVideo(filePath) {
            return filePath && filePath.endsWith('.mp4');
        }
    }
};
</script> -->




<style>
.mw-900px {
    max-width: 900px;
}
.animated-video>iframe{
    width: 100% !important ;
    height: 100% !important ;
}
</style>
