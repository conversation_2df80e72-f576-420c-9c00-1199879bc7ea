<?php

use App\Http\Controllers\LMS\DropzoneController;
use App\Http\Controllers\LMS\PathsController;
use App\Http\Controllers\LMS\ScormTrackingController;
use Illuminate\Support\Facades\Route;

/**
 * ===================================
 *  LMS WEB ROUTES
 * ===================================
 */
Route::group([ 'middleware' => 'auth' ], function () {
    // DROPZONE Routes
    Route::group([ 'prefix' => 'dropzone', 'as' => 'dropzone.' ], function() {
        Route::post('/store', [DropzoneController::class, 'store'])->name('store');
    });

    // SCORM Tracking
    Route::group(['prefix' => 'scorm-tracking', 'as' => 'scorm-tracking.'], function() {
        Route::post('/save', [ScormTrackingController::class, 'save'])->name('save');
        Route::get('/get/{trackableType}/{trackableId}', [ScormTrackingController::class, 'get'])->name('get');
        Route::delete('/{trackableType}/{trackableId}', [ScormTrackingController::class, 'destroy'])->name('destroy');
    });

    // Paths
    Route::group(['middleware' => 'paths'], function () {
        Route::get('/my-path', [PathsController::class, 'myPath'])->name('my-path');
        Route::get('/my-path-modules', [PathsController::class, 'myPathModules'])->name('my-path-modules');
    });
});