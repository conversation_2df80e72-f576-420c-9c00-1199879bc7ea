/*! For license information please see 508.js.LICENSE.txt */
"use strict";(self.webpackChunk=self.webpackChunk||[]).push([[508],{1715:(e,t,a)=>{a.d(t,{Z:()=>n});var l=a(1519),r=a.n(l)()((function(e){return e[1]}));r.push([e.id,".mins .multiselect-wrapper{min-height:22px}.wrap{max-width:55ch;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.banner_detail_box{left:20%;position:absolute;top:50%;transform:translate(-50%,-50%)}.response-upload-input>input{width:104px}.response-upload-input>label{background-color:#fff;border:1px solid #e4e6ef;border-left:none;color:#5e6278;flex-grow:1;font-size:1.1rem;font-weight:500;line-height:1.5;overflow:hidden;padding:.775rem 1rem;text-overflow:ellipsis;transition:border-color .15s ease-in-out,box-shadow .15s ease-in-out;white-space:nowrap}.response-upload-input>input:focus+label{border-color:#b5b5c3}.btn-white-custom{background:#fff;color:#000}.btn-white-custom:hover,.btn.btn-white-custom:hover:not(.btn-active){background-color:#000!important;color:#fff!important}.btn-white-custom:disabled{background-color:#fff;opacity:1}.pointer{cursor:pointer}.overlay{overflow:overlay}.related{right:5%!important}div#kt_app_content{padding-bottom:0;padding-top:0}.btn-white{border:1px solid #000!important}.btn-white:hover,.btn.btn-white:hover:not(.btn-active){background-color:#000!important;color:#fff!important}::-webkit-scrollbar-thumb,::-webkit-scrollbar-thumb:hover{background:#000!important}.module-sections{margin-left:-30px;margin-right:-30px;overflow:auto hidden}.app-content{padding:0}.full-page{margin-left:-20px;margin-right:-20px}.banner{background-color:#000;background-position:50%;background-repeat:no-repeat;background-size:cover;display:block;min-height:calc(45.25vw - 149px);overflow:hidden;position:relative}.full-view-banner{margin-left:-30px;margin-right:-30px}.banner-video{height:100%}.banner-video>video{left:50%;position:absolute;top:50%;transform:translate(-50%,-50%);width:101%!important}.page-content{padding:0 15px;position:absolute;top:40%;width:100%}.module-section{border-bottom:1px solid;border-left:1px solid;border-top:1px solid;cursor:pointer;height:100px}.module-sections>.text-center:last-of-type>.module-section{border-right:1px solid}@media (max-width:1280px){.banner{height:56.25vw}.banner_detail_box{left:40%}.banner-video>video{height:100%!important;width:calc(65vw + 65vh)!important}}@media (max-width:991px){.full-view-banner,.module-sections{margin-left:-20px;margin-right:-20px}.full-view-banner{margin-top:58.16px}}@media (max-width:991px) and (min-width:768px) and (orientation:portrait){.banner{height:86.25vw}.banner-video>video{height:100%!important;width:calc(66vw + 66vh)!important}}@media (max-width:991px) and (orientation:landscape){.banner-video>video{height:auto!important;width:calc(70vw + 70vh)!important}}@media (max-width:767px){.full-page{margin-left:0;margin-right:0}.banner{height:calc(100vh - 300px)}.banner_detail_box{left:50%}}@media (max-width:575px){div#kt_app_content{padding-top:30px}.banner_detail_box{width:70vw!important}.full-view-banner{margin-top:0}.banner-video>video{height:100%!important;width:calc(90vw + 90vh)!important}}",""]);const n=r},77508:(e,t,a)=>{a.r(t),a.d(t,{default:()=>X});var l=a(70821),r=["innerHTML"],n=(0,l.createElementVNode)("div",{style:{position:"absolute",width:"100%",height:"100%",opacity:".3",background:"#000"}},null,-1),o={class:"banner_detail_box w-450px"},i=(0,l.createElementVNode)("h1",{class:"fw-normal text-light"},"Final Step",-1),u={class:"display-4 fw-normal text-light"},s={key:0},c={key:0,class:"row mt-5"},d={class:"col-12 fs-6 text-light d-flex response-upload-input"},p={for:"taskfiles"},v=["textContent"],m={class:"text-danger"},f={class:"row mt-5"},h=(0,l.createElementVNode)("div",{class:"col-12 mb-2"}," Time taken to complete the task: ",-1),g={class:"col-sm-6"},b={class:"form-group m-0 margin-xs time-border"},y={class:"col-sm-6"},w={class:"form-group m-0 margin-xs time-border"},k={class:"col-12"},x={class:"text-danger"},E={class:"text-danger"},L={class:"row mt-5"},C={class:"col-sm-12"},S=["disabled"],V={class:"svg-icon svg-icon-primary svg-icon-2x"},N={key:0,xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",width:"24px",height:"24px",viewBox:"0 0 24 24",version:"1.1"},_=[(0,l.createElementVNode)("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},[(0,l.createElementVNode)("mask",{fill:"white"},[(0,l.createElementVNode)("use",{"xlink:href":"#path-1"})]),(0,l.createElementVNode)("g"),(0,l.createElementVNode)("path",{d:"M15.6274517,4.55882251 L14.4693753,6.2959371 C13.9280401,5.51296885 13.0239252,5 12,5 C10.3431458,5 9,6.34314575 9,8 L9,10 L14,10 L17,10 L18,10 C19.1045695,10 20,10.8954305 20,12 L20,18 C20,19.1045695 19.1045695,20 18,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,12 C4,10.8954305 4.8954305,10 6,10 L7,10 L7,8 C7,5.23857625 9.23857625,3 12,3 C13.4280904,3 14.7163444,3.59871093 15.6274517,4.55882251 Z",fill:"#ffffff"})],-1)],O={key:1,xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",width:"24px",height:"24px",viewBox:"0 0 24 24",version:"1.1"},B=[(0,l.createElementVNode)("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},[(0,l.createElementVNode)("mask",{fill:"white"},[(0,l.createElementVNode)("use",{"xlink:href":"#path-1"})]),(0,l.createElementVNode)("g"),(0,l.createElementVNode)("path",{d:"M7,10 L7,8 C7,5.23857625 9.23857625,3 12,3 C14.7614237,3 17,5.23857625 17,8 L17,10 L18,10 C19.1045695,10 20,10.8954305 20,12 L20,18 C20,19.1045695 19.1045695,20 18,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,12 C4,10.8954305 4.8954305,10 6,10 L7,10 Z M12,5 C10.3431458,5 9,6.34314575 9,8 L9,10 L15,10 L15,8 C15,6.34314575 13.6568542,5 12,5 Z",fill:"#000000"})],-1)],T=["innerHTML"],P=["textContent"],q=["textContent"],I={class:"module-section d-flex flex-column justify-content-center align-items-center py-5"},R={class:"svg-icon svg-icon-primary svg-icon-2x"},D={key:0,xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",width:"24px",height:"24px",viewBox:"0 0 24 24",version:"1.1"},z=[(0,l.createElementVNode)("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},[(0,l.createElementVNode)("mask",{fill:"white"},[(0,l.createElementVNode)("use",{"xlink:href":"#path-1"})]),(0,l.createElementVNode)("g"),(0,l.createElementVNode)("path",{d:"M15.6274517,4.55882251 L14.4693753,6.2959371 C13.9280401,5.51296885 13.0239252,5 12,5 C10.3431458,5 9,6.34314575 9,8 L9,10 L14,10 L17,10 L18,10 C19.1045695,10 20,10.8954305 20,12 L20,18 C20,19.1045695 19.1045695,20 18,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,12 C4,10.8954305 4.8954305,10 6,10 L7,10 L7,8 C7,5.23857625 9.23857625,3 12,3 C13.4280904,3 14.7163444,3.59871093 15.6274517,4.55882251 Z",fill:"#ffffff"})],-1)],M={key:1,xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",width:"24px",height:"24px",viewBox:"0 0 24 24",version:"1.1"},j=[(0,l.createElementVNode)("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},[(0,l.createElementVNode)("mask",{fill:"white"},[(0,l.createElementVNode)("use",{"xlink:href":"#path-1"})]),(0,l.createElementVNode)("g"),(0,l.createElementVNode)("path",{d:"M7,10 L7,8 C7,5.23857625 9.23857625,3 12,3 C14.7614237,3 17,5.23857625 17,8 L17,10 L18,10 C19.1045695,10 20,10.8954305 20,12 L20,18 C20,19.1045695 19.1045695,20 18,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,12 C4,10.8954305 4.8954305,10 6,10 L7,10 Z M12,5 C10.3431458,5 9,6.34314575 9,8 L9,10 L15,10 L15,8 C15,6.34314575 13.6568542,5 12,5 Z",fill:"#000000"})],-1)];var A=a(70655),F=a(72961),H=a(55135),$=a(80894),K=a(22201);function G(e){return G="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},G(e)}function Z(){Z=function(){return e};var e={},t=Object.prototype,a=t.hasOwnProperty,l=Object.defineProperty||function(e,t,a){e[t]=a.value},r="function"==typeof Symbol?Symbol:{},n=r.iterator||"@@iterator",o=r.asyncIterator||"@@asyncIterator",i=r.toStringTag||"@@toStringTag";function u(e,t,a){return Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,a){return e[t]=a}}function s(e,t,a,r){var n=t&&t.prototype instanceof p?t:p,o=Object.create(n.prototype),i=new C(r||[]);return l(o,"_invoke",{value:k(e,a,i)}),o}function c(e,t,a){try{return{type:"normal",arg:e.call(t,a)}}catch(e){return{type:"throw",arg:e}}}e.wrap=s;var d={};function p(){}function v(){}function m(){}var f={};u(f,n,(function(){return this}));var h=Object.getPrototypeOf,g=h&&h(h(S([])));g&&g!==t&&a.call(g,n)&&(f=g);var b=m.prototype=p.prototype=Object.create(f);function y(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function w(e,t){function r(l,n,o,i){var u=c(e[l],e,n);if("throw"!==u.type){var s=u.arg,d=s.value;return d&&"object"==G(d)&&a.call(d,"__await")?t.resolve(d.__await).then((function(e){r("next",e,o,i)}),(function(e){r("throw",e,o,i)})):t.resolve(d).then((function(e){s.value=e,o(s)}),(function(e){return r("throw",e,o,i)}))}i(u.arg)}var n;l(this,"_invoke",{value:function(e,a){function l(){return new t((function(t,l){r(e,a,t,l)}))}return n=n?n.then(l,l):l()}})}function k(e,t,a){var l="suspendedStart";return function(r,n){if("executing"===l)throw new Error("Generator is already running");if("completed"===l){if("throw"===r)throw n;return V()}for(a.method=r,a.arg=n;;){var o=a.delegate;if(o){var i=x(o,a);if(i){if(i===d)continue;return i}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if("suspendedStart"===l)throw l="completed",a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);l="executing";var u=c(e,t,a);if("normal"===u.type){if(l=a.done?"completed":"suspendedYield",u.arg===d)continue;return{value:u.arg,done:a.done}}"throw"===u.type&&(l="completed",a.method="throw",a.arg=u.arg)}}}function x(e,t){var a=t.method,l=e.iterator[a];if(void 0===l)return t.delegate=null,"throw"===a&&e.iterator.return&&(t.method="return",t.arg=void 0,x(e,t),"throw"===t.method)||"return"!==a&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+a+"' method")),d;var r=c(l,e.iterator,t.arg);if("throw"===r.type)return t.method="throw",t.arg=r.arg,t.delegate=null,d;var n=r.arg;return n?n.done?(t[e.resultName]=n.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,d):n:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,d)}function E(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function L(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function C(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(E,this),this.reset(!0)}function S(e){if(e){var t=e[n];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var l=-1,r=function t(){for(;++l<e.length;)if(a.call(e,l))return t.value=e[l],t.done=!1,t;return t.value=void 0,t.done=!0,t};return r.next=r}}return{next:V}}function V(){return{value:void 0,done:!0}}return v.prototype=m,l(b,"constructor",{value:m,configurable:!0}),l(m,"constructor",{value:v,configurable:!0}),v.displayName=u(m,i,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===v||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,m):(e.__proto__=m,u(e,i,"GeneratorFunction")),e.prototype=Object.create(b),e},e.awrap=function(e){return{__await:e}},y(w.prototype),u(w.prototype,o,(function(){return this})),e.AsyncIterator=w,e.async=function(t,a,l,r,n){void 0===n&&(n=Promise);var o=new w(s(t,a,l,r),n);return e.isGeneratorFunction(a)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},y(b),u(b,i,"Generator"),u(b,n,(function(){return this})),u(b,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),a=[];for(var l in t)a.push(l);return a.reverse(),function e(){for(;a.length;){var l=a.pop();if(l in t)return e.value=l,e.done=!1,e}return e.done=!0,e}},e.values=S,C.prototype={constructor:C,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(L),!e)for(var t in this)"t"===t.charAt(0)&&a.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function l(a,l){return o.type="throw",o.arg=e,t.next=a,l&&(t.method="next",t.arg=void 0),!!l}for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r],o=n.completion;if("root"===n.tryLoc)return l("end");if(n.tryLoc<=this.prev){var i=a.call(n,"catchLoc"),u=a.call(n,"finallyLoc");if(i&&u){if(this.prev<n.catchLoc)return l(n.catchLoc,!0);if(this.prev<n.finallyLoc)return l(n.finallyLoc)}else if(i){if(this.prev<n.catchLoc)return l(n.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<n.finallyLoc)return l(n.finallyLoc)}}}},abrupt:function(e,t){for(var l=this.tryEntries.length-1;l>=0;--l){var r=this.tryEntries[l];if(r.tryLoc<=this.prev&&a.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var n=r;break}}n&&("break"===e||"continue"===e)&&n.tryLoc<=t&&t<=n.finallyLoc&&(n=null);var o=n?n.completion:{};return o.type=e,o.arg=t,n?(this.method="next",this.next=n.finallyLoc,d):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),d},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var a=this.tryEntries[t];if(a.finallyLoc===e)return this.complete(a.completion,a.afterLoc),L(a),d}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var a=this.tryEntries[t];if(a.tryLoc===e){var l=a.completion;if("throw"===l.type){var r=l.arg;L(a)}return r}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,a){return this.delegate={iterator:S(e),resultName:t,nextLoc:a},"next"===this.method&&(this.arg=void 0),d}},e}const U=(0,l.defineComponent)({name:"vwe-final-step",components:{Multiselect:H.Z},setup:function(e){var t=(0,l.ref)(),a=(0,l.ref)(),r=(0,l.ref)(),n=(0,l.ref)(0),o=(0,l.ref)(0),i=(0,l.ref)(0),u=(0,l.ref)(),s=(0,$.oR)(),c=(0,K.yj)(),d=(0,K.tv)(),p=s.getters.currentUser;(0,l.onMounted)((function(){E()}));var v=(0,l.ref)(),m=(0,l.ref)(),f=(0,l.ref)(),h=(0,l.ref)(),g=(0,l.ref)(),b=(0,l.ref)(),y=(0,l.ref)();m.value={id:1,background_imagepath:null,background_video:null,response:!1,steps:[]},f.value=0,h.value=c.params.id;var w=(0,l.ref)();var k=(0,l.ref)(),x=(0,l.ref)("");k.value={response:"",hour:"",mins:"",nofile:!m.value.response};var E=function(){F.Z.get("api/vwe",h.value).then((function(e){var t=e.data;if(t.steps.length)for(var a=0;a<t.steps.length;a++)t.steps[a].user_response||(f.value=t.steps[a].id);m.value=t})).catch((function(e){e.response}))};return{currentUser:p,vwe:m,hour:t,minutes:a,uploadedResponse:r,hourError:n,maxHourError:o,minutesError:i,uploadedresponseError:u,toggleRelated:function(){y.value=!y.value},currentvwe:h,showRelatedModuleList:y,latestStep:f,favouriteVwe:function(e){g.value={id:e},F.Z.post("api/vwe/"+e+"/fav",g.value).then((function(e){var t=e.data;m.value.favourite=t.favourite})).catch((function(e){e.response}))},getlatestStep:function(e){if(e.length)for(var t=0;t<e.length;t++)if(!e[t].user_response)return e[t].id},saveResponse:function(){var l;return(0,A.mG)(this,void 0,void 0,Z().mark((function r(){return Z().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:if(n.value=0,i.value=0,u.value=0,console.log(t.value),!m.value.response||(t.value&&!(t.value<0)||a.value&&"00"!=a.value)&&v.value)if(m.value.response&&v.value)try{k.value.response=v.value,k.value.hour=t.value,k.value.mins=a.value,k.value.nofile=!m.value.response,F.Z.upload("api/vwe/"+e.id+"/submit-task",k.value).then((function(e){var t=e.data;void 0!==t.error?x.value=t.error:d.push({name:"task-vwe-view-response",params:{id:m.value.id}}).then((function(){}))})).catch((function(e){e.response}))}catch(e){console.error(e),null===(l=w.value)||void 0===l||l.reset(),v.value=null}else m.value.response||F.Z.upload("api/vwe/"+e.id+"/submit-task",k.value).then((function(e){var t=e.data;void 0!==t.error?x.value=t.error:d.push({name:"task-vwe-view-response",params:{id:m.value.id}}).then((function(){}))})).catch((function(e){e.response}));else t.value&&!(t.value<0)||a.value&&"00"!=a.value||(n.value=1),v.value||(u.value=1);case 5:case"end":return r.stop()}}),r)})))},onFileChanged:function(e){var t=e.target;t&&t.files&&(v.value=t.files[0],b.value=t.files[0].name)},responseError:x,fileName:b,mins:[{value:"00",label:"00"},{value:"15",label:"15"},{value:"30",label:"30"},{value:"45",label:"45"}]}},props:["id"]});var W=a(93379),Y=a.n(W),J=a(1715),Q={insert:"head",singleton:!1};Y()(J.Z,Q);J.Z.locals;const X=(0,a(83744).Z)(U,[["render",function(e,t,a,A,F,H){var $,K=(0,l.resolveComponent)("Multiselect");return(0,l.openBlock)(),(0,l.createElementBlock)(l.Fragment,null,[(0,l.createElementVNode)("div",{class:"full-view-banner banner text-light",style:(0,l.normalizeStyle)({backgroundImage:"url("+e.vwe.background_imagepath+")"})},[e.vwe.background_videoid?((0,l.openBlock)(),(0,l.createElementBlock)("div",{key:0,class:"banner-video",innerHTML:e.vwe.background_videoid},null,8,r)):(0,l.createCommentVNode)("",!0),n,(0,l.createElementVNode)("div",o,[i,(0,l.createElementVNode)("h1",u,[e.vwe.response?((0,l.openBlock)(),(0,l.createElementBlock)("span",s,"Upload & ")):(0,l.createCommentVNode)("",!0),(0,l.createTextVNode)("Submit")]),e.vwe.response?((0,l.openBlock)(),(0,l.createElementBlock)("div",c,[(0,l.createElementVNode)("div",d,[(0,l.createElementVNode)("input",{type:"file",ref:"uploadedResponse",id:"taskfiles",class:"form-control rounded-0",onChange:t[0]||(t[0]=function(t){return e.onFileChanged(t)})},null,544),(0,l.createElementVNode)("label",p,(0,l.toDisplayString)(null!==($=e.fileName)&&void 0!==$?$:"Upload your task"),1)]),e.responseError.length?((0,l.openBlock)(),(0,l.createElementBlock)("p",{key:0,textContent:(0,l.toDisplayString)(e.responseError),class:"form-error mt-2 ms-2"},null,8,v)):(0,l.createCommentVNode)("",!0),(0,l.withDirectives)((0,l.createElementVNode)("p",m,"Please upload your task.",512),[[l.vShow,e.uploadedresponseError]])])):(0,l.createCommentVNode)("",!0),(0,l.createElementVNode)("div",f,[h,(0,l.createElementVNode)("div",g,[(0,l.createElementVNode)("div",b,[(0,l.withDirectives)((0,l.createElementVNode)("input",{type:"number",step:"1",min:"0",id:"hour",name:"hour","onUpdate:modelValue":t[1]||(t[1]=function(t){return e.hour=t}),placeholder:"Hours",class:"form-control rounded-0","aria-describedby":"hour-error","aria-invalid":"true"},null,512),[[l.vModelText,e.hour]])])]),(0,l.createElementVNode)("div",y,[(0,l.createElementVNode)("div",w,[(0,l.createVNode)(K,{class:"rounded-0 form-control fs-6 mins",modelValue:e.minutes,"onUpdate:modelValue":t[2]||(t[2]=function(t){return e.minutes=t}),searchable:!1,placeholder:"Mins","resolve-on-load":!1,options:e.mins},null,8,["modelValue","options"])])]),(0,l.createElementVNode)("div",k,[(0,l.withDirectives)((0,l.createElementVNode)("p",x,"Please enter the time taken to complete the task.",512),[[l.vShow,e.hourError]]),(0,l.withDirectives)((0,l.createElementVNode)("p",E,"Please enter the time taken to complete the task.",512),[[l.vShow,e.maxHourError]])])]),(0,l.createElementVNode)("div",L,[(0,l.createElementVNode)("div",C,[(0,l.createElementVNode)("button",{disabled:!e.currentUser.isStudent,onClick:t[3]||(t[3]=function(t){return e.saveResponse()}),class:"btn btn-white-custom btn-lg rounded-0 w-100 p-md-5"}," Submit Task ",8,S)])])])],4),(0,l.createElementVNode)("div",{class:(0,l.normalizeClass)([{row:e.vwe.steps.length<6},"d-flex module-sections"])},[((0,l.openBlock)(!0),(0,l.createElementBlock)(l.Fragment,null,(0,l.renderList)(e.vwe.steps,(function(t){return(0,l.openBlock)(),(0,l.createElementBlock)("div",{key:t.id,class:(0,l.normalizeClass)([[e.vwe.steps.length<6?"col":"col-6 col-sm-4 col-md-2",t.user_response?"bg-black":""],"text-center p-0"])},[(0,l.createElementVNode)("div",{class:(0,l.normalizeClass)(["module-section d-flex flex-column justify-content-center align-items-center py-5",{"bg-white":!t.user_response}])},[(0,l.createElementVNode)("span",V,[t.user_response?((0,l.openBlock)(),(0,l.createElementBlock)("svg",N,_)):((0,l.openBlock)(),(0,l.createElementBlock)("svg",O,B))]),(0,l.createElementVNode)("p",{class:(0,l.normalizeClass)(["m-0 px-5",{"text-white":t.user_response}]),innerHTML:t.title},null,10,T),(0,l.createElementVNode)("p",{class:(0,l.normalizeClass)(["m-0",{"text-white":t.user_response}])},[t.estimated_time&&t.estimated_time.hours?((0,l.openBlock)(),(0,l.createElementBlock)("span",{key:0,textContent:(0,l.toDisplayString)(t.estimated_time.hours+"h ")},null,8,P)):(0,l.createCommentVNode)("",!0),t.estimated_time&&t.estimated_time.minutes?((0,l.openBlock)(),(0,l.createElementBlock)("span",{key:1,textContent:(0,l.toDisplayString)(t.estimated_time.minutes+"m")},null,8,q)):(0,l.createCommentVNode)("",!0)],2)],2)],2)})),128)),(0,l.createElementVNode)("div",{class:(0,l.normalizeClass)(["text-center p-0",[e.vwe.steps.length<6?"col":"col-6 col-sm-4 col-md-2",e.vwe.user_response?"bg-black":""]])},[(0,l.createElementVNode)("div",I,[(0,l.createElementVNode)("span",R,[e.vwe.user_response?((0,l.openBlock)(),(0,l.createElementBlock)("svg",D,z)):((0,l.openBlock)(),(0,l.createElementBlock)("svg",M,j))]),(0,l.createElementVNode)("p",{class:(0,l.normalizeClass)(["m-0",{"text-white":e.vwe.user_response}])},"Submit",2)])],2)],2)],64)}]])},55135:(e,t,a)=>{a.d(t,{Z:()=>y});var l=a(70821);function r(e){return-1!==[null,void 0].indexOf(e)}function n(e,t,a){const{object:n,valueProp:o,mode:i}=(0,l.toRefs)(e),u=(0,l.getCurrentInstance)().proxy,s=a.iv,c=e=>n.value||r(e)?e:Array.isArray(e)?e.map((e=>e[o.value])):e[o.value],d=e=>r(e)?"single"===i.value?{}:[]:e;return{update:(e,a=!0)=>{s.value=d(e);const l=c(e);t.emit("change",l,u),a&&(t.emit("input",l),t.emit("update:modelValue",l))}}}function o(e,t){const{value:a,modelValue:r,mode:n,valueProp:o}=(0,l.toRefs)(e),i=(0,l.ref)("single"!==n.value?[]:{}),u=r&&void 0!==r.value?r:a,s=(0,l.computed)((()=>"single"===n.value?i.value[o.value]:i.value.map((e=>e[o.value])))),c=(0,l.computed)((()=>"single"!==n.value?i.value.map((e=>e[o.value])).join(","):i.value[o.value]));return{iv:i,internalValue:i,ev:u,externalValue:u,textValue:c,plainValue:s}}function i(e,t,a){const{regex:r}=(0,l.toRefs)(e),n=(0,l.getCurrentInstance)().proxy,o=a.isOpen,i=a.open,u=(0,l.ref)(null),s=(0,l.ref)(null);return(0,l.watch)(u,(e=>{!o.value&&e&&i(),t.emit("search-change",e,n)})),{search:u,input:s,clearSearch:()=>{u.value=""},handleSearchInput:e=>{u.value=e.target.value},handleKeypress:e=>{if(r&&r.value){let t=r.value;"string"==typeof t&&(t=new RegExp(t)),e.key.match(t)||e.preventDefault()}},handlePaste:e=>{if(r&&r.value){let t=(e.clipboardData||window.clipboardData).getData("Text"),a=r.value;"string"==typeof a&&(a=new RegExp(a)),t.split("").every((e=>!!e.match(a)))||e.preventDefault()}t.emit("paste",e,n)}}}function u(e,t,a){const{groupSelect:r,mode:n,groups:o,disabledProp:i}=(0,l.toRefs)(e),u=(0,l.ref)(null),s=e=>{void 0===e||null!==e&&e[i.value]||o.value&&e&&e.group&&("single"===n.value||!r.value)||(u.value=e)};return{pointer:u,setPointer:s,clearPointer:()=>{s(null)}}}function s(e,t=!0){return t?String(e).toLowerCase().trim():String(e).toLowerCase().normalize("NFD").trim().replace(new RegExp(/æ/g),"ae").replace(new RegExp(/œ/g),"oe").replace(new RegExp(/ø/g),"o").replace(/\p{Diacritic}/gu,"")}function c(e,t,a){const{options:n,mode:o,trackBy:i,limit:u,hideSelected:c,createTag:d,createOption:p,label:v,appendNewTag:m,appendNewOption:f,multipleLabel:h,object:g,loading:b,delay:y,resolveOnLoad:w,minChars:k,filterResults:x,clearOnSearch:E,clearOnSelect:L,valueProp:C,allowAbsent:S,groupLabel:V,canDeselect:N,max:_,strict:O,closeOnSelect:B,closeOnDeselect:T,groups:P,reverse:q,infinite:I,groupOptions:R,groupHideEmpty:D,groupSelect:z,onCreate:M,disabledProp:j,searchStart:A,searchFilter:F}=(0,l.toRefs)(e),H=(0,l.getCurrentInstance)().proxy,$=a.iv,K=a.ev,G=a.search,Z=a.clearSearch,U=a.update,W=a.pointer,Y=a.clearPointer,J=a.focus,Q=a.deactivate,X=a.close,ee=a.localize,te=(0,l.ref)([]),ae=(0,l.ref)([]),le=(0,l.ref)(!1),re=(0,l.ref)(null),ne=(0,l.ref)(I.value&&-1===u.value?10:u.value),oe=(0,l.computed)((()=>d.value||p.value||!1)),ie=(0,l.computed)((()=>void 0!==m.value?m.value:void 0===f.value||f.value)),ue=(0,l.computed)((()=>{if(P.value){let e=de.value||[],t=[];return e.forEach((e=>{Me(e[R.value]).forEach((a=>{t.push(Object.assign({},a,e[j.value]?{[j.value]:!0}:{}))}))})),t}{let e=Me(ae.value||[]);return te.value.length&&(e=e.concat(te.value)),e}})),se=(0,l.computed)((()=>{let e=ue.value;return q.value&&(e=e.reverse()),be.value.length&&(e=be.value.concat(e)),ze(e)})),ce=(0,l.computed)((()=>{let e=se.value;return ne.value>0&&(e=e.slice(0,ne.value)),e})),de=(0,l.computed)((()=>{if(!P.value)return[];let e=[],t=ae.value||[];return te.value.length&&e.push({[V.value]:" ",[R.value]:[...te.value],__CREATE__:!0}),e.concat(t)})),pe=(0,l.computed)((()=>{let e=[...de.value].map((e=>({...e})));return be.value.length&&(e[0]&&e[0].__CREATE__?e[0][R.value]=[...be.value,...e[0][R.value]]:e=[{[V.value]:" ",[R.value]:[...be.value],__CREATE__:!0}].concat(e)),e})),ve=(0,l.computed)((()=>{if(!P.value)return[];let e=pe.value;return De((e||[]).map(((e,t)=>{const a=Me(e[R.value]);return{...e,index:t,group:!0,[R.value]:ze(a,!1).map((t=>Object.assign({},t,e[j.value]?{[j.value]:!0}:{}))),__VISIBLE__:ze(a).map((t=>Object.assign({},t,e[j.value]?{[j.value]:!0}:{})))}})))})),me=(0,l.computed)((()=>{switch(o.value){case"single":return!r($.value[C.value]);case"multiple":case"tags":return!r($.value)&&$.value.length>0}})),fe=(0,l.computed)((()=>void 0!==h&&void 0!==h.value?h.value($.value,H):$.value&&$.value.length>1?`${$.value.length} options selected`:"1 option selected")),he=(0,l.computed)((()=>!ue.value.length&&!le.value&&!be.value.length)),ge=(0,l.computed)((()=>ue.value.length>0&&0==ce.value.length&&(G.value&&P.value||!P.value))),be=(0,l.computed)((()=>!1!==oe.value&&G.value?-1!==Ie(G.value)?[]:[{[C.value]:G.value,[ye.value]:G.value,[v.value]:G.value,__CREATE__:!0}]:[])),ye=(0,l.computed)((()=>i.value||v.value)),we=(0,l.computed)((()=>{switch(o.value){case"single":return null;case"multiple":case"tags":return[]}})),ke=(0,l.computed)((()=>b.value||le.value)),xe=e=>{switch("object"!=typeof e&&(e=qe(e)),o.value){case"single":U(e);break;case"multiple":case"tags":U($.value.concat(e))}t.emit("select",Le(e),e,H)},Ee=e=>{switch("object"!=typeof e&&(e=qe(e)),o.value){case"single":Se();break;case"tags":case"multiple":U(Array.isArray(e)?$.value.filter((t=>-1===e.map((e=>e[C.value])).indexOf(t[C.value]))):$.value.filter((t=>t[C.value]!=e[C.value])))}t.emit("deselect",Le(e),e,H)},Le=e=>g.value?e:e[C.value],Ce=e=>{Ee(e)},Se=()=>{t.emit("clear",H),U(we.value)},Ve=e=>{if(void 0!==e.group)return"single"!==o.value&&(Pe(e[R.value])&&e[R.value].length);switch(o.value){case"single":return!r($.value)&&$.value[C.value]==e[C.value];case"tags":case"multiple":return!r($.value)&&-1!==$.value.map((e=>e[C.value])).indexOf(e[C.value])}},Ne=e=>!0===e[j.value],_e=()=>!(void 0===_||-1===_.value||!me.value&&_.value>0)&&$.value.length>=_.value,Oe=e=>{switch(e.__CREATE__&&delete(e={...e}).__CREATE__,o.value){case"single":if(e&&Ve(e))return N.value&&Ee(e),void(T.value&&(Y(),X()));e&&Be(e),L.value&&Z(),B.value&&(Y(),X()),e&&xe(e);break;case"multiple":if(e&&Ve(e))return Ee(e),void(T.value&&(Y(),X()));if(_e())return void t.emit("max",H);e&&(Be(e),xe(e)),L.value&&Z(),c.value&&Y(),B.value&&X();break;case"tags":if(e&&Ve(e))return Ee(e),void(T.value&&(Y(),X()));if(_e())return void t.emit("max",H);e&&Be(e),L.value&&Z(),e&&xe(e),c.value&&Y(),B.value&&X()}B.value||J()},Be=e=>{void 0===qe(e[C.value])&&oe.value&&(t.emit("tag",e[C.value],H),t.emit("option",e[C.value],H),t.emit("create",e[C.value],H),ie.value&&Re(e),Z())},Te=e=>void 0===e.find((e=>!Ve(e)&&!e[j.value])),Pe=e=>void 0===e.find((e=>!Ve(e))),qe=e=>ue.value[ue.value.map((e=>String(e[C.value]))).indexOf(String(e))],Ie=(e,t=!0)=>ue.value.map((e=>parseInt(e[ye.value])==e[ye.value]?parseInt(e[ye.value]):e[ye.value])).indexOf(parseInt(e)==e?parseInt(e):e),Re=e=>{te.value.push(e)},De=e=>D.value?e.filter((e=>G.value?e.__VISIBLE__.length:e[R.value].length)):e.filter((e=>!G.value||e.__VISIBLE__.length)),ze=(e,t=!0)=>{let a=e;if(G.value&&x.value){let e=F.value;e||(e=(e,t)=>{let a=s(ee(e[ye.value]),O.value);return A.value?a.startsWith(s(G.value,O.value)):-1!==a.indexOf(s(G.value,O.value))}),a=a.filter(e)}return c.value&&t&&(a=a.filter((e=>!(e=>-1!==["tags","multiple"].indexOf(o.value)&&c.value&&Ve(e))(e)))),a},Me=e=>{let t=e;var a;return a=t,"[object Object]"===Object.prototype.toString.call(a)&&(t=Object.keys(t).map((e=>{let a=t[e];return{[C.value]:e,[ye.value]:a,[v.value]:a}}))),t=t.map((e=>"object"==typeof e?e:{[C.value]:e,[ye.value]:e,[v.value]:e})),t},je=()=>{r(K.value)||($.value=He(K.value))},Ae=e=>(le.value=!0,new Promise(((t,a)=>{n.value(G.value,H).then((t=>{ae.value=t||[],"function"==typeof e&&e(t),le.value=!1})).catch((e=>{console.error(e),ae.value=[],le.value=!1})).finally((()=>{t()}))}))),Fe=()=>{if(me.value)if("single"===o.value){let e=qe($.value[C.value]);if(void 0!==e){let t=e[v.value];$.value[v.value]=t,g.value&&(K.value[v.value]=t)}}else $.value.forEach(((e,t)=>{let a=qe($.value[t][C.value]);if(void 0!==a){let e=a[v.value];$.value[t][v.value]=e,g.value&&(K.value[t][v.value]=e)}}))},He=e=>r(e)?"single"===o.value?{}:[]:g.value?e:"single"===o.value?qe(e)||(S.value?{[v.value]:e,[C.value]:e,[ye.value]:e}:{}):e.filter((e=>!!qe(e)||S.value)).map((e=>qe(e)||{[v.value]:e,[C.value]:e,[ye.value]:e})),$e=()=>{re.value=(0,l.watch)(G,(e=>{e.length<k.value||!e&&0!==k.value||(le.value=!0,E.value&&(ae.value=[]),setTimeout((()=>{e==G.value&&n.value(G.value,H).then((t=>{e!=G.value&&G.value||(ae.value=t,W.value=ce.value.filter((e=>!0!==e[j.value]))[0]||null,le.value=!1)})).catch((e=>{console.error(e)}))}),y.value))}),{flush:"sync"})};if("single"!==o.value&&!r(K.value)&&!Array.isArray(K.value))throw new Error(`v-model must be an array when using "${o.value}" mode`);return n&&"function"==typeof n.value?w.value?Ae(je):1==g.value&&je():(ae.value=n.value,je()),y.value>-1&&$e(),(0,l.watch)(y,((e,t)=>{re.value&&re.value(),e>=0&&$e()})),(0,l.watch)(K,(e=>{if(r(e))U(He(e),!1);else switch(o.value){case"single":(g.value?e[C.value]!=$.value[C.value]:e!=$.value[C.value])&&U(He(e),!1);break;case"multiple":case"tags":(function(e,t){const a=t.slice().sort();return e.length===t.length&&e.slice().sort().every((function(e,t){return e===a[t]}))})(g.value?e.map((e=>e[C.value])):e,$.value.map((e=>e[C.value])))||U(He(e),!1)}}),{deep:!0}),(0,l.watch)(n,((t,a)=>{"function"==typeof e.options?w.value&&(!a||t&&t.toString()!==a.toString())&&Ae():(ae.value=e.options,Object.keys($.value).length||je(),Fe())})),(0,l.watch)(v,Fe),{pfo:se,fo:ce,filteredOptions:ce,hasSelected:me,multipleLabelText:fe,eo:ue,extendedOptions:ue,eg:de,extendedGroups:de,fg:ve,filteredGroups:ve,noOptions:he,noResults:ge,resolving:le,busy:ke,offset:ne,select:xe,deselect:Ee,remove:Ce,selectAll:()=>{"single"!==o.value&&xe(ce.value.filter((e=>!e.disabled&&!Ve(e))))},clear:Se,isSelected:Ve,isDisabled:Ne,isMax:_e,getOption:qe,handleOptionClick:e=>{if(!Ne(e))return M&&M.value&&!Ve(e)&&e.__CREATE__&&(delete(e={...e}).__CREATE__,(e=M.value(e,H))instanceof Promise)?(le.value=!0,void e.then((e=>{le.value=!1,Oe(e)}))):void Oe(e)},handleGroupClick:e=>{if(!Ne(e)&&"single"!==o.value&&z.value){switch(o.value){case"multiple":case"tags":Te(e[R.value])?Ee(e[R.value]):xe(e[R.value].filter((e=>-1===$.value.map((e=>e[C.value])).indexOf(e[C.value]))).filter((e=>!e[j.value])).filter(((e,t)=>$.value.length+1+t<=_.value||-1===_.value)))}B.value&&Q()}},handleTagRemove:(e,t)=>{0===t.button?Ce(e):t.preventDefault()},refreshOptions:e=>{Ae(e)},resolveOptions:Ae,refreshLabels:Fe}}function d(e,t,a){const{valueProp:r,showOptions:n,searchable:o,groupLabel:i,groups:u,mode:s,groupSelect:c,disabledProp:d,groupOptions:p}=(0,l.toRefs)(e),v=a.fo,m=a.fg,f=a.handleOptionClick,h=a.handleGroupClick,g=a.search,b=a.pointer,y=a.setPointer,w=a.clearPointer,k=a.multiselect,x=a.isOpen,E=(0,l.computed)((()=>v.value.filter((e=>!e[d.value])))),L=(0,l.computed)((()=>m.value.filter((e=>!e[d.value])))),C=(0,l.computed)((()=>"single"!==s.value&&c.value)),S=(0,l.computed)((()=>b.value&&b.value.group)),V=(0,l.computed)((()=>D(b.value))),N=(0,l.computed)((()=>{const e=S.value?b.value:D(b.value),t=L.value.map((e=>e[i.value])).indexOf(e[i.value]);let a=L.value[t-1];return void 0===a&&(a=O.value),a})),_=(0,l.computed)((()=>{let e=L.value.map((e=>e.label)).indexOf(S.value?b.value[i.value]:D(b.value)[i.value])+1;return L.value.length<=e&&(e=0),L.value[e]})),O=(0,l.computed)((()=>[...L.value].slice(-1)[0])),B=(0,l.computed)((()=>b.value.__VISIBLE__.filter((e=>!e[d.value]))[0])),T=(0,l.computed)((()=>{const e=V.value.__VISIBLE__.filter((e=>!e[d.value]));return e[e.map((e=>e[r.value])).indexOf(b.value[r.value])-1]})),P=(0,l.computed)((()=>{const e=D(b.value).__VISIBLE__.filter((e=>!e[d.value]));return e[e.map((e=>e[r.value])).indexOf(b.value[r.value])+1]})),q=(0,l.computed)((()=>[...N.value.__VISIBLE__.filter((e=>!e[d.value]))].slice(-1)[0])),I=(0,l.computed)((()=>[...O.value.__VISIBLE__.filter((e=>!e[d.value]))].slice(-1)[0])),R=()=>{y(E.value[0]||null)},D=e=>L.value.find((t=>-1!==t.__VISIBLE__.map((e=>e[r.value])).indexOf(e[r.value]))),z=()=>{let e=k.value.querySelector("[data-pointed]");if(!e)return;let t=e.parentElement.parentElement;u.value&&(t=S.value?e.parentElement.parentElement.parentElement:e.parentElement.parentElement.parentElement.parentElement),e.offsetTop+e.offsetHeight>t.clientHeight+t.scrollTop&&(t.scrollTop=e.offsetTop+e.offsetHeight-t.clientHeight),e.offsetTop<t.scrollTop&&(t.scrollTop=e.offsetTop)};return(0,l.watch)(g,(e=>{o.value&&(e.length&&n.value?R():w())})),(0,l.watch)(x,(e=>{if(e){let e=k.value.querySelectorAll("[data-selected]")[0];if(!e)return;let t=e.parentElement.parentElement;(0,l.nextTick)((()=>{t.scrollTop>0||(t.scrollTop=e.offsetTop)}))}})),{pointer:b,canPointGroups:C,isPointed:e=>!(!b.value||!(!e.group&&b.value[r.value]===e[r.value]||void 0!==e.group&&b.value[i.value]===e[i.value]))||void 0,setPointerFirst:R,selectPointer:()=>{b.value&&!0!==b.value[d.value]&&(S.value?h(b.value):f(b.value))},forwardPointer:()=>{if(null===b.value)y((u.value&&C.value?L.value[0].__CREATE__?E.value[0]:L.value[0]:E.value[0])||null);else if(u.value&&C.value){let e=S.value?B.value:P.value;void 0===e&&(e=_.value,e.__CREATE__&&(e=e[p.value][0])),y(e||null)}else{let e=E.value.map((e=>e[r.value])).indexOf(b.value[r.value])+1;E.value.length<=e&&(e=0),y(E.value[e]||null)}(0,l.nextTick)((()=>{z()}))},backwardPointer:()=>{if(null===b.value){let e=E.value[E.value.length-1];u.value&&C.value&&(e=I.value,void 0===e&&(e=O.value)),y(e||null)}else if(u.value&&C.value){let e=S.value?q.value:T.value;void 0===e&&(e=S.value?N.value:V.value,e.__CREATE__&&(e=q.value,void 0===e&&(e=N.value))),y(e||null)}else{let e=E.value.map((e=>e[r.value])).indexOf(b.value[r.value])-1;e<0&&(e=E.value.length-1),y(E.value[e]||null)}(0,l.nextTick)((()=>{z()}))}}}function p(e,t,a){const{disabled:r}=(0,l.toRefs)(e),n=(0,l.getCurrentInstance)().proxy,o=(0,l.ref)(!1);return{isOpen:o,open:()=>{o.value||r.value||(o.value=!0,t.emit("open",n))},close:()=>{o.value&&(o.value=!1,t.emit("close",n))}}}function v(e,t,a){const{searchable:r,disabled:n,clearOnBlur:o}=(0,l.toRefs)(e),i=a.input,u=a.open,s=a.close,c=a.clearSearch,d=a.isOpen,p=(0,l.ref)(null),v=(0,l.ref)(null),m=(0,l.ref)(null),f=(0,l.ref)(!1),h=(0,l.ref)(!1),g=(0,l.computed)((()=>r.value||n.value?-1:0)),b=()=>{r.value&&i.value.blur(),v.value.blur()},y=(e=!0)=>{n.value||(f.value=!0,e&&u())},w=()=>{f.value=!1,setTimeout((()=>{f.value||(s(),o.value&&c())}),1)};return{multiselect:p,wrapper:v,tags:m,tabindex:g,isActive:f,mouseClicked:h,blur:b,focus:()=>{r.value&&!n.value&&i.value.focus()},activate:y,deactivate:w,handleFocusIn:e=>{e.target.closest("[data-tags]")&&"INPUT"!==e.target.nodeName||e.target.closest("[data-clear]")||y(h.value)},handleFocusOut:()=>{w()},handleCaretClick:()=>{w(),b()},handleMousedown:e=>{h.value=!0,d.value&&(e.target.isEqualNode(v.value)||e.target.isEqualNode(m.value))?setTimeout((()=>{w()}),0):document.activeElement.isEqualNode(v.value)&&!d.value&&y(),setTimeout((()=>{h.value=!1}),0)}}}function m(e,t,a){const{mode:r,addTagOn:n,openDirection:o,searchable:i,showOptions:u,valueProp:s,groups:c,addOptionOn:d,createTag:p,createOption:v,reverse:m}=(0,l.toRefs)(e),f=(0,l.getCurrentInstance)().proxy,h=a.iv,g=a.update,b=a.search,y=a.setPointer,w=a.selectPointer,k=a.backwardPointer,x=a.forwardPointer,E=a.multiselect,L=a.wrapper,C=a.tags,S=a.isOpen,V=a.open,N=a.blur,_=a.fo,O=(0,l.computed)((()=>p.value||v.value||!1)),B=(0,l.computed)((()=>void 0!==n.value?n.value:void 0!==d.value?d.value:["enter"])),T=()=>{"tags"===r.value&&!u.value&&O.value&&i.value&&!c.value&&y(_.value[_.value.map((e=>e[s.value])).indexOf(b.value)])};return{handleKeydown:e=>{let a,l;switch(t.emit("keydown",e,f),-1!==["ArrowLeft","ArrowRight","Enter"].indexOf(e.key)&&"tags"===r.value&&(a=[...E.value.querySelectorAll("[data-tags] > *")].filter((e=>e!==C.value)),l=a.findIndex((e=>e===document.activeElement))),e.key){case"Backspace":if("single"===r.value)return;if(i.value&&-1===[null,""].indexOf(b.value))return;if(0===h.value.length)return;g((e=>{let t=e.length-1;for(;t>=0&&(!1===e[t].remove||e[t].disabled);)t--;return t<0||e.splice(t,1),e})([...h.value]));break;case"Enter":if(e.preventDefault(),229===e.keyCode)return;if(-1!==l&&void 0!==l)return g([...h.value].filter(((e,t)=>t!==l))),void(l===a.length-1&&(a.length-1?a[a.length-2].focus():i.value?C.value.querySelector("input").focus():L.value.focus()));if(-1===B.value.indexOf("enter")&&O.value)return;T(),w();break;case" ":if(!O.value&&!i.value)return e.preventDefault(),T(),void w();if(!O.value)return!1;if(-1===B.value.indexOf("space")&&O.value)return;e.preventDefault(),T(),w();break;case"Tab":case";":case",":if(-1===B.value.indexOf(e.key.toLowerCase())||!O.value)return;T(),w(),e.preventDefault();break;case"Escape":N();break;case"ArrowUp":if(e.preventDefault(),!u.value)return;S.value||V(),k();break;case"ArrowDown":if(e.preventDefault(),!u.value)return;S.value||V(),x();break;case"ArrowLeft":if(i.value&&C.value&&C.value.querySelector("input").selectionStart||e.shiftKey||"tags"!==r.value||!h.value||!h.value.length)return;e.preventDefault(),-1===l?a[a.length-1].focus():l>0&&a[l-1].focus();break;case"ArrowRight":if(-1===l||e.shiftKey||"tags"!==r.value||!h.value||!h.value.length)return;e.preventDefault(),a.length>l+1?a[l+1].focus():i.value?C.value.querySelector("input").focus():i.value||L.value.focus()}},handleKeyup:e=>{t.emit("keyup",e,f)},preparePointer:T}}function f(e,t,a){const{classes:r,disabled:n,openDirection:o,showOptions:i}=(0,l.toRefs)(e),u=a.isOpen,s=a.isPointed,c=a.isSelected,d=a.isDisabled,p=a.isActive,v=a.canPointGroups,m=a.resolving,f=a.fo,h=(0,l.computed)((()=>({container:"multiselect",containerDisabled:"is-disabled",containerOpen:"is-open",containerOpenTop:"is-open-top",containerActive:"is-active",wrapper:"multiselect-wrapper",singleLabel:"multiselect-single-label",singleLabelText:"multiselect-single-label-text",multipleLabel:"multiselect-multiple-label",search:"multiselect-search",tags:"multiselect-tags",tag:"multiselect-tag",tagDisabled:"is-disabled",tagRemove:"multiselect-tag-remove",tagRemoveIcon:"multiselect-tag-remove-icon",tagsSearchWrapper:"multiselect-tags-search-wrapper",tagsSearch:"multiselect-tags-search",tagsSearchCopy:"multiselect-tags-search-copy",placeholder:"multiselect-placeholder",caret:"multiselect-caret",caretOpen:"is-open",clear:"multiselect-clear",clearIcon:"multiselect-clear-icon",spinner:"multiselect-spinner",inifinite:"multiselect-inifite",inifiniteSpinner:"multiselect-inifite-spinner",dropdown:"multiselect-dropdown",dropdownTop:"is-top",dropdownHidden:"is-hidden",options:"multiselect-options",optionsTop:"is-top",group:"multiselect-group",groupLabel:"multiselect-group-label",groupLabelPointable:"is-pointable",groupLabelPointed:"is-pointed",groupLabelSelected:"is-selected",groupLabelDisabled:"is-disabled",groupLabelSelectedPointed:"is-selected is-pointed",groupLabelSelectedDisabled:"is-selected is-disabled",groupOptions:"multiselect-group-options",option:"multiselect-option",optionPointed:"is-pointed",optionSelected:"is-selected",optionDisabled:"is-disabled",optionSelectedPointed:"is-selected is-pointed",optionSelectedDisabled:"is-selected is-disabled",noOptions:"multiselect-no-options",noResults:"multiselect-no-results",fakeInput:"multiselect-fake-input",assist:"multiselect-assistive-text",spacer:"multiselect-spacer",...r.value}))),g=(0,l.computed)((()=>!!(u.value&&i.value&&(!m.value||m.value&&f.value.length))));return{classList:(0,l.computed)((()=>{const e=h.value;return{container:[e.container].concat(n.value?e.containerDisabled:[]).concat(g.value&&"top"===o.value?e.containerOpenTop:[]).concat(g.value&&"top"!==o.value?e.containerOpen:[]).concat(p.value?e.containerActive:[]),wrapper:e.wrapper,spacer:e.spacer,singleLabel:e.singleLabel,singleLabelText:e.singleLabelText,multipleLabel:e.multipleLabel,search:e.search,tags:e.tags,tag:[e.tag].concat(n.value?e.tagDisabled:[]),tagDisabled:e.tagDisabled,tagRemove:e.tagRemove,tagRemoveIcon:e.tagRemoveIcon,tagsSearchWrapper:e.tagsSearchWrapper,tagsSearch:e.tagsSearch,tagsSearchCopy:e.tagsSearchCopy,placeholder:e.placeholder,caret:[e.caret].concat(u.value?e.caretOpen:[]),clear:e.clear,clearIcon:e.clearIcon,spinner:e.spinner,inifinite:e.inifinite,inifiniteSpinner:e.inifiniteSpinner,dropdown:[e.dropdown].concat("top"===o.value?e.dropdownTop:[]).concat(u.value&&i.value&&g.value?[]:e.dropdownHidden),options:[e.options].concat("top"===o.value?e.optionsTop:[]),group:e.group,groupLabel:t=>{let a=[e.groupLabel];return s(t)?a.push(c(t)?e.groupLabelSelectedPointed:e.groupLabelPointed):c(t)&&v.value?a.push(d(t)?e.groupLabelSelectedDisabled:e.groupLabelSelected):d(t)&&a.push(e.groupLabelDisabled),v.value&&a.push(e.groupLabelPointable),a},groupOptions:e.groupOptions,option:(t,a)=>{let l=[e.option];return s(t)?l.push(c(t)?e.optionSelectedPointed:e.optionPointed):c(t)?l.push(d(t)?e.optionSelectedDisabled:e.optionSelected):(d(t)||a&&d(a))&&l.push(e.optionDisabled),l},noOptions:e.noOptions,noResults:e.noResults,assist:e.assist,fakeInput:e.fakeInput}})),showDropdown:g}}function h(e,t,a){const{limit:r,infinite:n}=(0,l.toRefs)(e),o=a.isOpen,i=a.offset,u=a.search,s=a.pfo,c=a.eo,d=(0,l.ref)(null),p=(0,l.ref)(null),v=(0,l.computed)((()=>i.value<s.value.length)),m=e=>{const{isIntersecting:t,target:a}=e[0];if(t){const e=a.offsetParent,t=e.scrollTop;i.value+=-1==r.value?10:r.value,(0,l.nextTick)((()=>{e.scrollTop=t}))}},f=()=>{o.value&&i.value<s.value.length?d.value.observe(p.value):!o.value&&d.value&&d.value.disconnect()};return(0,l.watch)(o,(()=>{n.value&&f()})),(0,l.watch)(u,(()=>{n.value&&(i.value=r.value,f())}),{flush:"post"}),(0,l.watch)(c,(()=>{n.value&&f()}),{immediate:!1,flush:"post"}),(0,l.onMounted)((()=>{window&&window.IntersectionObserver&&(d.value=new IntersectionObserver(m))})),{hasMore:v,infiniteLoader:p}}function g(e,t,a){const{placeholder:r,id:n,valueProp:o,label:i,mode:u,groupLabel:s,aria:c,searchable:d}=(0,l.toRefs)(e),p=a.pointer,v=a.iv,m=a.hasSelected,f=a.multipleLabelText,h=(0,l.ref)(null),g=(0,l.computed)((()=>{let e=[];return n&&n.value&&e.push(n.value),e.push("assist"),e.join("-")})),b=(0,l.computed)((()=>{let e=[];return n&&n.value&&e.push(n.value),e.push("multiselect-options"),e.join("-")})),y=(0,l.computed)((()=>{let e=[];if(n&&n.value&&e.push(n.value),p.value)return e.push(p.value.group?"multiselect-group":"multiselect-option"),e.push(p.value.group?p.value.index:p.value[o.value]),e.join("-")})),w=(0,l.computed)((()=>r.value)),k=(0,l.computed)((()=>"single"!==u.value)),x=(0,l.computed)((()=>{let e="";return"single"===u.value&&m.value&&(e+=v.value[i.value]),"multiple"===u.value&&m.value&&(e+=f.value),"tags"===u.value&&m.value&&(e+=v.value.map((e=>e[i.value])).join(", ")),e})),E=(0,l.computed)((()=>{let e={...c.value};return d.value&&(e["aria-labelledby"]=e["aria-labelledby"]?`${g.value} ${e["aria-labelledby"]}`:g.value,x.value&&e["aria-label"]&&(e["aria-label"]=`${x.value}, ${e["aria-label"]}`)),e}));return(0,l.onMounted)((()=>{if(n&&n.value&&document&&document.querySelector){let e=document.querySelector(`[for="${n.value}"]`);h.value=e?e.innerText:null}})),{arias:E,ariaLabel:x,ariaAssist:g,ariaControls:b,ariaPlaceholder:w,ariaMultiselectable:k,ariaActiveDescendant:y,ariaOptionId:e=>{let t=[];return n&&n.value&&t.push(n.value),t.push("multiselect-option"),t.push(e[o.value]),t.join("-")},ariaOptionLabel:e=>{let t=[];return t.push(e),t.join(" ")},ariaGroupId:e=>{let t=[];return n&&n.value&&t.push(n.value),t.push("multiselect-group"),t.push(e.index),t.join("-")},ariaGroupLabel:e=>{let t=[];return t.push(e),t.join(" ")},ariaTagLabel:e=>`${e} ❎`}}function b(e,t,a){const{locale:r,fallbackLocale:n}=(0,l.toRefs)(e);return{localize:e=>e&&"object"==typeof e?e&&e[r.value]?e[r.value]:e&&r.value&&e[r.value.toUpperCase()]?e[r.value.toUpperCase()]:e&&e[n.value]?e[n.value]:e&&n.value&&e[n.value.toUpperCase()]?e[n.value.toUpperCase()]:e&&Object.keys(e)[0]?e[Object.keys(e)[0]]:"":e}}var y={name:"Multiselect",emits:["paste","open","close","select","deselect","input","search-change","tag","option","update:modelValue","change","clear","keydown","keyup","max","create"],props:{value:{required:!1},modelValue:{required:!1},options:{type:[Array,Object,Function],required:!1,default:()=>[]},id:{type:[String,Number],required:!1},name:{type:[String,Number],required:!1,default:"multiselect"},disabled:{type:Boolean,required:!1,default:!1},label:{type:String,required:!1,default:"label"},trackBy:{type:String,required:!1,default:void 0},valueProp:{type:String,required:!1,default:"value"},placeholder:{type:String,required:!1,default:null},mode:{type:String,required:!1,default:"single"},searchable:{type:Boolean,required:!1,default:!1},limit:{type:Number,required:!1,default:-1},hideSelected:{type:Boolean,required:!1,default:!0},createTag:{type:Boolean,required:!1,default:void 0},createOption:{type:Boolean,required:!1,default:void 0},appendNewTag:{type:Boolean,required:!1,default:void 0},appendNewOption:{type:Boolean,required:!1,default:void 0},addTagOn:{type:Array,required:!1,default:void 0},addOptionOn:{type:Array,required:!1,default:void 0},caret:{type:Boolean,required:!1,default:!0},loading:{type:Boolean,required:!1,default:!1},noOptionsText:{type:[String,Object],required:!1,default:"The list is empty"},noResultsText:{type:[String,Object],required:!1,default:"No results found"},multipleLabel:{type:Function,required:!1},object:{type:Boolean,required:!1,default:!1},delay:{type:Number,required:!1,default:-1},minChars:{type:Number,required:!1,default:0},resolveOnLoad:{type:Boolean,required:!1,default:!0},filterResults:{type:Boolean,required:!1,default:!0},clearOnSearch:{type:Boolean,required:!1,default:!1},clearOnSelect:{type:Boolean,required:!1,default:!0},canDeselect:{type:Boolean,required:!1,default:!0},canClear:{type:Boolean,required:!1,default:!0},max:{type:Number,required:!1,default:-1},showOptions:{type:Boolean,required:!1,default:!0},required:{type:Boolean,required:!1,default:!1},openDirection:{type:String,required:!1,default:"bottom"},nativeSupport:{type:Boolean,required:!1,default:!1},classes:{type:Object,required:!1,default:()=>({})},strict:{type:Boolean,required:!1,default:!0},closeOnSelect:{type:Boolean,required:!1,default:!0},closeOnDeselect:{type:Boolean,required:!1,default:!1},autocomplete:{type:String,required:!1},groups:{type:Boolean,required:!1,default:!1},groupLabel:{type:String,required:!1,default:"label"},groupOptions:{type:String,required:!1,default:"options"},groupHideEmpty:{type:Boolean,required:!1,default:!1},groupSelect:{type:Boolean,required:!1,default:!0},inputType:{type:String,required:!1,default:"text"},attrs:{required:!1,type:Object,default:()=>({})},onCreate:{required:!1,type:Function},disabledProp:{type:String,required:!1,default:"disabled"},searchStart:{type:Boolean,required:!1,default:!1},reverse:{type:Boolean,required:!1,default:!1},regex:{type:[Object,String,RegExp],required:!1,default:void 0},rtl:{type:Boolean,required:!1,default:!1},infinite:{type:Boolean,required:!1,default:!1},aria:{required:!1,type:Object,default:()=>({})},clearOnBlur:{required:!1,type:Boolean,default:!0},locale:{required:!1,type:String,default:null},fallbackLocale:{required:!1,type:String,default:"en"},searchFilter:{required:!1,type:Function,default:null},allowAbsent:{required:!1,type:Boolean,default:!1}},setup:(e,t)=>function(e,t,a,l={}){return a.forEach((a=>{a&&(l={...l,...a(e,t,l)})})),l}(e,t,[b,o,u,p,i,n,v,c,h,d,m,f,g])};const w=["id","dir"],k=["tabindex","aria-controls","aria-placeholder","aria-expanded","aria-activedescendant","aria-multiselectable","role"],x=["type","modelValue","value","autocomplete","id","aria-controls","aria-placeholder","aria-expanded","aria-activedescendant","aria-multiselectable"],E=["onKeyup","aria-label"],L=["onClick"],C=["type","modelValue","value","id","autocomplete","aria-controls","aria-placeholder","aria-expanded","aria-activedescendant","aria-multiselectable"],S=["innerHTML"],V=["id"],N=["id","aria-label","aria-selected"],_=["data-pointed","onMouseenter","onClick"],O=["innerHTML"],B=["aria-label"],T=["data-pointed","data-selected","onMouseenter","onClick","id","aria-selected","aria-label"],P=["data-pointed","data-selected","onMouseenter","onClick","id","aria-selected","aria-label"],q=["innerHTML"],I=["innerHTML"],R=["value"],D=["name","value"],z=["name","value"],M=["id"];y.render=function(e,t,a,r,n,o){return(0,l.openBlock)(),(0,l.createElementBlock)("div",{ref:"multiselect",class:(0,l.normalizeClass)(e.classList.container),id:a.searchable?void 0:a.id,dir:a.rtl?"rtl":void 0,onFocusin:t[10]||(t[10]=(...t)=>e.handleFocusIn&&e.handleFocusIn(...t)),onFocusout:t[11]||(t[11]=(...t)=>e.handleFocusOut&&e.handleFocusOut(...t)),onKeyup:t[12]||(t[12]=(...t)=>e.handleKeyup&&e.handleKeyup(...t)),onKeydown:t[13]||(t[13]=(...t)=>e.handleKeydown&&e.handleKeydown(...t))},[(0,l.createElementVNode)("div",(0,l.mergeProps)({class:e.classList.wrapper,onMousedown:t[9]||(t[9]=(...t)=>e.handleMousedown&&e.handleMousedown(...t)),ref:"wrapper",tabindex:e.tabindex,"aria-controls":a.searchable?void 0:e.ariaControls,"aria-placeholder":a.searchable?void 0:e.ariaPlaceholder,"aria-expanded":a.searchable?void 0:e.isOpen,"aria-activedescendant":a.searchable?void 0:e.ariaActiveDescendant,"aria-multiselectable":a.searchable?void 0:e.ariaMultiselectable,role:a.searchable?void 0:"combobox"},a.searchable?{}:e.arias),[(0,l.createCommentVNode)(" Search "),"tags"!==a.mode&&a.searchable&&!a.disabled?((0,l.openBlock)(),(0,l.createElementBlock)("input",(0,l.mergeProps)({key:0,type:a.inputType,modelValue:e.search,value:e.search,class:e.classList.search,autocomplete:a.autocomplete,id:a.searchable?a.id:void 0,onInput:t[0]||(t[0]=(...t)=>e.handleSearchInput&&e.handleSearchInput(...t)),onKeypress:t[1]||(t[1]=(...t)=>e.handleKeypress&&e.handleKeypress(...t)),onPaste:t[2]||(t[2]=(0,l.withModifiers)(((...t)=>e.handlePaste&&e.handlePaste(...t)),["stop"])),ref:"input","aria-controls":e.ariaControls,"aria-placeholder":e.ariaPlaceholder,"aria-expanded":e.isOpen,"aria-activedescendant":e.ariaActiveDescendant,"aria-multiselectable":e.ariaMultiselectable,role:"combobox"},{...a.attrs,...e.arias}),null,16,x)):(0,l.createCommentVNode)("v-if",!0),(0,l.createCommentVNode)(" Tags (with search) "),"tags"==a.mode?((0,l.openBlock)(),(0,l.createElementBlock)("div",{key:1,class:(0,l.normalizeClass)(e.classList.tags),"data-tags":""},[((0,l.openBlock)(!0),(0,l.createElementBlock)(l.Fragment,null,(0,l.renderList)(e.iv,((t,r,n)=>(0,l.renderSlot)(e.$slots,"tag",{option:t,handleTagRemove:e.handleTagRemove,disabled:a.disabled},(()=>[((0,l.openBlock)(),(0,l.createElementBlock)("span",{class:(0,l.normalizeClass)([e.classList.tag,t.disabled?e.classList.tagDisabled:null]),tabindex:"-1",onKeyup:(0,l.withKeys)((a=>e.handleTagRemove(t,a)),["enter"]),key:n,"aria-label":e.ariaTagLabel(e.localize(t[a.label]))},[(0,l.createTextVNode)((0,l.toDisplayString)(e.localize(t[a.label]))+" ",1),a.disabled||t.disabled?(0,l.createCommentVNode)("v-if",!0):((0,l.openBlock)(),(0,l.createElementBlock)("span",{key:0,class:(0,l.normalizeClass)(e.classList.tagRemove),onClick:(0,l.withModifiers)((a=>e.handleTagRemove(t,a)),["stop"])},[(0,l.createElementVNode)("span",{class:(0,l.normalizeClass)(e.classList.tagRemoveIcon)},null,2)],10,L))],42,E))])))),256)),(0,l.createElementVNode)("div",{class:(0,l.normalizeClass)(e.classList.tagsSearchWrapper),ref:"tags"},[(0,l.createCommentVNode)(" Used for measuring search width "),(0,l.createElementVNode)("span",{class:(0,l.normalizeClass)(e.classList.tagsSearchCopy)},(0,l.toDisplayString)(e.search),3),(0,l.createCommentVNode)(" Actual search input "),a.searchable&&!a.disabled?((0,l.openBlock)(),(0,l.createElementBlock)("input",(0,l.mergeProps)({key:0,type:a.inputType,modelValue:e.search,value:e.search,class:e.classList.tagsSearch,id:a.searchable?a.id:void 0,autocomplete:a.autocomplete,onInput:t[3]||(t[3]=(...t)=>e.handleSearchInput&&e.handleSearchInput(...t)),onKeypress:t[4]||(t[4]=(...t)=>e.handleKeypress&&e.handleKeypress(...t)),onPaste:t[5]||(t[5]=(0,l.withModifiers)(((...t)=>e.handlePaste&&e.handlePaste(...t)),["stop"])),ref:"input","aria-controls":e.ariaControls,"aria-placeholder":e.ariaPlaceholder,"aria-expanded":e.isOpen,"aria-activedescendant":e.ariaActiveDescendant,"aria-multiselectable":e.ariaMultiselectable,role:"combobox"},{...a.attrs,...e.arias}),null,16,C)):(0,l.createCommentVNode)("v-if",!0)],2)],2)):(0,l.createCommentVNode)("v-if",!0),(0,l.createCommentVNode)(" Single label "),"single"==a.mode&&e.hasSelected&&!e.search&&e.iv?(0,l.renderSlot)(e.$slots,"singlelabel",{key:2,value:e.iv},(()=>[(0,l.createElementVNode)("div",{class:(0,l.normalizeClass)(e.classList.singleLabel)},[(0,l.createElementVNode)("span",{class:(0,l.normalizeClass)(e.classList.singleLabelText)},(0,l.toDisplayString)(e.localize(e.iv[a.label])),3)],2)])):(0,l.createCommentVNode)("v-if",!0),(0,l.createCommentVNode)(" Multiple label "),"multiple"==a.mode&&e.hasSelected&&!e.search?(0,l.renderSlot)(e.$slots,"multiplelabel",{key:3,values:e.iv},(()=>[(0,l.createElementVNode)("div",{class:(0,l.normalizeClass)(e.classList.multipleLabel),innerHTML:e.multipleLabelText},null,10,S)])):(0,l.createCommentVNode)("v-if",!0),(0,l.createCommentVNode)(" Placeholder "),!a.placeholder||e.hasSelected||e.search?(0,l.createCommentVNode)("v-if",!0):(0,l.renderSlot)(e.$slots,"placeholder",{key:4},(()=>[(0,l.createElementVNode)("div",{class:(0,l.normalizeClass)(e.classList.placeholder),"aria-hidden":"true"},(0,l.toDisplayString)(a.placeholder),3)])),(0,l.createCommentVNode)(" Spinner "),a.loading||e.resolving?(0,l.renderSlot)(e.$slots,"spinner",{key:5},(()=>[(0,l.createElementVNode)("span",{class:(0,l.normalizeClass)(e.classList.spinner),"aria-hidden":"true"},null,2)])):(0,l.createCommentVNode)("v-if",!0),(0,l.createCommentVNode)(" Clear "),e.hasSelected&&!a.disabled&&a.canClear&&!e.busy?(0,l.renderSlot)(e.$slots,"clear",{key:6,clear:e.clear},(()=>[(0,l.createElementVNode)("span",{"aria-hidden":"true",tabindex:"0",role:"button","data-clear":"","aria-roledescription":"❎",class:(0,l.normalizeClass)(e.classList.clear),onClick:t[6]||(t[6]=(...t)=>e.clear&&e.clear(...t)),onKeyup:t[7]||(t[7]=(0,l.withKeys)(((...t)=>e.clear&&e.clear(...t)),["enter"]))},[(0,l.createElementVNode)("span",{class:(0,l.normalizeClass)(e.classList.clearIcon)},null,2)],34)])):(0,l.createCommentVNode)("v-if",!0),(0,l.createCommentVNode)(" Caret "),a.caret&&a.showOptions?(0,l.renderSlot)(e.$slots,"caret",{key:7},(()=>[(0,l.createElementVNode)("span",{class:(0,l.normalizeClass)(e.classList.caret),onClick:t[8]||(t[8]=(...t)=>e.handleCaretClick&&e.handleCaretClick(...t)),"aria-hidden":"true"},null,2)])):(0,l.createCommentVNode)("v-if",!0)],16,k),(0,l.createCommentVNode)(" Options "),(0,l.createElementVNode)("div",{class:(0,l.normalizeClass)(e.classList.dropdown),tabindex:"-1"},[(0,l.renderSlot)(e.$slots,"beforelist",{options:e.fo}),(0,l.createElementVNode)("ul",{class:(0,l.normalizeClass)(e.classList.options),id:e.ariaControls,role:"listbox"},[a.groups?((0,l.openBlock)(!0),(0,l.createElementBlock)(l.Fragment,{key:0},(0,l.renderList)(e.fg,((t,r,n)=>((0,l.openBlock)(),(0,l.createElementBlock)("li",{class:(0,l.normalizeClass)(e.classList.group),key:n,id:e.ariaGroupId(t),"aria-label":e.ariaGroupLabel(e.localize(t[a.groupLabel])),"aria-selected":e.isSelected(t),role:"option"},[t.__CREATE__?(0,l.createCommentVNode)("v-if",!0):((0,l.openBlock)(),(0,l.createElementBlock)("div",{key:0,class:(0,l.normalizeClass)(e.classList.groupLabel(t)),"data-pointed":e.isPointed(t),onMouseenter:a=>e.setPointer(t,r),onClick:a=>e.handleGroupClick(t)},[(0,l.renderSlot)(e.$slots,"grouplabel",{group:t,isSelected:e.isSelected,isPointed:e.isPointed},(()=>[(0,l.createElementVNode)("span",{innerHTML:e.localize(t[a.groupLabel])},null,8,O)]))],42,_)),(0,l.createElementVNode)("ul",{class:(0,l.normalizeClass)(e.classList.groupOptions),"aria-label":e.ariaGroupLabel(e.localize(t[a.groupLabel])),role:"group"},[((0,l.openBlock)(!0),(0,l.createElementBlock)(l.Fragment,null,(0,l.renderList)(t.__VISIBLE__,((r,n,o)=>((0,l.openBlock)(),(0,l.createElementBlock)("li",{class:(0,l.normalizeClass)(e.classList.option(r,t)),"data-pointed":e.isPointed(r),"data-selected":e.isSelected(r)||void 0,key:o,onMouseenter:t=>e.setPointer(r),onClick:t=>e.handleOptionClick(r),id:e.ariaOptionId(r),"aria-selected":e.isSelected(r),"aria-label":e.ariaOptionLabel(e.localize(r[a.label])),role:"option"},[(0,l.renderSlot)(e.$slots,"option",{option:r,isSelected:e.isSelected,isPointed:e.isPointed,search:e.search},(()=>[(0,l.createElementVNode)("span",null,(0,l.toDisplayString)(e.localize(r[a.label])),1)]))],42,T)))),128))],10,B)],10,N)))),128)):((0,l.openBlock)(!0),(0,l.createElementBlock)(l.Fragment,{key:1},(0,l.renderList)(e.fo,((t,r,n)=>((0,l.openBlock)(),(0,l.createElementBlock)("li",{class:(0,l.normalizeClass)(e.classList.option(t)),"data-pointed":e.isPointed(t),"data-selected":e.isSelected(t)||void 0,key:n,onMouseenter:a=>e.setPointer(t),onClick:a=>e.handleOptionClick(t),id:e.ariaOptionId(t),"aria-selected":e.isSelected(t),"aria-label":e.ariaOptionLabel(e.localize(t[a.label])),role:"option"},[(0,l.renderSlot)(e.$slots,"option",{option:t,isSelected:e.isSelected,isPointed:e.isPointed,search:e.search},(()=>[(0,l.createElementVNode)("span",null,(0,l.toDisplayString)(e.localize(t[a.label])),1)]))],42,P)))),128))],10,V),e.noOptions?(0,l.renderSlot)(e.$slots,"nooptions",{key:0},(()=>[(0,l.createElementVNode)("div",{class:(0,l.normalizeClass)(e.classList.noOptions),innerHTML:e.localize(a.noOptionsText)},null,10,q)])):(0,l.createCommentVNode)("v-if",!0),e.noResults?(0,l.renderSlot)(e.$slots,"noresults",{key:1},(()=>[(0,l.createElementVNode)("div",{class:(0,l.normalizeClass)(e.classList.noResults),innerHTML:e.localize(a.noResultsText)},null,10,I)])):(0,l.createCommentVNode)("v-if",!0),a.infinite&&e.hasMore?((0,l.openBlock)(),(0,l.createElementBlock)("div",{key:2,class:(0,l.normalizeClass)(e.classList.inifinite),ref:"infiniteLoader"},[(0,l.renderSlot)(e.$slots,"infinite",{},(()=>[(0,l.createElementVNode)("span",{class:(0,l.normalizeClass)(e.classList.inifiniteSpinner)},null,2)]))],2)):(0,l.createCommentVNode)("v-if",!0),(0,l.renderSlot)(e.$slots,"afterlist",{options:e.fo})],2),(0,l.createCommentVNode)(" Hacky input element to show HTML5 required warning "),a.required?((0,l.openBlock)(),(0,l.createElementBlock)("input",{key:0,class:(0,l.normalizeClass)(e.classList.fakeInput),tabindex:"-1",value:e.textValue,required:""},null,10,R)):(0,l.createCommentVNode)("v-if",!0),(0,l.createCommentVNode)(" Native input support "),a.nativeSupport?((0,l.openBlock)(),(0,l.createElementBlock)(l.Fragment,{key:1},["single"==a.mode?((0,l.openBlock)(),(0,l.createElementBlock)("input",{key:0,type:"hidden",name:a.name,value:void 0!==e.plainValue?e.plainValue:""},null,8,D)):((0,l.openBlock)(!0),(0,l.createElementBlock)(l.Fragment,{key:1},(0,l.renderList)(e.plainValue,((e,t)=>((0,l.openBlock)(),(0,l.createElementBlock)("input",{type:"hidden",name:`${a.name}[]`,value:e,key:t},null,8,z)))),128))],64)):(0,l.createCommentVNode)("v-if",!0),(0,l.createCommentVNode)(" Screen reader assistive text "),a.searchable&&e.hasSelected?((0,l.openBlock)(),(0,l.createElementBlock)("div",{key:2,class:(0,l.normalizeClass)(e.classList.assist),id:e.ariaAssist,"aria-hidden":"true"},(0,l.toDisplayString)(e.ariaLabel),11,M)):(0,l.createCommentVNode)("v-if",!0),(0,l.createCommentVNode)(" Create height for empty input "),(0,l.createElementVNode)("div",{class:(0,l.normalizeClass)(e.classList.spacer)},null,2)],42,w)},y.__file="src/Multiselect.vue"}}]);