<?php

namespace App\Http\Controllers\Vue;

use App\CompassUser;
use App\SkillstrainingResponse;
use App\SkillstrainingTemplate;
use App\SkillstrainingCategory;
use Auth;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use App\Menu;
use App\StActivityResponse;
use App\User;
use App\Standard;
use App\Banner;
use App\Events\SkillsTrainingResponseReset;
use App\Events\SkillsTrainingResponseSubmitted;
use App\Services\SCORM;
use App\Services\TimelineService;
use App\WewStep;
use App\Services\UserAccessService;

class WewSkillstrainingController extends Controller
{

    protected $compassSkillsTraining = array(
        22,
        23,
        24,
        26,
        27,
        31,
        32,
        33,
        41,
        43,
        47,
        48,
        49,
        46,
        45,
        58,
        57,
        56,
        55,
        53,
        52,
        51,
        50,
        54
    );

    protected $scorm;

    public function __construct(SCORM $scorm)
    {
        $this->scorm = $scorm;

        $this->middleware(function ($request, $next) {

            if (Auth::user()->isStudent()/*  && Auth::user()->school()->exists() */) {
                return $next($request);
            }
            abort(403, 'You do not have permission to perform this action.');
        })->only('storeResponse');

        $this->middleware(function ($request, $next) {
            if (isset($request->APIkey) && $request->APIkey != "") {
                $isLoggedIn = $this->authenticateCompassUser($request);
                if ($isLoggedIn && in_array($request->id, $this->compassSkillsTraining)) {
                    return redirect($request->url());
                }
                abort(403);
            } elseif (!(Auth::check())) {
                session(['nextLink' => $request->url()]);
                return redirect('/login');
            }
            return $next($request);
        })->only('show');

        $this->middleware(function ($request, $next) {
            if (Auth::user()->school_id && Auth::user()->organisation_id) {
                if (Auth::user()->school->campuses()->exists()) {
                    $menu1 = Menu::whereYearId(Auth::user()->profile->standard_id)->whereParentMenu('Work Experience Week')->whereCampusId(Auth::user()->campuses->first()->id)->where(function ($query) {
                        $query->where('title', 'Skills Training')->orWhere('title', 'Work Experience Week');
                    })->exists();
                } else {
                    $menu1 = Menu::whereYearId(Auth::user()->profile->standard_id)->whereParentMenu('Work Experience Week')->whereSchoolId(Auth::user()->school_id)->where(function ($query) {
                        $query->where('title', 'Skills Training')->orWhere('title', 'Work Experience Week');
                    })->exists();
                }

                if (Auth::user()->organisation->campuses()->exists()) {
                    $menu2 = Menu::whereYearId(Auth::user()->profile->standard_id)->whereParentMenu('Work Experience Week')->whereCampusId(Auth::user()->orgCampuses->first()->id)->where(function ($query) {
                        $query->where('title', 'Skills Training')->orWhere('title', 'Work Experience Week');
                    })->exists();
                } else {
                    $menu2 = Menu::whereYearId(Auth::user()->profile->standard_id)->whereParentMenu('Work Experience Week')->whereSchoolId(Auth::user()->organisation_id)->where(function ($query) {
                        $query->where('title', 'Skills Training')->orWhere('title', 'Work Experience Week');
                    })->exists();
                }
                if ($menu1 && $menu2) {
                    abort(403, 'You do not have permission to perform this action.');
                }
            } elseif (Auth::user()->organisation_id && !Auth::user()->school_id) {
                if (Auth::user()->organisation->campuses()->exists()) {
                    $menu = Menu::whereYearId(Auth::user()->profile->standard_id)->whereParentMenu('Work Experience Week')->whereCampusId(Auth::user()->orgCampuses->first()->id)->where(function ($query) {
                        $query->where('title', 'Skills Training')->orWhere('title', 'Work Experience Week');
                    })->exists();
                } else {
                    $menu = Menu::whereYearId(Auth::user()->profile->standard_id)->whereParentMenu('Work Experience Week')->whereSchoolId(Auth::user()->organisation_id)->where(function ($query) {
                        $query->where('title', 'Skills Training')->orWhere('title', 'Work Experience Week');
                    })->exists();
                }
                if ($menu) {
                    abort(403, 'You do not have permission to perform this action.');
                }
            } elseif (!Auth::user()->organisation_id && Auth::user()->school_id) {
                if (Auth::user()->school->campuses()->exists()) {
                    $menu = Menu::whereYearId(Auth::user()->profile->standard_id)->whereParentMenu('Work Experience Week')->whereCampusId(Auth::user()->campuses->first()->id)->where(function ($query) {
                        $query->where('title', 'Skills Training')->orWhere('title', 'Work Experience Week');
                    })->exists();
                } else {
                    $menu = Menu::whereYearId(Auth::user()->profile->standard_id)->whereParentMenu('Work Experience Week')->whereSchoolId(Auth::user()->school_id)->where(function ($query) {
                        $query->where('title', 'Skills Training')->orWhere('title', 'Work Experience Week');
                    })->exists();
                }
                if ($menu) {
                    abort(403, 'You do not have permission to perform this action.');
                }
            }

            // if (Auth::user()->school()->exists()) {
            //     $menu = Menu::whereYearId(Auth::user()->profile->standard_id)->whereParentMenu('Work Experience Week')->whereSchoolId(Auth::user()->school_id)->where(function($query){
            //         $query->where('title', 'Skills Training')->orWhere('title', 'Work Experience Week');
            //     })->exists();
            //     if($menu) {
            //         abort(403, 'You do not have permission to perform this action.');
            //     }
            // }
            return $next($request);
        });
    }

    public function authenticateCompassUser(Request $request)
    {
        $compassUser = User::whereCompassToken($request->APIkey)->whereNotNull('compass_token')->first();
        if ($compassUser) {
            if (Auth::attempt(['email' => $compassUser->email, 'password' => 'compass'])) {
                $user = new CompassUser();
                //$user->username = $request->username;
                $user->name = $request->name;
                $user->email = $request->emailaddress;
                $user->school = $request->schoolurl;
                $user->state = $request->region;
                $user->save();
            }
            if (Auth::check()) {
                return true;
            }
        }
        abort(403);
    }

    public function list()
    {
        $difficulty = request('difficulty');
        $category = request('category');
        $skill = request('skill');

        $categories = SkillstrainingCategory::whereHas('templates')
            ->with([
                'templates.userResponse',
                'templates.years',
                'templates' => function ($templates) use ($difficulty, $skill) {
                    $templates->published()->select('skillstraining_templates.id', 'skillstraining_templates.tileimage', 'skillstraining_templates.title', 'skillstraining_templates.level', 'skillstraining_templates.estimated_time')
                        ->when(Auth::user()->isStudent(), function ($query) {
                            return $query->forStudents()->whereHas('years', function ($q) {
                                $q->where('standard_id', Auth::user()->profile->standard_id);
                            });
                        })
                        ->when(Auth::user()->isTeacher(), function ($query) {
                            return $query->whereHas('years', function ($q) {
                                $q->whereIn('standard_id', Standard::pluck('id'));
                            });
                        })
                        ->when($difficulty, function ($query) use ($difficulty) {
                            return $query->where('level', $difficulty);
                        })
                        ->when($skill, function ($query) use ($skill) {
                            return $query->withAllTags([$skill]);
                        });
                }
            ])
            ->when($category, function ($query) use ($category) {
                return $query->whereId($category);
            })->orderBy('order')->orderBy('name')->get();
        return $categories->toArray();
    }

    public function detail($id, $student = null)
    {
        if ($student && !UserAccessService::currentUserCanAccess($student)) {
            abort(403, 'You do not have permission to perform this action.');
        }

        $skillstrainingTemplate = SkillstrainingTemplate::find($id)->append('hasresponse')->append('firststepresponse');
        if (!$skillstrainingTemplate->hasresponse && $skillstrainingTemplate->userResponse) {
            $skillstrainingTemplate->userResponse()->delete();
        }
        if (@$skillstrainingTemplate->userResponse()->with('activityResponses.activity')->first()->activityResponses) {
            foreach ($skillstrainingTemplate->userResponse()->with('activityResponses.activity')->first()->activityResponses as $step) {
                if (!$step->activity) {
                    $step->delete();
                }
            }
        }

        $skillstrainingTemplate =  SkillstrainingTemplate::select('id', 'tileimage', 'title', 'level', 'badge_id', 'estimated_time', 'background_imagepath', 'background_videoid', 'foreground_videoid', 'response', 'curriculum')
            ->when(Auth::user()->isStudent(), function ($query) {
                return $query->forStudents()->whereHas('years', function ($q) {
                    $q->where('standard_id', Auth::user()->profile->standard_id);
                });
            })->with('worksheets', 'teacherResources', 'audio', 'tagged:id,tag_name,taggable_id', 'userResponse:id,status,template_id,student_id,filename,response_path,feedback,org_feedback', 'userResponse.activityResponses.activity', 'userResponse.badgeKey.badge.companies', 'steps.userResponse:stresponse_id,response,wew_step_id','badge')->where('id', $id)->first()->append('favourite')->append('hasresponse')->append('firststepresponse');

        // ANZSCO and SCORM -- START
        $skillstrainingTemplate->append([
            'anzsco_tag_names_grouped',
        ]);
        $skillstrainingTemplate->load([
            'steps.scormTrackings' => fn($q) => $q->where('user_id', $student ?: auth()->id()),
            'steps.scormResult' => fn($q) => $q->where('user_id', $student ?: auth()->id()),
        ]);
        $skillstrainingTemplate->steps->each->append('scorm_interactions');
        $skillstrainingTemplate->scorm_scoring_step_result =  $skillstrainingTemplate->userScormScoringStepResult($student ?: auth()->id());
        // ANZSCO and SCORM -- END

        // $relatedskillstraining = $skillstrainingTemplate->relatedskillstraining();
        $relatedModules = $skillstrainingTemplate->workexperienceTemplates()->get();
        $skillstrainingTemplate = $skillstrainingTemplate->toArray();
        $skillstrainingTemplate['relatedModules'] = $relatedModules;
        if ($skillstrainingTemplate && !empty($skillstrainingTemplate['background_imagepath'])) {
            $skillstrainingTemplate['background_imagepath'] = Storage::url($skillstrainingTemplate['background_imagepath']);
        }

        if ($skillstrainingTemplate['user_response'] && (($skillstrainingTemplate['user_response']['feedback'] && Auth::user()->school_id) || ($skillstrainingTemplate['user_response']['org_feedback'] && Auth::user()['organisation_id']))) {
            if ($skillstrainingTemplate['user_response']['feedback'] && Auth::user()['school_id']) {
                $skillstrainingTemplate['feedback'] = $skillstrainingTemplate['user_response']['feedback'];
            } else {
                $skillstrainingTemplate['feedback'] = $skillstrainingTemplate['user_response']['org_feedback'];
            }
        }
        if ($skillstrainingTemplate['relatedModules'] && !empty($skillstrainingTemplate['relatedModules'])) {
            foreach ($skillstrainingTemplate['relatedModules'] as $k => $rl) {
                if (!empty($rl['background_imagepath'])) {
                    $skillstrainingTemplate['relatedModules'][$k]['background_imagepath'] = Storage::url($rl['background_imagepath']);
                }
            }
        }

        // dd($skillstrainingTemplate);

        return $skillstrainingTemplate;
    }

    public function searchSkillsTrainings($value)
    {
        $searchedListData = [];

        $skills = SkillstrainingTemplate::forStudents()->select('id', 'title', 'tileimage', 'level', 'publish')->where('title', 'like', '%' . $value . '%')->take(10)->get();
        if (Auth::user()->hasSkillsTrainingAccess() && $skills->count() > 0) {
            foreach ($skills as $template) {
                if ($template->tileimage) {
                    $searchedListData['skills_trainings'][] = [
                        'id' => $template->id,
                        'tile_img' => $template->tileimage_fullpath,
                        'title' => strlen($template->title) > 35 ? substr($template->title, 0, 35) . "..." : $template->title,
                        'url' =>  "/#/tasks/skillstraining/{$template->id}",
                        'level' => $template->level
                    ];
                }
            }
        } else {
            $searchedListData['skills_trainings'] = '';
        }
        return $searchedListData;
    }

    public function studentResponse($id, $student)
    {
        if (!UserAccessService::currentUserCanAccess($student)) {
            abort(403, 'You do not have permission to perform this action.');
        }

        $skillstraining =  SkillstrainingTemplate::select('id', 'tileimage', 'title', 'level', 'estimated_time', 'badge_id', 'background_imagepath', 'background_videoid', 'foreground_videoid', 'response')->with('tagged:id,tag_name,taggable_id', 'badge')->where('id', $id)->first();

        if (@$skillstraining->userResponse($student)->with('activityResponses.activity')->first()->activityResponses) {
            foreach ($skillstraining->userResponse($student)->with('activityResponses.activity')->first()->activityResponses as $step) {
                if (!$step->activity) {
                    $step->delete();
                }
            }
        }

        // ANZSCO and SCORM -- START
        $skillstraining->append([
            'anzsco_tag_names_grouped'
        ]);
        $skillstraining->load([
            'steps.scormTrackings' => fn($q) => $q->where('user_id', $student),
            'steps.scormResult' => fn($q) => $q->where('user_id', $student),
        ]);
        $skillstraining->steps->each->append('scorm_interactions');
        $skillstraining->scorm_scoring_step_result =  $skillstraining->userScormScoringStepResult($student);
        // ANZSCO and SCORM -- END

        $skillstraining->student_response = $skillstraining->userResponse($student)->with('activityResponses.activity', 'badgeKey.badge.companies')->first()->toArray();

        if ($skillstraining->student_response['status'] != 'Submitted') {
            abort(403, 'You do not have permission to perform this action.');
        }

        if ($skillstraining && !empty($skillstraining['background_imagepath'])) {
            $skillstraining['background_imagepath'] = Storage::url($skillstraining['background_imagepath']);
        }

        // if ($skillstraining->activityResponses) {
        //     foreach ($skillstraining->activityResponses as $k => $rl) {
        //         if ($rl->userResponse($student)->first()) {
        //             $skillstraining->activityResponses[$k]->student_response = $rl->userResponse($student)->with('activity')->first()->toArray();
        //         }
        //     }
        // }

        $skillstraining->student_completed_percentage =  round(((($skillstraining->steps->pluck('student_response')->count() + 1) / ($skillstraining->steps->count() + 1)) * 100) / 5) * 5;

        $skillstraining->feedback = Auth::user()->isTeacher() ? $skillstraining->student_response['feedback'] : $skillstraining->student_response['org_feedback'];

        return $skillstraining->toArray();
    }


    public function banner()
    {
        return Banner::whereType('Skills Training')->first();
    }

    public function  togglefav($id)
    {
        $skillstrainingTemplate = SkillstrainingTemplate::find($id);
        if ($skillstrainingTemplate) {
            Auth::user()->toggleFavorite($skillstrainingTemplate);
        }
        return $skillstrainingTemplate->append('favourite')->only(['id', 'tileimage', 'title', 'level', 'estimated_time', 'favourite']);
    }

    public function  resetSkillstraining($id)
    {
        $skillstrainingTemplate = SkillstrainingTemplate::find($id);
        $stepids = $skillstrainingTemplate->steps->pluck('id');

        $skillstrainingresponse = SkillstrainingResponse::where('template_id', $skillstrainingTemplate->id)->where('student_id', Auth::user()->id)->where('standard_id', Auth::user()->profile->standard_id)->first();

        if ($stepids) {
            if ($skillstrainingresponse) {
                StActivityResponse::where('stresponse_id', $skillstrainingresponse->id)->whereIn('wew_step_id', $stepids)->delete();
            }
        }

        $skillstrainingresponse->delete();
        // ANZSCO and SCORM -- START
        $this->scorm->resetModuleScormTracking(auth()->user(), $skillstrainingTemplate);
        // ANZSCO and SCORM -- END
        event(new SkillsTrainingResponseReset($skillstrainingTemplate,Auth::user()));

        return 'success';
    }

    public function  sectiondetail($id, $sectionid)
    {
        $skillstraining = SkillstrainingTemplate::with('userResponse:status,template_id,student_id,filename', 'steps.userResponse', 'steps.userScormResult')->where('id', $id)->first();
        $skillstraining->toArray();
        if ($skillstraining && !empty($skillstraining['background_imagepath'])) {
            $skillstraining['background_imagepath'] = Storage::url($skillstraining['background_imagepath']);
        }
        if ($skillstraining['steps'] && !empty($skillstraining['steps'])) {
            foreach ($skillstraining['steps'] as $k => $rl) {
                if (!empty($rl['bg_image'])) {
                    $skillstraining['steps'][$k]['bg_image'] = Storage::url($rl['bg_image']);
                }
                if (!empty($rl['scorm_path'])) { // SCORM launch file url
                    $skillstraining['steps'][$k]['scorm_path'] = $this->scorm->scormUrl($rl['scorm_path']);
                }
            }
        }
        return $skillstraining;
    }

    public function storeResponse(Request $request, $id, $sectionid)
    {
        // dd("store");
        if (!Auth::user()->isStudent()) {
            return ['error' => "This serves a demo purpose. You can't submit the task. Thank you!"];
        }

        $section = WewStep::find($sectionid);
        $errors = [];
        if ($section) {
            if ($section->stepable_id == $id) {
                $response = $request->response;
                $user = Auth::user();
                $skillstrainingresponseExisting = SkillstrainingResponse::where(
                    [
                        'template_id' => $section->stepable_id,
                        'student_id' => $user->id,
                        'standard_id' => $user->profile->standard_id
                    ],
                )->first();


                if ($skillstrainingresponseExisting) {
                    $skillstrainingresponse = $skillstrainingresponseExisting;
                } else {
                    $skillstrainingresponse = SkillstrainingResponse::create(
                        [
                            'template_id' => $section->stepable_id,
                            'student_id' => $user->id,
                            'standard_id' => $user->profile->standard_id,
                            'status' => 'Draft'
                        ]
                    );
                }

                event(new SkillsTrainingResponseSubmitted($skillstrainingresponse));

                $response = StActivityResponse::updateOrCreate([
                    'stresponse_id' => $skillstrainingresponse->id,
                    'wew_step_id' => $sectionid
                ], [
                    'response' => $response
                ]);

                if ($skillstrainingresponseExisting) {
                    (new TimelineService())->log($skillstrainingresponse, 'updated');
                } else {
                    (new TimelineService())->log($skillstrainingresponse, 'created');
                }
            } else {
                $errors[] = "Invalid request";
            }
        } else {
            $errors[] = "Invalid request: Section does not exists ";
        }
        if (!empty($errors)) {
        }
        return $response->toArray();
    }


    public function storeFinalResponse(Request $request, $id)
    {
        if (!Auth::user()->isStudent()) {
            return ['error' => "This serves a demo purpose. You can't submit the task. Thank you!"];
        }

        $skillstraining = SkillstrainingTemplate::find($id);
        if ($skillstraining->response && $request->file('response')) {
            $ext = $request->file('response')->getClientOriginalExtension();
            $validator = ['ppt', 'pptx', 'doc', 'docx', 'xlsx', 'xls', 'pages', 'pdf'];

            if (!in_array($ext, $validator)) {
                return ['error' => 'Invalid file format . Valid files are ppt, pptx, doc, docx, xlsx, xls, pages, pdf '];
            }
        } else if ($skillstraining->response && $request->nofile != "false") {
            return ['error' => 'Please select a file to upload'];
        }
        try {
            if ($request->nofile == "false") {
                $path = Storage::cloud()->put('st_responses', $request->file('response'));
                $filename = $request->file('response')->getClientOriginalName();
            } else {
                $path = '';
                $filename = '';
            }

            if (($skillstraining->response && ($path || $request->nofile == "false")) || !$skillstraining->response) {
                $response = SkillstrainingResponse::updateOrCreate(
                    [
                        'template_id' => $skillstraining->id,
                        'student_id' => Auth::id(),
                    ],
                    [
                        'standard_id' => Auth::user()->profile->standard_id,
                        'response_path' => $path,
                        'filename' => $filename,
                        'submitted_at' => now(),
                        'status' => 'Submitted'
                    ]
                );

                if ($response) {
                    event(new SkillsTrainingResponseSubmitted($response));
                    return $response;
                }
                return ['error' => 'Unable to upload your response at the moment '];
            }
        } catch (\Exception $ex) {
            return ['error' => 'Unable to upload your response at the moment ' . $ex->getMessage()];
        }
    }
}
